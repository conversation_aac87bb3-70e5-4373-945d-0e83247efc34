#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试模型文件用于验证期号功能
"""

import sys
import pickle
import json
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def create_test_model_files():
    """创建测试模型文件"""
    print("🔧 创建测试模型文件...")
    
    # 创建模型目录
    positions = ['hundreds', 'tens', 'units']
    for position in positions:
        model_dir = Path(f'models/{position}')
        model_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建几个测试期号的模型
    test_issues = ['2025215', '2025216', '2025217']
    
    for issue in test_issues:
        for position in positions:
            model_dir = Path(f'models/{position}')
            
            # 创建XGBoost模型文件
            xgb_file = model_dir / f"xgb_{position}_model_{issue}.pkl"
            test_xgb_model = {
                'model': 'test_xgb_model',
                'issue': issue,
                'position': position,
                'created_at': datetime.now().isoformat()
            }
            with open(xgb_file, 'wb') as f:
                pickle.dump(test_xgb_model, f)
            print(f"   ✅ 创建: {xgb_file}")
            
            # 创建LightGBM模型文件
            lgb_file = model_dir / f"lgb_{position}_model_{issue}.pkl"
            test_lgb_model = {
                'model': 'test_lgb_model',
                'issue': issue,
                'position': position,
                'created_at': datetime.now().isoformat()
            }
            with open(lgb_file, 'wb') as f:
                pickle.dump(test_lgb_model, f)
            print(f"   ✅ 创建: {lgb_file}")
            
            # 创建LSTM模型文件（.h5和_components.pkl）
            lstm_h5_file = model_dir / f"lstm_{position}_model_{issue}.h5"
            lstm_pkl_file = model_dir / f"lstm_{position}_model_{issue}_components.pkl"
            
            # 创建模拟的.h5文件（实际应该是HDF5格式，这里用文本模拟）
            with open(lstm_h5_file, 'w') as f:
                f.write(f"# Mock LSTM model for {issue} {position}\n")
                f.write(f"# Created at: {datetime.now().isoformat()}\n")
            print(f"   ✅ 创建: {lstm_h5_file}")
            
            # 创建LSTM组件文件
            test_lstm_components = {
                'scaler': 'test_scaler',
                'label_encoder': 'test_label_encoder',
                'issue': issue,
                'position': position,
                'created_at': datetime.now().isoformat()
            }
            with open(lstm_pkl_file, 'wb') as f:
                pickle.dump(test_lstm_components, f)
            print(f"   ✅ 创建: {lstm_pkl_file}")
            
            # 创建集成模型文件
            ensemble_file = model_dir / f"ensemble_{position}_model_{issue}_ensemble.pkl"
            test_ensemble_model = {
                'ensemble_config': 'test_ensemble',
                'models': ['xgb', 'lgb', 'lstm'],
                'issue': issue,
                'position': position,
                'created_at': datetime.now().isoformat()
            }
            with open(ensemble_file, 'wb') as f:
                pickle.dump(test_ensemble_model, f)
            print(f"   ✅ 创建: {ensemble_file}")
    
    print(f"\n🎉 成功创建 {len(test_issues)} 个期号的测试模型文件")
    print(f"   期号: {', '.join(test_issues)}")
    print(f"   位置: {', '.join(positions)}")
    print(f"   每个期号包含: XGBoost, LightGBM, LSTM, 集成模型")

def verify_test_models():
    """验证测试模型文件"""
    print("\n🔍 验证测试模型文件...")
    
    try:
        from src.utils.dynamic_issue_manager import issue_manager
        
        # 获取可用模型
        available_models = issue_manager.get_available_model_issues('hundreds')
        print(f"   ✅ 百位可用模型: {len(available_models)} 个")
        
        for model in available_models:
            print(f"      - {model['issue']}期 ({model['train_date']})")
        
        # 检查模型完整性
        if available_models:
            test_issue = available_models[0]['issue']
            complete = issue_manager.check_models_exist_for_issue(test_issue)
            print(f"   ✅ 期号 {test_issue} 模型完整性检查: {complete}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 创建测试模型文件用于验证期号功能")
    print("=" * 50)
    
    # 创建测试模型文件
    create_test_model_files()
    
    # 验证测试模型
    verify_success = verify_test_models()
    
    if verify_success:
        print("\n🎉 测试模型文件创建和验证成功！")
        print("\n📋 现在可以测试以下功能:")
        print("   ✅ 前端期号选择器应该显示可用期号")
        print("   ✅ API端点应该返回可用模型列表")
        print("   ✅ 模型加载功能应该正常工作")
        return True
    else:
        print("\n⚠️ 测试模型验证失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
