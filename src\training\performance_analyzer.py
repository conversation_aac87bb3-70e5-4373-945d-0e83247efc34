#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能分析器
实现性能瓶颈识别、优化建议生成、资源使用可视化
"""

import sys
import time
import json
import statistics
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from collections import defaultdict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.training.task_manager import task_manager, TaskStatus
from src.training.smart_scheduler import smart_scheduler

@dataclass
class PerformanceMetrics:
    """性能指标"""
    task_id: str
    position: str
    model_type: str
    duration_seconds: float
    memory_peak_mb: float
    cpu_avg_percent: float
    success: bool
    error_type: Optional[str]
    timestamp: str

@dataclass
class BottleneckAnalysis:
    """瓶颈分析结果"""
    bottleneck_type: str  # cpu, memory, io, network
    severity: str  # low, medium, high, critical
    description: str
    impact_score: float  # 0-100
    recommendations: List[str]

@dataclass
class OptimizationSuggestion:
    """优化建议"""
    category: str  # performance, resource, scheduling
    priority: str  # low, medium, high
    title: str
    description: str
    expected_improvement: str
    implementation_effort: str  # low, medium, high

class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self):
        """初始化性能分析器"""
        self.performance_history: List[PerformanceMetrics] = []
        self.analysis_cache = {}
        self.cache_ttl = 300  # 5分钟缓存
        
        # 性能基准
        self.performance_baselines = {
            'xgb': {'duration_seconds': 300, 'memory_mb': 1500},
            'lgb': {'duration_seconds': 250, 'memory_mb': 1200},
            'lstm': {'duration_seconds': 600, 'memory_mb': 3000},
            'ensemble': {'duration_seconds': 400, 'memory_mb': 2500},
        }
        
        # 数据目录
        self.data_dir = Path("data/performance")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
    def record_task_performance(self, task_id: str, metrics: Dict):
        """记录任务性能数据"""
        task = task_manager.get_task(task_id)
        if not task:
            return
            
        performance_metric = PerformanceMetrics(
            task_id=task_id,
            position=task.position,
            model_type=task.model_type,
            duration_seconds=metrics.get('duration_seconds', 0),
            memory_peak_mb=metrics.get('memory_peak_mb', 0),
            cpu_avg_percent=metrics.get('cpu_avg_percent', 0),
            success=task.status == TaskStatus.COMPLETED,
            error_type=metrics.get('error_type'),
            timestamp=datetime.now().isoformat()
        )
        
        self.performance_history.append(performance_metric)
        
        # 限制历史记录大小
        if len(self.performance_history) > 1000:
            self.performance_history = self.performance_history[-1000:]
            
        # 清除缓存
        self.analysis_cache.clear()
        
    def analyze_performance_trends(self, days: int = 7) -> Dict:
        """分析性能趋势"""
        cache_key = f"trends_{days}"
        if cache_key in self.analysis_cache:
            cached_time, cached_result = self.analysis_cache[cache_key]
            if time.time() - cached_time < self.cache_ttl:
                return cached_result
                
        cutoff_time = datetime.now() - timedelta(days=days)
        recent_metrics = [
            m for m in self.performance_history
            if datetime.fromisoformat(m.timestamp) > cutoff_time
        ]
        
        if not recent_metrics:
            return {'error': '没有足够的性能数据'}
            
        # 按模型类型分组分析
        model_trends = defaultdict(list)
        for metric in recent_metrics:
            model_trends[metric.model_type].append(metric)
            
        trends = {}
        for model_type, metrics in model_trends.items():
            if len(metrics) < 2:
                continue
                
            durations = [m.duration_seconds for m in metrics if m.success]
            memory_usage = [m.memory_peak_mb for m in metrics if m.success]
            
            if durations and memory_usage:
                trends[model_type] = {
                    'avg_duration': statistics.mean(durations),
                    'duration_trend': self._calculate_trend(durations),
                    'avg_memory': statistics.mean(memory_usage),
                    'memory_trend': self._calculate_trend(memory_usage),
                    'success_rate': sum(1 for m in metrics if m.success) / len(metrics) * 100,
                    'sample_count': len(metrics)
                }
                
        result = {
            'trends': trends,
            'analysis_period_days': days,
            'total_tasks': len(recent_metrics),
            'timestamp': datetime.now().isoformat()
        }
        
        self.analysis_cache[cache_key] = (time.time(), result)
        return result
        
    def identify_bottlenecks(self) -> List[BottleneckAnalysis]:
        """识别性能瓶颈"""
        cache_key = "bottlenecks"
        if cache_key in self.analysis_cache:
            cached_time, cached_result = self.analysis_cache[cache_key]
            if time.time() - cached_time < self.cache_ttl:
                return cached_result
                
        bottlenecks = []
        
        # 分析最近的任务
        recent_metrics = self.performance_history[-50:] if self.performance_history else []
        
        if not recent_metrics:
            return bottlenecks
            
        # CPU瓶颈分析
        high_cpu_tasks = [m for m in recent_metrics if m.cpu_avg_percent > 90]
        if len(high_cpu_tasks) > len(recent_metrics) * 0.3:
            bottlenecks.append(BottleneckAnalysis(
                bottleneck_type="cpu",
                severity="high",
                description=f"30%以上的任务CPU使用率超过90%",
                impact_score=80.0,
                recommendations=[
                    "降低并发任务数量",
                    "优化CPU密集型算法",
                    "考虑使用更多CPU核心"
                ]
            ))
            
        # 内存瓶颈分析
        for model_type, baseline in self.performance_baselines.items():
            model_metrics = [m for m in recent_metrics if m.model_type == model_type]
            if model_metrics:
                avg_memory = statistics.mean([m.memory_peak_mb for m in model_metrics])
                if avg_memory > baseline['memory_mb'] * 1.5:
                    bottlenecks.append(BottleneckAnalysis(
                        bottleneck_type="memory",
                        severity="medium",
                        description=f"{model_type}模型内存使用超出基准50%",
                        impact_score=60.0,
                        recommendations=[
                            "减少批次大小",
                            "优化数据加载策略",
                            "增加系统内存"
                        ]
                    ))
                    
        # 性能退化分析
        for model_type, baseline in self.performance_baselines.items():
            model_metrics = [m for m in recent_metrics if m.model_type == model_type and m.success]
            if len(model_metrics) >= 5:
                avg_duration = statistics.mean([m.duration_seconds for m in model_metrics])
                if avg_duration > baseline['duration_seconds'] * 1.3:
                    bottlenecks.append(BottleneckAnalysis(
                        bottleneck_type="performance",
                        severity="medium",
                        description=f"{model_type}模型训练时间超出基准30%",
                        impact_score=50.0,
                        recommendations=[
                            "检查数据质量",
                            "优化特征工程",
                            "调整模型参数"
                        ]
                    ))
                    
        self.analysis_cache[cache_key] = (time.time(), bottlenecks)
        return bottlenecks
        
    def generate_optimization_suggestions(self) -> List[OptimizationSuggestion]:
        """生成优化建议"""
        suggestions = []
        
        # 获取瓶颈分析
        bottlenecks = self.identify_bottlenecks()
        
        # 获取资源统计
        resource_stats = smart_scheduler.get_resource_statistics()
        
        # 基于瓶颈生成建议
        for bottleneck in bottlenecks:
            if bottleneck.bottleneck_type == "cpu" and bottleneck.severity == "high":
                suggestions.append(OptimizationSuggestion(
                    category="resource",
                    priority="high",
                    title="CPU使用率优化",
                    description="系统CPU使用率过高，影响训练性能",
                    expected_improvement="提升20-30%的训练速度",
                    implementation_effort="medium"
                ))
                
            elif bottleneck.bottleneck_type == "memory":
                suggestions.append(OptimizationSuggestion(
                    category="resource",
                    priority="medium",
                    title="内存使用优化",
                    description="优化内存使用策略，减少内存峰值",
                    expected_improvement="降低30-40%的内存使用",
                    implementation_effort="low"
                ))
                
        # 基于资源统计生成建议
        if resource_stats:
            if resource_stats.get('cpu_avg', 0) < 50:
                suggestions.append(OptimizationSuggestion(
                    category="scheduling",
                    priority="medium",
                    title="提高并发度",
                    description="CPU使用率较低，可以增加并发任务数",
                    expected_improvement="提升40-50%的整体吞吐量",
                    implementation_effort="low"
                ))
                
            if resource_stats.get('memory_avg', 0) > 80:
                suggestions.append(OptimizationSuggestion(
                    category="performance",
                    priority="high",
                    title="内存压力缓解",
                    description="内存使用率过高，需要优化内存管理",
                    expected_improvement="提升系统稳定性",
                    implementation_effort="medium"
                ))
                
        # 通用优化建议
        if len(self.performance_history) > 10:
            recent_success_rate = sum(1 for m in self.performance_history[-10:] if m.success) / 10
            if recent_success_rate < 0.9:
                suggestions.append(OptimizationSuggestion(
                    category="performance",
                    priority="high",
                    title="提高训练成功率",
                    description=f"最近训练成功率仅{recent_success_rate*100:.1f}%，需要改进",
                    expected_improvement="提升训练可靠性",
                    implementation_effort="high"
                ))
                
        return suggestions
        
    def _calculate_trend(self, values: List[float]) -> str:
        """计算趋势方向"""
        if len(values) < 2:
            return "stable"
            
        # 简单的线性趋势计算
        x = list(range(len(values)))
        n = len(values)
        
        sum_x = sum(x)
        sum_y = sum(values)
        sum_xy = sum(x[i] * values[i] for i in range(n))
        sum_x2 = sum(x[i] ** 2 for i in range(n))
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x ** 2)
        
        if slope > 0.1:
            return "increasing"
        elif slope < -0.1:
            return "decreasing"
        else:
            return "stable"
            
    def get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        if not self.performance_history:
            return {'error': '没有性能数据'}
            
        recent_metrics = self.performance_history[-20:]
        
        # 计算总体统计
        total_tasks = len(recent_metrics)
        successful_tasks = sum(1 for m in recent_metrics if m.success)
        success_rate = successful_tasks / total_tasks * 100 if total_tasks > 0 else 0
        
        # 按模型类型统计
        model_stats = defaultdict(list)
        for metric in recent_metrics:
            if metric.success:
                model_stats[metric.model_type].append(metric)
                
        model_summary = {}
        for model_type, metrics in model_stats.items():
            if metrics:
                durations = [m.duration_seconds for m in metrics]
                memory_usage = [m.memory_peak_mb for m in metrics]
                
                model_summary[model_type] = {
                    'count': len(metrics),
                    'avg_duration': statistics.mean(durations),
                    'avg_memory': statistics.mean(memory_usage),
                    'success_rate': 100.0  # 这里只统计成功的任务
                }
                
        return {
            'total_tasks': total_tasks,
            'success_rate': success_rate,
            'model_summary': model_summary,
            'bottlenecks_count': len(self.identify_bottlenecks()),
            'suggestions_count': len(self.generate_optimization_suggestions()),
            'timestamp': datetime.now().isoformat()
        }
        
    def export_performance_report(self, filename: Optional[str] = None) -> str:
        """导出性能报告"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"performance_report_{timestamp}.json"
            
        report_path = self.data_dir / filename
        
        report = {
            'metadata': {
                'generated_at': datetime.now().isoformat(),
                'total_metrics': len(self.performance_history),
                'analysis_version': '1.0'
            },
            'performance_trends': self.analyze_performance_trends(),
            'bottlenecks': [asdict(b) for b in self.identify_bottlenecks()],
            'optimization_suggestions': [asdict(s) for s in self.generate_optimization_suggestions()],
            'performance_summary': self.get_performance_summary()
        }
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
            
        return str(report_path)

# 全局性能分析器实例
performance_analyzer = PerformanceAnalyzer()

if __name__ == "__main__":
    # 测试代码
    print("📊 性能分析器测试")
    
    # 模拟一些性能数据
    test_metrics = [
        {'duration_seconds': 280, 'memory_peak_mb': 1400, 'cpu_avg_percent': 75},
        {'duration_seconds': 320, 'memory_peak_mb': 1600, 'cpu_avg_percent': 85},
        {'duration_seconds': 290, 'memory_peak_mb': 1450, 'cpu_avg_percent': 70},
    ]
    
    # 创建测试任务并记录性能
    for i, metrics in enumerate(test_metrics):
        task_id = task_manager.create_task("hundreds", "xgb", f"202521{i}")
        task_manager.complete_task(task_id, metrics)
        performance_analyzer.record_task_performance(task_id, metrics)
        
    # 分析性能
    trends = performance_analyzer.analyze_performance_trends()
    print(f"性能趋势: {trends}")
    
    bottlenecks = performance_analyzer.identify_bottlenecks()
    print(f"瓶颈分析: {len(bottlenecks)} 个瓶颈")
    
    suggestions = performance_analyzer.generate_optimization_suggestions()
    print(f"优化建议: {len(suggestions)} 条建议")
    
    # 导出报告
    report_path = performance_analyzer.export_performance_report()
    print(f"报告已导出: {report_path}")
