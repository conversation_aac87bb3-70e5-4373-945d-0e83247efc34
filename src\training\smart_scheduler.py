#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能调度算法
实现资源感知调度、动态参数调整和智能任务优先级排序
"""

import sys
import time
import threading
import logging
import psutil
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.training.task_manager import task_manager, TaskStatus, TaskPriority, TrainingTask

class ResourceLevel(Enum):
    """资源使用级别"""
    LOW = "low"          # 低使用率 < 30%
    MEDIUM = "medium"    # 中等使用率 30-70%
    HIGH = "high"        # 高使用率 70-90%
    CRITICAL = "critical" # 临界使用率 > 90%

@dataclass
class SystemResources:
    """系统资源状态"""
    cpu_percent: float
    memory_percent: float
    memory_available_gb: float
    disk_usage_percent: float
    network_io_mbps: float
    timestamp: str
    
    def get_cpu_level(self) -> ResourceLevel:
        """获取CPU使用级别"""
        if self.cpu_percent < 30:
            return ResourceLevel.LOW
        elif self.cpu_percent < 70:
            return ResourceLevel.MEDIUM
        elif self.cpu_percent < 90:
            return ResourceLevel.HIGH
        else:
            return ResourceLevel.CRITICAL
    
    def get_memory_level(self) -> ResourceLevel:
        """获取内存使用级别（紧急模式：提高阈值）"""
        if self.memory_percent < 50:
            return ResourceLevel.LOW
        elif self.memory_percent < 80:
            return ResourceLevel.MEDIUM
        elif self.memory_percent < 95:  # 提高到95%
            return ResourceLevel.HIGH
        else:
            return ResourceLevel.CRITICAL

@dataclass
class SchedulingDecision:
    """调度决策"""
    should_start_task: bool
    recommended_concurrency: int
    suggested_batch_size: int
    estimated_memory_usage: float
    priority_adjustment: float
    reason: str

class SmartScheduler:
    """智能调度器"""
    
    def __init__(self, max_concurrent_tasks: int = 2):
        """
        初始化智能调度器
        
        Args:
            max_concurrent_tasks: 最大并发任务数
        """
        self.max_concurrent_tasks = max_concurrent_tasks
        self.is_monitoring = False
        self.monitor_thread = None
        
        # 资源历史记录
        self.resource_history: List[SystemResources] = []
        self.max_history_size = 100
        
        # 调度策略配置
        self.scheduling_config = {
            'cpu_threshold_high': 80.0,
            'memory_threshold_high': 85.0,
            'cpu_threshold_critical': 95.0,
            'memory_threshold_critical': 95.0,
            'min_available_memory_gb': 2.0,
            'priority_boost_factor': 1.5,
            'resource_check_interval': 10.0,
        }
        
        # 模型资源需求估算
        self.model_resource_requirements = {
            'xgb': {'memory_gb': 1.5, 'cpu_intensive': True},
            'lgb': {'memory_gb': 1.2, 'cpu_intensive': True},
            'lstm': {'memory_gb': 3.0, 'cpu_intensive': False},
            'ensemble': {'memory_gb': 2.5, 'cpu_intensive': True},
        }
        
        # 设置日志
        self.logger = logging.getLogger('SmartScheduler')
        self.logger.setLevel(logging.INFO)
        
        # 数据目录
        self.data_dir = Path("data/scheduling")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
    def start_monitoring(self):
        """开始资源监控"""
        if self.is_monitoring:
            return
            
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        self.logger.info("🧠 智能调度器监控已启动")
        
    def stop_monitoring(self):
        """停止资源监控"""
        if not self.is_monitoring:
            return
            
        self.is_monitoring = False
        
        if self.monitor_thread:
            self.monitor_thread.join(timeout=10)
            
        self.logger.info("⏹️ 智能调度器监控已停止")
        
    def _monitoring_loop(self):
        """监控主循环"""
        while self.is_monitoring:
            try:
                # 收集系统资源
                resources = self._collect_system_resources()
                self.resource_history.append(resources)
                
                # 限制历史记录大小
                if len(self.resource_history) > self.max_history_size:
                    self.resource_history = self.resource_history[-self.max_history_size:]
                
                # 执行智能调度
                self._perform_intelligent_scheduling(resources)
                
                time.sleep(self.scheduling_config['resource_check_interval'])
                
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                time.sleep(30)
                
    def _collect_system_resources(self) -> SystemResources:
        """收集系统资源信息"""
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存信息
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_available_gb = memory.available / 1024 / 1024 / 1024
        
        # 磁盘使用率
        disk = psutil.disk_usage('/')
        disk_usage_percent = disk.percent
        
        # 网络IO（简化版本）
        network_io_mbps = 0.0
        try:
            network_io = psutil.net_io_counters()
            # 这里可以计算实际的网络速度，暂时设为0
        except:
            pass
            
        return SystemResources(
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            memory_available_gb=memory_available_gb,
            disk_usage_percent=disk_usage_percent,
            network_io_mbps=network_io_mbps,
            timestamp=datetime.now().isoformat()
        )
        
    def _perform_intelligent_scheduling(self, resources: SystemResources):
        """执行智能调度"""
        # 获取当前任务状态
        running_tasks = task_manager.get_running_tasks()
        pending_tasks = task_manager.get_pending_tasks()
        
        # 检查是否需要调整并发数
        self._adjust_concurrency(resources, running_tasks)
        
        # 智能任务优先级调整
        self._adjust_task_priorities(resources, pending_tasks)
        
        # 检查是否可以启动新任务
        if len(running_tasks) < self.max_concurrent_tasks and pending_tasks:
            decision = self._make_scheduling_decision(resources, pending_tasks[0])
            if decision.should_start_task:
                self.logger.info(f"🚀 智能调度决定启动任务: {pending_tasks[0].task_id}")
                self.logger.info(f"📊 调度原因: {decision.reason}")
                
    def _adjust_concurrency(self, resources: SystemResources, running_tasks: List[TrainingTask]):
        """动态调整并发数"""
        current_concurrency = len(running_tasks)
        
        # 根据资源使用情况调整最大并发数
        if resources.get_cpu_level() == ResourceLevel.CRITICAL or \
           resources.get_memory_level() == ResourceLevel.CRITICAL:
            # 资源紧张，降低并发数
            new_max = max(1, self.max_concurrent_tasks - 1)
            if new_max != self.max_concurrent_tasks:
                self.max_concurrent_tasks = new_max
                self.logger.warning(f"⚠️ 资源紧张，降低最大并发数至: {new_max}")
                
        elif resources.get_cpu_level() == ResourceLevel.LOW and \
             resources.get_memory_level() == ResourceLevel.LOW:
            # 资源充足，可以提高并发数
            new_max = min(4, self.max_concurrent_tasks + 1)
            if new_max != self.max_concurrent_tasks:
                self.max_concurrent_tasks = new_max
                self.logger.info(f"📈 资源充足，提高最大并发数至: {new_max}")
                
    def _adjust_task_priorities(self, resources: SystemResources, pending_tasks: List[TrainingTask]):
        """智能任务优先级调整"""
        if not pending_tasks:
            return
            
        # 根据资源状况和模型特性调整优先级
        for task in pending_tasks:
            model_req = self.model_resource_requirements.get(task.model_type, {})
            
            # 如果内存紧张，优先执行内存需求较小的任务
            if resources.get_memory_level() in [ResourceLevel.HIGH, ResourceLevel.CRITICAL]:
                if model_req.get('memory_gb', 2.0) < 2.0:
                    # 提升低内存需求任务的优先级
                    if task.priority != TaskPriority.HIGH:
                        self.logger.info(f"📈 内存紧张，提升低内存任务优先级: {task.task_id}")
                        
            # 如果CPU紧张，优先执行非CPU密集型任务
            if resources.get_cpu_level() in [ResourceLevel.HIGH, ResourceLevel.CRITICAL]:
                if not model_req.get('cpu_intensive', True):
                    # 提升非CPU密集型任务的优先级
                    if task.priority != TaskPriority.HIGH:
                        self.logger.info(f"📈 CPU紧张，提升非CPU密集型任务优先级: {task.task_id}")
                        
    def _make_scheduling_decision(self, resources: SystemResources, task: TrainingTask) -> SchedulingDecision:
        """制定调度决策"""
        model_req = self.model_resource_requirements.get(task.model_type, {})
        estimated_memory = model_req.get('memory_gb', 2.0)
        
        # 检查内存是否足够
        if resources.memory_available_gb < estimated_memory:
            return SchedulingDecision(
                should_start_task=False,
                recommended_concurrency=0,
                suggested_batch_size=16,
                estimated_memory_usage=estimated_memory,
                priority_adjustment=0.0,
                reason=f"内存不足: 需要{estimated_memory}GB，可用{resources.memory_available_gb:.1f}GB"
            )
            
        # 检查CPU使用率
        if resources.cpu_percent > self.scheduling_config['cpu_threshold_critical']:
            return SchedulingDecision(
                should_start_task=False,
                recommended_concurrency=0,
                suggested_batch_size=16,
                estimated_memory_usage=estimated_memory,
                priority_adjustment=0.0,
                reason=f"CPU使用率过高: {resources.cpu_percent:.1f}%"
            )
            
        # 根据资源状况调整批次大小
        if resources.get_memory_level() == ResourceLevel.HIGH:
            suggested_batch_size = 8  # 降低批次大小
        elif resources.get_memory_level() == ResourceLevel.LOW:
            suggested_batch_size = 32  # 提高批次大小
        else:
            suggested_batch_size = 16  # 默认批次大小
            
        # 计算推荐并发数
        if resources.get_cpu_level() == ResourceLevel.LOW and resources.get_memory_level() == ResourceLevel.LOW:
            recommended_concurrency = min(self.max_concurrent_tasks, 3)
        else:
            recommended_concurrency = min(self.max_concurrent_tasks, 2)
            
        return SchedulingDecision(
            should_start_task=True,
            recommended_concurrency=recommended_concurrency,
            suggested_batch_size=suggested_batch_size,
            estimated_memory_usage=estimated_memory,
            priority_adjustment=1.0,
            reason=f"资源充足，CPU: {resources.cpu_percent:.1f}%, 内存: {resources.memory_percent:.1f}%"
        )
        
    def get_resource_statistics(self) -> Dict:
        """获取资源统计信息"""
        if not self.resource_history:
            return {}
            
        recent_resources = self.resource_history[-10:]  # 最近10次记录
        
        cpu_values = [r.cpu_percent for r in recent_resources]
        memory_values = [r.memory_percent for r in recent_resources]
        
        return {
            'cpu_avg': sum(cpu_values) / len(cpu_values),
            'cpu_max': max(cpu_values),
            'cpu_min': min(cpu_values),
            'memory_avg': sum(memory_values) / len(memory_values),
            'memory_max': max(memory_values),
            'memory_min': min(memory_values),
            'samples_count': len(recent_resources),
            'current_max_concurrent': self.max_concurrent_tasks,
            'monitoring_active': self.is_monitoring
        }
        
    def get_scheduling_recommendations(self) -> Dict:
        """获取调度建议"""
        if not self.resource_history:
            return {'recommendations': []}
            
        latest_resources = self.resource_history[-1]
        pending_tasks = task_manager.get_pending_tasks()
        
        recommendations = []
        
        # 资源使用建议
        if latest_resources.get_cpu_level() == ResourceLevel.CRITICAL:
            recommendations.append({
                'type': 'warning',
                'message': 'CPU使用率过高，建议暂停新任务启动',
                'action': 'reduce_concurrency'
            })
            
        if latest_resources.get_memory_level() == ResourceLevel.CRITICAL:
            recommendations.append({
                'type': 'warning',
                'message': '内存使用率过高，建议降低批次大小',
                'action': 'reduce_batch_size'
            })
            
        # 任务优化建议
        if pending_tasks:
            lstm_tasks = [t for t in pending_tasks if t.model_type == 'lstm']
            if lstm_tasks and latest_resources.memory_available_gb < 4.0:
                recommendations.append({
                    'type': 'info',
                    'message': 'LSTM任务需要更多内存，建议在资源充足时执行',
                    'action': 'defer_lstm_tasks'
                })
                
        return {
            'recommendations': recommendations,
            'current_resources': {
                'cpu_percent': latest_resources.cpu_percent,
                'memory_percent': latest_resources.memory_percent,
                'memory_available_gb': latest_resources.memory_available_gb
            },
            'timestamp': latest_resources.timestamp
        }

# 全局智能调度器实例
smart_scheduler = SmartScheduler()

if __name__ == "__main__":
    # 测试代码
    print("🧠 启动智能调度器测试")
    
    smart_scheduler.start_monitoring()
    
    try:
        # 运行一段时间
        time.sleep(30)
        
        # 显示统计信息
        stats = smart_scheduler.get_resource_statistics()
        print(f"📊 资源统计: {stats}")
        
        # 显示调度建议
        recommendations = smart_scheduler.get_scheduling_recommendations()
        print(f"💡 调度建议: {recommendations}")
        
    except KeyboardInterrupt:
        print("用户中断")
    finally:
        smart_scheduler.stop_monitoring()
