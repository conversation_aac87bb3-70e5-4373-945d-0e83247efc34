#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练执行器
负责后台执行训练任务、进程管理和状态更新
"""

import sys
import subprocess
import threading
import time
import logging
import psutil
import signal
from pathlib import Path
from typing import Dict, Optional, Callable
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.training.task_manager import task_manager, TaskStatus, TrainingTask
from src.training.smart_scheduler import smart_scheduler
from scripts.memory_monitor import MemoryMonitor

class TrainingWorker:
    """训练执行器"""
    
    def __init__(self, worker_id: str = "worker-1"):
        """
        初始化训练执行器
        
        Args:
            worker_id: 工作器ID
        """
        self.worker_id = worker_id
        self.is_running = False
        self.current_task_id = None
        self.current_process = None
        self.worker_thread = None
        self.memory_monitor = None
        
        # 设置日志
        self.logger = logging.getLogger(f'TrainingWorker-{worker_id}')
        self.logger.setLevel(logging.INFO)
        
        # 进度回调函数
        self.progress_callback: Optional[Callable] = None
        
    def start(self, progress_callback: Optional[Callable] = None):
        """
        启动工作器
        
        Args:
            progress_callback: 进度回调函数
        """
        if self.is_running:
            return
            
        self.is_running = True
        self.progress_callback = progress_callback
        
        self.worker_thread = threading.Thread(target=self._worker_loop)
        self.worker_thread.daemon = True
        self.worker_thread.start()

        # 启动智能调度器
        if not smart_scheduler.is_monitoring:
            smart_scheduler.start_monitoring()
            self.logger.info("🧠 智能调度器已启动")

        self.logger.info(f"🚀 训练工作器 {self.worker_id} 已启动")
        
    def stop(self):
        """停止工作器"""
        if not self.is_running:
            return
            
        self.is_running = False
        
        # 停止当前任务
        if self.current_process:
            self._terminate_current_task()
            
        # 等待工作线程结束
        if self.worker_thread:
            self.worker_thread.join(timeout=10)

        # 停止智能调度器
        if smart_scheduler.is_monitoring:
            smart_scheduler.stop_monitoring()
            self.logger.info("⏹️ 智能调度器已停止")

        self.logger.info(f"⏹️ 训练工作器 {self.worker_id} 已停止")
        
    def _worker_loop(self):
        """工作器主循环"""
        while self.is_running:
            try:
                # 获取下一个任务
                next_task_id = task_manager.get_next_task()
                
                if next_task_id:
                    self._execute_task(next_task_id)
                else:
                    # 没有任务时休息
                    time.sleep(5)
                    
            except Exception as e:
                self.logger.error(f"工作器循环异常: {e}")
                time.sleep(10)
                
    def _execute_task(self, task_id: str):
        """
        执行训练任务
        
        Args:
            task_id: 任务ID
        """
        self.current_task_id = task_id
        task = task_manager.get_task(task_id)
        
        if not task:
            return
            
        try:
            # 启动任务
            if not task_manager.start_task(task_id):
                self.logger.warning(f"无法启动任务: {task_id}")
                return
                
            self.logger.info(f"🎯 执行任务: {task_id} - {task.position} {task.model_type}")
            
            # 构建训练命令
            cmd = self._build_training_command(task)
            self.logger.info(f"📝 执行命令: {' '.join(cmd)}")
            
            # 启动内存监控
            self._start_memory_monitoring(task_id)
            
            # 执行训练
            result = self._run_training_process(cmd, task_id)
            
            # 停止内存监控
            memory_stats = self._stop_memory_monitoring()
            
            # 处理结果
            if result['success']:
                task_manager.complete_task(task_id, {
                    'duration_seconds': result['duration'],
                    'memory_stats': memory_stats,
                    'output': result['output']
                })
                self.logger.info(f"✅ 任务完成: {task_id}")
            else:
                task_manager.fail_task(task_id, result['error'])
                self.logger.error(f"❌ 任务失败: {task_id} - {result['error']}")
                
        except Exception as e:
            task_manager.fail_task(task_id, str(e))
            self.logger.error(f"❌ 任务执行异常: {task_id} - {e}")
            
        finally:
            self.current_task_id = None
            self.current_process = None
            
    def _build_training_command(self, task: TrainingTask) -> list:
        """构建训练命令"""
        script_name = f"train_{task.position}_predictor.py"
        cmd = [sys.executable, f"scripts/{script_name}"]
        
        # 添加模型参数
        if task.model_type != "all":
            cmd.extend(["--model", task.model_type])
            
        # 添加期号参数
        if task.issue:
            cmd.extend(["--issue", task.issue])
            
        # 添加训练截止期号
        if task.train_until:
            cmd.extend(["--train-until", task.train_until])
            
        return cmd
        
    def _run_training_process(self, cmd: list, task_id: str) -> Dict:
        """运行训练进程"""
        start_time = time.time()
        
        try:
            # 启动进程
            self.current_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                cwd=project_root,
                bufsize=1,
                universal_newlines=True
            )
            
            # 监控进程输出和进度
            output_lines = []
            error_lines = []
            
            while self.current_process.poll() is None:
                # 检查是否需要停止
                if not self.is_running:
                    self._terminate_current_task()
                    break
                    
                # 读取输出
                if self.current_process.stdout:
                    line = self.current_process.stdout.readline()
                    if line:
                        output_lines.append(line.strip())
                        self._parse_progress_from_output(line, task_id)
                        
                # 读取错误输出
                if self.current_process.stderr:
                    line = self.current_process.stderr.readline()
                    if line:
                        error_lines.append(line.strip())
                        
                time.sleep(0.1)
                
            # 获取剩余输出
            if self.current_process.stdout:
                remaining_output = self.current_process.stdout.read()
                if remaining_output:
                    output_lines.extend(remaining_output.strip().split('\n'))
                    
            if self.current_process.stderr:
                remaining_error = self.current_process.stderr.read()
                if remaining_error:
                    error_lines.extend(remaining_error.strip().split('\n'))
                    
            duration = time.time() - start_time
            return_code = self.current_process.returncode
            
            return {
                'success': return_code == 0,
                'duration': duration,
                'output': '\n'.join(output_lines),
                'error': '\n'.join(error_lines) if return_code != 0 else None
            }
            
        except Exception as e:
            return {
                'success': False,
                'duration': time.time() - start_time,
                'output': '',
                'error': str(e)
            }
            
    def _parse_progress_from_output(self, line: str, task_id: str):
        """从输出解析进度信息"""
        try:
            # 解析不同类型的进度信息
            if "训练进度:" in line:
                # 提取百分比
                import re
                match = re.search(r'(\d+(?:\.\d+)?)%', line)
                if match:
                    progress = float(match.group(1))
                    task_manager.update_task_progress(task_id, progress, line.strip())
                    
                    # 调用进度回调
                    if self.progress_callback:
                        self.progress_callback(task_id, progress, line.strip())
                        
            elif "开始训练" in line:
                task_manager.update_task_progress(task_id, 10, line.strip())
                
            elif "模型训练完成" in line:
                task_manager.update_task_progress(task_id, 90, line.strip())
                
            elif "保存模型" in line:
                task_manager.update_task_progress(task_id, 95, line.strip())
                
        except Exception as e:
            self.logger.warning(f"解析进度失败: {e}")
            
    def _start_memory_monitoring(self, task_id: str):
        """启动内存监控"""
        try:
            log_dir = Path("logs/training/memory")
            log_dir.mkdir(parents=True, exist_ok=True)
            
            memory_log = log_dir / f"memory_{task_id}_{datetime.now().strftime('%H%M%S')}.log"
            self.memory_monitor = MemoryMonitor(str(memory_log), interval=5.0)
            self.memory_monitor.start_monitoring()
            
        except Exception as e:
            self.logger.warning(f"启动内存监控失败: {e}")
            
    def _stop_memory_monitoring(self) -> Dict:
        """停止内存监控并返回统计信息"""
        if not self.memory_monitor:
            return {}
            
        try:
            self.memory_monitor.stop_monitoring()
            stats = self.memory_monitor.get_memory_stats()
            return stats
            
        except Exception as e:
            self.logger.warning(f"停止内存监控失败: {e}")
            return {}
            
    def _terminate_current_task(self):
        """终止当前任务"""
        if not self.current_process:
            return
            
        try:
            # 尝试优雅终止
            self.current_process.terminate()
            
            # 等待进程结束
            try:
                self.current_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                # 强制杀死进程
                self.current_process.kill()
                self.current_process.wait()
                
            self.logger.info("🛑 当前任务已终止")
            
        except Exception as e:
            self.logger.error(f"终止任务失败: {e}")
            
    def get_status(self) -> Dict:
        """获取工作器状态"""
        return {
            'worker_id': self.worker_id,
            'is_running': self.is_running,
            'current_task_id': self.current_task_id,
            'has_process': self.current_process is not None,
            'memory_monitoring': self.memory_monitor is not None
        }

# 全局工作器实例
training_worker = TrainingWorker()

if __name__ == "__main__":
    # 测试代码
    def progress_callback(task_id, progress, message):
        print(f"任务 {task_id}: {progress}% - {message}")
        
    # 启动工作器
    training_worker.start(progress_callback)
    
    try:
        # 创建测试任务
        task_id = task_manager.create_task("hundreds", "xgb", "2025217")
        print(f"创建测试任务: {task_id}")
        
        # 等待任务完成
        while True:
            task = task_manager.get_task(task_id)
            if task and task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
                print(f"任务完成: {task.status.value}")
                break
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("用户中断")
    finally:
        training_worker.stop()
