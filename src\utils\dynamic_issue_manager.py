#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态期号管理器
负责动态获取期号、管理模型文件命名、检测可用模型等核心功能
"""

import sqlite3
import re
import logging
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)


class DynamicIssueManager:
    """动态期号管理器"""
    
    def __init__(self, db_path: str = "data/fucai3d.db"):
        """
        初始化动态期号管理器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        self.models_base_dir = Path("models")
        
    def get_latest_issue(self) -> str:
        """
        从数据库自动获取最新期号
        
        Returns:
            最新期号字符串，如 "2025217"
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('SELECT MAX(issue) FROM lottery_data')
            result = cursor.fetchone()
            conn.close()
            
            if result and result[0]:
                latest_issue = str(result[0])
                logger.info(f"获取最新期号: {latest_issue}")
                return latest_issue
            else:
                logger.warning("数据库中没有找到期号数据")
                return "2025001"  # 默认期号
                
        except Exception as e:
            logger.error(f"获取最新期号失败: {e}")
            return "2025001"  # 默认期号
    
    def get_next_issue(self, current_issue: str) -> str:
        """
        计算下一期期号
        
        Args:
            current_issue: 当前期号
            
        Returns:
            下一期期号
        """
        try:
            next_issue = str(int(current_issue) + 1)
            logger.debug(f"计算下一期期号: {current_issue} → {next_issue}")
            return next_issue
        except ValueError:
            logger.error(f"期号格式错误: {current_issue}")
            return current_issue
    
    def get_available_model_issues(self, position: str = "hundreds") -> List[Dict]:
        """
        获取所有可用的模型期号列表
        
        Args:
            position: 位置名称 (hundreds, tens, units)
            
        Returns:
            可用模型期号列表，包含期号、训练日期等信息
        """
        try:
            model_dir = self.models_base_dir / position
            if not model_dir.exists():
                logger.warning(f"模型目录不存在: {model_dir}")
                return []
            
            # 扫描XGBoost模型文件来确定可用期号
            pattern = r'xgb_' + re.escape(position) + r'_model_(\d{7})\.pkl'
            issues = []
            
            for file in model_dir.glob(f'xgb_{position}_model_*.pkl'):
                match = re.search(pattern, file.name)
                if match:
                    issue = match.group(1)
                    
                    # 检查该期号的所有模型是否完整
                    if self.check_models_complete_for_issue(issue, position):
                        train_date = datetime.fromtimestamp(file.stat().st_mtime)
                        file_size = file.stat().st_size
                        
                        issues.append({
                            'issue': issue,
                            'display_name': f'{issue}期模型',
                            'train_date': train_date.strftime('%Y-%m-%d %H:%M'),
                            'file_size': file_size,
                            'status': 'available',
                            'position': position
                        })
            
            # 按期号倒序排列（最新的在前）
            issues.sort(key=lambda x: x['issue'], reverse=True)
            
            logger.info(f"找到 {len(issues)} 个可用的 {position} 位模型期号")
            return issues
            
        except Exception as e:
            logger.error(f"获取可用模型期号失败: {e}")
            return []
    
    def check_models_complete_for_issue(self, issue: str, position: str) -> bool:
        """
        检查指定期号和位置的所有模型是否完整
        
        Args:
            issue: 期号
            position: 位置
            
        Returns:
            是否完整
        """
        try:
            model_dir = self.models_base_dir / position
            algorithms = ['xgb', 'lgb', 'lstm', 'ensemble']
            
            for algorithm in algorithms:
                if algorithm == 'lstm':
                    # LSTM需要检查.h5和_components.pkl两个文件
                    h5_file = model_dir / f"lstm_{position}_model_{issue}.h5"
                    pkl_file = model_dir / f"lstm_{position}_model_{issue}_components.pkl"
                    if not (h5_file.exists() and pkl_file.exists()):
                        return False
                elif algorithm == 'ensemble':
                    # 集成模型需要检查_ensemble.pkl文件
                    ensemble_file = model_dir / f"ensemble_{position}_model_{issue}_ensemble.pkl"
                    if not ensemble_file.exists():
                        return False
                else:
                    # XGBoost和LightGBM检查.pkl文件
                    model_file = model_dir / f"{algorithm}_{position}_model_{issue}.pkl"
                    if not model_file.exists():
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查模型完整性失败: {e}")
            return False
    
    def check_models_exist_for_issue(self, issue: str) -> bool:
        """
        检查指定期号的所有位置模型是否存在
        
        Args:
            issue: 期号
            
        Returns:
            是否存在完整的模型
        """
        positions = ['hundreds', 'tens', 'units']
        
        for position in positions:
            if not self.check_models_complete_for_issue(issue, position):
                return False
        
        logger.info(f"期号 {issue} 的所有模型都存在且完整")
        return True
    
    def generate_model_filename(self, algorithm: str, position: str, issue: str, 
                              file_type: str = 'pkl') -> str:
        """
        动态生成模型文件名
        
        Args:
            algorithm: 算法名称 (xgb, lgb, lstm, ensemble)
            position: 位置名称 (hundreds, tens, units)
            issue: 期号
            file_type: 文件类型 (pkl, h5)
            
        Returns:
            生成的文件名
        """
        if algorithm == 'lstm' and file_type == 'components':
            return f"lstm_{position}_model_{issue}_components.pkl"
        elif algorithm == 'ensemble':
            return f"ensemble_{position}_model_{issue}_ensemble.pkl"
        else:
            extension = 'h5' if algorithm == 'lstm' and file_type == 'model' else 'pkl'
            return f"{algorithm}_{position}_model_{issue}.{extension}"
    
    def get_model_filepath(self, algorithm: str, position: str, issue: str, 
                          file_type: str = 'pkl') -> Path:
        """
        获取模型文件的完整路径
        
        Args:
            algorithm: 算法名称
            position: 位置名称
            issue: 期号
            file_type: 文件类型
            
        Returns:
            模型文件路径
        """
        filename = self.generate_model_filename(algorithm, position, issue, file_type)
        return self.models_base_dir / position / filename
    
    def get_latest_trained_issue(self, position: str = "hundreds") -> Optional[str]:
        """
        获取最新训练的模型期号
        
        Args:
            position: 位置名称
            
        Returns:
            最新训练的期号，如果没有则返回None
        """
        available_models = self.get_available_model_issues(position)
        
        if available_models:
            return available_models[0]['issue']  # 已按期号倒序排列
        
        return None
    
    def detect_new_issue(self) -> Optional[str]:
        """
        检测是否有新期号需要训练
        
        Returns:
            新期号，如果没有则返回None
        """
        latest_db_issue = self.get_latest_issue()
        latest_model_issue = self.get_latest_trained_issue()
        
        if latest_model_issue is None:
            logger.info(f"没有找到已训练的模型，需要训练期号: {latest_db_issue}")
            return latest_db_issue
        
        if latest_db_issue > latest_model_issue:
            logger.info(f"检测到新期号: {latest_model_issue} → {latest_db_issue}")
            return latest_db_issue
        
        logger.info("没有检测到新期号")
        return None
    
    def get_issue_summary(self) -> Dict:
        """
        获取期号状态摘要
        
        Returns:
            期号状态摘要信息
        """
        latest_db_issue = self.get_latest_issue()
        latest_model_issue = self.get_latest_trained_issue()
        new_issue = self.detect_new_issue()
        
        summary = {
            'latest_db_issue': latest_db_issue,
            'latest_model_issue': latest_model_issue,
            'new_issue_detected': new_issue,
            'models_up_to_date': new_issue is None,
            'available_models_count': len(self.get_available_model_issues()),
            'timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"期号状态摘要: {summary}")
        return summary


# 全局实例
issue_manager = DynamicIssueManager()


# 便捷函数
def get_latest_issue() -> str:
    """获取最新期号的便捷函数"""
    return issue_manager.get_latest_issue()


def get_available_model_issues(position: str = "hundreds") -> List[Dict]:
    """获取可用模型期号的便捷函数"""
    return issue_manager.get_available_model_issues(position)


def check_models_exist_for_issue(issue: str) -> bool:
    """检查模型是否存在的便捷函数"""
    return issue_manager.check_models_exist_for_issue(issue)


def detect_new_issue() -> Optional[str]:
    """检测新期号的便捷函数"""
    return issue_manager.detect_new_issue()
