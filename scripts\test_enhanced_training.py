#!/usr/bin/env python3
"""
测试增强训练系统

验证功能：
1. 数据验证
2. 进度显示
3. 日志记录
4. 状态管理

Author: Augment Code AI Assistant
Date: 2025-08-15
"""

import sys
import os
import time
import json
import sqlite3
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_data_verification():
    """测试数据验证功能"""
    print("🔍 测试数据验证功能...")
    
    db_path = "data/fucai3d.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取数据统计
        cursor.execute('SELECT COUNT(*) FROM lottery_data')
        total_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT MIN(issue), MAX(issue) FROM lottery_data')
        min_issue, max_issue = cursor.fetchone()
        
        cursor.execute('SELECT issue, hundreds, tens, units FROM lottery_data ORDER BY issue DESC LIMIT 3')
        recent_data = cursor.fetchall()
        
        conn.close()
        
        print("✅ 数据验证成功:")
        print(f"   数据库路径: {db_path}")
        print(f"   历史数据条数: {total_count}")
        print(f"   期号范围: {min_issue} - {max_issue}")
        print("   最近3期数据:")
        for record in recent_data:
            print(f"     {record[0]}: {record[1]}{record[2]}{record[3]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据验证失败: {e}")
        return False

def test_progress_simulation():
    """测试进度显示功能"""
    print("\n🚀 测试进度显示功能...")
    
    try:
        # 尝试导入tqdm
        try:
            from tqdm import tqdm
            tqdm_available = True
        except ImportError:
            print("⚠️ tqdm未安装，使用简单进度显示")
            tqdm_available = False
        
        models = ['XGBoost', 'LightGBM', 'LSTM', '集成模型']
        
        for i, model in enumerate(models):
            print(f"\n📊 训练 {model} 模型...")
            
            if tqdm_available:
                # 使用tqdm进度条
                with tqdm(total=100, desc=f"训练{model}", unit="%") as pbar:
                    for step in range(0, 101, 10):
                        time.sleep(0.1)  # 模拟训练时间
                        pbar.update(10)
                        pbar.set_description(f"训练{model} - {step}%")
            else:
                # 简单进度显示
                for step in range(0, 101, 20):
                    print(f"   进度: {step}%")
                    time.sleep(0.2)
            
            print(f"✅ {model} 训练完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 进度显示测试失败: {e}")
        return False

def test_log_system():
    """测试日志系统"""
    print("\n📝 测试日志系统...")
    
    try:
        # 创建日志目录
        log_dir = Path("logs/test")
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建测试日志文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"test_training_{timestamp}.log"
        
        # 写入测试日志
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"测试日志 - {datetime.now()}\n")
            f.write("=" * 50 + "\n")
            f.write("📊 数据验证结果\n")
            f.write("数据库路径: data/fucai3d.db\n")
            f.write("历史数据条数: 8370\n")
            f.write("期号范围: 2002001 - 2025216\n")
            f.write("✅ 确认使用真实历史数据训练\n")
            f.write("=" * 50 + "\n")
            f.write("🚀 开始训练 XGBoost 模型\n")
            f.write("✅ XGBoost 模型训练完成\n")
            f.write("   训练准确率: 0.8542\n")
            f.write("   验证准确率: 0.8234\n")
            f.write("   训练时间: 45.67秒\n")
        
        print(f"✅ 日志文件创建成功: {log_file}")
        
        # 创建状态文件
        status_file = log_dir / "training_status.json"
        status_data = {
            "position": "test",
            "status": "completed",
            "start_time": datetime.now().isoformat(),
            "progress": 1.0,
            "models_completed": ["xgb", "lgb", "lstm", "ensemble"],
            "total_models": 4,
            "data_stats": {
                "total_records": 8370,
                "period_range": "2002001 - 2025216",
                "is_real_data": True
            }
        }
        
        with open(status_file, 'w', encoding='utf-8') as f:
            json.dump(status_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 状态文件创建成功: {status_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 日志系统测试失败: {e}")
        return False

def test_status_query():
    """测试状态查询功能"""
    print("\n📋 测试状态查询功能...")
    
    try:
        # 检查状态查询脚本
        status_script = Path("scripts/check_training_status.py")
        if status_script.exists():
            print(f"✅ 状态查询脚本存在: {status_script}")
        else:
            print(f"❌ 状态查询脚本不存在: {status_script}")
            return False
        
        # 检查后台训练脚本
        bg_script = Path("scripts/start_background_training.py")
        if bg_script.exists():
            print(f"✅ 后台训练脚本存在: {bg_script}")
        else:
            print(f"❌ 后台训练脚本不存在: {bg_script}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 状态查询测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 增强训练系统功能测试")
    print("=" * 60)
    
    tests = [
        ("数据验证", test_data_verification),
        ("进度显示", test_progress_simulation),
        ("日志系统", test_log_system),
        ("状态查询", test_status_query)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！增强训练系统已就绪")
        print("\n📋 使用说明:")
        print("1. 启动增强训练: python scripts/enhanced_train_hundreds.py")
        print("2. 查看训练状态: python scripts/check_training_status.py")
        print("3. 后台训练: python scripts/start_background_training.py start hundreds")
        print("4. 实时监控: python scripts/check_training_status.py --monitor")
    else:
        print("⚠️ 部分测试失败，请检查系统配置")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
