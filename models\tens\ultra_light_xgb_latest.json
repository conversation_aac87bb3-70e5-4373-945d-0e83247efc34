{"learner": {"attributes": {}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "500"}, "iteration_indptr": [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130, 140, 150, 160, 170, 180, 190, 200, 210, 220, 230, 240, 250, 260, 270, 280, 290, 300, 310, 320, 330, 340, 350, 360, 370, 380, 390, 400, 410, 420, 430, 440, 450, 460, 470, 480, 490, 500], "tree_info": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "trees": [{"base_weights": [0.35672852, 0.20367752, 0.12183691, 0.26281208, -0.054775286, 0.024532706, 0.12532638], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [2.4466949, 0.0, 0.9718319, 0.34280932, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, 0.20367752, 7.0, 7.0, -0.054775286, 0.024532706, 0.12532638], "split_indices": [1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.48, 13.139999, 20.339998, 14.219999, 6.12, 7.5599995, 6.66], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.29640716, 0.45422283, -0.13504824, -0.02678572, 0.63730085, 0.2973373, 0.1309371], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [4.140072, 2.9157386, 0.0, 0.0, 0.94114876, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 3.0, -0.13504824, -0.02678572, 5.0, 0.2973373, 0.1309371], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.399998, 27.179998, 5.22, 6.8399997, 20.339998, 5.7599998, 14.579999], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.082386345, -0.030201362, 0.18281251, -0.14150946, 0.09224599, -5.334545e-09, -0.060225856], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [2.164823, 1.1470191, 0.0, 0.17542791, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, 0.18281251, 3.0, 0.09224599, -5.334545e-09, -0.060225856], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.199997, 28.8, 5.3999996, 22.32, 6.4799995, 7.3799996, 14.94], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.100982554, -0.13593751, -0.02240719, 0.15940487, -0.084947854, 0.105337076, 0.009448813], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.95611215, 0.0, 1.5385356, 0.41219273, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, -0.13593751, 6.0, 4.0, -0.084947854, 0.105337076, 0.009448813], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.64, 5.3999996, 30.239998, 17.82, 12.419999, 6.12, 11.7], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.06890851, -0.19255458, 0.018433161, -0.008645539, -0.084024906, -0.016438363, 0.05844154, 0.046997387, -0.0068649943], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.41276574, 0.18432409, 0.067141935, 0.0, 0.0, 0.0, 0.13996881, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [5.0, 2.0, 4.0, -0.008645539, -0.084024906, -0.016438363, 7.0, 0.046997387, -0.0068649943], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.28, 14.579999, 20.699999, 5.9399996, 8.639999, 6.2999997, 14.4, 6.66, 7.74], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.05905514, 0.06451611, -0.07267834, -0.155587, 0.1296729, -5.334545e-09, -0.093195274], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.83844674, 1.8512679, 0.0, 0.31007546, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [5.0, 7.0, -0.07267834, 4.0, 0.1296729, -5.334545e-09, -0.093195274], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.559998, 20.699999, 13.86, 13.139999, 7.5599995, 7.3799996, 5.7599998], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.11971049, -0.34277883, 0.206117, -0.2496715, -0.14099218, 0.10147992, -5.0953823e-09, -0.03579953, -0.10331634], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [2.6950488, 0.06981659, 0.4434896, 0.10042703, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 7.0, 2.0, -0.14099218, 0.10147992, -5.0953823e-09, -0.03579953, -0.10331634], "split_indices": [1, 2, 2, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.92, 20.88, 14.039999, 14.219999, 6.66, 8.46, 5.58, 7.3799996, 6.8399997], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.008792517, -0.0946832, 0.08616188, 0.080753684, -0.28676474, -0.020053484, 0.06443914, -0.038585216, -0.10739858], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.8753923, 0.9691105, 0.0, 0.32315284, 0.05849731, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.08616188, 3.0, 6.0, -0.020053484, 0.06443914, -0.038585216, -0.10739858], "split_indices": [2, 2, 0, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.12, 26.46, 6.66, 13.86, 12.599999, 6.4799995, 7.3799996, 5.22, 7.3799996], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.05905514, 0.028388908, -0.10739858, 0.13346042, -0.0695122, -0.03855141, 0.089418784], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.97268116, 0.791222, 0.0, 0.9599163, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 7.0, -0.10739858, 2.0, -0.0695122, -0.03855141, 0.089418784], "split_indices": [0, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.559998, 27.179998, 7.3799996, 19.98, 7.2, 7.5599995, 12.419999], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.02284411, -0.20392752, 0.07901666, -0.10182769, -5.0953823e-09, 0.23448275, -0.048491385, 0.037406478, 0.09224599], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.67455894, 0.33190227, 0.8974683, 0.0, 0.0, 0.03466618, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 5.0, 7.0, -0.10182769, -5.0953823e-09, 7.0, -0.048491385, 0.037406478, 0.09224599], "split_indices": [2, 0, 0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.019997, 12.24, 21.779999, 6.66, 5.58, 13.499999, 8.28, 7.0199995, 6.4799995], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.16048442, 0.36939126, 0.048443746, 0.050552726, 0.15527692, -0.07034039, 0.16634601, 0.025412686, 0.08125966], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.8626516, 0.23993158, 0.9365326, 0.0, 0.0, 0.0, 0.111905396, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [2.0, 1.0, 4.0, 0.050552726, 0.15527692, -0.07034039, 8.0, 0.025412686, 0.08125966], "split_indices": [1, 1, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.4056, 12.293721, 26.111881, 6.4128976, 5.880823, 7.140681, 18.9712, 12.205259, 6.765942], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.16571128, -0.088103786, 0.25046527, 0.39255667, 0.13689937, 0.050377063, 0.18985727, -0.06703317, 0.10202912], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [1.5932705, 0.0, 0.4382453, 0.57337046, 1.6029937, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.088103786, 5.0, 4.0, 7.0, 0.050377063, 0.18985727, -0.06703317, 0.10202912], "split_indices": [0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.14289, 5.3732724, 32.76962, 13.0742235, 19.695395, 7.8505163, 5.2237077, 6.8882523, 12.807141], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.007429555, -0.1247633, 0.09861956, 0.030328445, -0.08409912, -0.037341323, 0.17330757, 0.09777328, 0.008909385], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.46782035, 0.5911573, 0.41795576, 0.0, 0.0, 0.0, 0.35705644, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 1.0, 3.0, 0.030328445, -0.08409912, -0.037341323, 6.0, 0.09777328, 0.008909385], "split_indices": [1, 1, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.004417, 14.508094, 22.496323, 5.797871, 8.710222, 5.1394644, 17.35686, 7.4618974, 9.894961], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00095304375, -0.056516264, 0.07111587, 0.08493284, -0.09196226, -0.0, -0.045674264], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.48098138, 0.0, 0.8995923, 0.0, 0.08963211, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.056516264, 5.0, 0.08493284, 4.0, -0.0, -0.045674264], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.74668, 9.031476, 23.715204, 10.257771, 13.457433, 5.3156843, 8.141748], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.008599039, -0.10850898, 0.13747525, -0.008177307, -0.08891487, 0.06381199, -0.069429554], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [1.6960226, 0.5453608, 0.0, 1.1050483, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.13747525, 6.0, -0.08891487, 0.06381199, -0.069429554], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.04559, 29.676365, 5.369227, 20.372492, 9.303872, 10.039209, 10.333282], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.015066767, -0.20956422, 0.21378674, -0.0, -0.28981364, 0.029391631, 0.086239785, -0.1333811, -0.04260239], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [1.5847065, 0.31891245, 0.07116449, 0.0, 0.19772196, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 6.0, -0.0, 3.0, 0.029391631, 0.086239785, -0.1333811, -0.04260239], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.605198, 18.447948, 15.15725, 5.48219, 12.965758, 7.3270216, 7.830229, 5.021932, 7.943826], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.10845648, 0.06649497, -0.22749497, -0.108615905, -0.34265184, -0.111660995, 0.07236781, -0.05650966, -0.13460997], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [1.4308685, 0.0, 0.28076982, 1.4808102, 0.057634592, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, 0.06649497, 7.0, 5.0, 6.0, -0.111660995, 0.07236781, -0.05650966, -0.13460997], "split_indices": [0, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.115078, 8.590538, 25.52454, 13.942725, 11.581816, 8.078174, 5.8645515, 6.232917, 5.3488984], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.01608611, 0.10464939, -0.104910485, 0.034573536, -0.28585914, -0.024351802, 0.07376668, -0.1387359, -0.013637415], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [1.1938828, 0.0, 0.79998934, 0.46924305, 0.50611985, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.10464939, 6.0, 4.0, 8.0, -0.024351802, 0.07376668, -0.1387359, -0.013637415], "split_indices": [1, 0, 2, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.456314, 6.147501, 29.308811, 16.695442, 12.61337, 10.879133, 5.8163095, 6.4993978, 6.1139717], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.09847366, -0.19982158, 0.07231351, 0.018124713, -0.2847798, -0.058879636, -0.11094168], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.2259067, 0.64156175, 0.0, 0.0, 0.028776407, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, 0.07231351, 0.018124713, 6.0, -0.058879636, -0.11094168], "split_indices": [2, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.34699, 26.224766, 7.122224, 6.235746, 19.98902, 11.990734, 7.9982853], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.009255702, -0.17620283, 0.15244403, -0.0, -0.25075206, -0.048668515, 0.09882293, -0.039009567, -0.09331087], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.95220745, 0.2113424, 1.0502263, 0.0, 0.0031206012, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [4.0, 1.0, 4.0, -0.0, 5.0, -0.048668515, 0.09882293, -0.039009567, -0.09331087], "split_indices": [1, 1, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.190033, 16.590199, 16.599836, 5.5432196, 11.046978, 5.8017044, 10.798131, 5.283155, 5.7638235], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.26030275, -0.0690738, 0.43802494, 0.2501516, 0.22916287, 0.012068533, 0.15837021], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [3.5514095, 0.0, 2.1308522, 0.0, 1.0943184, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.0690738, 4.0, 0.2501516, 8.0, 0.012068533, 0.15837021], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.34071, 9.968219, 28.37249, 8.601238, 19.771252, 13.01158, 6.7596703], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.09676909, 0.53091824, -0.14497277, 0.04369829, 0.25363424, 0.033727054, -0.09029181, 0.10032835, -0.09032425], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [4.1487083, 1.4871421, 0.7427682, 0.0, 0.0, 1.3526762, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 2.0, 5.0, 0.04369829, 0.25363424, 7.0, -0.09029181, 0.10032835, -0.09032425], "split_indices": [2, 2, 0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.428467, 13.106921, 24.321545, 6.6916122, 6.415309, 11.4025545, 12.91899, 6.1652117, 5.237343], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.10847765, 0.047925312, 0.09074869, 0.051450502, -0.006215794, -0.04511681, 0.051624965], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.34660068, 0.20011117, 0.0, 0.0, 0.5216704, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, 0.09074869, 0.051450502, 7.0, -0.04511681, 0.051624965], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.71909, 26.279713, 6.4393764, 7.908351, 18.37136, 10.554401, 7.8169594], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.10911183, -0.21454877, 0.06328201, 0.02683655, -0.10511089], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, -1, -1, -1], "loss_changes": [1.1584607, 1.1118407, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [8.0, 3.0, 0.06328201, 0.02683655, -0.10511089], "split_indices": [0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [31.97773, 24.53657, 7.4411616, 7.4551125, 17.081455], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.08179751, -0.1725186, 0.00048244346, 0.011551165, -0.11261127, 0.09916004, -0.05457263], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.28061208, 0.7912006, 1.2138375, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 5.0, 7.0, 0.011551165, -0.11261127, 0.09916004, -0.05457263], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.276886, 16.35864, 17.918245, 8.173332, 8.185308, 6.2836123, 11.634633], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.061706286, -0.24281329, 0.14093132, -0.0, -0.1035413, 0.009377901, 0.07047638], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [1.2853336, 0.46218646, 0.13437203, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 4.0, -0.0, -0.1035413, 0.009377901, 0.07047638], "split_indices": [0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.81352, 17.483183, 15.330337, 5.4549494, 12.028234, 8.18096, 7.149377], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.12597048, 0.09102069, -0.28956255, -0.14763233, -0.112954125, 0.03289526, -0.10242499], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [2.4981024, 0.0, 0.79755425, 0.0, 0.83394074, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, 0.09102069, 6.0, -0.14763233, 8.0, 0.03289526, -0.10242499], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.33321, 8.791818, 24.541391, 10.335434, 14.205956, 7.236921, 6.969036], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.06655975, 0.11344122, -0.003684702, -0.11322156, 0.07333779, -0.0, -0.09911413], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.82916826, 0.0, 0.83529305, 0.55434525, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.11344122, 8.0, 6.0, 0.07333779, -0.0, -0.09911413], "split_indices": [2, 0, 2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.11021, 5.914809, 29.195402, 20.91134, 8.284061, 14.2593, 6.6520405], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.09996567, 0.0031142249, -0.1393011, 0.07707766, -0.089187935, -0.09032935, 0.00032127998], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.3472142, 0.6898322, 0.0, 0.0, 0.42823958, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 3.0, -0.1393011, 0.07707766, 3.0, -0.09032935, 0.00032127998], "split_indices": [0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.60245, 26.75151, 6.8509398, 6.9474297, 19.804081, 5.548737, 14.255343], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.07954194, -0.08935845, 0.008523796, -0.032288093, 0.04684565, 0.054842338, -0.012518925], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.70455587, 0.0, 0.11287387, 0.0, 0.2696603, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.08935845, 2.0, -0.032288093, 6.0, 0.054842338, -0.012518925], "split_indices": [1, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.00815, 9.4167595, 24.591387, 5.0046563, 19.586731, 7.7522454, 11.834485], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.119068, 0.2791348, -0.117744744, 0.3771262, 0.024094379, -0.0, -0.09329189, 0.1444478, 0.07075122], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [1.6081843, 0.4128282, 0.4088408, 0.053602934, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 6.0, 3.0, 0.024094379, -0.0, -0.09329189, 0.1444478, 0.07075122], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.132915, 24.092104, 16.040812, 15.200523, 8.89158, 10.370038, 5.6707735, 6.9506335, 8.24989], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.06633218, 0.3204881, -0.07868389, -0.006260585, 0.16109614, -0.1761508, 0.053300004, 0.033267986, -0.1242902], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [1.5271034, 1.172346, 0.68277323, 0.0, 0.0, 1.4216337, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 3.0, 7.0, -0.006260585, 0.16109614, 2.0, 0.053300004, 0.033267986, -0.1242902], "split_indices": [2, 1, 1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.093285, 14.013214, 25.080072, 5.6098337, 8.40338, 18.68051, 6.399563, 8.522599, 10.15791], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.036697328, -0.021640463, 0.08619824, 0.039604783, -0.042786375, -0.012190362, 0.065255046], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.5842368, 0.24713221, 0.0, 0.32557443, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 7.0, 0.08619824, 5.0, -0.042786375, -0.012190362, 0.065255046], "split_indices": [1, 2, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.71578, 30.200773, 6.5150056, 19.948244, 10.252529, 13.892277, 6.0559673], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.04981794, 0.03611016, -0.065316014, -0.05048658, 0.086646676], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.47548175, 1.149041, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [6.0, 6.0, -0.065316014, -0.05048658, 0.086646676], "split_indices": [1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [30.152905, 20.070614, 10.08229, 11.061892, 9.008723], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.016977392, -0.12725136, 0.08524799, 0.038723532, -0.21149209, -0.11051824, -0.017436733], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.1143408, 0.5763447, 0.0, 0.0, 0.40572858, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, 0.08524799, 0.038723532, 5.0, -0.11051824, -0.017436733], "split_indices": [2, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.940397, 24.044872, 7.8955245, 5.5222635, 18.52261, 8.14055, 10.38206], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.08828506, -0.15584321, -0.011631281, -0.033222146, -0.09334887, 0.043017667, -0.05200507, 0.0078002135, -0.031044992], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.15558669, 0.28091148, 0.46727672, 0.056358296, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 8.0, 4.0, -0.09334887, 0.043017667, -0.05200507, 0.0078002135, -0.031044992], "split_indices": [0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.62824, 16.100529, 16.52771, 10.035488, 6.065042, 8.180686, 8.347023, 5.015024, 5.0204635], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.1849661, 0.037649866, -0.09168067], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 36, "left_children": [1, -1, -1], "loss_changes": [1.247262, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.037649866, -0.09168067], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [30.79683, 8.374698, 22.422132], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.051524103, 0.10134398, -0.15067801, -0.23262982, -0.012876631, -0.13538025, -0.026818438, 0.07643906, -0.09040786], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [1.3813839, 0.0, 0.2971478, 0.4541248, 1.0262873, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.10134398, 6.0, 4.0, 8.0, -0.13538025, -0.026818438, 0.07643906, -0.09040786], "split_indices": [1, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.94936, 6.1977615, 27.751598, 16.51571, 11.235887, 5.409626, 11.106086, 5.708911, 5.526976], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.02761673, 0.14736427, -0.20724744, -0.009698284, 0.10081712, -0.007275889, -0.13655582], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [1.0342047, 0.5966579, 0.6832865, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 7.0, -0.009698284, 0.10081712, -0.007275889, -0.13655582], "split_indices": [1, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.740799, 15.38093, 15.359869, 8.031376, 7.349554, 9.701034, 5.658836], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0067317807, -0.05467135, 0.08365742, -0.14897494, 0.039546397, -0.0, -0.08655171], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.58732665, 0.5028057, 0.0, 0.40447274, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 7.0, 0.08365742, 3.0, 0.039546397, -0.0, -0.08655171], "split_indices": [1, 2, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.89539, 26.375572, 5.5198183, 18.00725, 8.368321, 9.07987, 8.927381], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.22524975, 0.14157237, 0.14796646, 0.23051715, 0.05641273, 0.10907419, 0.026902989, -0.0071752635, 0.068820655], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.85820174, 0.22631145, 0.0, 0.22957629, 0.30360657, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 3.0, 0.14796646, 3.0, 8.0, 0.10907419, 0.026902989, -0.0071752635, 0.068820655], "split_indices": [0, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [43.217983, 34.382973, 8.8350115, 15.408477, 18.974497, 6.7446914, 8.663786, 13.151205, 5.8232913], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0023599197, 0.28429517, -0.17945452, 0.011949472, 0.1255282, -0.27073148, -0.0032389075, -0.0, -0.12058028], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [2.1536891, 0.48197103, 0.3570366, 0.0, 0.0, 0.5381198, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 4.0, 7.0, 0.011949472, 0.1255282, 6.0, -0.0032389075, -0.0, -0.12058028], "split_indices": [2, 0, 0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.815273, 15.604346, 24.210926, 6.2520723, 9.352274, 14.931792, 9.279135, 5.372602, 9.55919], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.003356121, -0.16051568, 0.15572572, -0.25338182, -0.0, 0.12877986, -0.017650984, -0.03884305, -0.094389066, -0.053140715, 0.045760334], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.9515531, 0.2813254, 0.9308262, 0.007317126, 0.0, 0.0, 0.3584599, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 6.0, 5.0, 3.0, -0.0, 0.12877986, 8.0, -0.03884305, -0.094389066, -0.053140715, 0.045760334], "split_indices": [1, 2, 2, 2, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.041283, 18.44186, 17.599422, 11.184141, 7.257719, 6.478696, 11.120727, 5.269879, 5.9142623, 6.0230975, 5.0976295], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.016607035, 0.22566845, -0.098112226, 0.115228795, -0.0, -0.023373159, -0.08065337, -0.041500352, 0.0114408955], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.8140436, 0.3952263, 0.23711462, 0.0, 0.0, 0.1309965, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 4.0, 7.0, 0.115228795, -0.0, 2.0, -0.08065337, -0.041500352, 0.0114408955], "split_indices": [0, 1, 1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.486973, 11.122385, 20.364588, 5.8635254, 5.258859, 15.362274, 5.002314, 5.485895, 9.876379], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.033695605, -0.13426968, 0.106319025, -0.032775663, -0.10551281, -0.035623465, 0.014014058], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [1.3514017, 0.5658898, 0.0, 0.15244453, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.106319025, 2.0, -0.10551281, -0.035623465, 0.014014058], "split_indices": [1, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.96774, 26.906597, 6.061144, 19.442215, 7.4643826, 9.707516, 9.734698], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0017294342, 0.08996435, -0.096739106, -0.041529424, -0.061555386, -0.04427769, 0.029045956], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [1.0245664, 0.0, 0.11933355, 0.29443842, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, 0.08996435, 6.0, 3.0, -0.061555386, -0.04427769, 0.029045956], "split_indices": [2, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.21831, 7.7431183, 24.475193, 17.745846, 6.729348, 10.4163475, 7.329498], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.07967459, -0.284398, 0.16014607, -0.1436934, -0.010290241, 0.12440326, -0.04977177], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [1.5852904, 0.7774062, 1.3170311, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 8.0, -0.1436934, -0.010290241, 0.12440326, -0.04977177], "split_indices": [2, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.108282, 16.367355, 13.740926, 8.465801, 7.901554, 7.736408, 6.0045176], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0047103134, 0.19271971, -0.17260669, 0.10093589, -0.011114054, -0.0810657, -0.069851905, -0.0, -0.03757281], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [1.2114613, 0.58935994, 0.14814782, 0.0, 0.0, 0.0, 0.03833693, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [5.0, 7.0, 5.0, 0.10093589, -0.011114054, -0.0810657, 8.0, -0.0, -0.03757281], "split_indices": [2, 0, 0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.604424, 15.561793, 19.04263, 9.555275, 6.006518, 8.430812, 10.611817, 5.55572, 5.0560975], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.07907299, -0.017527664, -0.08971946, -0.07351047, 0.08015749, 0.0603851, -0.047953058], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.41095614, 0.64205474, 0.0, 0.0, 0.6037831, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, -0.08971946, -0.07351047, 7.0, 0.0603851, -0.047953058], "split_indices": [0, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.077908, 26.264376, 5.8135333, 7.7440004, 18.520374, 12.6925535, 5.8278213], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.033647608, 0.16443157, -0.070601635, 0.07446466, 0.10758538, 0.08050969, -0.029109191], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [1.2525389, 0.35261178, 0.0, 0.64657545, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 5.0, -0.070601635, 3.0, 0.10758538, 0.08050969, -0.029109191], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.706184, 23.190962, 10.515222, 17.145744, 6.0452185, 8.055758, 9.089986], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.043460242, -0.059150662, 0.2537015, -0.12643982, 0.041980457, 0.12799634, 0.030372651, 0.0016065103, -0.07778229], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.9817197, 0.4182195, 0.29717457, 0.43507046, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 7.0, 8.0, 2.0, 0.041980457, 0.12799634, 0.030372651, 0.0016065103, -0.07778229], "split_indices": [2, 1, 2, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [42.90933, 28.999006, 13.910322, 22.32167, 6.677336, 5.367233, 8.543089, 11.308521, 11.013149], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.09516056, -0.10873544, 0.21355875, 0.09592785, 0.1375167, -0.0029151023, 0.05877928], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [2.281876, 0.0, 0.17958903, 0.0, 0.20745939, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.10873544, 5.0, 0.09592785, 7.0, -0.0029151023, 0.05877928], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.9624, 7.616918, 32.34548, 11.610329, 20.735153, 5.640342, 15.0948105], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.056518253, -0.005829083, -0.0884462, -0.10557037, 0.070505686, -0.07952356, 0.05715652], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.41397542, 0.77568626, 0.0, 1.1663432, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 7.0, -0.0884462, 7.0, 0.070505686, -0.07952356, 0.05715652], "split_indices": [0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.90255, 30.796038, 5.1065116, 22.46824, 8.327796, 14.889547, 7.578693], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.015830817, 0.0980994, -0.10452808, -0.18847857, -0.0, -0.0024106281, -0.09543565, 0.026320051, -0.033781216], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [1.2732542, 0.0, 0.19849351, 0.26788542, 0.12996715, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, 0.0980994, 6.0, 3.0, 7.0, -0.0024106281, -0.09543565, 0.026320051, -0.033781216], "split_indices": [0, 0, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.490524, 8.536044, 22.954481, 11.955205, 10.999275, 5.764744, 6.1904616, 5.9963613, 5.0029135], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.04214325, 0.054440755, -0.10317972, -0.21623051, 0.04274211, -0.094244085, -0.042533774], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.4816873, 0.0, 0.81817925, 0.055015802, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.054440755, 8.0, 5.0, 0.04274211, -0.094244085, -0.042533774], "split_indices": [2, 0, 2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.494038, 6.407631, 27.086407, 18.910542, 8.175864, 6.1973233, 12.713219], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.064700425, -0.19559883, 0.059815377, -0.09798146, -0.019481564, -0.006659169, 0.034123745], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.54738915, 0.21765876, 0.08594708, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 5.0, -0.09798146, -0.019481564, -0.006659169, 0.034123745], "split_indices": [0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.033474, 15.242201, 15.791272, 6.480031, 8.76217, 5.762438, 10.028834], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.08153561, 0.03546022, -0.18429722, -0.13234587, -0.05795277, 0.035216745, -0.09176587], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.67010146, 0.0, 0.61786175, 0.0, 0.7387145, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, 0.03546022, 5.0, -0.13234587, 8.0, 0.035216745, -0.09176587], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.264341, 9.899484, 20.364857, 5.6319885, 14.732869, 8.701937, 6.0309324], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0921418, 0.065943755, -0.17089565, -0.13752334, -0.07221593, 0.029644938, -0.051008403], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.8597672, 0.0, 0.69969654, 0.0, 0.39308333, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.065943755, 4.0, -0.13752334, 6.0, 0.029644938, -0.051008403], "split_indices": [2, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.666042, 5.9674153, 26.69863, 5.6315613, 21.067068, 7.289007, 13.77806], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.009899632, 0.14800893, -0.12752281, -0.0053521604, 0.09462926, 0.018306004, -0.10162584], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.61063266, 0.4886138, 0.67564416, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 5.0, -0.0053521604, 0.09462926, 0.018306004, -0.10162584], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.071169, 15.322615, 14.748553, 7.846227, 7.476388, 7.9183636, 6.8301897], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.013469261, -0.08424227, 0.1322249, 0.10103009, -0.059755266], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 59, "left_children": [1, -1, 3, -1, -1], "loss_changes": [1.2156533, 0.0, 1.7925909, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [2.0, -0.08424227, 7.0, 0.10103009, -0.059755266], "split_indices": [1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [33.1483, 8.896385, 24.251915, 15.172927, 9.078988], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.0685682, 0.013620221, 0.1082949, 0.06853781, -0.03987588, -0.013529046, 0.04331454], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.684245, 0.31971782, 0.0, 0.27338278, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 7.0, 0.1082949, 3.0, -0.03987588, -0.013529046, 0.04331454], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.222805, 38.39628, 5.826528, 28.812967, 9.583311, 11.167685, 17.645283], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.13594519, -0.07658569, 0.23648371, 0.33952773, -0.08695981, -0.017285787, 0.1331686], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [1.7308707, 0.0, 1.9771547, 1.2874138, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.07658569, 8.0, 1.0, -0.08695981, -0.017285787, 0.1331686], "split_indices": [0, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [41.517662, 7.9699516, 33.54771, 28.511007, 5.0367026, 5.827094, 22.683914], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.038206033, -0.04705788, 0.09093663, 0.03399562, -0.09607138, 0.032944467, -0.03161693], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.88521713, 0.6792554, 0.0, 0.25405985, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 6.0, 0.09093663, 7.0, -0.09607138, 0.032944467, -0.03161693], "split_indices": [1, 1, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.424946, 27.892548, 8.532399, 21.903267, 5.989281, 14.788554, 7.114712], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.049549136, 0.027510518, -0.09739386, 0.14259239, -0.07726334, 0.0057694064, 0.060603023, -0.055660002, 0.0063914675], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.73842907, 0.3361706, 0.0, 0.072205216, 0.1700573, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, -0.09739386, 2.0, 3.0, 0.0057694064, 0.060603023, -0.055660002, 0.0063914675], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.01043, 25.424934, 6.585494, 12.399267, 13.025667, 5.0081577, 7.3911095, 6.2703686, 6.7552986], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.03309672, -0.064346135, 0.11090908, 0.0020813837, 0.15643755, 0.0145026585, 0.100818776], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.65298104, 0.0, 0.10671854, 0.0, 0.28555864, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.064346135, 5.0, 0.0020813837, 8.0, 0.0145026585, 0.100818776], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.19738, 6.9720535, 25.225328, 8.701942, 16.523386, 11.4555, 5.067886], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.021959914, 0.08069369, -0.097946845, 0.04569958, 0.0004805108, -0.09684666, 0.030305494, 0.043968804, -0.03229404], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.2540442, 0.059815675, 0.58750486, 0.0, 0.0, 0.0, 0.22401316, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 5.0, 4.0, 0.04569958, 0.0004805108, -0.09684666, 7.0, 0.043968804, -0.03229404], "split_indices": [1, 2, 2, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.333944, 12.316783, 18.017162, 5.3135915, 7.0031915, 6.2758417, 11.741321, 6.7311873, 5.0101333], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.12442741, -0.026213797, -0.1335792, -0.2173732, 0.11102375, -0.1406759, 0.009450387], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 66, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.8772577, 1.8867497, 0.0, 1.1241026, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 6.0, -0.1335792, 4.0, 0.11102375, -0.1406759, 0.009450387], "split_indices": [2, 2, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.70683, 23.04557, 5.6612577, 16.021242, 7.024328, 7.7279606, 8.2932825], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.009451282, -0.09988258, 0.0756733, -0.021288432, -0.09558078, -0.037303675, 0.054846756], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 67, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.8606844, 0.43567535, 0.0, 0.47539905, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 5.0, 0.0756733, 3.0, -0.09558078, -0.037303675, 0.054846756], "split_indices": [1, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.94797, 26.716345, 8.231627, 20.797556, 5.9187894, 14.405058, 6.392498], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.092796385, -0.009750607, -0.12919217, 0.098370805, -0.096476786, -0.012915241, 0.12498399], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 68, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.8459248, 0.9589911, 0.0, 0.9760945, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 7.0, -0.12919217, 6.0, -0.096476786, -0.012915241, 0.12498399], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.870045, 25.842525, 5.0275187, 19.459389, 6.3831367, 13.853585, 5.605803], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.015051499, -0.112965465, 0.11061897, -0.03341228, 0.20221718, 0.13097407, 0.023540685], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 69, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [1.7143917, 0.0, 0.5898887, 0.0, 0.49746364, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.112965465, 4.0, -0.03341228, 6.0, 0.13097407, 0.023540685], "split_indices": [1, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.234844, 8.7794695, 26.455376, 7.3455095, 19.109865, 5.4507647, 13.6591015], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.13216613, 0.04623426, 0.114432655, 0.20324633, -0.11863405, 0.09620611, 0.018235093, -0.004311816, -0.047608178], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 70, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.87514377, 0.9105034, 0.0, 0.24436253, 0.050849497, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, 0.114432655, 5.0, 5.0, 0.09620611, 0.018235093, -0.004311816, -0.047608178], "split_indices": [0, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [42.674797, 32.91484, 9.759959, 17.050545, 15.864294, 8.275545, 8.775001, 5.473583, 10.390711], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.033109386, 0.122964926, -0.13834047, 0.032978445, 0.19245309, 0.031865474, -0.008042584, -0.0424608, 0.095671825], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 71, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1.9868568, 0.2111038, 0.0, 0.092047974, 0.9423152, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 3.0, -0.13834047, 5.0, 4.0, 0.031865474, -0.008042584, -0.0424608, 0.095671825], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [43.223022, 37.380615, 5.842406, 17.557505, 19.82311, 8.293667, 9.263838, 5.1941566, 14.628954], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.03699892, -0.1865873, 0.15904501, -0.09833375, -0.0, 0.27384928, -0.05393179, 0.11920514, 0.041148808], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 72, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [1.0030285, 0.36112347, 0.99097556, 0.0, 0.0, 0.20467484, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 6.0, 9.0, -0.09833375, -0.0, 5.0, -0.05393179, 0.11920514, 0.041148808], "split_indices": [1, 2, 1, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.77079, 11.801841, 22.968948, 6.5095263, 5.292315, 17.519552, 5.4493957, 7.8175244, 9.702028], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.028813599, -0.11600511, 0.06459633, 0.025726074, -0.2441648, -0.106984355, -0.02014763], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 73, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.6914548, 0.6668154, 0.0, 0.0, 0.24146795, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 3.0, 0.06459633, 0.025726074, 6.0, -0.106984355, -0.02014763], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.776432, 23.365868, 7.410564, 8.902106, 14.463761, 7.878803, 6.5849586], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0074017765, -0.043153428, 0.069949016, 0.19154967, -0.0412357, 0.0002447066, 0.13187759], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 74, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.29644296, 0.0, 0.6145588, 0.6789594, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.043153428, 7.0, 5.0, -0.0412357, 0.0002447066, 0.13187759], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.053816, 7.9445047, 22.10931, 14.289738, 7.819573, 8.873125, 5.4166126], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.072786205, -0.23965517, 0.067853354, -0.03505758, -0.09880721, -0.010428347, 0.060210783], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 75, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.7979883, 0.087022364, 0.27075648, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 4.0, -0.03505758, -0.09880721, -0.010428347, 0.060210783], "split_indices": [0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.577742, 14.445335, 17.132404, 7.5404634, 6.9048724, 9.658027, 7.474378], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.09418577, 0.027607061, -0.110198975, 0.056962285, -0.04723386, 0.018338246, -0.041328035], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 76, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.0407093, 0.28306967, 0.0, 0.0, 0.16343884, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 4.0, -0.110198975, 0.056962285, 3.0, 0.018338246, -0.041328035], "split_indices": [0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.957502, 20.395935, 8.561566, 6.374379, 14.021556, 6.0271115, 7.9944444], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.018556144, -0.046968307, 0.06226425, 0.031358954, -0.088733256, -0.01555651, 0.028937355], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 77, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.46604028, 0.5584044, 0.0, 0.12320599, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 6.0, 0.06226425, 3.0, -0.088733256, -0.01555651, 0.028937355], "split_indices": [2, 2, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.549904, 25.820229, 8.729676, 19.99704, 5.8231893, 8.192336, 11.804703], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.10574156, -0.19377235, 0.022151994, -0.10739071, -0.0836372, 0.004176348, -0.061023828], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 78, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.51038706, 0.08712208, 0.0, 0.17000668, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [6.0, 3.0, 0.022151994, 5.0, -0.0836372, 0.004176348, -0.061023828], "split_indices": [1, 1, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.621464, 20.198162, 9.423301, 11.724757, 8.473406, 5.0978518, 6.6269054], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.06455457, 0.20031095, -0.05410397, -0.03076517, 0.29625273, 0.11399297, 0.03873066], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 79, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.1844457, 0.7157129, 0.0, 0.0, 0.15072286, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 2.0, -0.05410397, -0.03076517, 7.0, 0.11399297, 0.03873066], "split_indices": [0, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.47913, 21.919094, 11.560038, 5.0058465, 16.913246, 10.021879, 6.8913674], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.07460065, -0.018375697, 0.11600157, 0.24909204, 0.036085866, 0.10878194, 0.02059922, -0.011197205, 0.08548568], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 80, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.27834877, 0.0, 0.35736102, 0.20416701, 0.48912668, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.018375697, 4.0, 6.0, 9.0, 0.10878194, 0.02059922, -0.011197205, 0.08548568], "split_indices": [2, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.252457, 10.126959, 36.125496, 12.28987, 23.835627, 6.5838, 5.7060704, 18.752323, 5.083304], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.07167166, -0.07995408, 0.004833979, 0.07083731, -0.13423483, -0.14581962, 0.012107875], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 81, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.648795, 0.0, 1.0243354, 0.0, 1.2584796, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [4.0, -0.07995408, 4.0, 0.07083731, 6.0, -0.14581962, 0.012107875], "split_indices": [0, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.68239, 11.138571, 29.543816, 11.101569, 18.442247, 5.714855, 12.727392], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.025188776, -0.10954869, 0.060847435, -0.16851501, -0.023642538, -0.093392104, -0.005998297, 0.029822942, -0.04763067], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 82, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.6781095, 0.10958484, 0.0, 0.27219248, 0.22994345, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, 0.060847435, 3.0, 8.0, -0.093392104, -0.005998297, 0.029822942, -0.04763067], "split_indices": [1, 2, 0, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.583363, 25.213173, 8.370188, 13.69037, 11.522803, 6.0777197, 7.6126504, 5.781852, 5.740951], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0034427263, 0.06855477, -0.051153652, -0.040163703, 0.14471124, 0.08060147, 0.013926004], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 83, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.38644055, 0.3515291, 0.0, 0.0, 0.15637633, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [6.0, 2.0, -0.051153652, -0.040163703, 5.0, 0.08060147, 0.013926004], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.307428, 20.474977, 8.83245, 5.0309715, 15.444007, 5.613595, 9.830412], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0009270452, 0.09046458, -0.07257902, 0.08019216, -0.17342488, -0.11445266, -0.0038558657], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 84, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.7281331, 0.0, 1.0060608, 0.0, 0.7120243, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.09046458, 2.0, 0.08019216, 4.0, -0.11445266, -0.0038558657], "split_indices": [1, 0, 2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.643005, 5.3699303, 27.273073, 5.6358857, 21.637188, 8.553747, 13.083442], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0046424787, 0.099140175, -0.10420751, -0.047170825, 0.092147045, 0.05385616, -0.056669235], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 85, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [1.2044469, 0.8095028, 0.0, 0.5302162, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 7.0, -0.10420751, 3.0, 0.092147045, 0.05385616, -0.056669235], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.197174, 24.267365, 6.9298096, 14.418467, 9.848898, 5.1726213, 9.245846], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.03453376, -0.10413878, 0.08287256, 0.079967156, -0.093457036], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 86, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.9336354, 1.4168471, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [6.0, 3.0, 0.08287256, 0.079967156, -0.093457036], "split_indices": [1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [25.482792, 16.291489, 9.191304, 5.5691752, 10.722314], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.09413697, -0.17171463, 0.048478328, -0.13553257, -0.046680268, 0.105828546, -0.08625725, 0.03412098, -0.055161554], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 87, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.3951713, 0.72042835, 1.3606049, 0.0, 0.4017514, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 8.0, -0.13553257, 2.0, 0.105828546, -0.08625725, 0.03412098, -0.055161554], "split_indices": [1, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.85135, 21.599262, 11.252088, 5.613274, 15.985988, 6.0347567, 5.2173305, 7.112022, 8.873966], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.067822255, -0.07288492, -0.0, 0.096835665, -0.057365034, -0.0024701762, 0.049347404], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 88, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.38083303, 0.0, 0.42683253, 0.1299482, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.07288492, 7.0, 4.0, -0.057365034, -0.0024701762, 0.049347404], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.303556, 7.813704, 21.489853, 14.891846, 6.5980062, 5.598137, 9.293709], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.003347277, 0.06603006, -0.052226227, -0.04774309, 0.1390194, 0.07197119, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 89, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.403587, 0.47881854, 0.0, 0.0, 0.32176727, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, -0.052226227, -0.04774309, 6.0, 0.07197119, -0.0], "split_indices": [2, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.224968, 26.909895, 8.315074, 5.963117, 20.946777, 11.960359, 8.98642], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.09532592, 0.22426763, -0.007060098, 0.017366756, 0.31564793, -0.03886257, 0.024410736, 0.12559149, 0.054546077, 0.026024878, -0.035088416], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 90, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.6625718, 0.2772553, 0.076327406, 0.0, 0.05018425, 0.22658761, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [3.0, 4.0, 8.0, 0.017366756, 7.0, 6.0, 0.024410736, 0.12559149, 0.054546077, 0.026024878, -0.035088416], "split_indices": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.42267, 20.894756, 26.52791, 8.374646, 12.520109, 20.755003, 5.7729087, 5.4502892, 7.0698204, 7.4237056, 13.331297], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.04313729, -0.1619862, 0.13170934, -0.0033148904, -0.11900128, 0.058899444, 0.0011739305, -0.05490917, 0.050814826], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 91, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.8825835, 0.9036309, 0.11897078, 0.5414433, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 9.0, 3.0, -0.11900128, 0.058899444, 0.0011739305, -0.05490917, 0.050814826], "split_indices": [0, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.299664, 24.375994, 15.923671, 15.402516, 8.973477, 9.699957, 6.2237134, 7.802331, 7.6001854], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.029008836, 0.20223483, -0.05532129, 0.07753798, 0.030574307, -0.07296846, 0.08980341, 0.078266986, -0.011323478], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 92, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.55194515, 0.007531464, 0.70850474, 0.0, 0.0, 0.0, 0.34465182, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 5.0, 7.0, 0.07753798, 0.030574307, -0.07296846, 5.0, 0.078266986, -0.011323478], "split_indices": [0, 2, 0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.011753, 11.394179, 23.617573, 5.652859, 5.7413206, 10.297491, 13.320082, 5.5774217, 7.7426605], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0024181767, -0.05341209, 0.10645027, -0.011399306, 0.08953833, -0.0071511, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 93, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.60264933, 0.0, 0.4595383, 0.0023528375, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.05341209, 7.0, 4.0, 0.08953833, -0.0071511, -0.0], "split_indices": [1, 0, 2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.154118, 11.265018, 17.8891, 11.34547, 6.543628, 5.9641714, 5.381299], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.09012485, 0.10833167, -0.2291591, 0.073953375, -0.0006954868, -0.29867196, -0.010159874, -0.11029286, -0.038084637], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 94, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.9664915, 0.24304742, 0.23395169, 0.0, 0.0, 0.07631838, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 1.0, 8.0, 0.073953375, -0.0006954868, 8.0, -0.010159874, -0.11029286, -0.038084637], "split_indices": [1, 1, 2, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.690018, 13.261799, 19.428219, 5.7531605, 7.508638, 13.560181, 5.8680377, 8.445067, 5.1151133], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.05315485, -0.16079126, 0.07108796, 0.003207465, -0.105179094, 0.06847349, -0.02702014], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 95, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.43834105, 0.60678774, 0.40843174, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 4.0, 0.003207465, -0.105179094, 0.06847349, -0.02702014], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.252436, 16.483147, 13.769289, 8.878526, 7.6046205, 7.074507, 6.6947823], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.05681137, 0.011189182, -0.06695591, -0.0513254, 0.101198636, -0.0028014395, 0.07160827], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 96, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.3359576, 0.34039176, 0.0, 0.0, 0.24498203, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 2.0, -0.06695591, -0.0513254, 6.0, -0.0028014395, 0.07160827], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [26.600487, 19.118212, 7.482274, 5.654937, 13.463276, 7.576828, 5.886448], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.094305314, 0.045775246, -0.2247364, -0.03552195, 0.04338598, -0.14176461, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 97, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.60694736, 0.27509376, 0.91664946, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 7.0, -0.03552195, 0.04338598, -0.14176461, -0.0], "split_indices": [1, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.67688, 14.734577, 15.942302, 5.0826564, 9.651921, 6.936978, 9.005324], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.013797535, 0.08943224, -0.05131906, -0.026622774, 0.06557618], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 98, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.4970248, 0.4439165, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [6.0, 3.0, -0.05131906, -0.026622774, 0.06557618], "split_indices": [0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.170105, 16.88332, 11.286786, 6.901308, 9.982012], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.002034904, 0.06273524, -0.039331608, 0.070805356, -0.0, -0.03527761, 0.021769209], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 99, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.2951757, 0.28303275, 0.0, 0.0, 0.1809093, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 3.0, -0.039331608, 0.070805356, 6.0, -0.03527761, 0.021769209], "split_indices": [0, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.352413, 25.161058, 10.191356, 5.929343, 19.231716, 7.0717244, 12.159991], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.10427683, -0.016811691, 0.13119267, 0.0723841, 0.086312234, -0.005209592, 0.0672873], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 100, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.22568852, 0.0, 0.33589166, 0.47196716, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.016811691, 7.0, 7.0, 0.086312234, -0.005209592, 0.0672873], "split_indices": [2, 0, 1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.857365, 6.1901836, 41.667183, 31.81461, 9.852572, 20.14183, 11.672779], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.086928934, 0.23683164, -0.063542865, -0.037156798, 0.38270077, -0.0, -0.057457868, 0.06383114, 0.13675275, 0.037208073, -0.038727425], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 101, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.91195846, 1.1297046, 0.1546101, 0.0, 0.019338846, 0.2485083, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 9.0, -0.037156798, 3.0, 4.0, -0.057457868, 0.06383114, 0.13675275, 0.037208073, -0.038727425], "split_indices": [0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.995193, 19.079, 18.916193, 5.3400745, 13.738927, 13.509133, 5.407059, 5.7217646, 8.017162, 6.757968, 6.7511654], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.09778078, -0.19631603, 0.054671146, 5.24724e-06, -0.24839787, -0.11689577, -0.030340524], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 102, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.0200312, 0.30189753, 0.0, 0.0, 0.36533737, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 1.0, 0.054671146, 5.24724e-06, 5.0, -0.11689577, -0.030340524], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.6212, 26.09477, 8.52643, 5.2779665, 20.816805, 9.438427, 11.378377], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.029774358, -0.05210096, 0.079597905, 0.055785682, -0.07673482, -0.009883613, 0.03416268], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 103, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.6783216, 0.5895296, 0.0, 0.099565536, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.079597905, 2.0, -0.07673482, -0.009883613, 0.03416268], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.349518, 24.268372, 8.081144, 16.0187, 8.249672, 5.8279786, 10.1907215], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.02928263, -0.048976265, 0.06972516, 0.12321378, -0.107748546, -0.034801636, 0.07960363], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 104, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.5317803, 1.3106582, 0.0, 0.5727253, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 6.0, 0.06972516, 3.0, -0.107748546, -0.034801636, 0.07960363], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.581463, 22.268856, 8.312607, 14.500006, 7.7688503, 5.2105184, 9.289488], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.029962102, 0.080040306, -0.058255464, -0.0, -0.056259688, 0.025682984, -0.0466373], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 105, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.696246, 0.0, 0.1777308, 0.24200824, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, 0.080040306, 8.0, 8.0, -0.056259688, 0.025682984, -0.0466373], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.581764, 8.0578785, 22.523886, 16.20015, 6.323736, 10.74938, 5.45077], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.098333366, -0.13768624, -0.0, 0.09551518, -0.057695493, -0.019464673, 0.08986069], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 106, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [1.00428, 0.0, 0.43274748, 0.56702584, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.13768624, 7.0, 6.0, -0.057695493, -0.019464673, 0.08986069], "split_indices": [0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [26.810368, 5.0444255, 21.765942, 15.016388, 6.7495546, 8.51437, 6.502018], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.16492838, -0.09290045, -0.13298777, -0.21718141, 0.06017824, -0.02826016, -0.1069959], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 107, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.5532508, 1.0383922, 0.0, 0.26175314, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 6.0, -0.13298777, 6.0, 0.06017824, -0.02826016, -0.1069959], "split_indices": [1, 1, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.321815, 26.228148, 5.0936675, 18.847757, 7.38039, 11.287372, 7.5603857], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.041688357, -0.07087153, 0.025835512, 0.038251303, -0.07272187, 0.034401935, -0.08351515], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 108, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.4299076, 0.0, 0.24801813, 0.0, 0.50915945, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.07087153, 4.0, 0.038251303, 6.0, 0.034401935, -0.08351515], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.659275, 7.3795757, 22.2797, 11.321605, 10.958096, 5.710756, 5.2473392], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.056560133, -0.09711718, 0.01479081, -0.16091199, 0.17803799, -0.093477115, -0.0045897025, 0.10857186, -0.039967615], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 109, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.7005244, 0.0, 0.83884305, 0.26545212, 0.9513641, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.09711718, 5.0, 6.0, 6.0, -0.093477115, -0.0045897025, 0.10857186, -0.039967615], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.877213, 6.690398, 27.186815, 12.805742, 14.381073, 5.395719, 7.4100237, 9.140315, 5.240758], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.102355495, 0.15601921, -0.07298534, -0.045881532, 0.07766877, 0.007953487, 0.0070525245, -0.068952285], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 110, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.82374895, 0.21130529, 0.23434442, 0.0, 0.336077, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 2.0, 4.0, -0.07298534, 6.0, 0.07766877, 0.007953487, 0.0070525245, -0.068952285], "split_indices": [2, 2, 1, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.832005, 30.585617, 19.24639, 7.2971582, 23.288458, 9.738892, 9.507498, 17.14858, 6.1398773], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.05163752, -0.08938104, -0.041082, 0.10552472, -0.02741027, 0.056226205], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 111, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.66291535, 0.3769189, 0.0, 0.0, 0.4936929, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 1.0, -0.08938104, -0.041082, 3.0, -0.02741027, 0.056226205], "split_indices": [1, 1, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.36579, 34.978447, 5.3873453, 7.018909, 27.959538, 7.8206377, 20.1389], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.049020253, -0.050214145, 0.17371842, 0.033832986, -0.06734677, 0.07433448, 0.01915868, -0.048664063, 0.06161038], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 112, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.4710641, 0.32809007, 0.08958453, 0.520723, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 7.0, 8.0, 3.0, -0.06734677, 0.07433448, 0.01915868, -0.048664063, 0.06161038], "split_indices": [2, 1, 2, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.364784, 19.579145, 15.785642, 13.365475, 6.21367, 8.135222, 7.650419, 5.99042, 7.375055], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.02655527, 0.07935064, -0.047446206, -0.02548927, 0.21511029, 0.0076995674, 0.10670998], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 113, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.3137126, 0.6053307, 0.0, 0.0, 0.3444513, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 5.0, -0.047446206, -0.02548927, 3.0, 0.0076995674, 0.10670998], "split_indices": [1, 2, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.690914, 24.733223, 5.957692, 11.025825, 13.707397, 6.645886, 7.0615115], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.037539486, -0.046845753, 0.10892524, -0.05681771, 0.003428108, -0.012328343, 0.013286448], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 114, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.9462216, 0.20979674, 0.0, 0.0, 0.03874257, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 3.0, 0.10892524, -0.05681771, 5.0, -0.012328343, 0.013286448], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.673859, 25.58963, 6.0842295, 6.4690795, 19.12055, 7.976238, 11.144312], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.016345479, 0.024807427, -0.05181571, -0.038790725, 0.14233112, -0.00612863, 0.10235454], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 115, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.21819566, 0.47002837, 0.0, 0.0, 0.5229167, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 4.0, -0.05181571, -0.038790725, 7.0, -0.00612863, 0.10235454], "split_indices": [2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.94216, 23.86378, 6.078379, 9.913285, 13.950494, 7.8882847, 6.0622096], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.009773646, -0.093785636, 0.10166579, 0.0058686216, -0.085430965, 0.00030718162, 0.05199944], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 116, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.26159084, 0.38776392, 0.0658845, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 6.0, 0.0058686216, -0.085430965, 0.00030718162, 0.05199944], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [26.00677, 15.430091, 10.576677, 9.917875, 5.512216, 5.40124, 5.1754375], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.009852578, 0.08535646, -0.089424245, -0.09146721, -0.0026569099, 0.04855198, -0.025022248], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 117, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.7209122, 0.0, 0.43688238, 0.0, 0.2607323, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.08535646, 3.0, -0.09146721, 5.0, 0.04855198, -0.025022248], "split_indices": [1, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.523556, 5.4668055, 24.05675, 5.853802, 18.202948, 5.255139, 12.947809], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.15402842, 0.021783812, -0.29766345, -0.13865544, -0.17526433, -0.091052085, -0.0017514798], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 118, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [1.0026029, 0.0, 0.22754812, 0.0, 0.24020272, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, 0.021783812, 5.0, -0.13865544, 6.0, -0.091052085, -0.0017514798], "split_indices": [1, 0, 1, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.443241, 10.955252, 17.48799, 5.9275274, 11.560462, 5.7935643, 5.7668977], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.12617974, -0.089691445, -0.029899318, 0.19284041, 0.013114825, 0.13045111], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 119, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.3528595, 0.40688372, 0.0, 0.0, 0.6580059, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 2.0, -0.089691445, -0.029899318, 5.0, 0.013114825, 0.13045111], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.79005, 24.1311, 9.658953, 5.044676, 19.086424, 12.795435, 6.2909884], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.026391868, -0.0, -0.073914185, -0.060375087, 0.036199283, -0.055700686, -0.001325713, 0.035136804, -0.048294622, 0.004260319, -0.010127973], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 120, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.069053695, 0.24288738, 0.13479662, 0.40151703, 0.0, 0.0, 0.009001775, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 7.0, 3.0, 0.036199283, -0.055700686, 6.0, 0.035136804, -0.048294622, 0.004260319, -0.010127973], "split_indices": [1, 2, 1, 2, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.513912, 30.561718, 17.952196, 20.183043, 10.378674, 5.9131107, 12.039084, 6.8824406, 13.300603, 6.436716, 5.602369], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.005630695, 0.042952307, -0.12705326, -0.00482875, 0.055548105, -0.057350606, -0.0068920376, 0.034305293, -0.032874305], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 121, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.26226056, 0.22435735, 0.060103446, 0.3001672, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 8.0, 7.0, 4.0, 0.055548105, -0.057350606, -0.0068920376, 0.034305293, -0.032874305], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [41.107166, 29.233206, 11.87396, 22.077984, 7.155223, 6.2353168, 5.638643, 9.838345, 12.239639], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.08072149, -0.07939839, -0.014699305, 0.048158843, -0.09428198, -0.065291874, 0.00690858], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 122, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.40717977, 0.0, 0.3998176, 0.0, 0.31851274, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.07939839, 5.0, 0.048158843, 6.0, -0.065291874, 0.00690858], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.14973, 8.089321, 27.06041, 7.741383, 19.319027, 9.4154415, 9.903585], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.04848388, 0.11696681, -0.039638188, -0.005808333, 0.21473944, 0.006391504, 0.13723706], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 123, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.41108274, 0.34643295, 0.0, 0.0, 0.62832624, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [6.0, 2.0, -0.039638188, -0.005808333, 7.0, 0.006391504, 0.13723706], "split_indices": [1, 1, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.108604, 23.212938, 7.8956676, 9.602717, 13.61022, 8.408676, 5.2015433], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.08713762, -0.11172884, 0.10359731, -0.055729993, -0.08283631, 0.041219562], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 124, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.33361235, 1.4370563, 0.6665698, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 5.0, 0.10359731, -0.055729993, -0.08283631, 0.041219562], "split_indices": [0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.269127, 18.258385, 14.010743, 9.43411, 8.824275, 8.617406, 5.3933372], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.019668939, -0.11377882, 0.08111052, -0.053609274, -0.005347073, 0.087309405, -0.012123496], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 125, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.3241964, 0.088817015, 0.44347176, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 2.0, -0.053609274, -0.005347073, 0.087309405, -0.012123496], "split_indices": [0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.864786, 16.924047, 14.940739, 8.958619, 7.9654293, 5.2682424, 9.672496], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.045879293, -0.09780008, 0.16912077, -0.0, 0.11919958], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 126, "left_children": [1, -1, 3, -1, -1], "loss_changes": [1.6930734, 0.0, 0.6304747, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, -0.09780008, 5.0, -0.0, 0.11919958], "split_indices": [1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [25.93606, 11.24019, 14.69587, 8.858681, 5.83719], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.013919614, 0.08020622, -0.05939316, -0.1256266, 0.042046744, -0.059581347, -0.009731068], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 127, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.63842607, 0.0, 0.3549344, 0.10747895, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, 0.08020622, 8.0, 6.0, 0.042046744, -0.059581347, -0.009731068], "split_indices": [1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.387056, 6.7545514, 24.632505, 19.148422, 5.484084, 9.502581, 9.645841], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.034497075, 0.18058701, -0.12765306, -0.0, 0.10673287, -0.00082699483, -0.08053516], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 128, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.6962476, 0.5126127, 0.21172757, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 7.0, -0.0, 0.10673287, -0.00082699483, -0.08053516], "split_indices": [1, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.195185, 14.570676, 12.624509, 7.5091743, 7.061501, 7.6066976, 5.017812], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0046207043, -0.08995102, 0.053374205, 0.1774679, -0.10140644, 0.08316443, 0.0002752111, -0.044432946, -0.0028126268], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 129, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.6701275, 0.0, 0.62670153, 0.29053432, 0.043912202, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.08995102, 7.0, 6.0, 6.0, 0.08316443, 0.0002752111, -0.044432946, -0.0028126268], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.766354, 5.4710217, 30.295334, 17.091726, 13.203607, 10.195134, 6.896592, 7.633743, 5.5698643], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.021855934, 0.06756337, -0.058673788, -0.141251, 0.12823206, -0.091694534, -0.021974482, 0.06751707, 0.009792832], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 130, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.4594347, 0.0, 0.72060645, 0.30145288, 0.09714857, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.06756337, 8.0, 3.0, 2.0, -0.091694534, -0.021974482, 0.06751707, 0.009792832], "split_indices": [0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.884995, 5.4538455, 44.43115, 31.35669, 13.074458, 7.722432, 23.634256, 5.3026805, 7.7717776], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.07117324, -0.026619108, 0.14799005, 0.04246515, -0.108834155, 0.2753535, -0.0, -0.04635442, -0.015671488, -0.010645404, 0.16448244, -0.051743027, 0.044188946], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 131, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.33641672, 0.27531502, 0.4599924, 0.0, 0.006800309, 1.1884425, 0.34091622, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 4.0, 6.0, 0.04246515, 7.0, 4.0, 5.0, -0.04635442, -0.015671488, -0.010645404, 0.16448244, -0.051743027, 0.044188946], "split_indices": [1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [41.643257, 18.035133, 23.608122, 5.2855635, 12.74957, 12.1751795, 11.432942, 5.1199875, 7.6295824, 5.9276175, 6.2475624, 5.1440415, 6.288901], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.04526586, -0.053449713, 0.09595738, -0.08350805, 0.03672697, 0.14132108, -0.0, 0.110347904, -0.00034228477], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 132, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.20056748, 0.56135875, 0.107978344, 0.0, 0.0, 0.60238504, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 5.0, 8.0, -0.08350805, 0.03672697, 3.0, -0.0, 0.110347904, -0.00034228477], "split_indices": [2, 1, 2, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.206676, 11.935879, 25.270798, 5.231531, 6.704347, 16.518589, 8.752209, 6.0382247, 10.480364], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.051206116, 0.07201577, 0.07002945, -0.025022222], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 133, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.39715692, 0.0, 0.6055333, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [2.0, -0.051206116, 5.0, 0.07002945, -0.025022222], "split_indices": [1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [30.342539, 8.579992, 21.762547, 10.744879, 11.017668], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.03589279, -0.096275315, 0.04324062, 0.17045112, -0.051350765, 0.009732937, 0.084521435], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 134, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.7610218, 0.0, 0.72653663, 0.21811405, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.096275315, 7.0, 4.0, -0.051350765, 0.009732937, 0.084521435], "split_indices": [0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.894379, 6.324154, 24.570225, 15.842314, 8.727911, 8.049794, 7.792519], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.020554887, 0.10095537, -0.070924595, -0.0018199502, 0.061778285], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 135, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.8516091, 0.24890678, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [5.0, 7.0, -0.070924595, -0.0018199502, 0.061778285], "split_indices": [1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [30.041512, 19.26569, 10.775823, 9.53455, 9.731139], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.053521395, -0.15005904, 0.09194257, -0.0013558655, -0.23046371, -0.10935636, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 136, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.0492368, 0.25641108, 0.0, 0.0, 0.49771565, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 3.0, 0.09194257, -0.0013558655, 5.0, -0.10935636, -0.0], "split_indices": [1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.317823, 23.008696, 5.309127, 9.006344, 14.002353, 8.742515, 5.2598376], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0757741, -0.19621482, 0.006648484, -0.014540225, -0.09084362, 0.10736187, -0.14170565, -0.0, -0.06862254], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 137, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.32338387, 0.15386751, 1.0416533, 0.0, 0.0, 0.0, 0.15131351, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 1.0, 5.0, -0.014540225, -0.09084362, 0.10736187, 4.0, -0.0, -0.06862254], "split_indices": [1, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.535065, 11.931995, 17.60307, 6.003595, 5.9284005, 5.00816, 12.59491, 5.485522, 7.109388], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.02009433, -0.09681147, 0.06642008, -0.066926725, 0.0074472977, -0.04342054, 0.04369469], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 138, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.55357, 0.33022296, 0.0, 0.0, 0.30103493, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 4.0, 0.06642008, -0.066926725, 4.0, -0.04342054, 0.04369469], "split_indices": [2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.395014, 22.318237, 6.0767756, 10.011617, 12.306622, 5.490184, 6.8164377], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.012533287, -0.05619049, 0.041231263, -0.16616215, 0.04188448, 0.007898942, -0.08370067], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 139, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.23135997, 0.6426275, 0.0, 0.44803154, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 7.0, 0.041231263, 3.0, 0.04188448, 0.007898942, -0.08370067], "split_indices": [1, 2, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.398064, 27.643139, 6.754926, 18.12536, 9.517779, 6.6543207, 11.471039], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.058858983, -0.079521164, 0.10323732, -0.005822131, -0.034371175, 0.04269956, 0.075121544, -0.019456895, 0.09878427], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 140, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.30083984, 0.008597992, 0.28759095, 0.0, 0.0, 0.9028492, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 3.0, 7.0, -0.005822131, -0.034371175, 8.0, 0.075121544, -0.019456895, 0.09878427], "split_indices": [2, 1, 1, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.265076, 10.49486, 35.770218, 5.4884, 5.006459, 26.740215, 9.030002, 19.751934, 6.9882827], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.009134642, 0.16770048, -0.11056716, 0.0015502034, 0.07298771, -0.061801095, -0.041240886, -0.061701786, 0.054833192], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 141, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.84908843, 0.21286738, 0.13222757, 0.0, 0.0, 0.0, 0.64184177, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 4.0, 6.0, 0.0015502034, 0.07298771, -0.061801095, 7.0, -0.061701786, 0.054833192], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [42.456596, 18.45034, 24.006258, 6.6160846, 11.834255, 8.664501, 15.341756, 9.152217, 6.189539], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.009105111, 0.0693975, -0.14033434, -0.19633056, -0.010614768, -0.029411882, -0.075362235], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 142, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [1.1887884, 0.0, 0.11555022, 0.023091257, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [4.0, 0.0693975, 8.0, 6.0, -0.010614768, -0.029411882, -0.075362235], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.93727, 12.150709, 23.786562, 14.321788, 9.464773, 6.7824388, 7.5393496], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.06500873, 0.13315459, -0.037370738, 0.00047100827, 0.1998777, 0.12825821, 0.011034543], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 143, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.43888867, 0.19787315, 0.0, 0.0, 0.5314753, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [6.0, 2.0, -0.037370738, 0.00047100827, 4.0, 0.12825821, 0.011034543], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.622099, 23.867702, 7.754397, 8.879235, 14.988468, 5.3234797, 9.6649885], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.050290566, 0.07265025, -0.0, 0.07909707, -0.086182535, -0.10384524, 0.020822916], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 144, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.3164441, 0.0, 0.6127291, 0.0, 0.8850441, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.07265025, 2.0, 0.07909707, 4.0, -0.10384524, 0.020822916], "split_indices": [1, 0, 2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.990938, 5.775566, 25.215372, 5.613064, 19.602308, 7.1214876, 12.480822], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.050863493, -0.17385215, 0.049374055, -0.011589528, -0.0943304, 0.09404874, -0.082966164, -0.053222194, 0.0065623308], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 145, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.40056607, 0.22806007, 0.6535132, 0.0, 0.0, 0.0, 0.13883239, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [5.0, 3.0, 2.0, -0.011589528, -0.0943304, 0.09404874, 6.0, -0.053222194, 0.0065623308], "split_indices": [2, 2, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.786175, 13.493391, 16.292784, 7.890233, 5.6031585, 5.233882, 11.058902, 5.955664, 5.103237], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0932328, -0.07233514, -0.005007917, -0.0382364, 0.04424685], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 146, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.30542678, 0.0, 0.34070435, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, -0.07233514, 5.0, -0.0382364, 0.04424685], "split_indices": [1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [24.388582, 8.041989, 16.346592, 9.52141, 6.825183], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.043626007, 0.058955148, -0.028168103, -0.03432217, 0.02970891], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 147, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.34157473, 0.0, 0.23464428, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [2.0, 0.058955148, 7.0, -0.03432217, 0.02970891], "split_indices": [1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.080215, 8.862491, 19.217724, 11.981258, 7.236466], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.025385655, -0.09571423, 0.03611508, -0.14916982, -0.0054060984, -0.0, -0.08189909], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 148, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.31327742, 0.06796436, 0.0, 0.19340661, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 4.0, 0.03611508, 4.0, -0.0054060984, -0.0, -0.08189909], "split_indices": [0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.675148, 20.037268, 8.63788, 10.562736, 9.474531, 5.515339, 5.0473967], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.06138355, 0.11701327, -0.024568317, -0.020938773, 0.18306759, 0.07789899, 0.0032163998], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 149, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.29798964, 0.35309517, 0.0, 0.0, 0.23905915, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 2.0, -0.024568317, -0.020938773, 7.0, 0.07789899, 0.0032163998], "split_indices": [0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.743637, 25.661463, 9.082174, 6.2931542, 19.368307, 12.656519, 6.7117887], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.031305786, 0.10889412, -0.12318201, 0.04723797, 0.06175364, -0.0, -0.21214533, 0.039868396, -0.015835823, -0.012974863, 0.013842194, -0.019204745, -0.095165916], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 150, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6545726, 0.083095044, 0.35767692, 0.13504112, 0.0, 0.029267833, 0.22743577, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 2.0, 2.0, 0.06175364, 7.0, 7.0, 0.039868396, -0.015835823, -0.012974863, 0.013842194, -0.019204745, -0.095165916], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.664474, 18.761475, 29.903, 13.007832, 5.753643, 12.536568, 17.366432, 7.368699, 5.6391325, 5.5003486, 7.0362186, 8.269451, 9.096981], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.011435546, 0.12706886, -0.067287356, 0.22224072, -0.019842733, -0.19572957, 0.032626126, 0.012994637, 0.11256401, -0.0, -0.09138075], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 151, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.3948405, 0.3573082, 0.59516704, 0.28376508, 0.0, 0.32346058, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 7.0, 4.0, -0.019842733, 4.0, 0.032626126, 0.012994637, 0.11256401, -0.0, -0.09138075], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.77882, 16.783003, 23.99582, 11.490396, 5.2926073, 14.137267, 9.858552, 6.1886635, 5.3017325, 5.4008574, 8.736409], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.01568948, 0.091041945, -0.101746105, -0.0015590134, 0.14779669, 0.051885653, -0.05014426, 0.007712008, 0.08923779], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 152, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.9855806, 0.17902446, 0.0, 0.3805888, 0.32634798, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 3.0, -0.101746105, 1.0, 7.0, 0.051885653, -0.05014426, 0.007712008, 0.08923779], "split_indices": [1, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.63338, 30.237553, 5.3958287, 11.1902, 19.047352, 5.1326265, 6.0575733, 11.503278, 7.5440745], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.007901866, 0.024050329, -0.035285767, -0.012617515, 0.078693405, 0.04350171, -0.0027077235], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 153, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.12347961, 0.09623785, 0.0, 0.0, 0.10044359, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 4.0, -0.035285767, -0.012617515, 5.0, 0.04350171, -0.0027077235], "split_indices": [2, 2, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.746473, 23.710733, 7.035741, 10.005749, 13.704984, 8.082135, 5.622848], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.025346883, 0.090567075, -0.035521865, 0.19444227, -0.053941537, 0.0042806277, 0.12734829], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 154, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.31937197, 0.7075262, 0.0, 0.6965964, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 8.0, -0.035521865, 5.0, -0.053941537, 0.0042806277, 0.12734829], "split_indices": [1, 2, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.217293, 22.895372, 9.3219185, 17.01068, 5.884694, 10.378832, 6.6318474], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.06821304, -0.096843064, -0.0, 0.06476432, -0.07409817, 0.023407532, -0.06775278], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 155, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.5515707, 0.0, 0.43343422, 0.0, 0.49927533, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, -0.096843064, 2.0, 0.06476432, 7.0, 0.023407532, -0.06775278], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.85814, 5.7155857, 25.142555, 5.917505, 19.22505, 9.514053, 9.710997], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.09659929, 0.020866118, -0.19787052, -0.10531116, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 156, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.45621586, 0.0, 0.4776104, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [3.0, 0.020866118, 6.0, -0.10531116, -0.0], "split_indices": [0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [24.53241, 9.034876, 15.497533, 8.117588, 7.3799458], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.035420842, 0.075707026, -0.12869398, -0.23179832, 0.028141042, -0.10934099, -0.023886105], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 157, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.87547135, 0.0, 0.6088487, 0.28337717, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.075707026, 8.0, 5.0, 0.028141042, -0.10934099, -0.023886105], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.86661, 6.8755727, 23.991037, 16.672647, 7.31839, 7.8108506, 8.861796], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.03495406, 0.038177658, -0.08722298, -0.04034573, 0.102723606, -0.008920698, 0.055654064], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 158, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.5675922, 0.2632286, 0.0, 0.0, 0.2128498, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 2.0, -0.08722298, -0.04034573, 2.0, -0.008920698, 0.055654064], "split_indices": [1, 2, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.398758, 21.664532, 5.7342257, 5.1786237, 16.485909, 6.1227713, 10.363138], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.07326311, -0.04592132, 0.13106225, 0.06619521, 0.083987445, 0.068805404, -0.040839978], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 159, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.45604122, 0.0, 0.067285776, 0.0, 0.69396555, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.04592132, 4.0, 0.06619521, 6.0, 0.068805404, -0.040839978], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.495102, 5.922984, 26.572119, 7.151166, 19.420952, 11.945026, 7.475926], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.06014744, 0.10982822, -0.025727913, 0.016742727, 0.2227204, -0.041457087, 0.029740252, -0.021205116, 0.06247858, 0.028080374, 0.083710656], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 160, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.22432959, 0.31458497, 0.27567518, 0.35360104, 0.039522707, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 9.0, 7.0, 2.0, -0.041457087, 0.029740252, -0.021205116, 0.06247858, 0.028080374, 0.083710656], "split_indices": [0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.947536, 31.569464, 17.378075, 18.417435, 13.152027, 9.508225, 7.8698497, 12.719782, 5.697653, 5.3419156, 7.810112], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.04082654, 0.10009477, -0.0933381, 0.14961351, -0.032381985, 0.0030474777, 0.072619], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 161, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.95938575, 0.43188184, 0.0, 0.39877492, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, -0.0933381, 3.0, -0.032381985, 0.0030474777, 0.072619], "split_indices": [1, 2, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.511124, 38.951504, 5.55962, 32.077557, 6.873945, 13.661505, 18.416054], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0046171388, 0.09477106, -0.07664745, -0.0, 0.045321975, -0.0009934271, -0.060169067, -0.050533596, 0.04099548], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 162, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.28324363, 0.09397641, 0.16386761, 0.0, 0.0, 0.34214327, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [5.0, 5.0, 6.0, -0.0, 0.045321975, 3.0, -0.060169067, -0.050533596, 0.04099548], "split_indices": [0, 1, 1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.422237, 17.771313, 18.650925, 7.2666698, 10.5046425, 12.70761, 5.943315, 5.959873, 6.747737], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.06678787, -0.14414604, 0.05056893, -0.004902508, -0.20826074, -0.010340924, 0.03899634, -0.00327262, -0.10266525], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 163, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.3191291, 0.14502257, 0.10496256, 0.0, 0.31207305, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 4.0, -0.004902508, 3.0, -0.010340924, 0.03899634, -0.00327262, -0.10266525], "split_indices": [2, 2, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.31848, 19.854685, 12.463798, 7.573159, 12.281526, 5.684201, 6.779597, 5.704826, 6.5766997], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.043204457, -0.03168914, 0.099780604, -0.15571125, 0.04935155, -0.003826296, -0.08100399, -0.0, 0.029337188], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 164, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.7745693, 0.29511422, 0.0, 0.15802026, 0.036082804, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.099780604, 2.0, 5.0, -0.003826296, -0.08100399, -0.0, 0.029337188], "split_indices": [0, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.862568, 26.539364, 6.3232045, 10.61186, 15.927504, 5.597525, 5.0143356, 8.843118, 7.084385], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0699359, -0.0, -0.06757121, -0.063904546, 0.04184311, -0.037878912, 0.0011956572], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 165, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.287259, 0.18761471, 0.0, 0.077442475, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [6.0, 4.0, -0.06757121, 7.0, 0.04184311, -0.037878912, 0.0011956572], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.091406, 19.720829, 7.370577, 14.285496, 5.4353333, 7.765692, 6.519803], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.07086729, -0.09507971, 0.26434442, -0.058103926, 0.00057205267, 0.10773596, 0.032255214], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 166, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.88527274, 0.15911056, 0.10301763, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 6.0, 6.0, -0.058103926, 0.00057205267, 0.10773596, 0.032255214], "split_indices": [2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [25.262617, 13.594346, 11.66827, 6.758007, 6.836339, 6.071513, 5.5967574], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.101860106, 0.0069781113, 0.10640743, 0.037598632, -0.05074363], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 167, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.7054552, 0.5141986, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.10640743, 0.037598632, -0.05074363], "split_indices": [2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [30.018087, 22.861713, 7.156375, 14.185557, 8.676156], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.075329594, 0.04705846, -0.17871818, -0.111294724, -0.018460738, 0.005448035, -0.017978232], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 168, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.71686167, 0.0, 0.5875791, 0.0, 0.024318684, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, 0.04705846, 6.0, -0.111294724, 8.0, 0.005448035, -0.017978232], "split_indices": [2, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.603037, 8.046869, 19.556168, 8.00886, 11.547308, 5.149801, 6.3975077], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0053875255, -0.08831763, 0.07136737, 0.088857085, -0.012512322, 0.024858149, -0.032218333], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 169, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.69987005, 0.0, 0.6003182, 0.0, 0.21568844, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.08831763, 3.0, 0.088857085, 6.0, 0.024858149, -0.032218333], "split_indices": [2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.44911, 5.3164296, 29.13268, 7.552025, 21.580656, 10.300074, 11.280581], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.06598568, 0.08248751, 0.030858746, -0.10118968, 0.093949, 0.005162017, -0.059702773, 0.090277165, 0.0117866], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 170, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.34676564, 0.0, 0.39645487, 0.1954872, 0.32206517, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.08248751, 4.0, 5.0, 3.0, 0.005162017, -0.059702773, 0.090277165, 0.0117866], "split_indices": [0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.561626, 6.0277467, 45.53388, 14.013851, 31.52003, 6.250425, 7.7634263, 5.261645, 26.258385], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.017851694, 0.077095725, -0.08995954, -0.053077564, 0.19988061, -0.0695184, 0.013035109, 0.021402512, -0.072510004, 0.11631101, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 171, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.29576167, 0.5119846, 0.33095947, 0.3904161, 0.617481, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 7.0, 2.0, 5.0, -0.0695184, 0.013035109, 0.021402512, -0.072510004, 0.11631101, -0.0], "split_indices": [2, 2, 0, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.421124, 29.450203, 14.97092, 14.175258, 15.274946, 7.2642007, 7.706719, 8.596466, 5.5787916, 7.433697, 7.841249], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.036031634, -0.057979353, 0.09361102, 0.08708247, -6.202338e-05, 0.011835877, -0.025329113], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 172, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.4749961, 0.0, 0.56855595, 0.0, 0.076148234, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.057979353, 5.0, 0.08708247, 8.0, 0.011835877, -0.025329113], "split_indices": [2, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.361607, 6.0752316, 28.286377, 8.79347, 19.492905, 12.830622, 6.6622834], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.07960543, -0.0, 0.12404978, 0.11121402, -0.13026647, -0.0059806732, 0.088970244], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 173, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.11879133, 2.0826564, 0.54943395, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 6.0, 0.11121402, -0.13026647, -0.0059806732, 0.088970244], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.683973, 10.916739, 19.767233, 5.9008956, 5.0158443, 10.940746, 8.826488], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.036069527, -0.114832744, 0.078749284, -0.0579143, -0.067381844, 0.019661289, -0.07267154], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 174, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.76310295, 0.12194067, 0.0, 0.46927038, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 6.0, 0.078749284, 6.0, -0.067381844, 0.019661289, -0.07267154], "split_indices": [0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.796335, 25.119919, 5.676416, 18.148378, 6.971542, 10.929929, 7.218448], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.045815285, 0.041478097, -0.09840672, -0.17423974, 0.004886562, 0.0046454594, -0.08736151], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 175, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.28811756, 0.0, 0.22200245, 0.3579803, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.041478097, 7.0, 5.0, 0.004886562, 0.0046454594, -0.08736151], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.76799, 5.3860035, 22.381987, 13.685965, 8.696023, 5.224375, 8.46159], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.010731752, 0.06879703, -0.048430067, -0.0005454356, 0.03473059], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 176, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.32122865, 0.06699213, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 4.0, -0.048430067, -0.0005454356, 0.03473059], "split_indices": [2, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [24.102678, 15.69827, 8.404408, 5.80445, 9.89382], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.008744708, 0.10395932, -0.08337883, -0.00088381657, -0.057766672, 0.018403854, -0.03726374], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 177, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [1.010368, 0.0, 0.2028636, 0.13355754, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.10395932, 5.0, 7.0, -0.057766672, 0.018403854, -0.03726374], "split_indices": [2, 0, 1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.694685, 6.0039835, 23.690702, 14.590226, 9.100475, 9.492285, 5.097942], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.11565005, -0.0, 0.16334477, 0.08113144, 0.019647332], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 178, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.1663476, 0.0, 0.15942776, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [2.0, -0.0, 4.0, 0.08113144, 0.019647332], "split_indices": [1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [26.677986, 7.568976, 19.10901, 7.7937317, 11.315278], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.041245837, -0.08206214, 0.026952375, -0.13277192, 0.02353459, -0.0063470793, -0.055681393], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 179, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.19480819, 0.24317452, 0.0, 0.10553381, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, 0.026952375, 3.0, 0.02353459, -0.0063470793, -0.055681393], "split_indices": [1, 2, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.176872, 26.928915, 7.2479596, 21.068592, 5.8603215, 7.826808, 13.241785], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.06928762, -0.0, -0.149662, -0.086389795, 0.09022405, -0.20075831, -0.005678691, 0.020482672, -0.10946406, 0.0073912134, -0.111992404], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 180, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.26318893, 0.69548297, 0.11855522, 0.9318445, 0.0, 0.62277675, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 8.0, 9.0, 5.0, 0.09022405, 3.0, -0.005678691, 0.020482672, -0.10946406, 0.0073912134, -0.111992404], "split_indices": [0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.981354, 24.375874, 20.605482, 19.367174, 5.008699, 13.880003, 6.725478, 12.706253, 6.6609216, 6.1387887, 7.7412148], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.008343869, 0.1468894, -0.09388059, -0.03149414, 0.15080477, 0.021744836, -0.17852974, -0.03019012, 0.007879281, -0.10005654, 0.03131597], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 181, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.60251534, 1.2241068, 0.36010396, 0.06175273, 0.0, 0.0, 0.78242135, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 2.0, 4.0, 0.15080477, 0.021744836, 7.0, -0.03019012, 0.007879281, -0.10005654, 0.03131597], "split_indices": [2, 2, 1, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.194313, 17.29525, 22.899063, 11.939552, 5.355697, 7.3723516, 15.526712, 5.8845234, 6.0550284, 10.144028, 5.382684], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.03279375, -0.07825646, 0.030358305, 0.03611891, -0.13643324, 0.00273169, -0.07878013], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 182, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.22896828, 0.34737208, 0.0, 0.0, 0.44824, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 1.0, 0.030358305, 0.03611891, 5.0, 0.00273169, -0.07878013], "split_indices": [1, 1, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.664917, 27.497261, 8.167654, 5.5970287, 21.900234, 10.232206, 11.668027], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.062464193, -0.11383629, 0.027935939, 0.012674881, -0.22367242, 0.029439056, -0.017300904, -0.10122172, -0.0146358665], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 183, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.27595288, 0.38100383, 0.0, 0.084637105, 0.22799194, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 3.0, 0.027935939, 5.0, 7.0, 0.029439056, -0.017300904, -0.10122172, -0.0146358665], "split_indices": [0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.940266, 24.696745, 7.2435207, 11.465145, 13.2316, 5.640088, 5.825057, 7.1079907, 6.1236086], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.044793244, -0.027204875, 0.08822432, 0.060645953, -0.15011387, 0.041881718, -0.011750652, -0.08189285, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 184, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.6105617, 0.29338223, 0.0, 0.1345847, 0.19821578, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.08822432, 5.0, 8.0, 0.041881718, -0.011750652, -0.08189285, -0.0], "split_indices": [0, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.085218, 24.499203, 6.5860157, 14.1081705, 10.391033, 8.21377, 5.8944, 5.1047745, 5.286258], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.013673654, 0.086036004, -0.09529958, -0.0090348, -0.08695294, -0.04934072, 0.057117935], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 185, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.9106246, 0.0, 0.32399958, 0.52795637, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, 0.086036004, 8.0, 6.0, -0.08695294, -0.04934072, 0.057117935], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.035181, 7.792143, 20.243038, 15.105431, 5.137607, 8.869368, 6.2360625], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0076431376, -0.04610725, 0.102797, 0.08595924, -0.020394403], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 186, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.4038813, 0.0, 0.5778573, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, -0.04610725, 7.0, 0.08595924, -0.020394403], "split_indices": [1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [24.611204, 8.51235, 16.098854, 7.710831, 8.388023], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.017129377, 0.14725265, -0.15466192, -0.0, 0.24048068, 0.016432475, -0.13400416, 0.09948488, 0.032371696], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 187, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.73734003, 0.2705843, 0.9167013, 0.0, 0.06310117, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [5.0, 5.0, 5.0, -0.0, 2.0, 0.016432475, -0.13400416, 0.09948488, 0.032371696], "split_indices": [1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.998976, 18.05111, 12.947866, 7.2291365, 10.821973, 7.813778, 5.1340885, 5.076568, 5.7454047], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.02458369, -0.052204344, 0.069955535, 0.044458523, -0.07138815], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 188, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.48549604, 0.8348885, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [8.0, 4.0, 0.069955535, 0.044458523, -0.07138815], "split_indices": [2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.419409, 20.239447, 7.1799626, 9.574284, 10.665163], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.08539647, -0.0, -0.21810523, -0.07217269, 0.14530253, -0.0007797581, -0.10630096, 0.11088455, -0.010148213], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 189, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.3934394, 0.79658926, 0.36092645, 0.0, 0.6199882, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 4.0, -0.07217269, 7.0, -0.0007797581, -0.10630096, 0.11088455, -0.010148213], "split_indices": [0, 1, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.377113, 20.836271, 12.540843, 7.5532794, 13.282992, 5.5361924, 7.004651, 5.673413, 7.60958], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.004737879, -0.086516514, 0.077460416, -0.14630957, 0.013234531, 0.059107315, -0.040993035, -0.059025336, -0.018114302, -0.0, -0.023322528], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 190, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.3376717, 0.21738096, 0.36933288, 0.042090803, 0.0, 0.0, 0.022914132, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 3.0, 2.0, 0.013234531, 0.059107315, 8.0, -0.059025336, -0.018114302, -0.0, -0.023322528], "split_indices": [0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.101658, 24.695889, 23.40577, 17.384228, 7.3116603, 11.738314, 11.667455, 9.408068, 7.9761596, 5.354005, 6.31345], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.09530478, -0.0034475639, -0.09295104, -0.17667358, 0.09828733, -0.008209175, -0.0882094, -0.02979774, 0.10601781], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 191, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.7643603, 0.54768664, 0.0, 0.16466638, 1.0161661, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, -0.09295104, 3.0, 5.0, -0.008209175, -0.0882094, -0.02979774, 0.10601781], "split_indices": [0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.43156, 28.647585, 10.783977, 10.690684, 17.9569, 5.62257, 5.0681143, 10.241863, 7.715037], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.03335416, 0.04756906, -0.048910294, 0.032479245, -0.13562696, -0.10420718, 0.013235209], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 192, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.38360178, 0.0, 0.31264746, 0.0, 0.6093936, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [4.0, 0.04756906, 4.0, 0.032479245, 7.0, -0.10420718, 0.013235209], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.424385, 13.763683, 20.6607, 6.8476443, 13.813056, 6.1695075, 7.6435485], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.054107625, -0.13109243, 0.0454378, -0.028335243, -0.10002033, -0.027724644, 0.0031493655], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 193, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.5303667, 0.46272507, 0.0, 0.053050574, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.0454378, 5.0, -0.10002033, -0.027724644, 0.0031493655], "split_indices": [0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.387426, 23.453022, 7.934406, 16.637041, 6.81598, 6.728825, 9.908216], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.068616964, 0.014640571, 0.064259365, 0.12872183, -0.06884334, 0.055592116, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 194, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.22912435, 0.7229846, 0.0, 0.13575763, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 6.0, 0.064259365, 6.0, -0.06884334, 0.055592116, -0.0], "split_indices": [2, 1, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.46963, 24.261696, 7.207933, 17.136038, 7.1256585, 11.704595, 5.431443], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.014739139, 0.029411433, -0.020321568, -0.027652794, 0.0156045845], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 195, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.0994684, 0.0, 0.121082105, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, 0.029411433, 4.0, -0.027652794, 0.0156045845], "split_indices": [0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [29.715227, 9.115093, 20.600134, 10.890464, 9.70967], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.050645646, -0.0, -0.057133656, -0.03734191, 0.040657975], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 196, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.20569186, 0.35915488, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 6.0, -0.057133656, -0.03734191, 0.040657975], "split_indices": [0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [25.80218, 19.235518, 6.5666614, 9.747532, 9.487986], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.004491421, -0.085030854, 0.077061325, 0.06476955, -0.21250655, -0.0, 0.032110542, -0.036449652, -0.0852933], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 197, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.77582484, 0.4969803, 0.0, 0.025875024, 0.015364468, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 3.0, 0.077061325, 4.0, 4.0, -0.0, 0.032110542, -0.036449652, -0.0852933], "split_indices": [2, 2, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.610952, 23.531048, 8.079906, 10.683729, 12.847318, 5.1468945, 5.5368342, 7.467488, 5.3798304], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.056883078, -0.15441114, 0.05028623, -0.063988075, -0.07813724, -0.050552186, 0.0078776125], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 198, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.66960543, 0.15579885, 0.0, 0.14475203, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [6.0, 3.0, 0.05028623, 5.0, -0.07813724, -0.050552186, 0.0078776125], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.460215, 20.369162, 8.091052, 12.368895, 8.000268, 5.8428707, 6.526024], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.07916722, 0.020717226, -0.14399181, -0.083625555, -0.0, 0.07943493, -0.06950595], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 199, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.34190148, 0.0, 0.45380116, 0.0, 0.85470366, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, 0.020717226, 6.0, -0.083625555, 8.0, 0.07943493, -0.06950595], "split_indices": [1, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.76811, 9.481126, 23.286982, 11.320809, 11.966172, 5.391319, 6.574854], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.04292735, 0.08177957, 0.0031749324, -0.06941582, 0.08969857, -0.033844847, 0.0011366542, 0.040706653, 0.0076590884], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 200, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.42518348, 0.0, 0.28296125, 0.08668761, 0.042441964, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.08177957, 6.0, 7.0, 3.0, -0.033844847, 0.0011366542, 0.040706653, 0.0076590884], "split_indices": [0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.685318, 5.9479303, 42.73739, 22.718887, 20.018501, 14.780818, 7.938069, 10.10391, 9.91459], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.011380696, 0.12043545, -0.104662076, 0.07527524, -0.02423119, -0.109693035, 0.0059636617, 0.02596454, -0.03411874], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 201, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.5190534, 0.48733386, 0.7649528, 0.0, 0.0, 0.0, 0.18454914, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [5.0, 4.0, 7.0, 0.07527524, -0.02423119, -0.109693035, 9.0, 0.02596454, -0.03411874], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.316364, 16.14239, 24.173975, 9.943787, 6.1986036, 6.7793274, 17.394648, 11.06044, 6.334208], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.08894656, -0.1912498, 0.027354868, -0.08483907, -0.024615182, -0.0031606436, 0.025335828], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 202, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.4567127, 0.14062488, 0.046208672, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 6.0, -0.08483907, -0.024615182, -0.0031606436, 0.025335828], "split_indices": [2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.568424, 19.037607, 16.530815, 8.992913, 10.044694, 9.374183, 7.1566324], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.114808, -0.11355534, 0.0038056043, 0.06724334, 0.007678884, -0.09592833], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 203, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.41900223, 0.14807054, 0.51134, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 5.0, 0.0038056043, 0.06724334, 0.007678884, -0.09592833], "split_indices": [2, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.146961, 14.636851, 15.510111, 8.618041, 6.01881, 9.494176, 6.015935], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0071460125, -0.09278592, 0.0899897, 0.040612046, 0.055374756, -0.03118769, 0.03926556], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 204, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.8968722, 0.0, 0.10058312, 0.28185588, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.09278592, 8.0, 5.0, 0.055374756, -0.03118769, 0.03926556], "split_indices": [0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.987747, 6.026867, 26.96088, 19.34779, 7.6130905, 6.9531226, 12.394668], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.04430334, -0.06522633, 0.05059074, 0.06193897, -0.0319502, -0.015633304, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 205, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.5006361, 0.0, 0.2689414, 0.0, 0.0066393567, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [4.0, -0.06522633, 6.0, 0.06193897, 8.0, -0.015633304, -0.0], "split_indices": [2, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.789743, 9.755372, 18.03437, 6.1433268, 11.891044, 6.2908545, 5.6001897], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.036979806, 0.010199438, -0.0267119], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 206, "left_children": [1, -1, -1], "loss_changes": [0.10528675, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.010199438, -0.0267119], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [25.184782, 9.990247, 15.194534], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.030644467, -0.06253329, 0.086338475, 0.048997343, -0.09960196, 0.04910348, -0.016359707], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 207, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.7834703, 0.7447175, 0.0, 0.22269462, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 7.0, 0.086338475, 2.0, -0.09960196, 0.04910348, -0.016359707], "split_indices": [1, 2, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.00691, 22.29597, 7.7109385, 16.085514, 6.2104564, 7.814377, 8.271138], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.021780644, -0.014994502, 0.03982801, 0.10530893, -0.043290805, 0.0077658505, 0.04443445], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 208, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.1302255, 0.35595515, 0.0, 0.017506912, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.03982801, 3.0, -0.043290805, 0.0077658505, 0.04443445], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.534716, 20.65433, 6.8803864, 10.395242, 10.259088, 5.0206704, 5.3745713], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.011358372, -0.07954404, 0.041062996, 0.12013622, -0.05406693, 0.0917567, -0.006475563, -0.053038206, 0.016596137], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 209, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.47455692, 0.0, 0.22394213, 0.46117252, 0.19427915, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.07954404, 6.0, 5.0, 8.0, 0.0917567, -0.006475563, -0.053038206, 0.016596137], "split_indices": [1, 0, 1, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.16769, 5.138794, 27.028898, 15.190602, 11.838295, 6.3788605, 8.811742, 5.719392, 6.1189036], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.040738076, -0.063907616, 0.086654335, -0.041679785, 0.003851403, 0.123631805, 0.0014531309, 0.05257306, -0.0, -0.013275237, 0.018143216], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 210, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.24031213, 0.09884871, 0.09669706, 0.0, 0.0, 0.16411725, 0.037583135, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [4.0, 5.0, 8.0, -0.041679785, 0.003851403, 8.0, 6.0, 0.05257306, -0.0, -0.013275237, 0.018143216], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.38762, 13.667935, 33.719685, 7.16059, 6.5073457, 22.381378, 11.338306, 15.981195, 6.400183, 5.5764227, 5.761884], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.006863661, -0.042533115, 0.03661483, -0.0, 0.06194842, -0.015303369, 0.010903973, 0.08539888, -0.009248623], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 211, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.25612447, 0.0, 0.03408619, 0.026127238, 0.44312784, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [4.0, -0.042533115, 7.0, 4.0, 2.0, -0.015303369, 0.010903973, 0.08539888, -0.009248623], "split_indices": [0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.986027, 9.755118, 30.23091, 11.438421, 18.792488, 5.5367875, 5.9016333, 5.2350917, 13.557397], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.036981378, 0.13259496, -0.044156663, -0.005563629, 0.084222496, 0.04864194, -0.06872906], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 212, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.27595568, 0.3958322, 0.7459061, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 4.0, -0.005563629, 0.084222496, 0.04864194, -0.06872906], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.676243, 15.274709, 17.401535, 7.659866, 7.6148434, 8.028501, 9.3730345], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.06911989, 0.009687997, 0.06262661, 0.07232447, -0.04409758, -0.0059968433, 0.04724238, 0.019518279, -0.04943331], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 213, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.25529867, 0.09073141, 0.0, 0.11794693, 0.19578837, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 3.0, 0.06262661, 3.0, 4.0, -0.0059968433, 0.04724238, 0.019518279, -0.04943331], "split_indices": [0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.639248, 24.147202, 8.492045, 11.85542, 12.291782, 5.4921036, 6.3633165, 6.260884, 6.0308986], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.012350892, -0.07762712, 0.05147479, 0.023277758, -0.080920346, 0.021598341, -7.9574265e-06], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 214, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.410047, 0.5359917, 0.0, 0.027250245, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.05147479, 4.0, -0.080920346, 0.021598341, -7.9574265e-06], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.825302, 25.055119, 7.770184, 16.661768, 8.39335, 6.103781, 10.557987], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.009104128, 0.04877029, -0.053490777, -0.10127568, 0.008991555, 0.005063118, -0.08505924], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 215, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.30028915, 0.0, 0.09409453, 0.3409475, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [4.0, 0.04877029, 7.0, 5.0, 0.008991555, 0.005063118, -0.08505924], "split_indices": [0, 0, 2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.219719, 8.175742, 20.043978, 13.372517, 6.67146, 8.330633, 5.0418835], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.01691784, -0.041696955, 0.088076614, -0.03883141, 0.01679343, 0.08120654, -0.019470014], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 216, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.12046857, 0.14016837, 0.4079376, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 6.0, 7.0, -0.03883141, 0.01679343, 0.08120654, -0.019470014], "split_indices": [0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [25.890247, 13.652859, 12.23739, 7.5601873, 6.092671, 5.5255866, 6.7118034], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.04484142, -0.01909909, 0.06136075, 0.14509471, -0.10306771, 0.083049074, 0.012700635], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 217, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.34790865, 1.3071424, 0.0, 0.17189273, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.06136075, 2.0, -0.10306771, 0.083049074, 0.012700635], "split_indices": [2, 2, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.873507, 22.227842, 8.645664, 14.921424, 7.3064184, 5.3700275, 9.551396], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.048253536, -0.055721734, -0.0, -0.073261425, 0.030071178, 0.0122861555, -0.055907883], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 218, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.19046682, 0.0, 0.17209908, 0.19790757, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.055721734, 6.0, 4.0, 0.030071178, 0.0122861555, -0.055907883], "split_indices": [1, 0, 2, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.939772, 6.474135, 21.465637, 12.604359, 8.861279, 6.1486654, 6.4556932], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.11267245, 0.18836352, -0.0062804506, 0.08762178, 0.12812816, 0.01609683, -0.02264966, 0.04874209, 0.013968756], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 219, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.32738626, 0.054563165, 0.063306205, 0.0, 0.016245544, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 4.0, 0.08762178, 4.0, 0.01609683, -0.02264966, 0.04874209, 0.013968756], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.20771, 20.451406, 12.756304, 5.490035, 14.96137, 6.1827545, 6.5735497, 8.967439, 5.9939322], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.051933482, 0.061088264, 0.021821586, -0.05104155, 0.07195322, -0.01929172, 0.044810023], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 220, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.19596465, 0.0, 0.4104083, 0.0, 0.38447762, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.061088264, 3.0, -0.05104155, 5.0, -0.01929172, 0.044810023], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.80977, 6.41413, 41.395638, 7.6633115, 33.732327, 11.814558, 21.91777], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.09610308, -0.07707768, -0.05442544, 0.120203756, -0.11093939, -0.0, 0.0044058915, -0.038412306, -0.0035841572, -0.069193766], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 221, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.32860273, 0.9520808, 0.07596794, 0.083093725, 0.0, 0.18879767, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 8.0, 5.0, 0.120203756, 5.0, -0.0, 0.0044058915, -0.038412306, -0.0035841572, -0.069193766], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [42.477043, 18.535429, 23.941616, 12.7346525, 5.8007765, 17.212017, 6.729598, 6.2804933, 6.454159, 10.480372, 6.731645], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.009900139, 0.15304567, -0.07386264, 0.005190342, 0.09447258, -0.05898915, 0.008283471, 0.06316818, -0.04326963], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 222, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.46108377, 0.27706715, 0.25727415, 0.0, 0.0, 0.0, 0.48556438, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 6.0, 7.0, 0.005190342, 0.09447258, -0.05898915, 5.0, 0.06316818, -0.04326963], "split_indices": [0, 2, 0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.851646, 13.36994, 22.481705, 8.209679, 5.160261, 8.941803, 13.539903, 5.938682, 7.6012206], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0076271477, 0.046638396, -0.021330023, -0.105343565, 0.07792409, 0.003593886, -0.07480253, 0.050026868, -0.0034086092], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 223, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.16096248, 0.0, 0.24850094, 0.30377707, 0.12124517, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.046638396, 4.0, 2.0, 6.0, 0.003593886, -0.07480253, 0.050026868, -0.0034086092], "split_indices": [2, 0, 1, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.69262, 5.2049026, 27.487717, 15.399222, 12.088493, 8.608093, 6.791129, 6.186141, 5.902353], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.042597435, 0.046992805, -0.10292305, -0.19445209, 0.034275807, -0.0050152177, -0.09205112], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 224, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.3932531, 0.0, 0.5354651, 0.33354092, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, 0.046992805, 8.0, 3.0, 0.034275807, -0.0050152177, -0.09205112], "split_indices": [1, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.824104, 6.407495, 24.41661, 17.574781, 6.8418293, 7.5977044, 9.977077], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.030297386, 0.043668706, -0.10787229, -0.05550033, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 225, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.42035365, 0.0, 0.17821196, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [3.0, 0.043668706, 7.0, -0.05550033, -0.0], "split_indices": [2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.937798, 8.194852, 20.742945, 11.599479, 9.143467], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.033086773, 0.051274005, -0.043541055, 0.00311281, -0.022951333], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 226, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.29517266, 0.0, 0.037735615, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [3.0, 0.051274005, 5.0, 0.00311281, -0.022951333], "split_indices": [0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [25.037743, 8.973015, 16.064728, 5.204106, 10.860622], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.03663906, 0.050472513, -0.055023983, -0.03351444, 0.070383474, -0.09069914, 0.047748882], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 227, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.42445642, 0.33855954, 0.0, 0.8028019, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 5.0, -0.055023983, 3.0, 0.070383474, -0.09069914, 0.047748882], "split_indices": [0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.43387, 19.082857, 11.351014, 13.294211, 5.7886453, 5.529528, 7.7646832], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.05949736, 0.038591154, -0.17702435, -0.08891549, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 228, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.638417, 0.0, 0.38192844, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, 0.038591154, 8.0, -0.08891549, -0.0], "split_indices": [2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [26.624691, 9.873698, 16.750994, 9.680317, 7.0706773], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.020608203, 0.101028025, -0.027073173, -0.0, 0.0512163, -0.09772902, 0.029871747, -0.0011670014, -0.058840595], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 229, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.14859119, 0.1069068, 0.21246272, 0.0, 0.0, 0.122988984, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 2.0, 8.0, -0.0, 0.0512163, 6.0, 0.029871747, -0.0011670014, -0.058840595], "split_indices": [1, 1, 1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.10889, 13.432299, 21.676592, 5.7101617, 7.7221375, 14.567365, 7.109228, 8.5090065, 6.058358], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04659902, 0.060671594, 0.0069938763, -0.02655883, 0.040406413, 0.03163306, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 230, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.27128065, 0.0, 0.12489674, 0.0, 0.08187623, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.060671594, 2.0, -0.02655883, 4.0, 0.03163306, -0.0], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.63339, 8.114371, 38.519016, 8.778793, 29.740225, 10.878137, 18.862087], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.038509056, -0.110276006, 0.10564296, -0.011808457, -0.043329734, 0.11162437, 0.020323444, 0.045835868, -0.027027108], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 231, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.4294789, 0.007537052, 0.63131833, 0.0, 0.0, 0.0, 0.3701329, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 3.0, 4.0, -0.011808457, -0.043329734, 0.11162437, 4.0, 0.045835868, -0.027027108], "split_indices": [2, 1, 2, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.8773, 12.046699, 28.830605, 5.5675755, 6.479123, 5.893406, 22.937199, 10.66472, 12.272478], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.06821718, -0.025455931, 0.23978993, -0.061524495, 0.05163363, 0.14497776, 0.0020159625], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 232, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.59247226, 0.8641973, 0.6790146, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 5.0, -0.061524495, 0.05163363, 0.14497776, 0.0020159625], "split_indices": [0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.150787, 22.264103, 11.886683, 11.906775, 10.3573265, 5.0787854, 6.807898], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.019596362, 0.09102964, -0.063733354, -0.0078649465, 0.06851267, 0.00939582, -0.045916755], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 233, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.20575362, 0.330518, 0.146317, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 6.0, -0.0078649465, 0.06851267, 0.00939582, -0.045916755], "split_indices": [1, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.132618, 17.897703, 14.234915, 9.699069, 8.198633, 6.668472, 7.5664425], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.12139285, -0.19569036, 0.04323928, -0.3264033, -0.0834914, -0.04213152, -0.13230756, 0.021470048, -0.057308964], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 234, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.6865567, 0.32171953, 0.0, 0.12560499, 0.2934727, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.04323928, 2.0, 7.0, -0.04213152, -0.13230756, 0.021470048, -0.057308964], "split_indices": [1, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.11049, 25.58972, 6.5207686, 10.526186, 15.063535, 5.195144, 5.331042, 5.924984, 9.138551], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.02136071, 0.042595014, -0.036253996, -0.049274947, 0.06387875, -0.010014495, 0.053948738], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 235, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.22700314, 0.0, 0.28724423, 0.0, 0.15549785, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.042595014, 6.0, -0.049274947, 5.0, -0.010014495, 0.053948738], "split_indices": [1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.40197, 9.5918, 19.81017, 8.852594, 10.957576, 5.8773, 5.0802755], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.056313775, 0.12624525, -0.04215012, 0.23433375, -0.020573722, 0.12690231, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 236, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.36052984, 0.44021732, 0.0, 0.6172477, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 7.0, -0.04215012, 3.0, -0.020573722, 0.12690231, -0.0], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [24.009468, 18.354462, 5.6550064, 12.004727, 6.349734, 6.440486, 5.564242], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.026803479, 0.05117002, -0.051772226, -0.028545592, 0.058294564, -0.052046593, 0.054262143], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 237, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.39139384, 0.26810598, 0.0, 0.46376896, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [6.0, 6.0, -0.051772226, 4.0, 0.058294564, -0.052046593, 0.054262143], "split_indices": [2, 1, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.589731, 20.556765, 11.032968, 13.269276, 7.2874885, 8.225405, 5.043871], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.03887144, -0.016850049, 0.05576257, -0.08081091, 0.038473688, 0.015602542, -0.08830076], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 238, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.2606783, 0.20725417, 0.0, 0.49816126, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 7.0, 0.05576257, 5.0, 0.038473688, 0.015602542, -0.08830076], "split_indices": [2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.378078, 20.726763, 7.6513157, 15.171247, 5.5555153, 9.53085, 5.640396], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.029051114, 0.06144146, -0.1942439, 0.0913799, -0.0, -0.01332853, -0.09252604, -0.005153822, 0.06953463], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 239, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.5347447, 0.032697797, 0.16816318, 0.23941071, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 7.0, 6.0, 4.0, -0.0, -0.01332853, -0.09252604, -0.005153822, 0.06953463], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.085182, 21.390293, 11.69489, 13.03812, 8.352172, 6.037626, 5.657264, 7.446145, 5.591975], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.03967548, -0.1251452, 0.08573975, -0.01140233, -0.15369405, 0.012415756, 0.06821128, -0.06720946, 0.039256774, 0.03508468, -0.01926389], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 240, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.54850745, 1.2848654, 0.1840494, 0.7886537, 0.0, 0.13214132, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 8.0, 3.0, -0.15369405, 8.0, 0.06821128, -0.06720946, 0.039256774, 0.03508468, -0.01926389], "split_indices": [2, 2, 0, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.80192, 29.498428, 19.303495, 23.79549, 5.7029376, 13.900683, 5.402812, 9.58989, 14.205601, 6.209205, 7.6914783], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.031819705, 0.08413353, -0.08615204, -0.021293519, 0.16614538, -0.00063937384, -0.040094998, 0.07064064, -0.07166732, 0.07603264, -0.0035959024], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 241, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.2578337, 0.27088568, 0.03558605, 0.79123116, 0.2930385, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 3.0, 4.0, 1.0, 6.0, -0.00063937384, -0.040094998, 0.07064064, -0.07166732, 0.07603264, -0.0035959024], "split_indices": [0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.636005, 28.245298, 11.390705, 12.148854, 16.096445, 5.2514515, 6.139253, 5.3244166, 6.824437, 10.895506, 5.200939], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.015141452, -0.069904454, 0.03524862, 0.037637975, -0.1743309, 0.050644338, -0.013990664, -0.027595697, -0.06616821], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 242, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.2716751, 0.3267768, 0.0, 0.17262606, 0.003648609, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, 0.03524862, 5.0, 6.0, 0.050644338, -0.013990664, -0.027595697, -0.06616821], "split_indices": [0, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.926735, 26.2964, 9.630337, 12.798238, 13.498162, 5.0777984, 7.7204394, 6.706065, 6.792097], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.025712907, -0.10030061, 0.09341937, -0.032844584, -0.050220516, -0.044912998, 0.090873495, -0.0461231, 0.019813221], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 243, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.2988878, 0.06947605, 0.698194, 0.16781253, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 6.0, 3.0, -0.050220516, -0.044912998, 0.090873495, -0.0461231, 0.019813221], "split_indices": [2, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.50462, 19.987864, 11.516755, 11.453526, 8.534339, 5.241582, 6.2751727, 5.3586645, 6.0948606], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.020771692, 0.033376075, -0.1323883, -0.06673817, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 244, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.46904254, 0.0, 0.21784237, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [5.0, 0.033376075, 8.0, -0.06673817, -0.0], "split_indices": [0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [29.686338, 13.190882, 16.495457, 9.597959, 6.897497], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.009929586, 0.071522415, -0.07075316, -0.0102341, 0.08683159, 0.027795574, -0.026615044], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 245, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.5628838, 0.4156239, 0.0, 0.13973376, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 7.0, -0.07075316, 3.0, 0.08683159, 0.027795574, -0.026615044], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.779888, 20.595182, 7.1847043, 15.335752, 5.259432, 6.006671, 9.329081], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.10107447, -0.016304526, -0.074369594, -0.049499545, 0.03799373], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 246, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.2805593, 0.3934055, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 5.0, -0.074369594, -0.049499545, 0.03799373], "split_indices": [2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [23.989117, 16.34459, 7.644527, 8.278633, 8.065956], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.04145528, 0.048015032, -0.06448644, -0.041638173, 0.07371189], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 247, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.49034676, 0.7882019, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 4.0, -0.06448644, -0.041638173, 0.07371189], "split_indices": [0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.847977, 19.129929, 9.718047, 9.728778, 9.401151], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.029078962, 0.12961249, -0.027302302, -0.0, 0.10042181], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 248, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.33663404, 0.43790755, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [6.0, 6.0, -0.027302302, -0.0, 0.10042181], "split_indices": [0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [25.587006, 14.337455, 11.249552, 9.129826, 5.207629], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.091430716, 0.009913987, -0.1475557, -0.10534804, -0.052578043, 0.0015579343, -0.04101106], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 249, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.25753456, 0.0, 0.41558045, 0.0, 0.10250895, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, 0.009913987, 5.0, -0.10534804, 7.0, 0.0015579343, -0.04101106], "split_indices": [1, 0, 1, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.525837, 9.992758, 23.53308, 6.2928557, 17.240225, 10.072646, 7.167578], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.040056992, -0.0, -0.16329452, -0.119669214, 0.08908567, -0.009942487, -0.06987012, 0.0543746, -0.11194516, -0.01669095, 0.06458498], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 250, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.2562545, 0.41484144, 0.079835504, 1.3353437, 0.44317836, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 2.0, 4.0, 5.0, 5.0, -0.009942487, -0.06987012, 0.0543746, -0.11194516, -0.01669095, 0.06458498], "split_indices": [2, 1, 1, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.70505, 37.103962, 11.601091, 15.368231, 21.735731, 5.057358, 6.5437336, 6.9765472, 8.391684, 10.016094, 11.719636], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.022081356, -0.035123337, 0.062382594, 0.024641972, -0.03248766, 0.045915563, -0.0, -0.040292513, 0.04794762], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 251, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.10349706, 0.1598405, 0.15218592, 0.0, 0.0, 0.0, 0.37691075, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 4.0, 5.0, 0.024641972, -0.03248766, 0.045915563, 8.0, -0.040292513, 0.04794762], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [41.760128, 16.287857, 25.472271, 5.6859794, 10.601878, 9.89462, 15.577651, 8.605793, 6.9718585], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.032740343, 0.055338804, -0.14418775, -0.043616068, 0.043609053, 0.03894348, -0.09174195], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 252, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [1.4976305, 0.2924112, 0.0, 0.7390673, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 5.0, -0.14418775, 3.0, 0.043609053, 0.03894348, -0.09174195], "split_indices": [0, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.211086, 29.970411, 5.2406735, 13.993502, 15.97691, 8.496757, 5.496745], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.0635467, -0.065650925, 0.108446434, -0.020565486, -0.0103506325, 0.06030316], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 253, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.5048555, 0.17224723, 0.0, 0.3039552, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [6.0, 8.0, -0.065650925, 4.0, -0.020565486, -0.0103506325, 0.06030316], "split_indices": [1, 2, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.37064, 26.114939, 7.255703, 20.188873, 5.926065, 7.75681, 12.432064], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0073349224, 0.05960204, -0.044927064, 0.011586637, -0.058890026, 0.054551918, -0.026589246], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 254, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.3295086, 0.0, 0.22929612, 0.33622003, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.05960204, 8.0, 6.0, -0.058890026, 0.054551918, -0.026589246], "split_indices": [2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.521635, 6.14304, 23.378593, 17.214542, 6.164052, 6.446452, 10.76809], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.016692527, 0.079066366, -0.027743416, 0.05729341, 0.0013221472, -0.01673606, 0.021703927], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 255, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.20139065, 0.1491831, 0.0, 0.0, 0.058313474, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [5.0, 5.0, -0.027743416, 0.05729341, 2.0, -0.01673606, 0.021703927], "split_indices": [1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.6986, 18.385773, 9.312829, 6.412422, 11.973351, 5.9753084, 5.9980416], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.029941827, 0.05288474, -0.07776302, 0.0070577157, -0.044128627], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 256, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.40803048, 0.0, 0.11660261, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [3.0, 0.05288474, 5.0, 0.0070577157, -0.044128627], "split_indices": [0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [23.392857, 10.041299, 13.351558, 5.10782, 8.243737], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.047052994, 0.14828509, -0.005472712, -0.0, 0.07606362, -0.08739342, 0.026247133, -0.005688018, -0.03923315], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 257, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.18296899, 0.15874386, 0.16832934, 0.0, 0.0, 0.019029967, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 4.0, 8.0, -0.0, 0.07606362, 4.0, 0.026247133, -0.005688018, -0.03923315], "split_indices": [2, 1, 2, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.571789, 10.47131, 20.10048, 5.0782366, 5.393073, 11.288485, 8.8119955, 5.8316717, 5.456813], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.01756975, -0.06391379, 0.1658153, 0.104145005, 0.053898644, 0.049958248, -0.011253281], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 258, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.95306265, 0.0, 0.29127282, 0.0, 0.14466742, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [4.0, -0.06391379, 6.0, 0.104145005, 6.0, 0.049958248, -0.011253281], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [25.982162, 9.731635, 16.250526, 5.058702, 11.191825, 5.1359057, 6.0559196], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.11804673, 0.007948219, 0.17876282, -0.034030646, 0.032583613, 0.09239064, 0.07497245, 0.03645349, 0.0043106424], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 259, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.1971688, 0.17293796, 0.21106791, 0.0, 0.0, 0.0, 0.021380551, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [5.0, 4.0, 4.0, -0.034030646, 0.032583613, 0.09239064, 7.0, 0.03645349, 0.0043106424], "split_indices": [2, 0, 0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.500944, 12.184429, 19.316515, 5.015861, 7.168568, 7.2992125, 12.0173025, 5.295589, 6.721714], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.031372793, 0.13217966, -0.05432383, 0.0039425725, 0.05814137, 0.028668778, -0.14614606, -0.014038728, -0.063859105], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 260, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.44783416, 0.15172434, 0.3941468, 0.0, 0.0, 0.0, 0.081468046, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 4.0, 4.0, 0.0039425725, 0.05814137, 0.028668778, 7.0, -0.014038728, -0.063859105], "split_indices": [1, 0, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.10738, 22.851334, 26.256046, 8.762064, 14.089269, 9.545162, 16.710884, 7.9992795, 8.711604], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0648092, -0.024475034, -0.07614794, -0.13109921, 0.18794672, -0.0066972296, -0.07994062, 0.08497057, 0.021841485], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 261, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.30398798, 0.88152647, 0.0, 0.35182905, 0.084486604, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 7.0, -0.07614794, 5.0, 4.0, -0.0066972296, -0.07994062, 0.08497057, 0.021841485], "split_indices": [0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [43.338547, 37.083817, 6.2547326, 25.258844, 11.82497, 15.060626, 10.198218, 5.1962795, 6.6286907], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.07043988, 0.17158325, 0.00709612, 0.021313513, 0.08022793, 0.057010476, -0.10269355, -0.09613214, 0.012124007], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 262, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.22294752, 0.08106342, 0.52796996, 0.0, 0.0, 0.0, 0.5350586, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 3.0, 4.0, 0.021313513, 0.08022793, 0.057010476, 7.0, -0.09613214, 0.012124007], "split_indices": [0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.777515, 12.896977, 23.880539, 7.65335, 5.2436275, 9.024033, 14.856506, 5.6774664, 9.17904], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.14079379, 0.032184802, -0.27481553, -0.13425194, -0.16325489, -0.08472298, -0.015414114], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 263, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [1.0291454, 0.0, 0.24609768, 0.0, 0.137624, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, 0.032184802, 6.0, -0.13425194, 3.0, -0.08472298, -0.015414114], "split_indices": [0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.563278, 9.826896, 18.736382, 5.8198347, 12.916548, 5.0837517, 7.8327956], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.018739363, -0.14292237, 0.10913942, -0.0, -0.10222658, 0.007880314, 0.05333584], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 264, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.5608491, 0.53278154, 0.071528435, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 8.0, -0.0, -0.10222658, 0.007880314, 0.05333584], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.12214, 17.140938, 15.981201, 10.361154, 6.7797837, 8.526826, 7.4543753], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.012569232, 0.056904133, -0.070672005, -0.051660653, 0.01928236], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 265, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.45039985, 0.0, 0.2953261, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [3.0, 0.056904133, 4.0, -0.051660653, 0.01928236], "split_indices": [2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.833696, 8.874401, 18.959295, 11.08454, 7.874755], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.023971688, -0.19632556, 0.042594098, -0.09266601, -0.009396133], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 266, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.7596274, 0.20094657, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [6.0, 6.0, 0.042594098, -0.09266601, -0.009396133], "split_indices": [2, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [24.39356, 12.171787, 12.221774, 6.357133, 5.814654], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.05417878, -0.026654437, 0.15419997, 0.030423842, -0.06619733, 0.07181399, 0.008904541], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 267, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.2691744, 0.47055528, 0.12135562, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 6.0, 0.030423842, -0.06619733, 0.07181399, 0.008904541], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.207008, 16.558664, 13.648343, 9.951318, 6.6073475, 7.079722, 6.5686207], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.054239072, 0.141927, -0.051370706, -0.015954727, 0.08280643, 0.036816828, -0.061046075], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 268, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.28174108, 0.46710306, 0.38307887, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 3.0, -0.015954727, 0.08280643, 0.036816828, -0.061046075], "split_indices": [0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.658148, 15.440708, 12.217439, 6.231473, 9.209235, 5.4807196, 6.7367187], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.054718953, -0.007916149, 0.089993104, 0.05589969, 0.025606724, 0.05129845, -0.017709084], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 269, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.11043836, 0.0, 0.1344433, 0.0, 0.23395026, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [4.0, -0.007916149, 4.0, 0.05589969, 6.0, 0.05129845, -0.017709084], "split_indices": [2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.548737, 9.688621, 24.860115, 8.578017, 16.282097, 6.0502524, 10.231844], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.013982427, 0.024843669, -0.10615048, -0.019118793, 0.063963965, 0.00095374126, -0.057119507, 0.018260803, -0.02076578], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 270, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.18590754, 0.3090432, 0.16171864, 0.11904023, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 4.0, 1.0, 0.063963965, 0.00095374126, -0.057119507, 0.018260803, -0.02076578], "split_indices": [1, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.941856, 33.482655, 14.459201, 27.445047, 6.0376067, 6.195068, 8.264133, 9.718306, 17.726742], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0119558005, -0.05027968, 0.048823953, 0.16280812, -0.011000934, 0.078910336, 0.006939228, -0.05932087, 0.016230056], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 271, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.2727155, 0.0, 0.2616283, 0.14712101, 0.315488, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.05027968, 5.0, 4.0, 7.0, 0.078910336, 0.006939228, -0.05932087, 0.016230056], "split_indices": [0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.75474, 5.876556, 34.878185, 11.98787, 22.890316, 6.0485816, 5.939288, 5.787686, 17.10263], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.06344838, -0.0, 0.067266785, -0.028824568, 0.0554074, 0.036801226, -0.0067973784], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 272, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.3581406, 0.14934672, 0.0, 0.0, 0.11041148, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 4.0, 0.067266785, -0.028824568, 7.0, 0.036801226, -0.0067973784], "split_indices": [1, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.645313, 26.587519, 9.0577965, 8.9945, 17.593018, 9.831503, 7.761515], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.02600257, 0.06949572, -0.13225955, 0.001925742, 0.030295547, -0.103378765, 0.016712524], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 273, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.3252429, 0.020863317, 0.6583458, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 4.0, 0.001925742, 0.030295547, -0.103378765, 0.016712524], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.527647, 15.237667, 14.289979, 6.5323043, 8.705363, 6.5685787, 7.7214003], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.1774544, -0.0, -0.24484652, -0.09814808, -0.018601848], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 274, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.3834442, 0.0, 0.29589856, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, -0.0, 6.0, -0.09814808, -0.018601848], "split_indices": [0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [31.741493, 9.1471615, 22.594332, 14.658781, 7.9355507], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.015881592, 0.0840313, -0.040780075, -0.0075684185, 0.15579377, 0.012128218, 0.073738195], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 275, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.3035202, 0.18089163, 0.0, 0.0, 0.09962821, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 2.0, -0.040780075, -0.0075684185, 4.0, 0.012128218, 0.073738195], "split_indices": [2, 1, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.734116, 19.904284, 7.8298316, 7.5953, 12.308984, 6.5163407, 5.792643], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.022387713, -0.118404575, 0.09046536, -0.0, -0.05460835, 0.0464337, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 276, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.28667724, 0.10391398, 0.056130283, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 6.0, -0.0, -0.05460835, 0.0464337, -0.0], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [24.210125, 13.532993, 10.677132, 5.2476625, 8.285331, 5.3321223, 5.3450093], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.02623483, -0.08559642, 0.10968516, 0.0036273543, -0.057620768, -0.0, 0.06888871], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 277, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.31597188, 0.16570526, 0.2562274, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [3.0, 6.0, 6.0, 0.0036273543, -0.057620768, -0.0, 0.06888871], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.59353, 12.977898, 18.615633, 6.7406545, 6.237243, 10.280821, 8.334811], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.002359753, 0.089920096, -0.12859523, -0.0038071915, 0.08503337, 0.0076769665, -0.08960634], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 278, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.36361918, 0.37616062, 0.3887856, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 5.0, -0.0038071915, 0.08503337, 0.0076769665, -0.08960634], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.890005, 16.398205, 12.4918, 10.989195, 5.409009, 6.6895294, 5.802271], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.07787048, 0.012026963, -0.12545255, -0.053213447, -0.09483288, 0.020307854, -0.09137168], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 279, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.21120769, 0.0, 0.30348444, 0.6640758, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, 0.012026963, 8.0, 7.0, -0.09483288, 0.020307854, -0.09137168], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.212753, 9.274999, 24.937754, 19.39332, 5.5444345, 13.319399, 6.0739202], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.04499223, 0.19110747, -0.0074325516, 0.11719833, -0.0058444478, -0.062006578, 0.03101504, 0.09333998, -0.009364015], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 280, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.40571317, 0.6265237, 0.3115462, 0.0, 0.0, 0.0, 0.5940143, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 5.0, 4.0, 0.11719833, -0.0058444478, -0.062006578, 3.0, 0.09333998, -0.009364015], "split_indices": [0, 2, 0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.381504, 12.844423, 36.537083, 6.3997784, 6.444645, 5.6226397, 30.914442, 5.184371, 25.73007], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.054875758, 0.037582926, -0.051027425, 0.028243238, 0.1633751, -0.024600303, 0.1205495, -0.018750854], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 281, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.08470803, 0.32197183, 0.3663555, 0.0, 0.0, 0.7007147, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 7.0, 6.0, -0.051027425, 0.028243238, 7.0, -0.024600303, 0.1205495, -0.018750854], "split_indices": [1, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.300426, 16.377638, 21.92279, 9.535137, 6.8424997, 10.913135, 11.009656, 5.1451755, 5.767959], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.033676274, 0.02246948, -0.14999343, 0.08540252, -0.031284593, -0.11147455, 0.038101178, -0.0010448821, 0.047880977], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 282, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.25228974, 0.20388353, 0.8378443, 0.13203806, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 7.0, 7.0, 4.0, -0.031284593, -0.11147455, 0.038101178, -0.0010448821, 0.047880977], "split_indices": [1, 2, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.162514, 23.679594, 11.482923, 16.568417, 7.111176, 6.3903847, 5.0925374, 7.3587255, 9.209692], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.043633185, 0.008882394, 0.09509254, -0.009157492, 0.019575728, 0.041705977, 0.005411078], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 283, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.04554015, 0.0525948, 0.020835929, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 6.0, 4.0, -0.009157492, 0.019575728, 0.041705977, 0.005411078], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.75294, 20.216507, 10.53643, 11.124234, 9.092274, 5.372051, 5.16438], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.102357425, 0.18203852, 0.00799459, 0.017155945, 0.09702074, -0.024270803, 0.026610171], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 284, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.22166675, 0.24064404, 0.1253552, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 8.0, 0.017155945, 0.09702074, -0.024270803, 0.026610171], "split_indices": [2, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.202093, 15.838774, 15.363319, 9.532042, 6.3067317, 6.7198896, 8.64343], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.07936674, -0.046522662, -0.021935903, 0.09334693, -0.082950294, -0.022528801, 0.08923665], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 285, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.10333006, 0.0, 0.57793033, 0.49141684, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [4.0, -0.046522662, 6.0, 3.0, -0.082950294, -0.022528801, 0.08923665], "split_indices": [2, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.618292, 10.428878, 17.189415, 11.996939, 5.192476, 6.6505985, 5.34634], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.034172274, -0.099814735, 0.06332197], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 286, "left_children": [1, -1, -1], "loss_changes": [1.6291765, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, -0.099814735, 0.06332197], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [23.207159, 7.079131, 16.128029], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.031144684, 0.042397443, -0.1192047, -0.054655164, -0.0156119345], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 287, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.48331228, 0.0, 0.057427824, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [2.0, 0.042397443, 6.0, -0.054655164, -0.0156119345], "split_indices": [1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [29.859102, 9.504214, 20.354887, 8.852613, 11.502274], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.124607556, -0.032183513, -0.08399236, 0.0059637236, -0.026675425], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 288, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.34553614, 0.06295436, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 5.0, -0.08399236, 0.0059637236, -0.026675425], "split_indices": [2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [25.864351, 17.377613, 8.486738, 8.481535, 8.896079], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.008870601, -0.068767644, 0.060129706, 0.010085471, -0.05460741, -0.031532906, 0.18631347, 0.07308175, 0.030968765], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 289, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.14166042, 0.17773014, 0.49172497, 0.0, 0.0, 0.0, 0.0035850704, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [5.0, 4.0, 5.0, 0.010085471, -0.05460741, -0.031532906, 7.0, 0.07308175, 0.030968765], "split_indices": [2, 1, 1, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.774075, 12.519422, 21.254652, 6.4808598, 6.0385623, 8.932256, 12.322396, 5.4373426, 6.885054], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.05033155, 0.13250634, -0.022279788, 0.041912477, 0.07866519, 0.036547285, -0.08583044, 0.08743521, -0.028799543, -0.10342981, 0.011786943], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 290, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.3026717, 0.23459464, 0.24401498, 0.5800095, 0.0, 0.0, 0.6609335, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [3.0, 7.0, 4.0, 1.0, 0.07866519, 0.036547285, 6.0, 0.08743521, -0.028799543, -0.10342981, 0.011786943], "split_indices": [1, 2, 2, 1, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.383682, 22.48714, 24.896542, 14.47291, 8.014231, 6.857143, 18.0394, 5.0133886, 9.459521, 5.565463, 12.473936], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0760327, 0.007000315, -0.111335956, 0.057036333, -0.05399505, -0.18251775, 0.028013587, -0.07824571, -0.03484773], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 291, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.12904127, 0.449116, 0.45012656, 0.0, 0.0, 0.04787284, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 4.0, 7.0, 0.057036333, -0.05399505, 5.0, 0.028013587, -0.07824571, -0.03484773], "split_indices": [2, 1, 1, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.19612, 11.087502, 28.108618, 5.863332, 5.22417, 21.299448, 6.809169, 7.734984, 13.564465], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04178952, -0.07408316, 0.107735075, 0.021763159, -0.06247756, 0.14797015, -0.006386193, 0.009928944, 0.058109753], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 292, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.30117524, 0.30016732, 0.14627251, 0.0, 0.0, 0.075301796, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 1.0, 9.0, 0.021763159, -0.06247756, 5.0, -0.006386193, 0.009928944, 0.058109753], "split_indices": [1, 1, 1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.929295, 12.799515, 24.129778, 5.9793334, 6.8201814, 18.872633, 5.257146, 6.4871416, 12.38549], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.09559893, -0.0010193055, 0.13427578, 0.16804804, 0.01143671, 0.012142022, 0.0871065], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 293, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.1255076, 0.0, 0.043696493, 0.19005698, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.0010193055, 6.0, 7.0, 0.01143671, 0.012142022, 0.0871065], "split_indices": [1, 0, 1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.241877, 7.4639354, 20.77794, 14.053998, 6.7239428, 7.930485, 6.1235137], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.025402911, 0.08513367, -0.029379595, -0.03550097, 0.20107366, 0.029391332, 0.09535571], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 294, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.23782538, 0.55543107, 0.0, 0.0, 0.10566604, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 5.0, -0.029379595, -0.03550097, 5.0, 0.029391332, 0.09535571], "split_indices": [0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.32502, 21.185482, 9.139537, 7.369357, 13.816125, 8.769183, 5.0469427], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.015316323, 0.061983902, -0.080974504, -0.07788646, 0.027908115], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 295, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.5265831, 0.0, 0.6105514, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [3.0, 0.061983902, 7.0, -0.07788646, 0.027908115], "split_indices": [2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [25.997055, 8.668512, 17.328543, 8.590914, 8.737629], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.08605022, 0.032712374, 0.06116139, -0.027953833, 0.050196085], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 296, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.12816852, 0.3472964, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [8.0, 6.0, 0.06116139, -0.027953833, 0.050196085], "split_indices": [1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [24.22086, 18.136719, 6.084141, 9.146935, 8.989782], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.04022136, 0.02148001, -0.08872218, 0.038300257, -0.03975944, -0.034306277, 0.00592332], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 297, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.52940303, 0.18254437, 0.0, 0.0, 0.085474424, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 3.0, -0.08872218, 0.038300257, 6.0, -0.034306277, 0.00592332], "split_indices": [0, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.436514, 24.938965, 5.497548, 9.322396, 15.616569, 7.2662582, 8.35031], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.037526473, -0.0021038868, 0.032855228, -0.029115325, 0.040182818], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 298, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.094751984, 0.25505644, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [5.0, 7.0, 0.032855228, -0.029115325, 0.040182818], "split_indices": [1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.365377, 18.024673, 10.340704, 11.217579, 6.8070936], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.050556786, 0.023994714, -0.11000234, -0.05826654, -0.046347078, 0.04175631, -0.04760422], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 299, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.28530294, 0.0, 0.10184887, 0.0, 0.3642091, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, 0.023994714, 5.0, -0.05826654, 5.0, 0.04175631, -0.04760422], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.24532, 10.080785, 24.164534, 8.842844, 15.32169, 5.3523207, 9.969369], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.017405327, -0.045099508, 0.06145086, 0.0548394, -0.059157357, -0.045675725, 0.16869459, 0.08990273, -0.0060824533], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 300, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.15128817, 0.7893862, 0.77582675, 0.0, 0.0, 0.0, 0.59172213, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [2.0, 5.0, 5.0, 0.0548394, -0.059157357, -0.045675725, 8.0, 0.08990273, -0.0060824533], "split_indices": [1, 2, 2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.352165, 20.692797, 31.659369, 7.9383736, 12.754423, 10.127092, 21.532276, 12.683945, 8.848332], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.026862206, 0.04507651, -0.11128525, -0.041932445, 0.067087516, -0.011150563, -0.049310796], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 301, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.25534457, 0.75710356, 0.05002421, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 8.0, -0.041932445, 0.067087516, -0.011150563, -0.049310796], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.12776, 20.738007, 18.389755, 10.020681, 10.717325, 9.170193, 9.219563], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0056169913, -0.041853175, 0.042913437, -0.12222563, 0.042799868, -0.0, -0.08020078], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 302, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.25479436, 0.42214525, 0.0, 0.3595387, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, 0.042913437, 4.0, 0.042799868, -0.0, -0.08020078], "split_indices": [0, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.58776, 26.427128, 9.160632, 19.02139, 7.405737, 10.805577, 8.215813], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.022102593, -0.016586121, 0.06817952, 0.02031214, 0.033426076, -0.038626626, 0.047863655], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 303, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.1269631, 0.0, 0.031519443, 0.29525492, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.016586121, 8.0, 6.0, 0.033426076, -0.038626626, 0.047863655], "split_indices": [0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.186367, 11.500403, 21.685965, 12.087506, 9.598458, 5.52708, 6.5604267], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.063947596, -0.006969709, 0.05622505, 0.037420064, -0.028851308, -0.009626324, 0.031364653], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 304, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.30377108, 0.092949614, 0.0, 0.07614851, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 6.0, 0.05622505, 3.0, -0.028851308, -0.009626324, 0.031364653], "split_indices": [2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.482496, 20.081736, 11.40076, 13.048015, 7.0337214, 5.9350004, 7.1130137], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.005822638, 0.07547695, -0.1265059, -0.07121077, -0.036215786, 0.022164263, -0.04748698], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 305, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.89971244, 0.0, 0.16648594, 0.0, 0.19248243, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.07547695, 4.0, -0.07121077, 7.0, 0.022164263, -0.04748698], "split_indices": [1, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.340017, 8.11587, 19.224148, 7.4006743, 11.823473, 5.993577, 5.829896], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.026235202, 0.05940203, -0.082522236, -0.10065677, 0.04362022], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 306, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.51343924, 0.0, 1.005657, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [3.0, 0.05940203, 6.0, -0.10065677, 0.04362022], "split_indices": [0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [24.99109, 9.727043, 15.264048, 7.218256, 8.045792], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.0717754, 0.072137095, -0.15376058, -0.24300574, 0.0029744953, -0.11016684, -0.02509955], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 307, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.8347656, 0.0, 0.40282005, 0.26346076, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, 0.072137095, 7.0, 6.0, 0.0029744953, -0.11016684, -0.02509955], "split_indices": [1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.504374, 5.6883183, 24.816055, 16.141232, 8.674825, 7.984518, 8.1567135], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.032147456, 0.077311255, -0.053302277, -0.0075125224, 0.07254048], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 308, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.4680821, 0.30087578, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [4.0, 7.0, -0.053302277, -0.0075125224, 0.07254048], "split_indices": [1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [26.89282, 15.223774, 11.669047, 9.53223, 5.691543], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.025807165, 0.100403816, -0.033285644, 0.15996808, -0.0, 0.0016263087, 0.09909284], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 309, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.37139902, 0.15191372, 0.0, 0.36893523, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [6.0, 7.0, -0.033285644, 4.0, -0.0, 0.0016263087, 0.09909284], "split_indices": [0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.41672, 22.914719, 11.502003, 14.357543, 8.557176, 8.376555, 5.9809875], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.030752957, -0.04508755, 0.146345, -0.11174975, 0.020912653, 0.07819004, 0.024534, -0.0011098048, -0.09612472], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 310, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.46460095, 0.24992138, 0.09938979, 0.43096077, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 4.0, 2.0, 0.020912653, 0.07819004, 0.024534, -0.0011098048, -0.09612472], "split_indices": [1, 2, 2, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.089237, 30.091131, 19.998106, 19.604471, 10.48666, 5.563826, 14.43428, 13.902507, 5.701965], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.029135693, 0.07102503, -0.022888022, 0.014207621, 0.07202306, 0.020479731, -0.046100214], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 311, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.19718802, 0.281958, 0.0, 0.23539266, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 7.0, -0.022888022, 5.0, 0.07202306, 0.020479731, -0.046100214], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [42.54182, 31.357584, 11.184239, 24.72367, 6.6339135, 19.600925, 5.122743], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.025505343, 0.07354351, -0.16619092, -0.008289515, 0.053197835, -0.0006977197, -0.104954325, -0.0, -0.006977899], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 312, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.51366246, 0.19675116, 0.42492574, 0.0028682903, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 5.0, 5.0, 0.053197835, -0.0006977197, -0.104954325, -0.0, -0.006977899], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.32002, 20.003275, 14.316744, 11.160078, 8.843197, 8.388966, 5.9277787, 5.3138647, 5.846213], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04681014, 0.08581815, -0.017616954, -0.0, 0.042340934], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 313, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.14272478, 0.13263567, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [8.0, 4.0, -0.017616954, -0.0, 0.042340934], "split_indices": [2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [31.78781, 24.037333, 7.750476, 9.121793, 14.91554], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.021248272, -0.04693616, 0.075976774, 0.07427856, -0.18234126, -0.03062226, 0.087529756, -0.0, -0.09294449], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 314, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.53025174, 0.43184853, 0.0, 0.5635116, 0.25784793, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, 0.075976774, 3.0, 2.0, -0.03062226, 0.087529756, -0.0, -0.09294449], "split_indices": [1, 2, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.463633, 23.827633, 6.6359987, 12.421136, 11.406498, 6.8983645, 5.5227714, 5.422686, 5.9838123], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.09424676, -0.0, 0.062399812, 0.028154662, -0.014938728], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 315, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.29040885, 0.08581112, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [4.0, 3.0, 0.062399812, 0.028154662, -0.014938728], "split_indices": [1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.296995, 15.774832, 11.522162, 5.595509, 10.179323], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.19218968, -0.27715513, 0.014394847, -0.15112351, -0.14021558, -0.10476335, 0.03137472], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 316, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.5612071, 0.334046, 0.0, 0.74471515, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 7.0, 0.014394847, 5.0, -0.14021558, -0.10476335, 0.03137472], "split_indices": [1, 2, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [24.726837, 18.459007, 6.26783, 12.4985485, 5.9604588, 7.057058, 5.4414907], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.046790347, 0.053955078, -0.0, -0.02160009, 0.06194298, 0.033031754, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 317, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.20477691, 0.0, 0.111627266, 0.0, 0.04363901, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.053955078, 6.0, -0.02160009, 5.0, 0.033031754, -0.0], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.363398, 7.453627, 22.909771, 10.914507, 11.995263, 6.712504, 5.2827597], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.1510654, -0.032271203, -0.23456542, -0.054826, 0.03399458, -0.10336852, -0.024266204], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 318, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.22669023, 0.30177423, 0.17966753, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [3.0, 5.0, 6.0, -0.054826, 0.03399458, -0.10336852, -0.024266204], "split_indices": [1, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [25.306608, 11.51231, 13.794299, 5.873199, 5.639111, 6.936577, 6.8577223], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0630578, -0.07278318, -4.6254318e-05, -0.043429684, 0.026817156, -0.08019145, 0.036310203], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 319, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.36250734, 0.0, 0.102955274, 0.76190746, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.07278318, 8.0, 4.0, 0.026817156, -0.08019145, 0.036310203], "split_indices": [2, 0, 2, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.186646, 7.468299, 25.718348, 18.417177, 7.301172, 7.7924623, 10.624714], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0576518, -0.09370702, 0.04935457, -0.014762295, -0.22683086, 0.059980243, -0.02307692, 0.01619776, -0.051201034, -0.10168273, -0.0053704414], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 320, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.20532224, 0.38333896, 0.27062333, 0.30046102, 0.2864493, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, 4.0, 6.0, 5.0, 0.059980243, -0.02307692, 0.01619776, -0.051201034, -0.10168273, -0.0053704414], "split_indices": [2, 2, 1, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.869656, 38.093178, 11.77648, 25.081493, 13.011684, 5.4665346, 6.309945, 17.414183, 7.6673107, 7.7513795, 5.260305], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.03202128, 0.12641801, -0.2168081, 0.19451845, -0.012949732, -0.09260481, -0.03427008, 0.013602284, 0.08559796], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 321, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [1.2451832, 0.28416008, 0.10826671, 0.18027765, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 6.0, 8.0, 3.0, -0.012949732, -0.09260481, -0.03427008, 0.013602284, 0.08559796], "split_indices": [0, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.285496, 21.5677, 18.717796, 15.70402, 5.8636804, 8.299656, 10.418141, 6.926254, 8.777765], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.018904405, -0.029620439, 0.072201736, 0.032826535, -0.04666146, 0.043598756, -0.006461281], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 322, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.1030799, 0.35944358, 0.14636073, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 8.0, 0.032826535, -0.04666146, 0.043598756, -0.006461281], "split_indices": [2, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.3836, 18.29489, 18.088707, 8.37918, 9.9157095, 10.451653, 7.637055], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.020605221, 0.14198463, -0.059172947, 0.018729055, 0.055528693, -0.060780104, 0.02702902, 0.05857997, -0.041277997], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 323, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.31563962, 0.00839898, 0.2559367, 0.0, 0.0, 0.0, 0.37087834, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 3.0, 3.0, 0.018729055, 0.055528693, -0.060780104, 5.0, 0.05857997, -0.041277997], "split_indices": [0, 1, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.87304, 12.03539, 17.837648, 5.905485, 6.1299047, 6.624771, 11.212877, 5.75391, 5.458967], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0008841358, -0.08950899, 0.1292038, 0.020354256, -0.16663384, -0.0, 0.08552462, -0.020608718, -0.06675689], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 324, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.3854007, 0.27044955, 0.2714211, 0.0, 0.034956247, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 2.0, 7.0, 0.020354256, 2.0, -0.0, 0.08552462, -0.020608718, -0.06675689], "split_indices": [2, 2, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.769932, 19.539158, 12.230774, 6.0398493, 13.49931, 7.173436, 5.0573378, 6.3646026, 7.1347065], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010631623, -0.037356656, 0.05768081, 0.006595569, 0.04525771, -0.02361701, 0.030700188], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 325, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.229355, 0.0, 0.07973311, 0.13154812, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.037356656, 7.0, 7.0, 0.04525771, -0.02361701, 0.030700188], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.712395, 9.444971, 19.267424, 13.829406, 5.438018, 6.8542933, 6.9751124], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.05134246, 0.04342839, -0.12807278, -0.056185935, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 326, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.35951433, 0.0, 0.13960263, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [3.0, 0.04342839, 8.0, -0.056185935, -0.0], "split_indices": [1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [21.77137, 5.5069857, 16.264385, 11.0533, 5.211085], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.021258488, -0.076062754, 0.07898806, 0.06493448, -0.081138, -0.010745597, 0.04992606], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 327, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.73348385, 0.62787855, 0.0, 0.15173689, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 4.0, 0.07898806, 4.0, -0.081138, -0.010745597, 0.04992606], "split_indices": [2, 2, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.493929, 20.51679, 7.9771385, 11.979535, 8.537254, 5.8417006, 6.1378345], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.031031812, -0.11338897, 0.038740486, -0.05953587, 0.010599084], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 328, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.38213974, 0.26903448, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [6.0, 7.0, 0.038740486, -0.05953587, 0.010599084], "split_indices": [1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [26.97932, 18.422794, 8.556526, 11.934523, 6.4882717], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.049936228, -0.030928737, 0.16152762, 0.073244, 0.009972412], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 329, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.577112, 0.0, 0.17134446, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, -0.030928737, 6.0, 0.073244, 0.009972412], "split_indices": [0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [31.477264, 12.928135, 18.54913, 10.259285, 8.289844], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.08175556, 0.015267145, -0.1818314, -0.034126572, 0.093472056, -0.100801304, -0.09079565, 0.061880074, -0.037525453, 0.048329186, -0.08165306], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 330, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.533598, 0.284091, 0.19007093, 0.0, 0.47840464, 0.8604974, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [3.0, 4.0, 7.0, -0.034126572, 9.0, 6.0, -0.09079565, 0.061880074, -0.037525453, 0.048329186, -0.08165306], "split_indices": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.954193, 26.302418, 25.651777, 9.272079, 17.030338, 16.947607, 8.704169, 11.571956, 5.458382, 6.4963117, 10.451296], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.016414603, -0.2614135, 0.07301812, -0.097695045, -0.040296562, 0.07497905, -0.04564055, -0.07588014, 0.017895246], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 331, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.9529258, 0.005653739, 0.6841724, 0.0, 0.0, 0.0, 0.4514526, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 4.0, 4.0, -0.097695045, -0.040296562, 0.07497905, 6.0, -0.07588014, 0.017895246], "split_indices": [0, 2, 2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.759926, 10.684666, 30.075258, 5.5369406, 5.147725, 11.968096, 18.107162, 5.9468236, 12.160339], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.094162524, -0.24646075, 0.049002543, -0.14204642, -0.0024665026, 0.07265773, -0.0281364], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 332, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.82386416, 0.92721987, 0.5677539, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 6.0, 5.0, -0.14204642, -0.0024665026, 0.07265773, -0.0281364], "split_indices": [1, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.325855, 17.10579, 18.220066, 8.038445, 9.067344, 7.745247, 10.474819], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.09238712, -0.07588874, -0.026992118, 0.035352636, -0.088109076, -0.007072841, 0.029687649, -0.044241283, -0.0051076435], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 333, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.3021279, 0.0, 0.10122483, 0.054717056, 0.037580483, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.07588874, 5.0, 5.0, 8.0, -0.007072841, 0.029687649, -0.044241283, -0.0051076435], "split_indices": [1, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.088095, 7.724194, 23.363901, 10.964492, 12.39941, 5.2021036, 5.7623878, 5.404614, 6.9947963], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.031696536, -0.04108294, 0.09324666, 0.08492217, -0.0065605114, -0.034548532, 0.041456956], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 334, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.34215823, 0.0, 0.4953212, 0.0, 0.2768588, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.04108294, 6.0, 0.08492217, 7.0, -0.034548532, 0.041456956], "split_indices": [2, 0, 2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.154627, 7.568443, 23.586184, 7.8402414, 15.745942, 9.507812, 6.2381306], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.05643328, -0.040261526, 0.16565089, 0.04657782, -0.0915879, -0.0026429116, 0.10629435], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 335, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.31793243, 0.8563795, 0.49729258, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 6.0, 0.04657782, -0.0915879, -0.0026429116, 0.10629435], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.286196, 14.322056, 12.96414, 8.26305, 6.0590053, 6.955513, 6.0086265], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.13704129, 0.20785853, -0.0003656428, 0.00026166684, 0.09029388], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 336, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.2504229, 0.27765346, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 4.0, -0.0003656428, 0.00026166684, 0.09029388], "split_indices": [2, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [22.43999, 14.984677, 7.4553123, 5.2960453, 9.688632], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.079065554, 0.0032577515, 0.107943125, 0.04828318, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 337, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.045779243, 0.0, 0.10269342, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, 0.0032577515, 5.0, 0.04828318, -0.0], "split_indices": [0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.869316, 10.026501, 18.842815, 11.890281, 6.952534], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.023201559, -0.04219863, 0.106291324, 0.0130479215, -0.03756238, 0.066301435, -0.0044840053], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 338, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.15619624, 0.12034754, 0.20101435, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 6.0, 0.0130479215, -0.03756238, 0.066301435, -0.0044840053], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [25.738926, 14.004534, 11.734393, 6.5324025, 7.472131, 6.030425, 5.703968], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.06257188, 0.026742173, -0.11842269, -0.043381955, 0.05994729, -0.006597657, -0.048437573], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 339, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.17271864, 0.40505126, 0.06382194, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 5.0, 4.0, -0.043381955, 0.05994729, -0.006597657, -0.048437573], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.152227, 11.497172, 19.655054, 5.569858, 5.9273148, 7.2483454, 12.406709], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.039145395, -0.023892976, 0.23896694, 0.004619702, -0.09560397, 0.008240711, 0.12660472, 0.02257557, -0.022473183, -0.045635145, -0.0020465252], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 340, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6593765, 0.08862581, 0.42764932, 0.16088721, 0.04351583, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 6.0, 6.0, 6.0, 0.008240711, 0.12660472, 0.02257557, -0.022473183, -0.045635145, -0.0020465252], "split_indices": [1, 1, 2, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.18563, 37.678917, 11.506712, 26.631273, 11.047645, 6.1255293, 5.381183, 14.799111, 11.832161, 5.626466, 5.4211793], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.026181953, -0.1340812, 0.10857095, -0.10975307, -0.017125355, 0.08112913, 0.015083005, 0.014628397, -0.017157542, -0.028346935, 0.041506454], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 341, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.6135081, 0.59618366, 0.24549504, 0.0, 0.048631504, 0.0, 0.19442372, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [7.0, 2.0, 2.0, -0.10975307, 4.0, 0.08112913, 5.0, 0.014628397, -0.017157542, -0.028346935, 0.041506454], "split_indices": [0, 1, 1, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.018604, 22.638557, 17.380047, 6.5922112, 16.046347, 5.2786207, 12.101426, 5.04209, 11.004257, 6.1008368, 6.000589], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.06084238, 0.012831379, 0.05280112, 0.11199982, -0.093217045, 0.07856816, -0.040934004, -0.048195507, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 342, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.18841094, 0.3114165, 0.0, 0.630666, 0.09980457, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, 0.05280112, 7.0, 6.0, 0.07856816, -0.040934004, -0.048195507, -0.0], "split_indices": [1, 1, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.900597, 27.440401, 9.460193, 14.638466, 12.801936, 9.347348, 5.291117, 7.5139155, 5.2880206], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.008001792, -0.09643665, 0.057434395, 0.005430981, -0.08151299, -0.005936376, 0.010282877], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 343, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.55515105, 0.4292274, 0.0, 0.01291356, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 7.0, 0.057434395, 3.0, -0.08151299, -0.005936376, 0.010282877], "split_indices": [0, 2, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.966091, 21.466042, 8.500051, 13.827861, 7.6381807, 5.695561, 8.1323], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.01201209, -0.040427063, 0.08491915, 0.0060058986, -0.03853462, 0.0029282214, 0.042455368], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 344, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.12889859, 0.11073217, 0.044587508, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 5.0, 0.0060058986, -0.03853462, 0.0029282214, 0.042455368], "split_indices": [0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.653286, 17.290678, 13.362608, 10.035511, 7.2551665, 7.002779, 6.359829], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.04247109, 0.101554275, -0.025356865, 0.01163584, 0.11971148, -0.0, 0.06886404], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 345, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.23774458, 0.004216641, 0.0, 0.0, 0.20933095, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 3.0, -0.025356865, 0.01163584, 4.0, -0.0, 0.06886404], "split_indices": [0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.314741, 20.680857, 8.633883, 6.57952, 14.1013365, 7.021402, 7.079934], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.110673234, 0.022641677, -0.18604314, -0.08248309, -0.0177866], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 346, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.35045812, 0.0, 0.14384675, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [3.0, 0.022641677, 6.0, -0.08248309, -0.0177866], "split_indices": [2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [22.190685, 6.0126767, 16.178009, 8.358008, 7.82], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.039159577, -0.050599482, 0.042507518, -0.031929195, 0.13669051, 0.07690879, 0.00010826608], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 347, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.36738518, 0.0, 0.3048664, 0.0, 0.19278511, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [4.0, -0.050599482, 5.0, -0.031929195, 7.0, 0.07690879, 0.00010826608], "split_indices": [2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.865843, 12.385105, 19.480738, 7.084972, 12.395766, 5.7416687, 6.6540976], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.068358555, 0.12652162, -0.003737953, 0.003584834, 0.05194765, 0.020475239, -0.030797409], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 348, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.123346806, 0.061675057, 0.095758624, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 3.0, 0.003584834, 0.05194765, 0.020475239, -0.030797409], "split_indices": [0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [25.374002, 14.393967, 10.980036, 5.1478467, 9.2461195, 5.94946, 5.0305758], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.005059834, 0.055934504, -0.0691263, -0.006426065, 0.057700872, 0.02146166, -0.063230194, 0.04682798, -0.067994386], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 349, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.12818038, 0.19929218, 0.29177657, 0.57726526, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 7.0, 6.0, 5.0, 0.057700872, 0.02146166, -0.063230194, 0.04682798, -0.067994386], "split_indices": [1, 2, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.287373, 20.098438, 12.188933, 13.943476, 6.154962, 6.009627, 6.179306, 7.929084, 6.014392], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.057400826, -0.093301386, 0.031179024, 0.00630798, -0.20688286, -0.009325526, 0.018504726, -0.0133913215, -0.11896709], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 350, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.29966363, 0.49807635, 0.0, 0.052497726, 0.559369, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, 0.031179024, 4.0, 3.0, -0.009325526, 0.018504726, -0.0133913215, -0.11896709], "split_indices": [1, 2, 0, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.24798, 41.13568, 8.112297, 21.949934, 19.185745, 12.28614, 9.663794, 11.290519, 7.8952265], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.015831662, 0.07596149, -0.044507526, -0.039661378, 0.13187122, 0.07470199, 0.010632483], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 351, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.3891135, 0.36255905, 0.0, 0.0, 0.2367211, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 3.0, -0.044507526, -0.039661378, 4.0, 0.07470199, 0.010632483], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.004814, 28.656584, 9.348231, 5.3995357, 23.257048, 9.318432, 13.938616], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.013452619, 0.05442339, -0.05306624, -0.030719647, 0.076260544, 0.02192153, -0.057285573], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 352, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.28965625, 0.575109, 0.0, 0.40833178, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 7.0, -0.05306624, 4.0, 0.076260544, 0.02192153, -0.057285573], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.479042, 31.03763, 5.4414134, 22.005037, 9.032593, 13.305917, 8.69912], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0040971944, 0.0561375, -0.081087336, 0.04481344, -0.006637419, -0.09255972, 0.02423163], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 353, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.1571918, 0.14757717, 0.61277175, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 8.0, 0.04481344, -0.006637419, -0.09255972, 0.02423163], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.376389, 17.026985, 14.349403, 7.9383745, 9.088611, 5.846101, 8.503303], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.047737006, -0.001643767, 0.095903784, 0.021569451, -0.0402529, -0.003921656, 0.04874642], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 354, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.08391042, 0.16585898, 0.13941112, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 5.0, 4.0, 0.021569451, -0.0402529, -0.003921656, 0.04874642], "split_indices": [0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.249275, 14.388194, 15.861081, 9.062444, 5.32575, 5.750793, 10.110288], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.047688875, 0.037670434, -0.04618497, 0.017200736], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 355, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.17844994, 0.26780745, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [6.0, 7.0, 0.037670434, -0.04618497, 0.017200736], "split_indices": [1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.435766, 21.340378, 7.0953884, 10.849485, 10.490893], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.10969107, 0.007102048, 0.056112986, 0.040411413, -0.033207543], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 356, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.16056243, 0.18891472, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [6.0, 3.0, 0.056112986, 0.040411413, -0.033207543], "split_indices": [2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [21.847412, 10.416734, 11.430678, 5.3387733, 5.0779605], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.0001297763, 0.053894635, -0.06264335, -0.1345762, 0.048523683, -0.053239238, -0.013658575], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 357, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.3663103, 0.0, 0.42851478, 0.046358407, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.053894635, 8.0, 8.0, 0.048523683, -0.053239238, -0.013658575], "split_indices": [2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.633694, 7.2656097, 24.368084, 19.093233, 5.2748494, 11.458555, 7.634679], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.040852994, -0.019344388, 0.05701903, 0.050760888, -0.038133383, 0.028312078, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 358, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.26694888, 0.1648917, 0.0, 0.029423922, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.05701903, 5.0, -0.038133383, 0.028312078, -0.0], "split_indices": [2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [26.410175, 18.9608, 7.4493756, 11.246011, 7.714789, 5.846536, 5.3994746], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.004741962, -0.086371, 0.08830724, -0.097507395, 0.04164373, 0.061399333, -0.01775619, 0.0555229, -0.023885095], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 359, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.26552737, 0.6278044, 0.29784033, 0.0, 0.25306574, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 5.0, -0.097507395, 6.0, 0.061399333, -0.01775619, 0.0555229, -0.023885095], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.979755, 18.17867, 14.8010845, 6.082138, 12.096533, 8.436213, 6.3648725, 5.67834, 6.4181933], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.004708632, -0.04628707, 0.15134966, 0.061426688, -0.09808484, -0.023866, 0.103095524, -0.07573582, -0.012660453], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 360, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.37996715, 0.48043513, 0.6419368, 0.0, 0.22759333, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [8.0, 1.0, 6.0, 0.061426688, 3.0, -0.023866, 0.103095524, -0.07573582, -0.012660453], "split_indices": [0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.46915, 35.222595, 12.246552, 5.1891813, 30.033415, 5.594891, 6.651662, 6.5721273, 23.461288], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0070426543, 0.04944122, -0.056551263, 0.0035977457, 0.04802545, 0.014337235, -0.041046333], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 361, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.33484408, 0.15986365, 0.0, 0.16089082, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, -0.056551263, 6.0, 0.04802545, 0.014337235, -0.041046333], "split_indices": [2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.940098, 33.85762, 6.0824747, 25.307972, 8.549649, 20.293083, 5.014889], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.018847845, 0.052092627, -0.03506252, 0.0029543508, -0.039166365, -0.01758902, 0.024955133], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 362, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.3170128, 0.0, 0.11027684, 0.103016414, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, 0.052092627, 8.0, 6.0, -0.039166365, -0.01758902, 0.024955133], "split_indices": [2, 0, 2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.598083, 8.842468, 25.755617, 18.365768, 7.3898478, 9.837364, 8.528404], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.005022947, 0.06759807, -0.05893453, -0.013034497, 0.12406544, 0.020837214, 0.047460206], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 363, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.41626593, 0.17831832, 0.0, 0.0, 0.00033175945, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [6.0, 2.0, -0.05893453, -0.013034497, 7.0, 0.020837214, 0.047460206], "split_indices": [1, 1, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.987099, 25.292356, 6.6947427, 8.006578, 17.285778, 8.943174, 8.342605], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.012144188, 0.05154155, -0.026201295, -0.09032203, 0.05413972, 0.044452596, -0.017656362], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 364, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.23277827, 0.0, 0.6706035, 0.0, 0.25808725, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.05154155, 5.0, -0.09032203, 5.0, 0.044452596, -0.017656362], "split_indices": [2, 0, 2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.907986, 6.397561, 27.510424, 5.882335, 21.628088, 12.117425, 9.510664], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.006008357, -0.09301727, 0.09098091, -0.0, -0.056868028, 0.14495702, -0.0015066959, 0.010626255, 0.06131973], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 365, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.26697874, 0.11947747, 0.10590555, 0.0, 0.0, 0.046676695, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [5.0, 7.0, 8.0, -0.0, -0.056868028, 7.0, -0.0015066959, 0.010626255, 0.06131973], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.747023, 13.150324, 16.596699, 7.4181023, 5.7322216, 10.964062, 5.6326365, 5.01005, 5.954012], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.043890737, -0.0874994, 0.04310065, 0.06776909, -0.03442984], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 366, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.55383325, 0.0, 0.5652806, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, -0.0874994, 7.0, 0.06776909, -0.03442984], "split_indices": [1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [22.958485, 5.6532655, 17.30522, 8.113262, 9.1919565], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.1343852, -0.0, -0.14962879, 0.03974286, -0.04394906], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 367, "left_children": [1, 3, -1, -1, -1], "loss_changes": [1.3839855, 0.45283288, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 5.0, -0.14962879, 0.03974286, -0.04394906], "split_indices": [1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.949495, 21.284422, 6.6650734, 10.985971, 10.29845], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.032039024, -0.042294502, 0.07108843, 0.038139798, -0.046698697, 0.023803663, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 368, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.453847, 0.20857453, 0.0, 0.025113575, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.07108843, 2.0, -0.046698697, 0.023803663, -0.0], "split_indices": [2, 2, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [26.85854, 19.933487, 6.9250526, 11.532608, 8.400879, 5.8846936, 5.6479144], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.03535332, 0.08836064, -0.037470035, 0.07219413, 0.00040619052, -0.056686837, 0.03791849], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 369, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.2864199, 0.3205467, 0.0, 0.0, 0.4407019, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 3.0, -0.037470035, 0.07219413, 5.0, -0.056686837, 0.03791849], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.8488, 24.7386, 7.1102004, 8.005203, 16.733397, 6.16853, 10.564866], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.03521926, 0.04941546, -0.068036355, -0.024392588, 0.053300027, 0.0047444105, -0.10874644, -0.003522107, 0.009265046, -0.047974363, -0.0032172252], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 370, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.15343843, 0.26949555, 0.12861818, 0.0, 0.0, 0.008450261, 0.1136252, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 4.0, -0.024392588, 0.053300027, 7.0, 8.0, -0.003522107, 0.009265046, -0.047974363, -0.0032172252], "split_indices": [2, 2, 0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.363132, 13.637999, 38.725136, 6.5490146, 7.088984, 13.326793, 25.398342, 6.164361, 7.1624317, 15.602292, 9.796051], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.027460016, 0.07047283, -0.03370726, 0.14414334, -0.0019924962, -0.012180751, 0.06996516, 0.04888184, -0.050792757], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 371, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.25238562, 0.18950464, 0.0, 0.30771253, 0.49259794, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, -0.03370726, 2.0, 2.0, -0.012180751, 0.06996516, 0.04888184, -0.050792757], "split_indices": [1, 2, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.346554, 31.78083, 8.565722, 15.953174, 15.827658, 5.0014005, 10.951773, 7.6828256, 8.144832], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.007489586, 0.03885106, -0.04202352, -0.008560062, 0.06620278, 0.07227134, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 372, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.16299254, 0.06320081, 0.0, 0.0, 0.27539837, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 3.0, -0.04202352, -0.008560062, 3.0, 0.07227134, -0.0], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.230534, 30.222054, 5.00848, 7.6574993, 22.564554, 5.5249863, 17.039568], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.059517052, -0.15622787, 0.040587552, -0.083479285, -0.07252799, 0.019632021, -0.07324881], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 373, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.62627465, 0.17146766, 0.0, 0.0, 0.38917285, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [5.0, 3.0, 0.040587552, -0.083479285, 8.0, 0.019632021, -0.07324881], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.008522, 21.196907, 9.811613, 7.1918015, 14.005107, 7.7840295, 6.221077], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.08412039, -0.0, 0.14815867, 0.030162383, -0.04107929, 0.0054534487, 0.06672473], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 374, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.15348518, 0.21717688, 0.13584894, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 5.0, 4.0, 0.030162383, -0.04107929, 0.0054534487, 0.06672473], "split_indices": [0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.002495, 14.016189, 15.986305, 8.61959, 5.396599, 6.7545443, 9.231761], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.034613512, -0.018912353, 0.091926455, 0.15683424, -0.011833691, 0.0061913133, 0.076795176], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 375, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.18095471, 0.0, 0.19229017, 0.16146639, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [4.0, -0.018912353, 8.0, 3.0, -0.011833691, 0.0061913133, 0.076795176], "split_indices": [2, 0, 2, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.779852, 10.331465, 19.448387, 13.396752, 6.0516343, 6.577845, 6.818907], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.04786315, 0.05108178, -0.03085564, -0.049326684, 0.020938003], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 376, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.2433344, 0.0, 0.21595049, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [3.0, 0.05108178, 6.0, -0.049326684, 0.020938003], "split_indices": [0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [22.260754, 8.754542, 13.50621, 5.9586678, 7.547543], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.062288206, 0.08919277, -0.236636, 0.05362904, -0.000946528, -0.01538521, -0.10715543], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 377, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.7992744, 0.14953493, 0.2540846, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 5.0, 0.05362904, -0.000946528, -0.01538521, -0.10715543], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.90621, 14.88678, 13.019431, 7.631694, 7.255086, 6.0193024, 7.0001283], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.046991397, -0.0, 0.045318775, 0.03358823, -0.03390789], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 378, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.14526206, 0.25306627, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 4.0, 0.045318775, 0.03358823, -0.03390789], "split_indices": [0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [25.965584, 17.990644, 7.9749384, 8.64874, 9.341905], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.064706, -0.020991836, 0.09947352, 0.031532716, 0.09648879, 0.020707466, -0.003937777], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 379, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.1707428, 0.0, 0.38173756, 0.04522048, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.020991836, 8.0, 6.0, 0.09648879, 0.020707466, -0.003937777], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.347897, 6.0445027, 27.303392, 22.12813, 5.1752634, 12.949915, 9.178215], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.011751003, -0.04300953, 0.02404712, -0.18710697, 0.06015139, -0.023086036, -0.073426075, 0.079274446, -0.038138855], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 380, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.13894732, 0.5832137, 0.0, 0.052781463, 0.8988404, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 2.0, 0.02404712, 3.0, 6.0, -0.023086036, -0.073426075, 0.079274446, -0.038138855], "split_indices": [0, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.45766, 36.62227, 10.83539, 15.3444395, 21.27783, 6.669571, 8.67487, 10.240657, 11.037173], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0055957315, 0.11044295, -0.06498143, -0.01361752, 0.09469777, 0.03786225, -0.1336984, -0.07367957, -0.012607138], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 381, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.27207026, 0.45826772, 0.37317795, 0.0, 0.0, 0.0, 0.17406899, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [2.0, 1.0, 2.0, -0.01361752, 0.09469777, 0.03786225, 5.0, -0.07367957, -0.012607138], "split_indices": [1, 1, 2, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.100014, 12.036746, 26.063267, 7.0072117, 5.029534, 6.2849603, 19.778305, 7.659322, 12.118984], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.007218554, -0.046301745, 0.05869682, -0.01330882, 0.06475512, -0.0397229, 0.00844639], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 382, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.31329358, 0.0, 0.36042783, 0.11759714, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.046301745, 8.0, 5.0, 0.06475512, -0.0397229, 0.00844639], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.731094, 7.9235077, 28.807587, 19.947092, 8.860495, 5.207441, 14.739651], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0160882, -0.038622078, 0.021263104, 0.05218624, -0.05213394, -0.052423608, 0.027766857], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 383, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.14538237, 0.0, 0.28722623, 0.0, 0.31454676, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.038622078, 3.0, 0.05218624, 8.0, -0.052423608, 0.027766857], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.346624, 7.61381, 22.732815, 7.377498, 15.355316, 8.573491, 6.7818246], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.06404067, 0.04941047, -0.18326096, -0.007989968, 0.098660186, -0.08856347, -0.0, 0.050189886, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 384, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.46092778, 0.07338228, 0.31839222, 0.0, 0.061191045, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 7.0, -0.007989968, 3.0, -0.08856347, -0.0, 0.050189886, -0.0], "split_indices": [2, 1, 1, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.418903, 15.969217, 15.449686, 5.659483, 10.309734, 8.992535, 6.457151, 5.119444, 5.1902905], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.019446174, 0.017594585, -0.061049234, -0.067298055, 0.029839668, 0.047704693, -0.025362594], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 385, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.102898754, 0.0, 0.34107146, 0.0, 0.22954705, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.017594585, 4.0, -0.067298055, 7.0, 0.047704693, -0.025362594], "split_indices": [1, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.495163, 9.273713, 20.22145, 7.099549, 13.1219015, 6.389744, 6.732158], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.017666452, 0.10945777, -0.06120977, -0.011865474, 0.0763118], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 386, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.5702414, 0.33046508, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 4.0, -0.06120977, -0.011865474, 0.0763118], "split_indices": [1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [21.727459, 12.843045, 8.884414, 6.350315, 6.4927297], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.012933028, -0.038182005, 0.04110818, -0.008863351, 0.07655677, 0.0541582, -0.0037742981], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 387, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.19838108, 0.0, 0.05815908, 0.0, 0.15653169, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [4.0, -0.038182005, 3.0, -0.008863351, 7.0, 0.0541582, -0.0037742981], "split_indices": [0, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.864042, 9.422741, 19.441301, 5.6085176, 13.832784, 6.4297276, 7.4030566], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.19127765, -0.095610306, -0.13563798, -0.0036309792, -0.06223891], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 388, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.1149081, 0.0, 0.173161, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [2.0, -0.095610306, 4.0, -0.0036309792, -0.06223891], "split_indices": [1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.713102, 6.4397845, 21.273317, 8.762732, 12.510586], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.035153206, 0.011638218, -0.051591683, -0.059165798, 0.056787107, -0.044563778, 0.0137979435], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 389, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.2352581, 0.34717453, 0.0, 0.1930219, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 5.0, -0.051591683, 6.0, 0.056787107, -0.044563778, 0.0137979435], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.930744, 24.675793, 8.254951, 17.718191, 6.957602, 9.868656, 7.8495345], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.05663433, -0.030826434, 0.099099725, -0.038590405, 0.15425825, 0.086317316, 0.019486533], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 390, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.3524868, 0.0, 0.53718376, 0.0, 0.34888482, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, -0.030826434, 3.0, -0.038590405, 3.0, 0.086317316, 0.019486533], "split_indices": [1, 0, 2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.62691, 9.646241, 39.98067, 7.1822186, 32.798454, 11.808558, 20.989895], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.026745131, -0.03945509, 0.09899467, 0.11521555, -0.0, 0.057793193, -0.044102546], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 391, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.47855073, 0.0, 0.8008723, 0.0, 0.6761979, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.03945509, 4.0, 0.11521555, 5.0, 0.057793193, -0.044102546], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.997776, 11.845621, 28.152155, 6.3667316, 21.785423, 9.461755, 12.3236685], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0064142374, -0.10953057, 0.039494734, -0.09862203, 0.040131457, 0.09989139, -0.048811495, 0.0127455015, -0.034878295], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 392, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.18024446, 0.6996505, 0.68617207, 0.0, 0.0, 0.0, 0.13324663, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 5.0, 3.0, -0.09862203, 0.040131457, 0.09989139, 6.0, 0.0127455015, -0.034878295], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.681507, 10.928149, 23.753359, 5.7801867, 5.1479626, 5.1152244, 18.638134, 7.4464684, 11.191666], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.035589974, 0.21204075, -0.07559451, 0.08808162, 0.026608214, -0.067722395, 0.01573287], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 393, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.61969644, 0.058829546, 0.39133564, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [3.0, 6.0, 5.0, 0.08808162, 0.026608214, -0.067722395, 0.01573287], "split_indices": [1, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.074675, 11.235745, 17.83893, 5.4444695, 5.7912755, 8.218822, 9.620109], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0032465167, 0.05569608, -0.058336496, 0.017192556, -0.13003962, 0.025741087, -0.07800395], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 394, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.335871, 0.0, 0.2328079, 0.0, 0.51872194, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.05569608, 2.0, 0.017192556, 6.0, 0.025741087, -0.07800395], "split_indices": [2, 0, 1, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.457815, 6.157819, 25.299995, 9.22605, 16.073946, 5.879869, 10.194077], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.009671837, 0.057824567, -0.060348783, 0.0038348543, -0.11653998, -0.08770674, 0.016206453], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 395, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.39134672, 0.0, 0.0983488, 0.0, 0.4226638, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, 0.057824567, 6.0, 0.0038348543, 7.0, -0.08770674, 0.016206453], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.58807, 7.5425763, 20.045494, 8.272233, 11.773261, 5.7344136, 6.0388474], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.07185582, 0.0008923681, 0.070923105, -0.050706644, 0.053890508], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 396, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.254855, 0.5814302, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [8.0, 6.0, 0.070923105, -0.050706644, 0.053890508], "split_indices": [1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [22.874943, 17.109617, 5.765326, 8.512485, 8.597133], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.12864012, 0.041690834, 0.10137881, -0.042082332, 0.11221886, 0.06524844, 0.007897802], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 397, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.48156536, 0.29777917, 0.0, 0.0, 0.123134404, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 3.0, 0.10137881, -0.042082332, 6.0, 0.06524844, 0.007897802], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.323507, 21.195034, 7.128474, 5.2480497, 15.946984, 5.9535346, 9.993449], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.069296725, 0.006041516, -0.059512153, -0.03553925, 0.07638911, 0.0036723523, 0.032891266], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 398, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.27422765, 0.1533994, 0.0, 0.0, 0.011976175, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 2.0, -0.059512153, -0.03553925, 5.0, 0.0036723523, 0.032891266], "split_indices": [2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [25.098764, 15.961187, 9.137578, 5.0023003, 10.958887, 5.2348504, 5.7240367], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.022304831, -0.0560205, 0.04161911, 0.10203421, -0.006086384, 0.0060914573, 0.047957893], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 399, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.37036306, 0.0, 0.10085033, 0.041328147, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [4.0, -0.0560205, 7.0, 5.0, -0.006086384, 0.0060914573, 0.047957893], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.115685, 8.89036, 23.225325, 12.24377, 10.981555, 6.3497033, 5.894066], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.08247947, -0.13505352, 0.04763161, -0.20306768, 0.020387702, -0.124979146, -0.03428995], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 400, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.61976826, 0.57640094, 0.0, 0.47564542, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, 0.04763161, 1.0, 0.020387702, -0.124979146, -0.03428995], "split_indices": [1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.3603, 38.766846, 7.5934525, 29.407066, 9.359778, 7.2199554, 22.187113], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.059179027, -0.09090762, 0.11435091, -0.0053700325, -0.039867368, -0.0022815517, 0.16790485, 0.12127687, 0.020473178], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 401, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.35614884, 0.01692412, 0.22143751, 0.0, 0.0, 0.0, 0.45116454, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 4.0, 3.0, -0.0053700325, -0.039867368, -0.0022815517, 4.0, 0.12127687, 0.020473178], "split_indices": [0, 2, 2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.47172, 10.21719, 30.254532, 5.1207275, 5.096462, 8.923697, 21.330833, 5.1225963, 16.208239], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.002845363, -0.04466717, 0.041274466, 0.0060647833, -0.04737727, 0.022794351, -0.017108977], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 402, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.24760492, 0.1713839, 0.0, 0.09010164, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.041274466, 7.0, -0.04737727, 0.022794351, -0.017108977], "split_indices": [0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.266876, 26.018253, 9.248622, 18.013056, 8.005198, 9.199548, 8.813507], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.1241475, 0.19034792, -0.025854351, 0.032934938, 0.06729246], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 403, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.43164298, 0.0043109655, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [8.0, 4.0, -0.025854351, 0.032934938, 0.06729246], "split_indices": [2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.073997, 21.78703, 6.2869687, 8.588833, 13.198195], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.024814615, -0.11151394, 0.16954307, -0.00056530815, -0.08579769, -0.0, 0.079800405], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 404, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.6611112, 0.29310805, 0.2900607, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 5.0, -0.00056530815, -0.08579769, -0.0, 0.079800405], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.254528, 15.856691, 15.397838, 10.71076, 5.1459303, 5.6368217, 9.761016], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.053146694, -0.15190144, 0.0057986183, -0.090636045, 0.0052072364, 0.037909858, -0.040839233, 0.0053835358, -0.040159952], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 405, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.19137834, 0.33793557, 0.121959284, 0.0, 0.0, 0.0, 0.09076592, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [6.0, 4.0, 2.0, -0.090636045, 0.0052072364, 0.037909858, 8.0, 0.0053835358, -0.040159952], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.215317, 10.964802, 18.250515, 5.706281, 5.258521, 5.2117715, 13.038744, 7.8257575, 5.2129865], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04964891, -0.0, 0.05890261, 0.051469903, -0.0791774, -0.053834695, 0.010996172], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 406, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.20822766, 0.27595723, 0.0, 0.0, 0.19184175, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 3.0, 0.05890261, 0.051469903, 6.0, -0.053834695, 0.010996172], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [25.027008, 18.961294, 6.065714, 5.2397447, 13.72155, 7.523117, 6.1984324], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.022073388, -0.039814286, 0.043746155, 0.05970688, -0.059035987], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 407, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.24182104, 0.78379875, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 3.0, 0.043746155, 0.05970688, -0.059035987], "split_indices": [0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.433567, 18.867718, 9.565849, 7.123777, 11.743941], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.024658302, -0.095020175, 0.02358351, -0.05675969, 0.0095163565], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 408, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.1949623, 0.21560948, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [5.0, 7.0, 0.02358351, -0.05675969, 0.0095163565], "split_indices": [1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [24.577326, 15.241155, 9.336171, 8.913751, 6.327404], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.008267569, -0.029003596, 0.034354698, -0.008873788, 0.033592902, -0.06236828, 0.029912097], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 409, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.0872814, 0.0, 0.104875356, 0.42074758, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.029003596, 6.0, 5.0, 0.033592902, -0.06236828, 0.029912097], "split_indices": [1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.94405, 5.057773, 26.886276, 17.010365, 9.875911, 6.006754, 11.003612], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.02544022, 0.015178867, -0.046563786, -0.024191594, 0.048774198, -0.05778762, 0.00512032], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 410, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.28406712, 0.24412137, 0.0, 0.23709022, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 4.0, -0.046563786, 3.0, 0.048774198, -0.05778762, 0.00512032], "split_indices": [1, 1, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.78653, 38.003723, 11.782807, 30.117039, 7.8866854, 5.680886, 24.436153], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.082344905, 0.018120395, 0.14973092, 0.02048184, -0.0104486225, 0.0356061, 0.10527019, 0.033321667, -0.011705771], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 411, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.16058716, 0.06733785, 0.3944376, 0.0, 0.0, 0.08933116, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [5.0, 3.0, 7.0, 0.02048184, -0.0104486225, 7.0, 0.10527019, 0.033321667, -0.011705771], "split_indices": [2, 2, 1, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.610985, 22.202774, 18.408209, 12.223814, 9.97896, 12.813257, 5.594952, 6.7941694, 6.019088], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.048964344, -0.113934234, 0.016371045, -0.009389491, -0.055387385, 0.06907396, -0.027308727], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 412, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.16676685, 0.08489786, 0.45593214, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 7.0, -0.009389491, -0.055387385, 0.06907396, -0.027308727], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.549213, 18.21893, 17.330282, 9.717131, 8.5018, 5.722277, 11.608006], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.08676298, 0.14113931, -0.025213318, 0.0019022069, 0.20521204, 0.10248237, 0.027635073], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 413, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.31616747, 0.19522059, 0.0, 0.0, 0.18043059, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [6.0, 2.0, -0.025213318, 0.0019022069, 4.0, 0.10248237, 0.027635073], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.148996, 24.20311, 6.945886, 8.654703, 15.548407, 5.7314143, 9.816993], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.069517985, -0.016644467, 0.1457213, 0.016299296, -0.042284355, 0.01659429, 0.054306712], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 414, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.2316531, 0.15522298, 0.024443895, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 5.0, 4.0, 0.016299296, -0.042284355, 0.01659429, 0.054306712], "split_indices": [0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.946154, 14.744251, 17.201902, 9.231584, 5.5126686, 6.3469343, 10.854968], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.013964911, -0.026057687, 0.03390768, -0.030756842, 0.009358671, -0.018620424, 0.025800774], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 415, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.13024244, 0.0682292, 0.0, 0.0, 0.08792212, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 2.0, 0.03390768, -0.030756842, 4.0, -0.018620424, 0.025800774], "split_indices": [2, 1, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.914364, 20.399862, 8.514503, 6.7516303, 13.648232, 6.4876437, 7.1605887], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.13171937, -0.076623976, 0.0054169837, 0.07742571], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 416, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.865151, 0.21601379, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 5.0, -0.076623976, 0.0054169837, 0.07742571], "split_indices": [2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [23.950886, 16.286476, 7.6644096, 9.5925255, 6.6939507], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.0027659182, 0.077525295, -0.039793465, -0.014305657, 0.068105325, -0.014187112, 0.0011465277], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 417, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.3092067, 0.3057057, 0.0, 0.012429045, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 4.0, -0.039793465, 4.0, 0.068105325, -0.014187112, 0.0011465277], "split_indices": [2, 2, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.072952, 19.482676, 9.590278, 12.214726, 7.267948, 5.7179866, 6.4967403], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.03913416, 0.121468544, -0.17351884, -0.012137073, 0.08976655, -0.0, -0.080673195], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 418, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.59538174, 0.39090747, 0.23150027, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [3.0, 5.0, 4.0, -0.012137073, 0.08976655, -0.0, -0.080673195], "split_indices": [1, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [25.368515, 11.261687, 14.106828, 6.00302, 5.258668, 5.6154633, 8.4913645], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.03288108, -0.006468841, 0.059815995, -0.066396, 0.047638662, 0.02991112, -0.048717782], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 419, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.24624535, 0.2819893, 0.0, 0.37242645, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, 0.059815995, 4.0, 0.047638662, 0.02991112, -0.048717782], "split_indices": [1, 2, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.44207, 27.341158, 6.1009116, 20.948336, 6.3928223, 7.27695, 13.671385], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.008969415, -0.045142055, 0.08180731, 0.020069983, -0.102879785, -0.0, 0.11099495, 0.026498014, -0.061489962, 0.04576854, 0.007857182], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 420, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.19562678, 0.1868482, 0.05895862, 0.0, 0.40014905, 0.0, 0.036175907, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 1.0, 3.0, 0.020069983, 3.0, -0.0, 7.0, 0.026498014, -0.061489962, 0.04576854, 0.007857182], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.76702, 26.263285, 20.503736, 8.288506, 17.97478, 5.0367737, 15.466963, 5.9624515, 12.012327, 9.0519705, 6.414993], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.00708525, -0.046276554, 0.082711406, -0.13857847, 0.021623565, 0.08003621, -0.02244408, -0.0065313787, -0.08195422, 0.023251565, -0.04492344], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 421, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.17835295, 0.28638947, 0.39145446, 0.19552198, 0.0, 0.0, 0.17720267, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 8.0, 3.0, 0.021623565, 0.08003621, 5.0, -0.0065313787, -0.08195422, 0.023251565, -0.04492344], "split_indices": [0, 2, 0, 2, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [41.34416, 23.692514, 17.651642, 13.690057, 10.002459, 6.204495, 11.447146, 8.349272, 5.340785, 6.1915307, 5.2556157], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.07580861, 0.14980121, -0.0021511116, -0.014534509, 0.0774145, -0.048496936, 0.046736028], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 422, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.21115944, 0.4221275, 0.45099267, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 8.0, -0.014534509, 0.0774145, -0.048496936, 0.046736028], "split_indices": [0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.99049, 17.111208, 15.879284, 5.9120946, 11.199113, 8.20374, 7.6755433], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.019952033, -0.08092042, 0.14790091, 0.003340863, -0.0675909, 0.015333811, 0.06220202], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 423, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.41244203, 0.25345933, 0.045482606, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 7.0, 7.0, 0.003340863, -0.0675909, 0.015333811, 0.06220202], "split_indices": [0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.509838, 16.269371, 13.240467, 10.075571, 6.1938, 6.38527, 6.8551965], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0046338495, -0.041376665, 0.030667067, -0.060603943, 0.056746442, 0.027243547, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 424, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.124873094, 0.42676985, 0.0, 0.0, 0.027763925, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 5.0, 0.030667067, -0.060603943, 8.0, 0.027243547, -0.0], "split_indices": [0, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.277205, 24.426714, 6.85049, 9.247716, 15.178999, 8.684119, 6.4948792], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.11285497, 0.064322025, 0.054621305, 0.044347085, -0.018185114], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 425, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.13149804, 0.0, 0.23517257, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [2.0, 0.064322025, 6.0, 0.044347085, -0.018185114], "split_indices": [1, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.765593, 8.515915, 19.249678, 10.965786, 8.283891], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.0627774, -0.16048233, 0.056018602, 0.01616153, -0.102990985], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 426, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.62610704, 0.77147776, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.056018602, 0.01616153, -0.102990985], "split_indices": [1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [23.561722, 17.475876, 6.085846, 8.117088, 9.358787], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.008033825, 0.08455908, -0.11400456, -0.0061496915, 0.055187464, 0.003532131, -0.09116861], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 427, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.3013941, 0.18489191, 0.38280976, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 7.0, -0.0061496915, 0.055187464, 0.003532131, -0.09116861], "split_indices": [0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.433893, 14.779, 13.654892, 7.081775, 7.697225, 8.461567, 5.1933255], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.013473083, -0.07244109, 0.085560486, -0.084532425, 0.037539948, 0.06015582, -0.030222619], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 428, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.16481452, 0.5264181, 0.34934166, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [3.0, 6.0, 7.0, -0.084532425, 0.037539948, 0.06015582, -0.030222619], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [24.399015, 10.49678, 13.902234, 5.1355433, 5.361237, 8.882002, 5.0202327], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.058106482, -0.0454407, 0.12037843, -0.040778086, 0.008633723, 0.054840554, 0.008104389], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 429, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.2266627, 0.09903757, 0.099806786, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 5.0, 5.0, -0.040778086, 0.008633723, 0.054840554, 0.008104389], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.14144, 11.568878, 20.572561, 5.4456563, 6.1232224, 11.1132, 9.459361], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.09697028, 0.19039324, 0.0020672078, 0.019765098, 0.08548409, 0.05466131, -0.1218967, -0.055881944, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 430, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.3905108, 0.21421146, 0.5751234, 0.0, 0.0, 0.0, 0.11681546, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 5.0, 4.0, 0.019765098, 0.08548409, 0.05466131, 8.0, -0.055881944, -0.0], "split_indices": [1, 0, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.260395, 21.770016, 23.49038, 10.585403, 11.184612, 9.712952, 13.777429, 8.694895, 5.082534], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.007539879, 0.11731561, -0.08548342, -0.0, 0.05085009, -0.05743635, 0.015368423], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 431, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.44296476, 0.11792967, 0.35509765, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 7.0, -0.0, 0.05085009, -0.05743635, 0.015368423], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [41.08437, 19.218588, 21.86578, 6.428391, 12.790197, 12.504237, 9.361543], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.01051539, 0.07150728, -0.090271786, 0.033302113, -0.0, -0.06350226, 0.014196931], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 432, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.2253402, 0.06745324, 0.2496753, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 6.0, 0.033302113, -0.0, -0.06350226, 0.014196931], "split_indices": [2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.912533, 22.549366, 12.363168, 14.245857, 8.30351, 6.668136, 5.695031], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.02503306, 0.042841475, -0.04225499, -0.013486925, 0.04989064], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 433, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.26251376, 0.23648253, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 3.0, -0.04225499, -0.013486925, 0.04989064], "split_indices": [0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [30.37999, 19.007374, 11.372616, 11.006263, 8.001111], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.028005108, -0.1001153, 0.038332053, -0.0, -0.082688235, -0.004487064, 0.027674424], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 434, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.16234176, 0.2995506, 0.056763887, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 6.0, -0.0, -0.082688235, -0.004487064, 0.027674424], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.84344, 15.256977, 15.586462, 10.169146, 5.087832, 7.269433, 8.317029], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.11301422, 0.008636747, -0.17164408, -0.018639198, -0.20997173, -0.10529555, -0.021763276], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 435, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.2682047, 0.0, 0.051371872, 0.0, 0.22430009, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, 0.008636747, 5.0, -0.018639198, 6.0, -0.10529555, -0.021763276], "split_indices": [2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.947128, 8.117449, 20.82968, 6.7559786, 14.0737, 5.820106, 8.253594], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.008873689, -0.1028918, 0.07515363, -0.05520334, -0.0, -0.0, 0.047514737], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 436, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.20041502, 0.1055623, 0.07890146, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 6.0, -0.05520334, -0.0, -0.0, 0.047514737], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [23.038776, 11.328693, 11.710083, 5.972301, 5.356392, 6.7029524, 5.00713], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.055208888, 0.04201311, 0.03377174, -0.1634266, -0.09282095, -0.004581265], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 437, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.2455669, 0.43105316, 0.0, 0.0, 0.27458465, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, 0.04201311, 0.03377174, 6.0, -0.09282095, -0.004581265], "split_indices": [2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.479626, 21.447123, 8.032504, 8.031199, 13.415922, 5.8694615, 7.546461], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.009363671, 0.08775891, -0.026781302, -0.039945066, 0.06521231], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 438, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.21872512, 0.50339353, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [6.0, 3.0, -0.026781302, -0.039945066, 0.06521231], "split_indices": [0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [26.292894, 15.275763, 11.017133, 5.353234, 9.922529], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.039821386, 0.031325683, -0.091807336, 0.08524452, -0.042911574, -0.0389449, 3.9721683e-05], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 439, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.65113235, 0.5279795, 0.0, 0.0, 0.09084022, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 3.0, -0.091807336, 0.08524452, 6.0, -0.0389449, 3.9721683e-05], "split_indices": [0, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.422918, 25.195646, 6.2272725, 5.382941, 19.812704, 6.664516, 13.148189], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.06599722, 0.027659431, -0.16862516, -0.045344066, 0.07218247, -0.031358432, -0.14251846, -0.0, -0.039447, -0.052738618, 0.0584732], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 440, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.47063822, 0.41800493, 0.9053888, 0.07947583, 0.0, 0.595704, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [3.0, 8.0, 8.0, 6.0, 0.07218247, 6.0, -0.14251846, -0.0, -0.039447, -0.052738618, 0.0584732], "split_indices": [1, 2, 2, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.01284, 23.930414, 22.082426, 18.05741, 5.8730054, 16.237555, 5.8448715, 12.194361, 5.8630476, 10.316882, 5.920673], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0041500893, -0.039839223, 0.031158423, 0.09622939, -0.030311555, 0.055523366, -0.000605433, -0.04429874, 0.018622674], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 441, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.146763, 0.0, 0.15320046, 0.1799986, 0.21768107, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.039839223, 4.0, 7.0, 7.0, 0.055523366, -0.000605433, -0.04429874, 0.018622674], "split_indices": [0, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.308613, 5.384861, 34.92375, 17.48053, 17.443222, 9.249734, 8.230797, 7.896774, 9.546448], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.031210111, -0.08135028, 0.029943861, -0.1482861, -0.008380555, 0.0037922987, -0.095942855, 0.00039440114, -0.007061571], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 442, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.2501965, 0.119636774, 0.0, 0.41585118, 0.0044797724, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, 0.029943861, 3.0, 3.0, 0.0037922987, -0.095942855, 0.00039440114, -0.007061571], "split_indices": [1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.129524, 26.956202, 9.173321, 12.806285, 14.149918, 6.784348, 6.0219364, 5.1634316, 8.986486], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.059457205, -0.10067471, 0.011157835, -0.009724267, -0.09329769, -0.023169633, 0.03127715], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 443, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.14295131, 0.43116492, 0.0, 0.14678264, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.011157835, 3.0, -0.09329769, -0.023169633, 0.03127715], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.350822, 23.37521, 8.975612, 17.380735, 5.994476, 11.669177, 5.7115583], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.08235432, -0.029777057, -0.049489655, -0.11367862, 0.03714759, 0.0053891223, -0.058336914], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 444, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.112720475, 0.2869142, 0.0, 0.17730185, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 5.0, -0.049489655, 4.0, 0.03714759, 0.0053891223, -0.058336914], "split_indices": [0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.566217, 20.24951, 10.316708, 13.685231, 6.564278, 5.029073, 8.656158], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.01859755, 0.017371673, -0.056290485, 0.015479183, -0.13156581, -0.057048455, -0.012011594], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 445, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.085463025, 0.0, 0.181417, 0.0, 0.038621113, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [4.0, 0.017371673, 5.0, 0.015479183, 7.0, -0.057048455, -0.012011594], "split_indices": [0, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.562984, 8.045086, 19.517899, 7.604666, 11.913232, 5.9401336, 5.9730988], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.05925593, 0.031941682, 0.019727375, -0.0149649745, 0.03267252], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 446, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.031407386, 0.0, 0.109624796, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [5.0, 0.031941682, 8.0, -0.0149649745, 0.03267252], "split_indices": [1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [23.714918, 8.882053, 14.832865, 7.9482007, 6.884664], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.052596506, 0.13255359, -0.08336429, 0.09493828, 0.055636253, -0.026061995, 0.056740932], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 447, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.8387953, 0.31015262, 0.0, 0.0, 0.40314063, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 1.0, -0.08336429, 0.09493828, 6.0, -0.026061995, 0.056740932], "split_indices": [0, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.899689, 24.800781, 5.0989084, 6.0119576, 18.788824, 8.894389, 9.894434], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.033613447, 0.1678867, -0.05444552, 0.0030196542, 0.08240554], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 448, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.7381507, 0.2370961, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 3.0, -0.05444552, 0.0030196542, 0.08240554], "split_indices": [2, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [23.549623, 14.920212, 8.629412, 6.8412127, 8.0789995], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.03733613, -0.067082785, 0.018369023, 0.07804037, -0.033655822, -0.036054444, 0.0664458], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 449, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.3591706, 0.0, 0.20266579, 0.562759, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.067082785, 8.0, 4.0, -0.033655822, -0.036054444, 0.0664458], "split_indices": [2, 0, 2, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.240458, 6.911158, 24.3293, 17.499296, 6.8300037, 7.1223097, 10.376986], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.050278816, -0.042455852, 0.07852728, 0.15409708, -0.0, 0.10348886, 0.010305559, 0.03421898, -0.06300746], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 450, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.27836916, 0.0, 0.26785502, 0.47574157, 0.57775396, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.042455852, 5.0, 5.0, 8.0, 0.10348886, 0.010305559, 0.03421898, -0.06300746], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.490005, 5.384062, 44.10594, 21.993532, 22.11241, 7.4318123, 14.56172, 14.622344, 7.4900665], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.015095092, -0.16154866, 0.08003689, 0.02756367, -0.09275003, 0.18370499, -0.021914518, 0.08636692, 0.020011507], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 451, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.57293546, 0.6615224, 0.40959892, 0.0, 0.0, 0.12991971, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 4.0, 7.0, 0.02756367, -0.09275003, 6.0, -0.021914518, 0.08636692, 0.020011507], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.609375, 15.34268, 23.266695, 5.5370164, 9.805664, 14.142633, 9.124061, 6.256301, 7.886333], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.03412449, -0.06439069, 0.03219302, 0.040058345, -0.032117795, -0.05081332, 0.04702274], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 452, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.47116128, 0.0, 0.19502273, 0.0, 0.47252142, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.06439069, 5.0, 0.040058345, 8.0, -0.05081332, 0.04702274], "split_indices": [1, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.25716, 9.545759, 26.711403, 10.556362, 16.15504, 9.721961, 6.43308], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.037599005, 0.019539073, -0.10994775, -0.11573939, 0.0544374, 0.040720817, -0.0097794775], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 453, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.24903402, 0.0, 0.9467492, 0.0, 0.10660827, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [5.0, 0.019539073, 4.0, -0.11573939, 8.0, 0.040720817, -0.0097794775], "split_indices": [2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.94036, 12.255765, 18.684593, 6.701091, 11.983503, 6.5007057, 5.482798], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0064213346, -0.07784748, 0.07836041, 0.07614892, -0.015199587, -0.021956638, 0.024217771], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 454, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.7283076, 0.0, 0.43085945, 0.0, 0.09742859, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.07784748, 6.0, 0.07614892, 8.0, -0.021956638, 0.024217771], "split_indices": [2, 0, 2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.152727, 7.596441, 23.556288, 7.968115, 15.588172, 10.512575, 5.0755973], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.009987844, 0.041006714, -0.060592018, -0.003143615, -0.033482034], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 455, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.27056977, 0.0, 0.033857316, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [5.0, 0.041006714, 8.0, -0.003143615, -0.033482034], "split_indices": [0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.479319, 10.002873, 17.476444, 10.413035, 7.0634093], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.02409621, -0.05164052, 0.10694358, 0.07375897, -0.0046306807], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 456, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.40327162, 0.0, 0.33119977, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, -0.05164052, 7.0, 0.07375897, -0.0046306807], "split_indices": [1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [23.074314, 6.1715884, 16.902727, 7.8265095, 9.076217], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.069682196, 0.023933072, -0.064112544, -0.08123553, 0.07302305, 0.017364085, -0.08221999], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 457, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.44064587, 0.479228, 0.0, 0.39758396, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [5.0, 3.0, -0.064112544, 1.0, 0.07302305, 0.017364085, -0.08221999], "split_indices": [1, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.8946, 18.202631, 11.69197, 12.423704, 5.7789264, 7.322337, 5.1013665], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.08218445, 0.032687522, -0.1429377, -0.0059018717, -0.084212445, 0.0031481471, -0.010800697], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 458, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.32632655, 0.0, 0.36451757, 0.008786732, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.032687522, 4.0, 2.0, -0.084212445, 0.0031481471, -0.010800697], "split_indices": [2, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [25.467424, 5.553972, 19.913452, 10.870048, 9.043405, 5.307966, 5.5620813], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.015404599, 0.040498234, -0.025568588, -0.08956331, 0.03421233, -0.041949254, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 459, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.17968243, 0.0, 0.23418278, 0.07494168, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, 0.040498234, 8.0, 6.0, 0.03421233, -0.041949254, -0.0], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.73255, 8.390094, 24.342459, 17.428352, 6.914107, 10.393142, 7.0352106], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0050290036, 0.06131725, -0.08897111, 0.09135149, -0.009801779, 0.016622717, 0.05265609], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 460, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.7965716, 0.124353066, 0.0, 0.06422156, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 7.0, -0.08897111, 5.0, -0.009801779, 0.016622717, 0.05265609], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.2366, 39.915356, 6.3212442, 31.07684, 8.838515, 23.771952, 7.3048882], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.021067955, -0.03743139, 0.10691336, 0.03218364, -0.065333, -0.0, 0.07760089, -0.014037321, 0.03238109], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 461, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.22151436, 0.33923867, 0.30588824, 0.12182633, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 6.0, 3.0, -0.065333, -0.0, 0.07760089, -0.014037321, 0.03238109], "split_indices": [0, 2, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.9292, 23.98423, 16.94497, 17.497196, 6.487035, 10.307728, 6.6372414, 8.107341, 9.389854], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.055181533, -0.039471857, -0.027830934, 0.05495032, -0.09388859, -0.051321093, -0.01154236], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 462, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.057452485, 0.0, 0.41789484, 0.0, 0.072560966, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.039471857, 3.0, 0.05495032, 6.0, -0.051321093, -0.01154236], "split_indices": [0, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.87975, 7.4908032, 28.388945, 5.9850535, 22.40389, 7.736552, 14.667338], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0021494653, 0.065257825, -0.057887267, 0.047189925, 0.011466424, -0.047568876, 0.03309331], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 463, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.38870436, 0.105090745, 0.0, 0.0, 0.30748954, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [6.0, 3.0, -0.057887267, 0.047189925, 6.0, -0.047568876, 0.03309331], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.583368, 23.998877, 6.5844917, 7.456628, 16.54225, 5.4873066, 11.054942], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0040710825, 0.09859047, -0.13562539, 0.001408244, 0.045521587, -0.083725266, 0.02498324], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 464, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.4315083, 0.07041712, 0.4918374, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 5.0, 8.0, 0.001408244, 0.045521587, -0.083725266, 0.02498324], "split_indices": [1, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.68611, 16.38787, 13.298238, 6.996708, 9.391164, 8.143358, 5.154879], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.03230454, 0.03051761, -0.10639027, -0.05780853, 0.0020049377], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 465, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.3192415, 0.0, 0.22426069, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [3.0, 0.03051761, 7.0, -0.05780853, 0.0020049377], "split_indices": [2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [30.013254, 10.070911, 19.94234, 11.412112, 8.53023], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.09467864, 0.03700094, -0.16601369, -0.108427525, -0.016177636, -0.04240288, 0.028430084], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 466, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.40833914, 0.0, 0.5231361, 0.0, 0.18874666, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.03700094, 5.0, -0.108427525, 6.0, -0.04240288, 0.028430084], "split_indices": [0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [23.66519, 5.308986, 18.356203, 7.064012, 11.2921915, 5.600821, 5.6913705], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.048561886, 0.08514906, -0.007560246, -0.009365228, 0.14070922, -0.0, -0.00797782, 0.0068690465, 0.06317864], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 467, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.0715991, 0.14329982, 0.004155803, 0.0, 0.088404596, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 5.0, -0.009365228, 4.0, -0.0, -0.00797782, 0.0068690465, 0.06317864], "split_indices": [0, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.777256, 18.791386, 10.985871, 5.632607, 13.158778, 5.1404734, 5.845398, 5.9265246, 7.2322536], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.033833373, -0.16609499, 0.07607592, -0.011993524, -0.09544036], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 468, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.9537446, 0.28556114, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.07607592, -0.011993524, -0.09544036], "split_indices": [2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [23.211075, 16.423523, 6.7875533, 10.026897, 6.3966255], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.109424524, 0.055951323, -0.24619429, -0.14717901, -0.097476386, -0.10232083, 0.023815338], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 469, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [1.2839152, 0.0, 0.07241225, 0.57621026, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, 0.055951323, 6.0, 5.0, -0.097476386, -0.10232083, 0.023815338], "split_indices": [1, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.439934, 8.959486, 20.480448, 10.904947, 9.5755, 5.84168, 5.0632677], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.005438539, -0.06320907, 0.06642026, -0.07903842, 0.056281976, 0.05797592, -0.0076941326], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 470, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.73616636, 0.9108382, 0.0, 0.0, 0.28454274, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, 0.06642026, -0.07903842, 5.0, 0.05797592, -0.0076941326], "split_indices": [0, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.645096, 35.5797, 11.065394, 13.158945, 22.420755, 8.339819, 14.0809355], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.027701605, -0.02173975, -0.08126984, 0.046521913, -0.0, -0.034493595, -0.021730412, 0.054201104], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 471, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.092636935, 0.0, 0.15114439, 0.04899609, 0.284793, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.027701605, 7.0, 4.0, 6.0, -0.0, -0.034493595, -0.021730412, 0.054201104], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [42.31096, 7.9019723, 34.40899, 19.06075, 15.348239, 6.3321667, 12.728581, 7.983467, 7.364772], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.006409107, -0.03915859, 0.04368824, -0.06619057, 0.067500316, -0.0, -0.051296204], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 472, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.23415907, 0.0, 0.5359265, 0.12258664, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.03915859, 7.0, 4.0, 0.067500316, -0.0, -0.051296204], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.34422, 9.988258, 24.355963, 15.217368, 9.138595, 9.594246, 5.6231217], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.031507425, 0.05197376, -0.0053056944, -0.04055629, 0.04707336, -0.0011781963, 0.02354887], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 473, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.18121053, 0.0, 0.1871856, 0.0, 0.036949776, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.05197376, 4.0, -0.04055629, 3.0, -0.0011781963, 0.02354887], "split_indices": [0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.33433, 6.050177, 24.284151, 7.0732217, 17.21093, 5.681778, 11.529152], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.067490816, -0.1541569, 0.040592067, -0.06800361, -0.09756371, 0.017765567, -0.091025084], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 474, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.58400106, 0.28024912, 0.0, 0.5537108, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 6.0, 0.040592067, 6.0, -0.09756371, 0.017765567, -0.091025084], "split_indices": [2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.933762, 22.18563, 8.748131, 16.072674, 6.112957, 10.644067, 5.4286065], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.037351884, -0.16681835, 0.04743104, -0.012469052, -0.080725186, 0.05392355, -0.007931446], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 475, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.345134, 0.11677891, 0.19815715, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 5.0, -0.012469052, -0.080725186, 0.05392355, -0.007931446], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.63436, 11.415059, 17.219301, 6.202506, 5.2125525, 6.1406517, 11.0786495], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.02252548, 0.05461996, -0.058289938, 0.0015330203, -0.05031505], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 476, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.33304414, 0.0, 0.13022543, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [5.0, 0.05461996, 7.0, 0.0015330203, -0.05031505], "split_indices": [2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [23.047525, 7.7440996, 15.303427, 9.7144785, 5.5889482], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.02206593, -0.01902047, 0.03119051, 0.037139967, -0.102918796, -0.0863913, 0.009401379], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 477, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.113477655, 0.25529256, 0.0, 0.0, 0.37874553, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 2.0, 0.03119051, 0.037139967, 6.0, -0.0863913, 0.009401379], "split_indices": [0, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.610107, 19.416574, 10.193533, 6.5681243, 12.84845, 5.2340426, 7.614407], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.032393914, -0.047335137, 0.10210845, 0.18319517, -0.030326141, 0.077892184, 0.030221842], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 478, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.3555992, 0.0, 0.35686052, 0.032537818, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.047335137, 7.0, 3.0, -0.030326141, 0.077892184, 0.030221842], "split_indices": [1, 0, 1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [25.025932, 5.9894137, 19.03652, 14.02879, 5.007729, 5.535321, 8.493469], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0447351, 0.029955478, -0.09755178, -0.15594582, -0.0, -0.10673115, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 479, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.24486351, 0.0, 0.13770138, 0.44695455, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, 0.029955478, 6.0, 6.0, -0.0, -0.10673115, -0.0], "split_indices": [2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.8191, 7.2334676, 22.585632, 13.891607, 8.694025, 5.3924174, 8.499189], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.007205041, -0.08025222, 0.04463105, 0.05753699, -0.0, -0.012060716, 0.060312036], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 480, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.7122602, 0.0, 0.26012388, 0.0, 0.29908624, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.08025222, 2.0, 0.05753699, 9.0, -0.012060716, 0.060312036], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.250546, 7.8083444, 41.4422, 8.37015, 33.072052, 27.824728, 5.2473216], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.010584931, 0.04379881, -0.09993941, 0.0005964031, 0.04759674, 0.04225506, -0.01332987], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 481, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.76265097, 0.16112599, 0.0, 0.19007236, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 6.0, -0.09993941, 4.0, 0.04759674, 0.04225506, -0.01332987], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.996254, 34.699787, 5.296467, 26.578403, 8.121384, 6.4818983, 20.096506], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0053597637, 0.10938676, -0.09397333, -0.0140583515, 0.09401245, -0.007731039, -0.03425937, -0.008077866, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 482, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.36444682, 0.47809702, 0.007202983, 0.002368447, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 4.0, 4.0, 0.09401245, -0.007731039, -0.03425937, -0.008077866, -0.0], "split_indices": [0, 2, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.12513, 16.622574, 16.502558, 10.630372, 5.9922023, 5.425352, 11.077205, 5.4371095, 5.1932626], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.023854377, 0.17508553, -0.11672772, 0.02454611, 0.08233427, 0.010577161, -0.08133745], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 483, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.67145693, 0.08428094, 0.40823627, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 4.0, 0.02454611, 0.08233427, 0.010577161, -0.08133745], "split_indices": [2, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.346746, 14.362349, 14.984398, 8.868189, 5.4941597, 7.6136575, 7.370741], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.036969017, -0.044034034, 0.10053833, 0.0046843705, 0.047523633], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 484, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.48316744, 0.0, 0.048079923, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [6.0, -0.044034034, 8.0, 0.0046843705, 0.047523633], "split_indices": [0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [29.714664, 16.887993, 12.826673, 6.4133897, 6.4132824], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.007020176, -0.042214993, 0.025375154, 0.14329284, -0.025488408, 0.09482067, -0.009728148], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 485, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.13597748, 0.0, 0.3134746, 0.3965005, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.042214993, 6.0, 7.0, -0.025488408, 0.09482067, -0.009728148], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [26.907799, 5.198124, 21.709677, 10.788032, 10.921644, 5.323802, 5.4642296], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.08468197, -0.17048647, 0.028509399, -0.005018987, -0.0813011], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 486, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.39371324, 0.22902113, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [8.0, 3.0, 0.028509399, -0.005018987, -0.0813011], "split_indices": [1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [22.992292, 15.973497, 7.018796, 7.168883, 8.804614], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.03658993, -0.042383093, 0.12503497, 0.16815495, 0.015307563, 0.0099810865, 0.08212939], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 487, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.50925964, 0.0, 0.039218843, 0.1384551, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [4.0, -0.042383093, 6.0, 4.0, 0.015307563, 0.0099810865, 0.08212939], "split_indices": [0, 0, 2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.352444, 9.506488, 20.845955, 11.514233, 9.331722, 6.0495987, 5.464634], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.058830153, 0.04587002, -0.042180367, -0.022029549, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 488, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.2692759, 0.0, 0.01713018, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, 0.04587002, 7.0, -0.022029549, -0.0], "split_indices": [1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [25.494757, 13.447525, 12.047232, 6.4817977, 5.565434], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.033267833, -0.0, 0.041502032, -0.08120158, 0.089133754, 0.04910658, -0.07758247, 0.008514822, 0.036829676], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 489, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.1119777, 0.1856668, 0.0, 0.6399576, 0.0061932504, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.041502032, 4.0, 7.0, 0.04910658, -0.07758247, 0.008514822, 0.036829676], "split_indices": [2, 1, 0, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.334915, 23.671747, 6.663167, 12.537402, 11.134345, 5.0626397, 7.4747624, 5.684115, 5.45023], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.05899681, 0.14704503, -0.024388982, 0.056529313, 0.078639366, -0.1412384, 0.037795912, 0.060907327, -0.048466194, -0.0069597545, -0.08375171], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 490, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.35644966, 0.1959303, 0.442796, 0.50971365, 0.0, 0.19786784, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 4.0, 2.0, 0.078639366, 9.0, 0.037795912, 0.060907327, -0.048466194, -0.0069597545, -0.08375171], "split_indices": [0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.390514, 22.290003, 23.10051, 13.79796, 8.4920435, 13.407755, 9.692755, 8.56925, 5.2287097, 8.245074, 5.1626806], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.003566538, -0.08757342, 0.10287278, -0.05643421, -0.023903416, 0.06337128, -0.02750889, 0.03377709, -0.05700427], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 491, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.36828992, 0.1254881, 0.40523833, 0.0, 0.39677608, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 2.0, 9.0, -0.05643421, 6.0, 0.06337128, -0.02750889, 0.03377709, -0.05700427], "split_indices": [0, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.351784, 22.617636, 16.734146, 7.402816, 15.21482, 11.0231285, 5.711018, 8.198379, 7.016441], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.005696937, 0.076850474, -0.06567435, -0.035290997, 0.19201328, 0.053451832, -0.061428998, 0.08658151, 0.022964982], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 492, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.1965082, 0.47645274, 0.66828346, 0.0, 0.08885184, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 4.0, -0.035290997, 3.0, 0.053451832, -0.061428998, 0.08658151, 0.022964982], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.48159, 18.931852, 17.549738, 6.710065, 12.2217865, 6.0111628, 11.538574, 5.3666553, 6.855131], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.06494144, -0.10888051, 0.014270314, -0.057535816, 0.0949513, 0.080635324, 0.00015136485], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 493, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.7942452, 0.0, 0.43975455, 0.0, 0.29567724, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.10888051, 4.0, -0.057535816, 6.0, 0.080635324, 0.00015136485], "split_indices": [0, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.951387, 6.012615, 24.938772, 6.280938, 18.657833, 5.5458283, 13.112004], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.004806178, 0.05577411, -0.087956935, -0.063822076, 0.014846157, 0.043564558, -0.028429033], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 494, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.46324486, 0.0, 0.2935991, 0.0, 0.191188, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [4.0, 0.05577411, 7.0, -0.063822076, 5.0, 0.043564558, -0.028429033], "split_indices": [2, 0, 2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.797684, 7.6993856, 20.098299, 9.034943, 11.063355, 5.321492, 5.7418637], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0270868, 0.047518358, -0.12546882, -0.088931255, 0.012302581], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 495, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.545823, 0.0, 0.602996, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [3.0, 0.047518358, 4.0, -0.088931255, 0.012302581], "split_indices": [2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.002699, 9.141544, 18.861155, 9.216275, 9.644879], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.016308062, -0.08114197, 0.041800547, 0.011558911, -0.061143763], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 496, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.26753685, 0.30943978, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [8.0, 3.0, 0.041800547, 0.011558911, -0.061143763], "split_indices": [1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [24.968815, 18.41791, 6.5509048, 9.257471, 9.160439], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.02848751, -0.050223276, 0.06704074, 0.01932434, -0.060396228, -0.04405271, 0.060717847], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 497, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.49053028, 0.252186, 0.0, 0.5053753, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 7.0, 0.06704074, 4.0, -0.060396228, -0.04405271, 0.060717847], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.102148, 20.89624, 8.205908, 14.45372, 6.4425206, 7.394005, 7.0597153], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.08329311, 0.14534633, -0.0, 0.02561816, 0.058863435, -0.003193434, 0.000215389], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 498, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.14882757, 0.0035362542, 0.00075815205, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 7.0, 0.02561816, 0.058863435, -0.003193434, 0.000215389], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [25.929138, 14.888575, 11.040565, 8.965797, 5.9227777, 5.6618586, 5.3787055], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0039288285, 0.11246235, -0.063820496, -0.0014144325, 0.06310715, 0.081479535, -0.080977276, 0.00024328406, 0.03684053], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 499, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.25278616, 0.17655557, 0.6466158, 0.0, 0.0, 0.028699495, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 6.0, 6.0, -0.0014144325, 0.06310715, 4.0, -0.080977276, 0.00024328406, 0.03684053], "split_indices": [0, 2, 2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.740679, 12.512259, 19.22842, 5.6635246, 6.8487334, 11.347839, 7.8805804, 5.008993, 6.3388457], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "5E-1", "boost_from_average": "1", "num_class": "10", "num_feature": "3", "num_target": "1"}, "objective": {"name": "multi:softprob", "softmax_multiclass_param": {"num_class": "10"}}}, "version": [3, 0, 2]}