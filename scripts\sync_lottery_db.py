#!/usr/bin/env python3
"""
同步lottery.db到最新状态
"""

import sqlite3

def sync_lottery_db():
    """同步lottery.db"""
    try:
        conn = sqlite3.connect('data/lottery.db')
        cursor = conn.cursor()
        
        # 检查是否已有2025217期
        cursor.execute('SELECT COUNT(*) FROM lottery_data WHERE issue = ?', ('2025217',))
        exists = cursor.fetchone()[0]
        
        if exists:
            print("2025217期数据已存在")
        else:
            # 添加2025217期数据
            cursor.execute('''
                INSERT INTO lottery_data 
                (issue, draw_date, hundreds, tens, units, sum_value, span, number_type) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', ('2025217', '2025-08-15', 8, 9, 4, 21, 5, '组六'))
            
            conn.commit()
            print("✅ 2025217期数据已添加到lottery.db: 894")
        
        # 验证最新数据
        cursor.execute('SELECT issue, hundreds, tens, units FROM lottery_data ORDER BY issue DESC LIMIT 3')
        recent = cursor.fetchall()
        
        print("最新3期数据:")
        for row in recent:
            print(f"  {row[0]}: {row[1]}{row[2]}{row[3]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 同步失败: {e}")
        return False

if __name__ == "__main__":
    sync_lottery_db()
