{"learner": {"attributes": {}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "500"}, "iteration_indptr": [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130, 140, 150, 160, 170, 180, 190, 200, 210, 220, 230, 240, 250, 260, 270, 280, 290, 300, 310, 320, 330, 340, 350, 360, 370, 380, 390, 400, 410, 420, 430, 440, 450, 460, 470, 480, 490, 500], "tree_info": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "trees": [{"base_weights": [0.16969907, 0.20364238, 0.105525695, 0.19988577, -0.07734809, 0.019596195, 0.0907195, 0.042025857, -0.08553973], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [1.7174226, 0.0, 0.9270965, 0.42656255, 0.8721955, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.20364238, 7.0, 4.0, 4.0, 0.019596195, 0.0907195, 0.042025857, -0.08553973], "split_indices": [0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.159996, 5.04, 51.12, 34.019997, 17.099998, 15.839999, 18.179998, 8.28, 8.82], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.22013651, -0.08245244, 0.31112882, 0.40917265, 0.22145326, 0.025936596, 0.15460123, -5.597265e-09, 0.10881697], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [2.7284362, 0.0, 0.28716278, 0.65723634, 0.9403995, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.08245244, 5.0, 3.0, 3.0, 0.025936596, 0.15460123, -5.597265e-09, 0.10881697], "split_indices": [0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.6, 8.46, 49.14, 21.24, 27.9, 5.9399996, 15.299999, 10.98, 16.92], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.008638583, -0.09558201, 0.09915254, 0.106097564, -0.18806422, -0.07953021, 0.008122738], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.7147827, 2.0059724, 0.0, 0.0, 0.6919365, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, 0.09915254, 0.106097564, 8.0, -0.07953021, 0.008122738], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.879997, 46.079998, 10.799999, 7.2, 38.879997, 28.8, 10.08], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.096313536, -0.14461885, -0.026768664, 0.19340973, -0.10676159, 0.1642012, -0.03292684, -0.07390954, 0.034659818], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [1.5517275, 0.0, 0.9331287, 1.6017224, 1.2623875, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.14461885, 3.0, 5.0, 6.0, 0.1642012, -0.03292684, -0.07390954, 0.034659818], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.219997, 7.9199996, 51.3, 12.959999, 38.34, 5.7599998, 7.2, 23.759998, 14.579999], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-2.0267462e-08, 0.089062504, -0.03877474, -0.11208793, 0.029898785, -0.015435842, 0.08129371], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.64161193, 0.0, 1.2316487, 0.0, 0.88863623, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.089062504, 3.0, -0.11208793, 8.0, -0.015435842, 0.08129371], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.979996, 5.3999996, 50.579998, 8.099999, 42.48, 32.039997, 10.44], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.10620366, 0.0030266142, -0.23826982, -0.09164225, 0.11982249, -0.02396168, -0.11611677, -0.07004557, 0.04197761, -0.016438363, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.8799733, 1.3072053, 0.81947553, 0.9380718, 0.0, 0.014729314, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 4.0, 7.0, 0.11982249, 8.0, -0.11611677, -0.07004557, 0.04197761, -0.016438363, -0.0], "split_indices": [1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.319996, 32.039997, 26.279999, 26.279999, 5.7599998, 11.5199995, 14.759999, 16.56, 9.719999, 6.2999997, 5.22], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.08649934, -0.18459582, 0.021023106, 0.02106741, -0.25296444, -0.07159906, 0.12759922, 0.01203208, -0.10839534, 0.0929003, -0.04372198], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.6421231, 0.583429, 0.80923104, 0.0, 0.8500204, 0.0, 1.1145815, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 3.0, 0.02106741, 1.0, -0.07159906, 8.0, 0.01203208, -0.10839534, 0.0929003, -0.04372198], "split_indices": [1, 2, 2, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.96, 30.419998, 27.539999, 6.12, 24.3, 7.3799996, 20.16, 6.4799995, 17.82, 12.24, 7.9199996], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0069324295, -0.12403531, 0.17841211, -0.08840865, -0.051660538, 0.3602941, -0.024439925, 0.014210521, -0.07912089, 0.059579436, 0.15397352], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [1.2690345, 0.39825243, 1.1169657, 0.0, 0.603274, 0.1632353, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 6.0, -0.08840865, 4.0, 4.0, -0.024439925, 0.014210521, -0.07912089, 0.059579436, 0.15397352], "split_indices": [1, 2, 2, 0, 1, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.699997, 35.28, 21.419998, 9.179999, 26.099998, 12.599999, 8.82, 18.0, 8.099999, 7.5599995, 5.04], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.010145398, 0.24682122, -0.17664672, -0.064184874, 0.18996711, -0.13410598, -0.109308906, 0.032926828, -0.07159906, 0.008122738, -0.057439834], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [2.6651483, 3.310987, 0.5035956, 0.5119226, 0.0, 0.0, 0.3393965, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 5.0, 1.0, 0.18996711, -0.13410598, 7.0, 0.032926828, -0.07159906, 0.008122738, -0.057439834], "split_indices": [1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.14, 25.74, 32.399998, 14.579999, 11.16, 5.04, 27.359999, 7.2, 7.3799996, 10.08, 17.279999], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.021915058, -0.17857145, 0.07226979, -0.09863015, -0.09603844, -0.054775286, 0.13124198, -5.0953823e-09, -0.040613726, 0.06893617, -0.04118994], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.89878184, 0.2105596, 0.5803232, 0.0, 0.049407125, 0.0, 0.86751866, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 1.0, 2.0, -0.09863015, 4.0, -0.054775286, 8.0, -5.0953823e-09, -0.040613726, 0.06893617, -0.04118994], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.319996, 21.96, 36.359997, 6.2999997, 15.659999, 6.12, 30.239998, 5.58, 10.08, 22.499998, 7.74], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.11137924, 0.047043625, 0.26528695, 0.11716406, -0.04731995, 0.12886094, -0.009785595, 0.013408367, 0.11796728], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.56904006, 0.6650978, 0.91335547, 0.6327068, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 9.0, 4.0, -0.04731995, 0.12886094, -0.009785595, 0.013408367, 0.11796728], "split_indices": [1, 1, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.883327, 44.314598, 16.568731, 33.693367, 10.621228, 10.611778, 5.956954, 27.89476, 5.798608], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.07635375, -0.084132195, 0.14072326, 0.32085696, -0.0, 0.15145704, 0.05318952, -0.072969206, 0.039039806], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [1.4861381, 0.0, 1.4144251, 0.4835372, 1.0379348, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.084132195, 5.0, 4.0, 7.0, 0.15145704, 0.05318952, -0.072969206, 0.039039806], "split_indices": [0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.598454, 8.816903, 53.78155, 23.276697, 30.504854, 8.793154, 14.483543, 10.544389, 19.960464], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.043686602, -0.065422915, -0.015492925, -0.11678553, 0.040639628, -0.008435679, -0.08649576, 0.047928188, -0.004993558], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.26769686, 0.0, 0.31439248, 0.26191467, 0.25093228, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.065422915, 3.0, 2.0, 4.0, -0.008435679, -0.08649576, 0.047928188, -0.004993558], "split_indices": [0, 0, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.884296, 6.7640386, 52.12026, 18.834286, 33.285973, 13.567883, 5.2664027, 10.795025, 22.490948], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.016904615, -0.1360209, 0.040116396, 0.10019235, -0.087026, -0.033389736, 0.055263728, 0.015535709, -0.05713876], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [1.4791073, 0.0, 0.40081963, 0.66294026, 0.25946626, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.1360209, 7.0, 3.0, 4.0, -0.033389736, 0.055263728, 0.015535709, -0.05713876], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.035507, 5.8737698, 50.16174, 34.74357, 15.418169, 9.44761, 25.295958, 6.3837523, 9.034417], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.07423548, 0.12435157, 0.15173459, -0.2303931, -0.11578577, 0.16370848, -0.0, 0.06694701, -0.103105865, -0.025055975, -0.06544863, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5654063, 1.4109877, 2.4076612, 0.16745672, 0.3210522, 0.17228195, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 6.0, 4.0, 4.0, 8.0, 0.16370848, -0.0, 0.06694701, -0.103105865, -0.025055975, -0.06544863, -0.0], "split_indices": [1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.5189, 37.772503, 21.746397, 15.185728, 22.586775, 14.162557, 7.5838413, 5.339968, 9.845759, 11.610185, 10.97659, 6.8907204, 7.271836], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.052882396, -0.12947884, 0.10443223, -0.022130843, -0.20712566, -0.011709979, 0.06685544, 0.037311707, -0.06852531, -0.12090541, -0.00072007265], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.705184, 0.300385, 0.34739813, 0.5879854, 0.8474765, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 5.0, 4.0, 7.0, -0.011709979, 0.06685544, 0.037311707, -0.06852531, -0.12090541, -0.00072007265], "split_indices": [0, 2, 2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.192722, 38.338383, 17.854338, 17.17146, 21.166924, 7.9946733, 9.859664, 9.989399, 7.18206, 10.072231, 11.094692], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.01369755, 0.16932616, -0.08179221, -0.019911004, 0.14047475, -0.30790272, 0.030259714, -0.1441575, -0.0073190224, 0.054024357, -0.08502341], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.734946, 1.1849757, 1.1456438, 0.0, 0.0, 0.66616964, 1.4444739, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 6.0, 5.0, -0.019911004, 0.14047475, 6.0, 7.0, -0.1441575, -0.0073190224, 0.054024357, -0.08502341], "split_indices": [0, 2, 0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.561626, 14.807392, 42.754234, 8.527235, 6.280157, 13.883397, 28.870838, 7.978036, 5.9053617, 20.042473, 8.828365], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.08642561, -0.19874026, -0.09383301, 0.16075487, -0.1380251, -0.032650404, 0.11789812, 0.02056358, 0.011295362, -0.038473018], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [1.0592041, 1.3034234, 0.7488556, 0.0, 0.7172599, 0.0, 0.09966891, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [7.0, 1.0, 8.0, -0.09383301, 2.0, -0.1380251, 9.0, 0.11789812, 0.02056358, 0.011295362, -0.038473018], "split_indices": [2, 2, 2, 0, 1, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.47833, 41.744934, 17.733393, 5.8378854, 35.90705, 5.968985, 11.764408, 8.969656, 26.937393, 6.525449, 5.2389593], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.021945534, 0.16313611, -0.044368662, -0.117661916, 0.18055356, -0.048421104, 0.010783559, -0.041551337, 0.1329216], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [2.0751772, 0.0, 0.8731949, 0.28849733, 1.1684905, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.16313611, 8.0, 8.0, 5.0, -0.048421104, 0.010783559, -0.041551337, 0.1329216], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.82631, 5.7653418, 51.060966, 39.20657, 11.854396, 30.983768, 8.222803, 5.39226, 6.462136], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017882597, -0.23531426, 0.05389339, -0.033704422, -0.08942527, 0.009453793, 0.098336145, 0.09191379, -0.012031232], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.774111, 0.014521956, 0.5306787, 0.0, 0.0, 0.64899236, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [2.0, 4.0, 9.0, -0.033704422, -0.08942527, 3.0, 0.098336145, 0.09191379, -0.012031232], "split_indices": [2, 1, 2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.16223, 10.537663, 45.62457, 5.042458, 5.495206, 40.459576, 5.164994, 5.334368, 35.125206], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.13661796, -0.21125916, 0.2808934, -0.04168903, -0.14011802, 0.51494724, 0.14406683, 0.0009041539, -0.025487935, 0.22081463, 0.08711479, 0.0082450565, 0.107539244], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [3.265549, 0.75613457, 1.3230827, 0.033193156, 0.0, 0.4878564, 0.7136838, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 6.0, 4.0, 1.0, -0.14011802, 6.0, 8.0, 0.0009041539, -0.025487935, 0.22081463, 0.08711479, 0.0082450565, 0.107539244], "split_indices": [2, 0, 0, 2, 0, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.742508, 18.053791, 44.688717, 11.791272, 6.262518, 15.197577, 29.491138, 5.1205587, 6.6707134, 6.2857676, 8.911811, 20.13623, 9.354909], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.06959209, 0.48972768, -0.1661409, -0.0122599, 0.69487447, -0.013126032, -0.31918475, 0.11736051, 0.25834772, 0.10424988, -0.06903473, -0.12877196, -0.023774853], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [6.144676, 2.5203242, 0.88997304, 0.0, 0.35935307, 1.7250097, 0.4281882, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 1.0, 5.0, -0.0122599, 5.0, 6.0, 8.0, 0.11736051, 0.25834772, 0.10424988, -0.06903473, -0.12877196, -0.023774853], "split_indices": [2, 2, 0, 0, 0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.894234, 21.292454, 38.601776, 6.024349, 15.268105, 20.172386, 18.429392, 6.801332, 8.466772, 7.178983, 12.993403, 11.80763, 6.6217623], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.095865875, 0.093221515, 0.046044055, -0.0, 0.054973107, -0.02067956, 0.032524947], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.5333304, 0.0, 0.28433147, 0.2732769, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.093221515, 8.0, 7.0, 0.054973107, -0.02067956, 0.032524947], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.48836, 8.704269, 44.784092, 34.425655, 10.358434, 21.117023, 13.308634], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.10136303, -0.16123182, 0.08785486, 0.021392033, -0.20784335, -0.08503583, -0.027360132], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.3718603, 0.5712445, 0.0, 0.0, 0.30998683, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 2.0, 0.08785486, 0.021392033, 6.0, -0.08503583, -0.027360132], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.684204, 49.075775, 6.60843, 7.7478085, 41.32797, 23.686604, 17.641363], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.082358964, 0.057881393, -0.12930752, 0.024700781, 0.0033513955, -0.25733468, -0.0021478944, -0.09909385, -0.04480038, -0.043148592, 0.070098855], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.41346025, 0.007262662, 0.7260527, 0.0, 0.0, 0.08110237, 0.8462338, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 1.0, 6.0, 0.024700781, 0.0033513955, 4.0, 6.0, -0.09909385, -0.04480038, -0.043148592, 0.070098855], "split_indices": [1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.575356, 14.349378, 45.22598, 7.5126476, 6.83673, 21.634129, 23.59185, 11.129378, 10.50475, 15.24589, 8.34596], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.069412, -0.17486186, 0.017497454, -0.3168962, 0.011432923, 0.12619628, -0.08502092, -0.044388544, -0.13878961, 0.008960505, -0.06967346], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.52378577, 0.8115076, 1.3338826, 0.2567885, 0.0, 0.0, 0.4504935, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 5.0, 4.0, 0.011432923, 0.12619628, 8.0, -0.044388544, -0.13878961, 0.008960505, -0.06967346], "split_indices": [1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.102272, 24.506462, 29.59581, 14.690617, 9.815845, 5.5687494, 24.02706, 8.054931, 6.635686, 13.561751, 10.465308], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0027384649, 0.32120177, -0.13194476, -0.0, 0.14546318, -0.10643638, -0.020093933, 0.033988163, -0.059991185], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [2.4247637, 0.8871381, 1.0008173, 0.0, 0.0, 0.0, 0.73396575, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 1.0, 5.0, -0.0, 0.14546318, -0.10643638, 8.0, 0.033988163, -0.059991185], "split_indices": [0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.371925, 15.429031, 40.942894, 5.446193, 9.982839, 12.658009, 28.284885, 16.134525, 12.150359], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.018075772, -0.0874612, 0.05578201, 0.14356866, -0.12547788, 0.08404571, 0.020708412, -0.13188297, 0.0067185587], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.69356894, 0.0, 0.8893563, 0.329095, 0.8873323, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.0874612, 7.0, 3.0, 8.0, 0.08404571, 0.020708412, -0.13188297, 0.0067185587], "split_indices": [2, 0, 2, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.036076, 5.3904195, 53.645653, 36.653496, 16.992157, 11.493526, 25.159971, 5.023637, 11.968521], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04043096, 0.31973356, -0.068563074, 0.016077254, 0.13603097, -0.0050645648, -0.13551006, 0.027709384, -0.039391384], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [1.9049101, 0.5500276, 1.0337927, 0.0, 0.0, 0.5003508, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 4.0, 9.0, 0.016077254, 0.13603097, 7.0, -0.13551006, 0.027709384, -0.039391384], "split_indices": [2, 0, 0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.02923, 16.564882, 43.464344, 6.257585, 10.307298, 38.37805, 5.0862923, 21.365131, 17.01292], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.04190538, -0.18598403, 0.07301269, -0.058891717, -0.11507909, 0.093960576, -0.0, -0.053769924, 0.0036838737, -0.0615783, 0.024737887], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.98136616, 0.5936934, 0.55474424, 0.1687422, 0.0, 0.0, 0.45207757, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 6.0, 6.0, 2.0, -0.11507909, 0.093960576, 7.0, -0.053769924, 0.0036838737, -0.0615783, 0.024737887], "split_indices": [2, 1, 2, 1, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.768677, 25.300245, 31.468431, 16.495453, 8.804792, 6.4195967, 25.048834, 6.0880623, 10.407391, 6.5895195, 18.459314], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.07976313, 0.2042359, -0.06966356, 0.31279904, -0.0, 0.03074527, -0.08559612, 0.1537266, 0.06298019, 0.01139681, -0.0111580165, -0.064217374, 0.049981844], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.2255012, 0.7854377, 0.6762029, 0.2980156, 0.02048304, 0.7232084, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 6.0, 2.0, 2.0, 7.0, -0.08559612, 0.1537266, 0.06298019, 0.01139681, -0.0111580165, -0.064217374, 0.049981844], "split_indices": [1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.400265, 34.7532, 28.647064, 22.269732, 12.483468, 19.815018, 8.8320465, 5.888566, 16.381165, 6.4754686, 6.008, 6.6039557, 13.2110615], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.059504177, 0.21410011, -0.15748829, -0.05101411, 0.2924759, -0.30465376, 0.043271706, 0.11782807, 0.022465851, -0.014387046, -0.12647179], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [2.1616442, 1.1951326, 1.2423465, 0.0, 0.6186595, 0.48632395, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 1.0, 7.0, -0.05101411, 6.0, 2.0, 0.043271706, 0.11782807, 0.022465851, -0.014387046, -0.12647179], "split_indices": [2, 2, 1, 0, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.264027, 36.63655, 25.627476, 5.793762, 30.84279, 17.44199, 8.185487, 20.24671, 10.596079, 6.167227, 11.274762], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.015646044, -0.08552877, 0.13940236, 0.06461795, -0.23095381, 0.06456731, 0.04291186, -0.0867011, 0.0762663, -0.009191728, -0.09723618, 0.022757787, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.7711915, 0.75871146, 0.16912216, 1.1982785, 0.2807045, 0.0, 0.023840677, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 8.0, 1.0, 3.0, 0.06456731, 6.0, -0.0867011, 0.0762663, -0.009191728, -0.09723618, 0.022757787, -0.0], "split_indices": [2, 2, 2, 2, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.209232, 32.302082, 26.907146, 15.828099, 16.473984, 13.797898, 13.109249, 5.157452, 10.670647, 5.992316, 10.481668, 7.657695, 5.4515543], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.020439716, -0.16077878, 0.14269188, -0.2601396, 0.037130028, 0.36157203, -0.10387908, -0.097008735, -0.051051363, 0.037032798, 0.18967739], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [1.2572402, 0.88663566, 2.8125932, 0.0243119, 0.0, 1.0044599, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 7.0, 7.0, 3.0, 0.037130028, 4.0, -0.10387908, -0.097008735, -0.051051363, 0.037032798, 0.18967739], "split_indices": [2, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.835236, 28.73902, 24.096214, 21.624975, 7.114044, 16.966312, 7.129904, 10.523908, 11.101067, 9.997086, 6.9692264], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.028667696, 0.075296625, -0.095757976, -0.112670735, 0.03982529, -0.032954186, 0.10876178], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [1.0303739, 0.0, 1.7533809, 0.0, 1.5592433, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.075296625, 5.0, -0.112670735, 7.0, -0.032954186, 0.10876178], "split_indices": [2, 0, 2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.463818, 9.525696, 43.93812, 14.004253, 29.93387, 20.688448, 9.245421], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.10178926, -0.063515, -0.098582335, -0.1555611, 0.10435773, -0.07813994, -0.022998298, -0.0, 0.04615667], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.41467416, 0.7693624, 0.0, 0.21161187, 0.09305851, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 6.0, -0.098582335, 4.0, 3.0, -0.07813994, -0.022998298, -0.0, 0.04615667], "split_indices": [0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.687374, 47.4084, 6.2789755, 31.051575, 16.356825, 11.79729, 19.254286, 5.4402504, 10.916574], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.042807925, 0.15076822, -0.120014146, -0.017697863, 0.11944028, -0.014766697, -0.10258075, -0.07204952, 0.037322976], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.85167277, 0.88944286, 0.9156372, 0.0, 0.0, 0.95509946, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 6.0, 7.0, -0.017697863, 0.11944028, 3.0, -0.10258075, -0.07204952, 0.037322976], "split_indices": [0, 2, 2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.981136, 15.03541, 39.94573, 8.329462, 6.7059484, 28.124632, 11.821095, 10.733876, 17.390757], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.008767319, 0.07821913, -0.05986591, -0.109195575, 0.07751545, -0.050860066, -0.000195184], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.7917208, 0.0, 0.79397213, 0.27123213, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, 0.07821913, 9.0, 6.0, 0.07751545, -0.050860066, -0.000195184], "split_indices": [1, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.817223, 8.090074, 48.727146, 43.028698, 5.698451, 26.702282, 16.326414], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.04729652, 0.11477178, -0.010044342, -0.08993727, 0.028608482, 0.0953931, -0.0043902043], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [1.1659583, 0.0, 0.60333997, 0.0, 0.6021715, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.11477178, 2.0, -0.08993727, 3.0, 0.0953931, -0.0043902043], "split_indices": [2, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.510365, 7.8255982, 49.68477, 5.357253, 44.327515, 5.2376432, 39.08987], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.047174096, -0.14058694, 0.031694066, 0.0024093846, -0.20167467, -0.08539655, 0.18910281, -0.080437236, -0.011622468, 0.16401133, 0.002433247], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.41644138, 0.25419587, 1.5259643, 0.0, 0.16160345, 0.0, 1.2786047, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 4.0, 0.0024093846, 7.0, -0.08539655, 6.0, -0.080437236, -0.011622468, 0.16401133, 0.002433247], "split_indices": [1, 2, 2, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.442795, 24.687666, 28.755129, 6.967708, 17.71996, 9.074386, 19.680742, 11.692158, 6.027801, 5.748944, 13.931799], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.12685831, 0.31225812, 0.049926467, 0.187717, 0.14069217, -0.012698741, 0.1395255, 0.1226415, -0.010476745, -0.046836875, 0.021671906, 0.065632574, -0.009241008], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8830683, 0.21030474, 0.28319865, 0.662761, 0.0, 0.36625525, 0.30150297, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [2.0, 6.0, 7.0, 3.0, 0.14069217, 4.0, 5.0, 0.1226415, -0.010476745, -0.046836875, 0.021671906, 0.065632574, -0.009241008], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.5449, 17.650295, 46.8946, 11.414875, 6.23542, 27.450365, 19.444235, 5.5332084, 5.881667, 10.336222, 17.114143, 13.476589, 5.967646], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.06895559, 0.23629278, -0.050548393, -0.0, 0.30262586, -0.09058726, -0.0, 0.048639946, 0.173556, 0.06425009, -0.021656439], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [1.2694032, 0.43998134, 0.4741452, 0.0, 0.64068973, 0.0, 0.50490695, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 5.0, -0.0, 3.0, -0.09058726, 6.0, 0.048639946, 0.173556, 0.06425009, -0.021656439], "split_indices": [2, 0, 2, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.95209, 25.335375, 35.616714, 5.475495, 19.85988, 5.226325, 30.390388, 14.45111, 5.4087715, 7.2672577, 23.12313], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.074115075, -0.2267891, 0.07099128, -0.0931798, -0.12505086, 0.10279452, -0.040566612, -0.099194296, 0.014219808, -0.039886788, 0.022995982], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [1.3465538, 0.16909683, 0.9792073, 0.0, 0.5948242, 0.0, 0.25956437, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 3.0, -0.0931798, 5.0, 0.10279452, 8.0, -0.099194296, 0.014219808, -0.039886788, 0.022995982], "split_indices": [1, 1, 2, 0, 2, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.351612, 28.495857, 29.855757, 13.960005, 14.5358515, 8.349241, 21.506516, 6.476743, 8.059109, 12.429106, 9.07741], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.08497161, -0.13106327, -0.03637322, 0.03944925, -0.18082663, 0.11533277, -0.024746748, -0.09496304, -0.022971561], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.8749463, 0.0, 0.5638813, 1.4440466, 0.18668813, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.13106327, 7.0, 3.0, 8.0, 0.11533277, -0.024746748, -0.09496304, -0.022971561], "split_indices": [1, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.856663, 5.315966, 48.540695, 31.854933, 16.685762, 7.970835, 23.884098, 5.947712, 10.738051], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.041000634, 0.08824715, -0.08486012, -0.08967281, -0.025316743, -0.03108634, 0.07313371], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.82378584, 0.0, 0.6017679, 0.0, 0.87205994, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.08824715, 3.0, -0.08967281, 8.0, -0.03108634, 0.07313371], "split_indices": [2, 0, 2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.7661, 5.413251, 49.352848, 9.500511, 39.852337, 31.63375, 8.218588], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.074522935, 0.0009239996, -0.17737785, -0.06349968, 0.07323337, -0.25382015, -0.00067857816, 0.05906191, -0.04599884, -0.13653804, -0.023153175], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.43535814, 0.48546952, 0.28617418, 0.0, 0.72305065, 0.47479618, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 7.0, -0.06349968, 7.0, 7.0, -0.00067857816, 0.05906191, -0.04599884, -0.13653804, -0.023153175], "split_indices": [1, 1, 2, 0, 2, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.807228, 30.473122, 22.334108, 6.8941493, 23.578972, 14.836938, 7.49717, 15.6247, 7.954273, 5.9329505, 8.903987], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0065898104, 0.030885994, -0.15750375, -0.045066457, 0.18895155, -0.1359158, 0.051179755, -0.039773908, 0.052123163, 0.09513942, -0.0029947776], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.33333927, 0.5636009, 1.2500732, 0.60870856, 0.42241943, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, 9.0, 7.0, 7.0, -0.1359158, 0.051179755, -0.039773908, 0.052123163, 0.09513942, -0.0029947776], "split_indices": [2, 2, 2, 1, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.84052, 44.03757, 10.802948, 29.800365, 14.237205, 5.6434197, 5.1595283, 21.894686, 7.9056787, 8.602716, 5.634488], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0024684728, 0.08661957, -0.21303795, 0.15196666, -0.09013482, -0.0, -0.09401598, 0.06663351, 0.026007196], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [1.0917886, 1.1465404, 0.33393592, 0.12671822, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 9.0, 4.0, 4.0, -0.09013482, -0.0, -0.09401598, 0.06663351, 0.026007196], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.882607, 43.11077, 15.771836, 37.624535, 5.4862356, 5.6210814, 10.150755, 16.23931, 21.385225], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.032631077, 0.009735577, -0.101998165, -0.071419746, 0.24617556, -0.07945199, 0.013865799, 0.13142167, 0.005730815], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.81890434, 1.0616786, 0.0, 0.9508767, 0.56516093, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 7.0, -0.101998165, 3.0, 4.0, -0.07945199, 0.013865799, 0.13142167, 0.005730815], "split_indices": [0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.096184, 52.502285, 6.593898, 39.29107, 13.211214, 14.707691, 24.58338, 6.41791, 6.793304], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0089254845, -0.13107021, 0.06037286, 0.09950042, -0.0, -0.09053819, 0.027326027], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [1.349098, 0.0, 0.8580838, 0.0, 1.2631392, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, -0.13107021, 3.0, 0.09950042, 5.0, -0.09053819, 0.027326027], "split_indices": [2, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.310215, 5.056481, 53.253735, 8.598153, 44.655582, 9.582482, 35.073097], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.017025258, -0.12604167, 0.22441013, -0.07523089, -0.04133361, 0.13849139, 0.083618075, 0.04404507, -0.056226127, -0.0336994, 0.08870442], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [1.76515, 0.45324916, 0.59885335, 0.0, 0.8411535, 0.0, 0.65355664, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 8.0, -0.07523089, 5.0, 0.13849139, 9.0, 0.04404507, -0.056226127, -0.0336994, 0.08870442], "split_indices": [2, 2, 2, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.36298, 45.644882, 19.718102, 17.191065, 28.45382, 6.230641, 13.487461, 12.135083, 16.318737, 7.009408, 6.478053], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.111597046, -0.18528636, 0.19741425, -0.0147592435, -0.07831469, 0.13597104, 0.13774991, -0.027359204, 0.078607604], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [1.735836, 0.112849474, 0.6790142, 0.0, 0.0, 0.0, 1.3009441, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 4.0, 4.0, -0.0147592435, -0.07831469, 0.13597104, 3.0, -0.027359204, 0.078607604], "split_indices": [0, 1, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.648285, 14.199828, 51.448456, 6.140037, 8.059791, 8.165367, 43.28309, 15.038961, 28.244127], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.07800275, -0.087098934, -0.090511486, 0.120325565, 0.01791389, -0.08545895, 0.028163824, -0.07327242, -0.057763737, 0.046552725], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.39161488, 1.7304752, 0.59505796, 0.62440825, 0.0, 0.5555347, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 7.0, 3.0, 0.120325565, 7.0, -0.08545895, 0.028163824, -0.07327242, -0.057763737, 0.046552725], "split_indices": [0, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.66909, 29.547264, 26.121826, 19.688442, 9.858821, 17.325663, 8.796164, 8.832798, 10.855644, 6.392669, 10.932993], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.009963644, 0.23096213, -0.097999215, 0.13101873, -0.01122554, -0.2164472, 0.044437822, -0.0, -0.09520057, 0.058936115, -0.029281516], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [1.1687953, 0.8638292, 0.7192578, 0.0, 0.0, 0.51107347, 0.4410602, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 6.0, 6.0, 0.13101873, -0.01122554, 2.0, 8.0, -0.0, -0.09520057, 0.058936115, -0.029281516], "split_indices": [0, 2, 2, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.665512, 13.623325, 40.042187, 7.567028, 6.0562973, 21.961706, 18.08048, 7.204111, 14.757595, 8.888675, 9.191806], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.045608845, 0.0937296, -0.09979079, -0.1780173, 0.100902185, 0.0663304, -0.08038656, -0.0, 0.049887117], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [1.1004585, 0.0, 0.80793613, 1.3767434, 0.09842704, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.0937296, 8.0, 2.0, 5.0, 0.0663304, -0.08038656, -0.0, 0.049887117], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.215546, 6.388379, 48.827168, 35.572792, 13.2543745, 6.069193, 29.503601, 5.4466906, 7.8076844], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.079435654, -0.2785601, -0.006998865, -0.02718109, -0.13498382, 0.056184977, -0.052931886, -0.03826584, 0.027097648], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.7246691, 0.34235632, 0.35151097, 0.0, 0.0, 0.0, 0.37199873, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 2.0, 4.0, -0.02718109, -0.13498382, 0.056184977, 7.0, -0.03826584, 0.027097648], "split_indices": [0, 0, 0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.355648, 12.571437, 38.78421, 7.003229, 5.5682077, 6.411443, 32.372765, 21.816502, 10.556263], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.007943158, -0.06596072, 0.087452106, 0.045055017, -0.24058555, 0.20065412, -0.0791881, -0.021672577, 0.04885648, -0.0913279, -0.03329016, 0.12779166, 0.006086731, -0.006874405, -0.034039624], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.34580442, 0.60654765, 0.5709926, 0.27868056, 0.024463952, 0.6692934, 0.0069143176, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 8.0, 6.0, 6.0, 4.0, 6.0, -0.021672577, 0.04885648, -0.0913279, -0.03329016, 0.12779166, 0.006086731, -0.006874405, -0.034039624], "split_indices": [0, 0, 0, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.502674, 28.72, 27.782673, 17.661339, 11.058661, 16.7798, 11.002872, 8.617989, 9.04335, 5.9888496, 5.069812, 6.608315, 10.171485, 5.9010105, 5.1018615], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.062188614, -0.0084309885, -0.05731316, -0.101894744, 0.08175569, 0.025025995, -0.07731032, -0.02259453, 0.07796631], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.36565757, 0.35625985, 0.0, 0.64937603, 0.6226693, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, -0.05731316, 2.0, 5.0, 0.025025995, -0.07731032, -0.02259453, 0.07796631], "split_indices": [2, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.89987, 40.037827, 14.862045, 20.142454, 19.895372, 9.119075, 11.02338, 10.581117, 9.314257], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.03358592, 0.17855293, -0.1022714, -0.008680483, 0.38296056, -0.05790672, -0.048819695, -0.050759513, 0.057182018, 0.15008377, 0.07518981, -0.03992913, 0.02274498], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [1.169561, 1.1434909, 0.11316022, 0.5342868, 0.017678738, 0.0, 0.23717313, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 6.0, 7.0, 5.0, -0.05790672, 5.0, -0.050759513, 0.057182018, 0.15008377, 0.07518981, -0.03992913, 0.02274498], "split_indices": [1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.06966, 27.813244, 29.256416, 14.76088, 13.052364, 9.183146, 20.073269, 8.537243, 6.2236376, 5.0440936, 8.00827, 12.413724, 7.6595454], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.010245961, 0.085127324, -0.16577831, -0.035594847, 0.13584249, -0.07477061, 0.0021055667, 0.07525143, 0.025475303], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.7615625, 0.44436124, 0.27181044, 0.0, 0.14368266, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 1.0, 9.0, -0.035594847, 3.0, -0.07477061, 0.0021055667, 0.07525143, 0.025475303], "split_indices": [1, 1, 1, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.318787, 40.271595, 16.047192, 7.3654428, 32.906155, 10.912871, 5.1343207, 8.292087, 24.614067], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0557792, -0.008108617, 0.19990648, 0.05374436, -0.067780584, 0.12758374, 0.0899373, -0.0, 0.07418754, 0.042702757, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.6468823, 0.6705637, 0.44067216, 0.3758755, 0.0, 0.0, 0.08257541, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 3.0, 4.0, -0.067780584, 0.12758374, 6.0, -0.0, 0.07418754, 0.042702757, -0.0], "split_indices": [1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.99338, 46.590813, 20.402567, 36.51608, 10.074735, 5.4521794, 14.950388, 29.597622, 6.918455, 9.443191, 5.5071974], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.08790834, -0.10517356, 0.14743078, 0.004097788, 0.22364283, 0.07712646, -0.032001156, 0.16248325, 0.046103142], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [1.8583715, 0.0, 0.6594647, 0.69214785, 0.74501276, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.10517356, 3.0, 4.0, 4.0, 0.07712646, -0.032001156, 0.16248325, 0.046103142], "split_indices": [0, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [69.000534, 7.4617558, 61.53878, 22.183632, 39.355145, 6.6278095, 15.555822, 5.6243258, 33.73082], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.008350278, -0.059448328, 0.23181143, -0.11365357, 0.020102654, 0.1137091, 0.009189591, 0.023766045, -0.047575027], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.92796946, 1.2090912, 0.3718006, 0.0, 0.39997718, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [8.0, 2.0, 9.0, -0.11365357, 6.0, 0.1137091, 0.009189591, 0.023766045, -0.047575027], "split_indices": [1, 2, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.203392, 44.874752, 13.328639, 8.448564, 36.42619, 6.898992, 6.4296465, 28.209496, 8.216694], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.07034722, -0.0, -0.113563105, 0.058423903, -0.07437516, -0.043522593, 0.003919881], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.1618567, 0.6578749, 0.0, 0.0, 0.21680568, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 3.0, -0.113563105, 0.058423903, 4.0, -0.043522593, 0.003919881], "split_indices": [1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.15915, 43.215157, 8.94399, 11.587329, 31.62783, 17.755919, 13.871911], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.07729452, -0.007976364, 0.19817935, 0.07544986, -0.07680596, 0.1156883, 0.10745736, -0.101407334, 0.008374392, 0.0759184, -0.022458345], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.58272696, 0.5814494, 0.2158187, 0.0, 0.7713715, 0.4848617, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 1.0, 8.0, 0.07544986, 3.0, 5.0, 0.10745736, -0.101407334, 0.008374392, 0.0759184, -0.022458345], "split_indices": [0, 0, 2, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.533195, 31.453215, 22.07998, 5.733203, 25.72001, 16.118399, 5.9615817, 6.9960036, 18.724009, 9.4804125, 6.6379876], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.008064289, 0.058848612, -0.09457273, -0.017393043, 0.08297981, -0.09341698, -0.0, 0.03922282, -0.048383623, 0.04652527, -0.024849122], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.30462483, 0.5079975, 0.47252488, 0.49079034, 0.0, 0.0, 0.23356426, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 7.0, 7.0, 3.0, 0.08297981, -0.09341698, 8.0, 0.03922282, -0.048383623, 0.04652527, -0.024849122], "split_indices": [2, 1, 2, 1, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.04696, 27.748058, 22.2989, 20.89346, 6.854599, 6.022324, 16.276577, 9.997254, 10.896205, 5.292578, 10.983999], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.05270695, 0.014993183, -0.21978071, -0.064041905, 0.08316538, -0.097166434, -0.0, -0.08500499, -0.0023463524], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 66, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.6661224, 0.8872222, 0.39871925, 0.36279047, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 9.0, 1.0, 0.08316538, -0.097166434, -0.0, -0.08500499, -0.0023463524], "split_indices": [2, 2, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.790123, 39.924145, 15.865981, 30.9655, 8.958644, 10.56688, 5.299101, 5.131129, 25.83437], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0069907736, -0.09863193, 0.13967954, -0.1172349, 0.0076679336, 0.137439, -0.010393712, -0.018615846, 0.03786574, 0.037529625, -0.04280702], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 67, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.78364885, 1.1731496, 1.1075051, 0.0, 0.24115562, 0.0, 0.30302382, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 3.0, -0.1172349, 3.0, 0.137439, 8.0, -0.018615846, 0.03786574, 0.037529625, -0.04280702], "split_indices": [1, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.583107, 35.445633, 21.137476, 8.993285, 26.452347, 6.3264956, 14.810981, 16.425472, 10.026875, 6.970554, 7.840427], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.02984193, 0.04658604, -0.22547652, -0.018309323, 0.07973966, -0.09282646, -0.017145013, -0.05436892, 0.058892194], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 68, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.9182955, 0.64501333, 0.18952459, 1.2215452, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 5.0, 7.0, 0.07973966, -0.09282646, -0.017145013, -0.05436892, 0.058892194], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.51746, 42.258987, 16.258474, 32.96144, 9.297545, 9.892662, 6.365812, 19.090736, 13.870705], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017087149, -0.1456006, 0.06256411, 0.034506056, -0.09670147, -0.11104603, 0.1847271, -0.074127495, 0.044673786, 0.0889162, 0.012667016], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 69, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.56173307, 0.9308238, 0.89537585, 0.0, 0.0, 0.65422165, 0.3470043, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 4.0, 0.034506056, -0.09670147, 8.0, 7.0, -0.074127495, 0.044673786, 0.0889162, 0.012667016], "split_indices": [2, 1, 1, 0, 0, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.855984, 17.953283, 39.9027, 7.1674356, 10.785847, 16.180475, 23.722223, 10.906755, 5.2737207, 12.301594, 11.42063], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.096229985, 0.04668507, 0.14243162, 0.16477624, -0.037273787, 0.09166371, -0.011683557, -0.080192745, 0.03979166], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 70, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1.1451402, 0.60225964, 0.0, 0.7591936, 1.4055705, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, 0.14243162, 5.0, 7.0, 0.09166371, -0.011683557, -0.080192745, 0.03979166], "split_indices": [0, 2, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.0206, 57.838055, 6.182548, 24.128675, 33.709377, 14.263669, 9.865006, 14.327496, 19.381882], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.013516252, 0.05657876, -0.13548265, 0.024709849, 0.06667308, 0.02500926, -0.087557115, 0.050171096, -0.00968463], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 71, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.43836853, 0.2562619, 0.5619421, 0.4017604, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [8.0, 7.0, 4.0, 4.0, 0.06667308, 0.02500926, -0.087557115, 0.050171096, -0.00968463], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [67.35649, 53.256214, 14.100278, 46.134174, 7.1220384, 5.8043957, 8.295882, 13.126721, 33.007454], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.03500862, -0.26345873, 0.07581779, -0.11961773, -0.020784767, 0.06568796, 0.00046782405, -0.04066024, 0.073784694], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 72, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [1.522568, 0.44202197, 0.4087861, 0.0, 0.0, 0.0, 0.9667924, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 6.0, 3.0, -0.11961773, -0.020784767, 0.06568796, 8.0, -0.04066024, 0.073784694], "split_indices": [1, 2, 2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.606968, 18.662481, 38.94449, 10.075362, 8.587118, 12.315579, 26.62891, 17.161552, 9.467357], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.03487493, -0.10810759, 0.18940745, -0.033880793, -0.108106576, -0.03300315, 0.12375054, -0.04199999, 0.06633453], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 73, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.85852134, 0.6937479, 0.924525, 0.89333045, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [8.0, 8.0, 5.0, 6.0, -0.108106576, -0.03300315, 0.12375054, -0.04199999, 0.06633453], "split_indices": [0, 2, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.51814, 38.809814, 11.708325, 31.168789, 7.641027, 5.0240827, 6.6842427, 22.60473, 8.564057], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.026441406, -0.18072908, 0.032781642, -0.08131733, -0.0033743072, 0.14818358, -0.16465153, -0.009565456, 0.09647581, -0.07720441, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 74, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.49772334, 0.1999174, 0.8908646, 0.0, 0.0, 0.8164665, 0.20783517, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 6.0, 7.0, -0.08131733, -0.0033743072, 4.0, 8.0, -0.009565456, 0.09647581, -0.07720441, -0.0], "split_indices": [2, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.203552, 14.101983, 37.10157, 8.423258, 5.6787252, 23.892235, 13.209333, 11.838285, 12.053949, 7.9547005, 5.2546334], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.013367895, -0.07248129, 0.052202452, -0.10163374, 0.18736625, -0.09229371, 0.0022297543, 0.12458134, 0.01724971], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 75, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.8037188, 0.0, 0.871051, 0.4653638, 0.5885624, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.07248129, 4.0, 6.0, 6.0, -0.09229371, 0.0022297543, 0.12458134, 0.01724971], "split_indices": [0, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.596294, 11.033044, 39.56325, 18.273527, 21.289722, 6.012621, 12.260906, 6.650696, 14.639028], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.032828767, 0.035703607, -0.18531586, 0.107068315, -0.03096325, -0.11301194, 0.002370615, -0.028709535, 0.040992577], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 76, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.6131907, 0.8882524, 0.71170807, 0.0, 0.37057447, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 2.0, 5.0, 0.107068315, 5.0, -0.11301194, 0.002370615, -0.028709535, 0.040992577], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.61205, 38.44414, 17.16791, 6.117509, 32.326633, 8.407664, 8.760245, 24.059605, 8.267027], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.09197464, 0.041424483, 0.2460283, -0.0144390045, 0.0053965137, 0.12016826, -0.019559646, 0.028773192], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 77, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.72966415, 0.0, 0.6274347, 0.36364913, 0.2375737, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.09197464, 3.0, 5.0, 7.0, 0.0053965137, 0.12016826, -0.019559646, 0.028773192], "split_indices": [2, 0, 2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.360737, 5.8034773, 51.55726, 10.711531, 40.84573, 5.039048, 5.6724825, 28.87765, 11.96808], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.015842818, -0.18016891, 0.040466644, -0.0009312027, -0.08172447, 0.18825102, -0.025274297, -0.0, 0.10910559, -0.0874566, 0.031164559], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 78, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.54199505, 0.21659353, 0.4318005, 0.0, 0.0, 0.42515382, 1.0694686, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 4.0, 4.0, -0.0009312027, -0.08172447, 3.0, 6.0, -0.0, 0.10910559, -0.0874566, 0.031164559], "split_indices": [1, 2, 1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.276627, 13.999274, 41.27735, 5.520485, 8.478789, 12.603198, 28.674154, 6.7441163, 5.8590813, 9.208503, 19.46565], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0187618, -0.108960785, 0.063821234, 0.002858414, -0.067271344, 0.11749962, -0.04260265, 0.011139347, 0.090630405, 0.0098993685, -0.046954658], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 79, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.33659896, 0.22757436, 0.26459688, 0.0, 0.0, 0.4032848, 0.1458774, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 5.0, 7.0, 0.002858414, -0.067271344, 5.0, 6.0, 0.011139347, 0.090630405, 0.0098993685, -0.046954658], "split_indices": [1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.063023, 13.9327345, 43.130287, 6.8952465, 7.0374885, 29.225176, 13.905111, 21.575071, 7.650104, 8.25739, 5.6477213], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.047573067, 0.012533069, 0.08128001, 0.07053928, -0.19898072, -0.008053404, 0.05495345, -0.09606825, -0.009769543], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 80, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.503513, 0.74410295, 0.0, 0.5539703, 0.21503586, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 7.0, 0.08128001, 4.0, 8.0, -0.008053404, 0.05495345, -0.09606825, -0.009769543], "split_indices": [0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [67.389725, 59.551334, 7.838391, 47.628757, 11.922577, 25.476484, 22.152271, 6.0150056, 5.9075713], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.02824234, 0.01482408, -0.13702114, -0.05625119, 0.0429146, 0.036348086, -0.007411143], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 81, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.3127095, 0.3509985, 0.0, 0.0, 0.31139705, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 2.0, -0.13702114, -0.05625119, 4.0, 0.036348086, -0.007411143], "split_indices": [2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [67.37355, 61.88568, 5.4878707, 6.4186993, 55.46698, 26.031404, 29.435577], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019660885, -0.10473532, 0.10508975, 0.022446385, -0.0689486, 0.1881415, -0.046810612, -0.05587626, 0.06670946, 0.00204464, 0.16580348], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 82, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.5983857, 0.47484177, 0.60017604, 0.65030795, 0.0, 1.3020188, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 9.0, 5.0, -0.0689486, 7.0, -0.046810612, -0.05587626, 0.06670946, 0.00204464, 0.16580348], "split_indices": [2, 2, 2, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.39139, 27.218506, 25.172886, 13.507085, 13.71142, 19.601133, 5.571754, 6.3971076, 7.109977, 13.95348, 5.6476526], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.056234095, 0.0011690406, -0.20974725, -0.06759197, 0.06458694, -0.037739187, -0.08343527, 0.06103857, -0.03647138], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 83, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.4600006, 0.52660906, 0.005178094, 0.0, 0.8046051, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 2.0, 5.0, -0.06759197, 7.0, -0.037739187, -0.08343527, 0.06103857, -0.03647138], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.806038, 35.771126, 13.034912, 6.9219937, 28.84913, 7.7820787, 5.252833, 16.777363, 12.071769], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0044786483, 0.079244204, -0.042384733, 0.20457327, -0.11616044, 0.10174102, 0.005449463, -0.08105586, 0.015819063], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 84, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.7150199, 0.0, 0.8892223, 0.23624939, 1.0218683, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.079244204, 2.0, 5.0, 6.0, 0.10174102, 0.005449463, -0.08105586, 0.015819063], "split_indices": [1, 0, 2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.015327, 8.028264, 46.98706, 10.08845, 36.898613, 5.071608, 5.016842, 19.294577, 17.604036], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.012544429, 0.09815966, -0.059995238, -0.00780521, -0.18888333, -0.030870536, 0.031092314, -0.10140443, 0.0036534294], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 85, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.8227303, 0.0, 0.2900955, 0.3765348, 0.4187129, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.09815966, 8.0, 6.0, 9.0, -0.030870536, 0.031092314, -0.10140443, 0.0036534294], "split_indices": [2, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.474186, 5.2048645, 45.26932, 33.52901, 11.740313, 18.617092, 14.911917, 6.635118, 5.1051946], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.019740498, 0.021992452, -0.0588664, -0.031150881, 0.106166475, 0.05475637, -0.057250474, -0.048483707, 0.18073446], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 86, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.4018212, 0.19948108, 0.0, 0.9193468, 2.4376209, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, -0.0588664, 4.0, 5.0, 0.05475637, -0.057250474, -0.048483707, 0.18073446], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.606186, 41.177555, 9.428631, 24.89928, 16.278275, 10.320389, 14.578893, 10.880393, 5.397881], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.020855365, -0.12965502, 0.1241507, -0.38032416, 0.06286205, 0.16363378, -0.07011803, -0.14106064, -0.06892111, 0.05500027, -0.0054237666], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 87, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.9479895, 1.7258847, 3.9916966, 0.030440807, 0.21689542, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 4.0, 3.0, 2.0, 0.16363378, -0.07011803, -0.14106064, -0.06892111, 0.05500027, -0.0054237666], "split_indices": [1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.03311, 33.61037, 24.422743, 14.401989, 19.208382, 11.105087, 13.317656, 7.273986, 7.1280036, 7.70732, 11.5010605], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.00059114554, 0.04065352, -0.092829004, -0.059106197, 0.11458445, 0.11750644, -0.005047978], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 88, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.72391033, 0.95974815, 0.0, 0.0, 1.5731859, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 2.0, -0.092829004, -0.059106197, 4.0, 0.11750644, -0.005047978], "split_indices": [0, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.436203, 52.804047, 5.6321573, 11.812266, 40.99178, 12.793899, 28.197882], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.029690918, -0.06251528, 0.060044754, -0.15307821, 0.011776485, 0.02487566, -0.092852965, -0.058355052, 0.03851951], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 89, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.4366398, 0.36533242, 0.0, 0.9373308, 0.7124597, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 4.0, 0.060044754, 4.0, 4.0, 0.02487566, -0.092852965, -0.058355052, 0.03851951], "split_indices": [2, 1, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.042187, 50.93154, 6.1106424, 23.080978, 27.850563, 9.133145, 13.947834, 9.507419, 18.343145], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.07781532, 0.20479557, 0.022807179, 0.0887803, 0.15008835, 0.07322102, -0.12374097, -0.0, 0.06340357, 0.05571792, 0.002522324, -0.060476333, -0.0015015525], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 90, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.46899047, 0.014724016, 0.38274378, 0.0, 0.12360254, 0.26543376, 0.09835677, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [2.0, 3.0, 7.0, 0.0887803, 5.0, 5.0, 5.0, -0.0, 0.06340357, 0.05571792, 0.002522324, -0.060476333, -0.0015015525], "split_indices": [1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [70.07019, 19.806993, 50.263195, 5.0900164, 14.716977, 38.277973, 11.985225, 5.023323, 9.693654, 12.74546, 25.532513, 6.3089166, 5.6763077], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.018567871, 0.09844444, -0.1137603, -0.06301906, 0.19853505, -0.19564791, 0.06558079, 0.07546896, 0.0077411956, -0.006093683, -0.10842817, -0.040012762, 0.07698029], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 91, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.74032724, 0.9473779, 0.5685413, 0.0, 0.17582309, 0.7103447, 0.5007127, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 1.0, 7.0, -0.06301906, 8.0, 4.0, 7.0, 0.07546896, 0.0077411956, -0.006093683, -0.10842817, -0.040012762, 0.07698029], "split_indices": [2, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.32159, 28.367302, 35.954285, 6.4349904, 21.932312, 25.004044, 10.950242, 15.988538, 5.9437747, 12.990474, 12.01357, 5.2550855, 5.695156], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.023175418, -0.08842754, 0.062907085, 0.20236771, -0.020944232, 0.014723112, 0.09843625, -0.044162318, 0.027124042], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 92, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.7196134, 0.0, 0.6312787, 0.33350778, 0.4826496, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.08842754, 4.0, 6.0, 7.0, 0.014723112, 0.09843625, -0.044162318, 0.027124042], "split_indices": [2, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.33484, 5.2653937, 51.069447, 19.093128, 31.976318, 9.589357, 9.503772, 15.255201, 16.721117], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0031594192, -0.17019533, 0.12337465, -0.08779986, -0.053047623, 0.074901715, 0.02974214, -0.02233015, -0.0016970237, -0.010837402, 0.037915636], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 93, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [1.0901774, 0.27227098, 0.30634177, 0.0, 0.0048700124, 0.0, 0.12934156, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 5.0, 6.0, -0.08779986, 7.0, 0.074901715, 8.0, -0.02233015, -0.0016970237, -0.010837402, 0.037915636], "split_indices": [1, 2, 1, 0, 2, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.364826, 21.469059, 27.895767, 9.346239, 12.122819, 10.712083, 17.183685, 6.5060635, 5.6167555, 9.976036, 7.2076488], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.028363846, 0.073990524, -0.07402854, 0.069375, -0.1336199, -0.069147825, -0.008743604], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 94, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.7051382, 0.0, 0.9156023, 0.0, 0.3928532, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.073990524, 1.0, 0.069375, 6.0, -0.069147825, -0.008743604], "split_indices": [2, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.07226, 6.904081, 48.168182, 7.1223993, 41.045784, 20.175188, 20.870596], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0061987005, 0.091647714, -0.06114571, -0.086225435, 0.12793757, -0.06956228, 0.023323774, -0.04024744, -0.004398635, 0.055253003, -0.025734149], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 95, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.27582365, 1.1442105, 0.5137584, 0.025711812, 0.0, 0.0, 0.43159145, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 6.0, 5.0, 0.12793757, -0.06956228, 3.0, -0.04024744, -0.004398635, 0.055253003, -0.025734149], "split_indices": [0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.827988, 17.024282, 32.803707, 11.407611, 5.616671, 10.672133, 22.131575, 5.471075, 5.9365354, 9.036903, 13.09467], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.017343966, 0.12773068, -0.08758285, -0.04311995, 0.26122487, -0.09051145, -0.0, 0.11670967, 0.023879781, 0.036159974, -0.06258489], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 96, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.5677322, 0.69753575, 0.6861961, 0.0, 0.21449459, 0.0, 0.74696577, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 3.0, -0.04311995, 5.0, -0.09051145, 7.0, 0.11670967, 0.023879781, 0.036159974, -0.06258489], "split_indices": [0, 0, 2, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.01144, 16.887985, 37.12346, 5.294892, 11.593093, 9.751147, 27.372309, 5.811262, 5.781831, 17.32679, 10.045519], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0476432, 0.045902807, -0.12165814, 0.03737803, -0.011772418, -0.2910165, 0.009488056, -0.013324316, 0.006799376, -0.021129765, -0.13810664, 0.045553356, -0.015507308], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 97, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.388825, 0.122106954, 0.7230303, 0.0, 0.019014295, 0.43609345, 0.17635146, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 5.0, 7.0, 0.03737803, 2.0, 5.0, 8.0, -0.013324316, 0.006799376, -0.021129765, -0.13810664, 0.045553356, -0.015507308], "split_indices": [1, 2, 1, 0, 1, 2, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.25704, 23.095648, 30.16139, 10.010779, 13.084868, 12.963802, 17.197588, 8.059549, 5.0253186, 6.5014095, 6.462393, 5.2151446, 11.982444], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.037803017, -0.027397824, 0.12758504, 0.038354598, -0.07472886, 0.21136428, -0.011517433, 0.035471663, -0.040242344, 0.15276383, -0.0057075787], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 98, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.3584657, 0.25638533, 0.38156343, 0.0, 0.3318345, 1.2647252, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 6.0, 0.038354598, 1.0, 7.0, -0.011517433, 0.035471663, -0.040242344, 0.15276383, -0.0057075787], "split_indices": [1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.927147, 33.314655, 24.612494, 6.892668, 26.421988, 16.575024, 8.037469, 5.590633, 20.831354, 6.8906236, 9.684401], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.034865085, -0.0, -0.092435986, 0.0863659, -0.027201759, -0.030753994, 0.10424779, -0.06544211, 0.0044729947], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 99, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.57284564, 0.12921044, 0.0, 0.7164489, 0.3490518, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 3.0, -0.092435986, 2.0, 2.0, -0.030753994, 0.10424779, -0.06544211, 0.0044729947], "split_indices": [0, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.569298, 51.78549, 5.7838116, 12.30717, 39.478317, 7.2717805, 5.0353894, 6.807422, 32.670895], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.041128155, -0.026578518, 0.17944172, -0.113401756, 0.19923106, 0.14020532, 0.028727705, 0.0056898003, -0.083692536, 0.0783028, 0.02537357, -0.024086252, 0.04619707], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 100, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6916365, 0.96549046, 0.98629063, 0.8205178, 0.039133668, 0.0, 0.25415367, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 7.0, 8.0, 3.0, 4.0, 0.14020532, 9.0, 0.0056898003, -0.083692536, 0.0783028, 0.02537357, -0.024086252, 0.04619707], "split_indices": [2, 1, 2, 1, 2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [70.71369, 47.531242, 23.182451, 35.010456, 12.520786, 7.0157757, 16.166676, 19.61262, 15.397838, 6.740222, 5.7805643, 8.409052, 7.757623], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.056733225, -0.1569743, 0.11569789, -0.0051102648, -0.071006365, 0.2418206, 0.027032014, 0.090135835, 0.01966732, -0.043079823, 0.034940064], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 101, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.8320083, 0.12782434, 0.54604405, 0.0, 0.0, 0.15865505, 0.5026266, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 4.0, 5.0, -0.0051102648, -0.071006365, 8.0, 3.0, 0.090135835, 0.01966732, -0.043079823, 0.034940064], "split_indices": [0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.91245, 13.094722, 50.817726, 5.647867, 7.446854, 19.799208, 31.01852, 13.902699, 5.8965073, 10.074249, 20.944271], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.037629053, -0.12306009, 0.119648464, -0.077433094, -0.07426844, 0.0711529, -0.024588363, -0.0019037717, -0.050573934], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 102, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.7518263, 0.15531874, 0.49453944, 0.16744849, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 8.0, 5.0, 4.0, -0.07426844, 0.0711529, -0.024588363, -0.0019037717, -0.050573934], "split_indices": [1, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.833305, 35.437508, 18.395798, 27.70821, 7.7292976, 11.823129, 6.5726695, 16.784435, 10.923774], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.017718991, -0.08116888, 0.108570695, -0.0, -0.07148064, -0.00901432, 0.09214271, -0.04024623, 0.011774046], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 103, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.42120418, 0.43447644, 0.5110748, 0.14350331, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 7.0, 7.0, 2.0, -0.07148064, -0.00901432, 0.09214271, -0.04024623, 0.011774046], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.806858, 34.581623, 16.225231, 23.799376, 10.78225, 9.75828, 6.4669523, 5.305168, 18.494207], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.03761288, -0.01871047, 0.14505441, 0.03562834, -0.086754724, 0.08540081, -0.026835611, -0.019996777, 0.043684155], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 104, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.33409607, 0.54759234, 0.6618366, 0.35045457, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 9.0, 3.0, -0.086754724, 0.08540081, -0.026835611, -0.019996777, 0.043684155], "split_indices": [2, 2, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.67279, 33.832253, 17.840538, 28.587473, 5.2447796, 11.316001, 6.524536, 14.545346, 14.042127], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.015518715, 0.11837502, -0.038137, 0.061407965, -0.14046286, -0.025131054, 0.048740786, -0.057166975, 0.006306476], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 105, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [1.1085474, 0.0, 0.48756084, 0.36832842, 0.21188778, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.11837502, 6.0, 3.0, 9.0, -0.025131054, 0.048740786, -0.057166975, 0.006306476], "split_indices": [2, 0, 2, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.011177, 5.768422, 45.242756, 22.64033, 22.602428, 8.95613, 13.684198, 17.58626, 5.0161686], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.07985056, -0.18738365, 0.024560498, -0.11613583, -0.08325502, 0.1576887, -0.057318013, 0.03822843, -0.07604163, 0.026072027, 0.071733534], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 106, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.5999173, 0.095332384, 0.7897218, 0.6087792, 0.0, 0.046132594, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 8.0, 4.0, -0.08325502, 6.0, -0.057318013, 0.03822843, -0.07604163, 0.026072027, 0.071733534], "split_indices": [0, 0, 0, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.572063, 24.993896, 25.578169, 15.857179, 9.136718, 16.267042, 9.311127, 5.480895, 10.376284, 10.46511, 5.8019314], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.07370437, -0.0, -0.28771394, -0.15441519, 0.1560265, -0.13470528, -0.0338805, -0.023462025, -0.07553487, -0.0, 0.116712235], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 107, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.88245, 1.0293279, 0.29536963, 0.10537487, 0.8108621, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 4.0, 7.0, 6.0, -0.13470528, -0.0338805, -0.023462025, -0.07553487, -0.0, 0.116712235], "split_indices": [2, 2, 1, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.988712, 40.719845, 13.268867, 20.301857, 20.417988, 5.7869115, 7.481955, 12.975975, 7.325882, 12.615587, 7.8024006], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.015772013, -0.13571429, 0.14588712, -0.35443196, 0.046537068, 0.25442904, -0.0, -0.13890463, -0.056691274, 0.1044962, -0.050468963, -9.4877265e-05, 0.11000062, -0.13412325, 0.09173959], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 108, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.1921185, 1.1482782, 0.49894923, 0.07942462, 1.094221, 0.5655118, 2.21864, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 5.0, 4.0, 3.0, 7.0, 7.0, 6.0, -0.13890463, -0.056691274, 0.1044962, -0.050468963, -9.4877265e-05, 0.11000062, -0.13412325, 0.09173959], "split_indices": [0, 1, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.42482, 26.620438, 31.80438, 11.9197, 14.700737, 17.492971, 14.31141, 5.746115, 6.1735845, 6.0471888, 8.653548, 5.3825865, 12.110385, 5.518229, 8.793181], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.03626586, -0.15285145, 0.0651284, -0.0, -0.1901037, -0.067850865, 0.17314205, -0.029952196, -0.09983657, 0.12463884, -0.00025792624], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 109, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.667601, 0.13567412, 0.96521854, 0.0, 0.19076043, 0.0, 0.9867426, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 2.0, -0.0, 4.0, -0.067850865, 7.0, -0.029952196, -0.09983657, 0.12463884, -0.00025792624], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.89111, 25.290375, 28.600733, 5.539874, 19.750502, 7.190158, 21.410576, 13.56794, 6.1825614, 8.608751, 12.801824], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.022385016, -0.114426374, 0.1366005, -0.18674216, 0.04148641, 0.20049328, -0.0055887634, -0.0325607, -0.13585702, 0.04686824, -0.006331518, 0.013823499, 0.08492163], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 110, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.0780506, 0.5524329, 0.28706744, 0.57613325, 0.12654035, 0.20243776, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 7.0, 7.0, 5.0, 8.0, 2.0, -0.0055887634, -0.0325607, -0.13585702, 0.04686824, -0.006331518, 0.013823499, 0.08492163], "split_indices": [2, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [71.75471, 46.021038, 25.733664, 31.742035, 14.279004, 18.479918, 7.253746, 25.910898, 5.831136, 5.0758195, 9.203185, 7.4040213, 11.075897], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.034671124, -0.13677168, 0.0053856797, 0.036113605, -0.08303613, 0.035281613, -0.0066373157], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 111, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [1.2024298, 0.0, 0.5345563, 0.28867084, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, -0.13677168, 9.0, 4.0, -0.08303613, 0.035281613, -0.0066373157], "split_indices": [2, 0, 2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [67.44672, 5.172115, 62.274605, 57.264996, 5.009609, 24.158283, 33.106712], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.044087045, 0.09174537, -0.07442315, -0.07068535, 0.1817652, -0.086374655, 0.011997187, 0.034409903, 0.09749314], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 112, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.79962385, 0.754439, 0.0, 0.47238436, 0.23397815, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 3.0, -0.07442315, 4.0, 8.0, -0.086374655, 0.011997187, 0.034409903, 0.09749314], "split_indices": [1, 1, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.91568, 48.957848, 6.9578342, 17.138056, 31.81979, 5.54894, 11.589116, 23.373335, 8.446456], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.080981106, -0.06652953, -0.005731197, 0.052681897, -0.00806564, -0.12596339, -0.011300911, 0.004893001, -0.04141038, 0.03614727, -0.002118507, -0.059328638], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 113, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.274352, 0.2051541, 0.08352701, 0.011422924, 0.0, 0.2878348, 0.089626566, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 6.0, 2.0, 0.052681897, 7.0, 6.0, -0.011300911, 0.004893001, -0.04141038, 0.03614727, -0.002118507, -0.059328638], "split_indices": [1, 2, 2, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.998055, 21.81003, 27.188025, 11.319625, 10.490404, 15.126571, 12.061454, 6.185095, 5.13453, 7.8721113, 7.254459, 5.479052, 6.5824018], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.016479045, -0.09865301, 0.25114474, -0.0395462, -0.10544255, -0.0044426993, 0.14371449, -0.0312428, 0.0314479], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 114, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [1.2112089, 0.58672726, 0.83133024, 0.34793735, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [8.0, 7.0, 4.0, 5.0, -0.10544255, -0.0044426993, 0.14371449, -0.0312428, 0.0314479], "split_indices": [0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.6425, 41.80982, 11.832684, 35.145233, 6.664584, 5.6895075, 6.143176, 24.952198, 10.193033], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.054465767, -0.05709926, -0.01741328, -0.079499945, 0.054372795, 0.042292565, 0.0015926737], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 115, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.2259375, 0.0, 0.75889254, 0.0, 0.120892555, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.05709926, 3.0, -0.079499945, 5.0, 0.042292565, 0.0015926737], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.666126, 8.963361, 39.702766, 8.632212, 31.070555, 9.822121, 21.248432], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.041563272, -0.1609735, 0.03933857, -0.08664992, -0.09800897, -0.033901032, 0.095769115, -0.0, -0.043691404, 0.051877175, -0.02205363], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 116, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.5186842, 0.10695797, 0.277055, 0.0, 0.077534184, 0.0, 0.33304125, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 1.0, 3.0, -0.08664992, 3.0, -0.033901032, 8.0, -0.0, -0.043691404, 0.051877175, -0.02205363], "split_indices": [0, 0, 2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.776592, 20.607086, 30.169508, 5.0918474, 15.515239, 7.404751, 22.764755, 5.3803577, 10.134881, 16.028513, 6.7362437], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.036165204, -0.080777876, 0.007824937, 0.0584321, -0.023221795, -0.063290074, 0.011799425], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 117, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.5909556, 0.0, 0.29317245, 0.0, 0.50166965, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.080777876, 1.0, 0.0584321, 3.0, -0.063290074, 0.011799425], "split_indices": [0, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.877434, 8.101522, 45.775913, 6.227036, 39.548878, 9.690418, 29.858458], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.04589109, 0.068461664, -0.09603397, -0.18668348, -0.0, -0.0, -0.070159495, 0.044811253, -0.04350707], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 118, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.8173364, 0.0, 0.43194494, 0.20653999, 0.5830343, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.068461664, 6.0, 2.0, 4.0, -0.0, -0.070159495, 0.044811253, -0.04350707], "split_indices": [2, 0, 2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.835743, 8.086578, 49.74917, 24.823866, 24.9253, 5.5923214, 19.231544, 12.084651, 12.84065], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.013619529, 0.062398735, -0.09557893, 0.026540572, 0.13329048, -0.06512802, 0.001325104, 0.029248111, -0.04047696, 0.05296175, 0.018694576], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 119, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.29566264, 0.08028613, 0.22572523, 0.3331589, 0.0019581914, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 4.0, 4.0, 4.0, -0.06512802, 0.001325104, 0.029248111, -0.04047696, 0.05296175, 0.018694576], "split_indices": [0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.009922, 38.26237, 15.747552, 27.259214, 11.003157, 7.0316963, 8.715856, 19.642916, 7.616299, 5.037367, 5.9657893], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.014656284, 0.008201596, -0.06587604, -0.022876026, 0.07349139, -0.022826575, 0.021668559], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 120, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.3502792, 0.49427602, 0.0, 0.29221487, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, -0.06587604, 7.0, 0.07349139, -0.022826575, 0.021668559], "split_indices": [1, 1, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.88053, 62.39322, 6.4873104, 55.579117, 6.814103, 36.44113, 19.137985], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.014678047, 0.008664704, -0.081903435, 0.08751566, -0.07324596, -0.05329209, 0.047804717, 0.06588351, -0.046191193], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 121, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.4373625, 0.41313604, 0.0, 0.655999, 0.7530522, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, -0.081903435, 2.0, 2.0, -0.05329209, 0.047804717, 0.06588351, -0.046191193], "split_indices": [2, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.752556, 61.752064, 5.000493, 32.050854, 29.701208, 6.221309, 25.829546, 5.778469, 23.922739], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.060977336, -0.006815185, -0.13660645, -0.21121092, 0.10116968, -0.13878919, -0.020177012, 0.015664041, 0.08593427], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 122, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1.1696944, 1.1672081, 0.0, 0.5824853, 0.2595596, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 3.0, -0.13660645, 4.0, 8.0, -0.13878919, -0.020177012, 0.015664041, 0.08593427], "split_indices": [0, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.069077, 50.416, 5.653077, 17.41766, 32.99834, 5.282769, 12.134891, 27.542336, 5.456006], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.051389728, -0.13067573, 0.11883176, 0.20100535, -0.014905262, -0.032972846, 0.093901075, 0.04654385, -0.031654533], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 123, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [1.7321693, 0.0, 0.5354233, 1.0816944, 0.29279712, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.13067573, 6.0, 3.0, 3.0, -0.032972846, 0.093901075, 0.04654385, -0.031654533], "split_indices": [1, 0, 1, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.129932, 5.375304, 45.75463, 28.492435, 17.262194, 7.31401, 21.178425, 5.3693757, 11.892818], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.01710184, -0.07296966, 0.085980274, 0.050175216, -0.2241471, -0.025568767, 0.19701302, 0.058875456, -0.08710932, -0.11211879, -0.0106296325, 0.10136029, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 124, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.3208382, 0.71261716, 0.39120588, 1.0772703, 0.42685723, 0.0, 0.33877563, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 4.0, 5.0, 8.0, -0.025568767, 7.0, 0.058875456, -0.08710932, -0.11211879, -0.0106296325, 0.10136029, -0.0], "split_indices": [1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.847504, 35.739723, 18.10778, 19.699608, 16.040117, 6.860632, 11.247148, 14.288034, 5.411573, 8.105288, 7.9348288, 6.131895, 5.115253], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.033405844, -0.08442623, 0.042113405, -0.1832065, 0.02828394, 0.04632892, -0.01683057, 0.0090304185, -0.0773398], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 125, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.20586032, 0.5875743, 0.2457853, 0.3675605, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 7.0, 3.0, 2.0, 0.02828394, 0.04632892, -0.01683057, 0.0090304185, -0.0773398], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.407337, 30.803133, 19.604202, 20.181915, 10.621218, 9.35355, 10.250653, 5.0361724, 15.145743], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.03663374, -0.08975272, 0.108286485, 0.046685264, -0.13737224, -0.055599738, 0.007219671], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 126, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.0863115, 0.5590996, 0.0, 0.0, 0.32675993, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 1.0, 0.108286485, 0.046685264, 8.0, -0.055599738, 0.007219671], "split_indices": [0, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.186573, 45.17662, 5.009951, 6.6326237, 38.544, 30.079384, 8.464613], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0058616945, -0.0820405, 0.09778478, 0.027127473, -0.10718551, 0.2077905, -0.020776078, -0.074464776, 0.06656242, 0.037762295, 0.09598916], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 127, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.45996222, 0.89844584, 0.54748046, 1.1817653, 0.0, 0.07730734, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 5.0, 6.0, -0.10718551, 3.0, -0.020776078, -0.074464776, 0.06656242, 0.037762295, 0.09598916], "split_indices": [0, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.736637, 27.479136, 27.257502, 20.060738, 7.418397, 16.648241, 10.609261, 7.9842157, 12.076523, 11.363502, 5.2847404], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.047290396, 0.06869837, 0.012789347, -0.1301819, 0.063975744, 0.009013937, -0.09166403, 0.08576143, -0.017329842], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 128, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.3441181, 0.0, 0.37704557, 0.411445, 1.0915897, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.06869837, 2.0, 1.0, 4.0, 0.009013937, -0.09166403, 0.08576143, -0.017329842], "split_indices": [2, 0, 1, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.21615, 7.9277673, 50.28838, 12.335777, 37.952602, 6.5811234, 5.754654, 13.259005, 24.6936], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.011487014, 0.076758325, -0.11481824, -0.045993716, 0.16730939, -0.055727135, -0.070954174, 0.117046095, 0.020690966, -0.03605313, 0.00029214678], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 129, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.5550528, 0.6971092, 0.16127759, 0.0, 0.45026577, 0.08851452, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 7.0, -0.045993716, 3.0, 4.0, -0.070954174, 0.117046095, 0.020690966, -0.03605313, 0.00029214678], "split_indices": [2, 2, 0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.48716, 31.163116, 27.324043, 8.293548, 22.869568, 19.927568, 7.396476, 5.7822595, 17.087309, 9.627959, 10.299609], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.028854027, -0.06184006, 0.09030205, -0.06745111, -0.025973642, 0.059300046, -0.0, 0.0665426, -0.021966793], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 130, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.29136062, 0.31260735, 0.14272498, 0.0, 0.58438116, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [8.0, 1.0, 2.0, -0.06745111, 2.0, 0.059300046, -0.0, 0.0665426, -0.021966793], "split_indices": [0, 1, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [72.431755, 57.761784, 14.6699705, 8.964635, 48.79715, 5.923217, 8.746753, 6.8842072, 41.91294], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.10450057, 0.057562247, 0.028458083, -0.22321606, -0.08853728, 0.13713016, -0.0921255, -0.023099482, -0.007321219, -0.039250728, 0.14641286, 0.0016152124], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 131, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.41166508, 0.61098385, 0.52727056, 0.0, 0.1328373, 0.02371405, 1.3004571, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 4.0, 0.028458083, 6.0, 7.0, 5.0, -0.0921255, -0.023099482, -0.007321219, -0.039250728, 0.14641286, 0.0016152124], "split_indices": [1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.27344, 23.37861, 42.89483, 8.486197, 14.892413, 14.641379, 28.25345, 8.330201, 6.562212, 7.3851166, 7.2562623, 6.766655, 21.486795], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.01925929, -0.12066957, 0.08067493, -0.0, -0.09490386, 0.12848723, -0.037598882, 0.07766361, 0.020448633], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 132, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.5140046, 0.42091358, 0.4320612, 0.0, 0.0, 0.22603899, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 7.0, 9.0, -0.0, -0.09490386, 3.0, -0.037598882, 0.07766361, 0.020448633], "split_indices": [1, 2, 1, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.143543, 16.944435, 41.199104, 11.12749, 5.8169456, 34.137688, 7.0614176, 9.232585, 24.905104], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.032324135, -0.12491887, 0.08600272, -0.049086332, -0.016907118, -0.007238512, 0.2692941, 0.024762144, -0.023809655, 0.13873683, 0.010986672], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 133, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.443816, 0.003564924, 0.705969, 0.0, 0.0, 0.18042485, 0.55160326, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 6.0, 7.0, -0.049086332, -0.016907118, 3.0, 5.0, 0.024762144, -0.023809655, 0.13873683, 0.010986672], "split_indices": [1, 2, 2, 0, 0, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.769444, 12.108726, 38.660717, 5.921247, 6.187478, 25.900126, 12.760592, 10.856207, 15.043919, 6.1978326, 6.5627604], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.03162084, 0.1550521, -0.118131384, 0.058159795, 0.089167014, -0.10385381, -0.0481754, 0.000777647, 0.025499316, -0.07154821, 0.03549863], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 134, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.8903404, 0.18464258, 0.54992235, 0.008787379, 0.0, 0.0, 1.0088896, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [2.0, 7.0, 4.0, 4.0, 0.089167014, -0.10385381, 5.0, 0.000777647, 0.025499316, -0.07154821, 0.03549863], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.226658, 16.23863, 36.98803, 10.913406, 5.325225, 7.420408, 29.56762, 5.124935, 5.7884707, 13.875633, 15.691987], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.01576034, 0.063469596, -0.08833343, -0.018906057, 0.14830779, -0.06667396, -0.022601988, -0.012381866, 0.090735085, 0.013587662, -0.050225694], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 135, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.29482403, 0.27288067, 0.20906545, 0.0, 0.4767836, 0.0, 0.21441473, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 7.0, -0.018906057, 3.0, -0.06667396, 5.0, -0.012381866, 0.090735085, 0.013587662, -0.050225694], "split_indices": [0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.866627, 22.763086, 26.10354, 8.768347, 13.9947405, 7.3095326, 18.794008, 6.3133764, 7.6813636, 12.793011, 6.0009975], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.01538759, -0.05024256, 0.062410057, -0.09156149, 0.03462109, 0.031217443, -0.045651533], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 136, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.4212304, 0.34065425, 0.0, 0.49395052, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, 0.062410057, 2.0, 0.03462109, 0.031217443, -0.045651533], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.775772, 47.5763, 6.199469, 38.919205, 8.657097, 8.64636, 30.272846], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.016743302, 0.021731716, -0.09204045, -0.0074910186, 0.06357247, 0.008741686, -0.03288449], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 137, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.6433643, 0.29292583, 0.0, 0.17120606, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 9.0, -0.09204045, 7.0, 0.06357247, 0.008741686, -0.03288449], "split_indices": [0, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.497345, 47.74455, 5.7527966, 41.762115, 5.982431, 30.54254, 11.219577], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.03037868, -0.08470125, 0.044221856, -0.14584818, 0.045546785, 0.15208063, -0.066042446, -0.0021256336, -0.06067088, 0.12060218, 0.0019133837], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 138, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.2522429, 0.54253393, 0.7480138, 0.2082178, 0.0, 0.6421962, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 8.0, 8.0, 3.0, 0.045546785, 7.0, -0.066042446, -0.0021256336, -0.06067088, 0.12060218, 0.0019133837], "split_indices": [2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.320732, 35.022877, 24.297857, 28.469107, 6.5537696, 17.827381, 6.4704766, 9.063716, 19.40539, 5.6648803, 12.162501], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.004622707, -0.024896862, 0.043590315, 0.0342822, -0.15694666, 0.05410121, -0.0052327868, -0.07226586, 0.0020679394], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 139, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.1647502, 0.42109326, 0.0, 0.29101193, 0.2544425, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 6.0, 0.043590315, 3.0, 8.0, 0.05410121, -0.0052327868, -0.07226586, 0.0020679394], "split_indices": [1, 1, 0, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.972748, 50.627365, 5.345384, 34.94818, 15.679182, 8.995228, 25.952953, 10.460226, 5.2189565], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04522977, -0.027437031, 0.17731826, -0.10844626, 0.1435485, 0.23316832, 0.01804889, 0.0102287205, -0.06939704, 0.0032878122, 0.072725564, 0.024627991, 0.0878638], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 140, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6840561, 0.6356221, 0.12140018, 0.5795578, 0.1621606, 0.08393037, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 7.0, 6.0, 2.0, 3.0, 8.0, 0.01804889, 0.0102287205, -0.06939704, 0.0032878122, 0.072725564, 0.024627991, 0.0878638], "split_indices": [2, 1, 1, 1, 2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.19899, 44.01817, 24.18082, 30.512577, 13.505592, 15.12611, 9.054711, 14.083689, 16.428888, 6.6755953, 6.829997, 5.432048, 9.694062], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.006813932, 0.09485901, -0.06479005, -0.0, 0.19185385, -0.14389482, 0.024209486, -0.004673103, 0.0012268192, 0.09302632, -0.0, -0.008509741, 0.07327236], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 141, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.45739838, 0.31885934, 1.5037422, 0.0021153172, 0.38219625, 0.0, 0.4104089, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 5.0, 5.0, 2.0, 8.0, -0.14389482, 8.0, -0.004673103, 0.0012268192, 0.09302632, -0.0, -0.008509741, 0.07327236], "split_indices": [2, 0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [70.10068, 31.92147, 38.17921, 16.210537, 15.710934, 6.1669693, 32.01224, 7.0151334, 9.195403, 9.371765, 6.3391685, 26.174582, 5.83766], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.028943306, 0.02677536, -0.19917431, 0.10633582, -0.072267644, -0.08516746, -0.014042166, -0.01259701, 0.084732875, -0.060632873, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 142, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5847487, 0.366419, 0.1495465, 0.71428347, 0.20867239, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, 9.0, 3.0, 7.0, -0.08516746, -0.014042166, -0.01259701, 0.084732875, -0.060632873, -0.0], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.26971, 44.092857, 14.17685, 24.97038, 19.122478, 8.134495, 6.042356, 13.659457, 11.310923, 6.6812115, 12.441267], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.020955369, -0.045914292, 0.045818422, -0.051091187, 0.13030973, 0.047737952, -0.04555317, 0.08996062, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 143, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.23106633, 0.0, 0.4137919, 0.50794923, 0.6216399, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.045914292, 4.0, 4.0, 6.0, 0.047737952, -0.04555317, 0.08996062, -0.0], "split_indices": [0, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.268032, 5.5123596, 47.755672, 21.87607, 25.8796, 6.5784373, 15.297632, 10.989071, 14.890531], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.03864035, 0.06775737, -0.0067736506, 0.07166889, -0.07500974, -0.07798051, 0.024673928], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 144, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.4699054, 0.0, 0.7190309, 0.0, 1.0446626, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.06775737, 1.0, 0.07166889, 6.0, -0.07798051, 0.024673928], "split_indices": [2, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.51136, 9.710176, 41.801186, 8.224401, 33.576782, 15.423037, 18.153746], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.026655737, -0.060380746, 0.016541952, 0.08588791, -0.043704856, -0.040956806, 0.056211043, 0.0067717903, -0.0552787], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 145, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.39096698, 0.0, 0.17383566, 0.47227004, 0.21721862, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.060380746, 6.0, 2.0, 7.0, -0.040956806, 0.056211043, 0.0067717903, -0.0552787], "split_indices": [2, 0, 2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.942062, 9.241562, 38.7005, 18.565392, 20.135109, 5.3999863, 13.165406, 13.770036, 6.365073], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.030823246, 0.105234504, -0.04263356, 0.2737792, -0.051988114, -0.18556862, 0.08491125, 0.09962755, 0.03680444, -0.020638654, -0.070520215], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 146, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.27656007, 1.2368675, 1.1835935, 0.057071567, 0.0, 0.056614816, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 7.0, 3.0, -0.051988114, 7.0, 0.08491125, 0.09962755, 0.03680444, -0.020638654, -0.070520215], "split_indices": [1, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.630226, 24.122766, 23.507462, 15.262878, 8.859888, 16.84955, 6.6579113, 9.670931, 5.591948, 6.3289495, 10.5206], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.034879856, 0.005283625, 0.06525191, 0.07998568, -0.0585335, -0.054458752, 0.058999185, 0.050849695, -0.07112987], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 147, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.26273766, 0.23115914, 0.0, 0.73364365, 1.0743967, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 4.0, 0.06525191, 4.0, 4.0, -0.054458752, 0.058999185, 0.050849695, -0.07112987], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.944107, 46.05671, 5.887398, 21.823969, 24.23274, 6.297132, 15.526836, 10.41866, 13.81408], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.016790904, 0.044962082, -0.044652417, -0.017365858, 0.20505977, -0.035864573, 0.008139389, 0.10933549, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 148, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.28541526, 0.56354654, 0.0, 0.19213876, 0.4969136, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 7.0, -0.044652417, 3.0, 3.0, -0.035864573, 0.008139389, 0.10933549, -0.0], "split_indices": [0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.960457, 53.237904, 7.7225537, 38.511738, 14.726167, 11.8531, 26.658636, 7.7196794, 7.0064874], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.031128354, -0.030051703, 0.059711248, 0.10007481, -0.03426779, 0.012747307, 0.0756787, 0.0040561715, -0.036096685], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 149, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.22021708, 0.0, 0.19594657, 0.26906195, 0.072873436, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.030051703, 7.0, 5.0, 6.0, 0.012747307, 0.0756787, 0.0040561715, -0.036096685], "split_indices": [0, 0, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.162655, 9.145934, 48.01672, 34.28897, 13.72775, 26.281155, 8.007817, 8.621069, 5.106681], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.029628903, 0.042619865, -0.20982161, -0.041866895, 0.11001563, -0.0010970528, -0.10363386, 0.024679309, -0.044288144, 0.07154961, 0.013497886], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 150, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.9546607, 0.30392933, 0.55066735, 0.31880423, 0.20745438, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 2.0, 3.0, 6.0, -0.0010970528, -0.10363386, 0.024679309, -0.044288144, 0.07154961, 0.013497886], "split_indices": [0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [70.35873, 50.37089, 19.987835, 21.861065, 28.509829, 8.631408, 11.356428, 9.724273, 12.136793, 8.166732, 20.343096], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.029295214, -0.05116083, -0.011097546, 0.09674779, -0.05987918, -0.009988013, 0.057808, -0.03397062, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 151, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.15753357, 0.0, 0.33304036, 0.26360327, 0.15051104, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.05116083, 4.0, 3.0, 7.0, -0.009988013, 0.057808, -0.03397062, -0.0], "split_indices": [0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.09411, 6.189316, 61.904793, 18.335608, 43.569187, 7.61935, 10.716258, 23.201242, 20.367943], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.009769213, 0.06071696, -0.10190841, 0.07717524, 0.008263738, -0.020404363, 0.039225884], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 152, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.0030217, 0.49317813, 0.0, 0.0, 0.40190884, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 2.0, -0.10190841, 0.07717524, 7.0, -0.020404363, 0.039225884], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.744083, 49.62216, 6.121921, 9.229575, 40.392586, 24.662638, 15.729949], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0044679684, 0.04689097, -0.054718483, -0.0067557837, 0.09098548, -0.030395024, 0.030071387], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 153, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.49465504, 0.58860433, 0.0, 0.3556536, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 7.0, -0.054718483, 5.0, 0.09098548, -0.030395024, 0.030071387], "split_indices": [2, 2, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.712048, 39.488155, 11.223895, 33.138107, 6.350047, 18.115414, 15.022693], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.017544838, 0.046778798, -0.14089033, 0.11342165, -0.03631666, -0.24241783, 0.028665015, -0.011929153, 0.11852907, 0.07900766, -0.10556602, -0.035083864, -0.10133262], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 154, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.46210253, 0.2176808, 0.5192422, 0.98061335, 1.6745547, 0.08980662, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 8.0, 4.0, 2.0, 5.0, 0.028665015, -0.011929153, 0.11852907, 0.07900766, -0.10556602, -0.035083864, -0.10133262], "split_indices": [1, 2, 2, 1, 1, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.226242, 36.17257, 19.053673, 20.538832, 15.633737, 13.608039, 5.4456334, 13.630092, 6.90874, 7.941139, 7.692598, 7.2904, 6.317639], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0557745, -0.087860756, -0.018723909, 0.06330671, -0.093665555, 0.050854474, -0.0007125723, -0.037760682, 0.0022442827], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 155, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.4182251, 0.0, 0.29307702, 0.17075469, 0.09483652, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.087860756, 5.0, 5.0, 7.0, 0.050854474, -0.0007125723, -0.037760682, 0.0022442827], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.780777, 5.5650754, 45.215702, 21.02393, 24.19177, 8.011278, 13.012652, 18.914082, 5.2776895], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.003447091, -0.043611854, 0.07704361, -0.048900016, -0.0, 0.030828862, -0.018251285], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 156, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.5309238, 0.22429426, 0.0, 0.0, 0.22292766, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 2.0, 0.07704361, -0.048900016, 5.0, 0.030828862, -0.018251285], "split_indices": [0, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.758442, 45.026363, 5.7320795, 10.736834, 34.289528, 11.919142, 22.370388], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016954378, 0.04618046, -0.033629995, -0.0014435829, -0.038789336, 0.018647484, -0.069618456], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 157, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.26519462, 0.0, 0.13076103, 0.57105875, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.04618046, 8.0, 7.0, -0.038789336, 0.018647484, -0.069618456], "split_indices": [0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.738255, 7.965815, 45.772438, 35.791233, 9.981207, 28.286879, 7.504354], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.061027013, -0.19305529, -0.015733078, -0.08959043, -0.0, 0.08332618, -0.108655706, -0.037868544, 0.08231175, -0.066739276, 0.04061349], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 158, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.32387674, 0.2698012, 0.42539653, 0.0, 0.0, 0.9223796, 0.709581, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 6.0, 4.0, -0.08959043, -0.0, 2.0, 8.0, -0.037868544, 0.08231175, -0.066739276, 0.04061349], "split_indices": [2, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.15858, 13.202947, 43.955635, 7.960815, 5.2421317, 20.804407, 23.151226, 9.833783, 10.970625, 16.127922, 7.023305], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.10403947, -0.0, 0.116387405, 0.060001854, 0.18658862, -0.027251173, 0.05666704, 0.1119239, 0.008465633], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 159, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.07483524, 0.0, 0.15955412, 0.6032278, 0.5614555, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.0, 6.0, 5.0, 4.0, -0.027251173, 0.05666704, 0.1119239, 0.008465633], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.866226, 5.4998035, 48.366425, 28.641104, 19.725319, 12.975326, 15.665779, 8.145852, 11.579468], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.05325352, 0.09679847, -0.053310625, -0.0064578606, 0.23361425, -0.093636, 0.0654721, 0.037227865, -0.018683707, 0.09651186, 0.036927067, 0.001226312, 0.03795046], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 160, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.34009564, 0.7531376, 0.6797755, 0.21988983, 0.13630736, 0.0, 0.041540347, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 8.0, 1.0, 5.0, -0.093636, 4.0, 0.037227865, -0.018683707, 0.09651186, 0.036927067, 0.001226312, 0.03795046], "split_indices": [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [70.242, 50.596825, 19.64517, 28.961306, 21.635519, 5.9327765, 13.712394, 7.7714615, 21.189844, 10.472786, 11.162733, 8.18516, 5.5272336], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.023007425, 0.16435105, -0.081936754, -0.053860996, 0.291803, 0.069329105, -0.21715775, -0.059240155, 0.026293108, 0.14533758, -0.022071255, 0.07840191, -0.027789928, -0.14919005, 0.010534531], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 161, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.0703707, 0.8978589, 0.86507106, 0.270811, 1.4795189, 0.653175, 1.6291825, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 4.0, 2.0, 7.0, 7.0, 7.0, -0.059240155, 0.026293108, 0.14533758, -0.022071255, 0.07840191, -0.027789928, -0.14919005, 0.010534531], "split_indices": [1, 2, 2, 1, 2, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [69.69705, 29.863003, 39.834053, 10.934575, 18.928429, 18.682505, 21.151546, 5.5814466, 5.3531284, 12.359326, 6.569102, 8.550041, 10.132463, 9.7497835, 11.401763], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0053028697, -0.05792905, 0.018117161, -0.068282016, 0.10442955, -0.04638821, -0.0, 0.07408053, -0.026763642], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 162, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.27942923, 0.0, 0.40363365, 0.15681589, 0.79197466, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.05792905, 5.0, 5.0, 5.0, -0.04638821, -0.0, 0.07408053, -0.026763642], "split_indices": [0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.759583, 6.0746665, 51.684917, 25.35487, 26.330048, 10.697959, 14.656911, 15.306691, 11.023357], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.045505896, -0.09479421, 0.041935053, -0.1873427, 0.05197917, 0.069093354, -0.024046449, -0.009769833, -0.0744015], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 163, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.23290229, 0.882895, 0.462811, 0.20984101, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 7.0, 8.0, 2.0, 0.05197917, 0.069093354, -0.024046449, -0.009769833, -0.0744015], "split_indices": [2, 1, 2, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.891113, 33.223423, 17.66769, 25.197882, 8.025538, 6.921289, 10.746402, 7.9920096, 17.205872], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.014940477, -0.078812115, 0.09948187, -0.14774963, 0.03975378, 0.06180188, -0.027720409, -0.0, -0.055134155], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 164, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.4133482, 0.55879873, 0.43632907, 0.16527683, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 8.0, 9.0, 2.0, 0.03975378, 0.06180188, -0.027720409, -0.0, -0.055134155], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.768425, 35.908905, 18.859518, 27.659863, 8.249044, 12.414906, 6.4446125, 5.242725, 22.417137], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.060862098, 0.029138222, -0.16678175, -0.04734711, 0.06535392, -0.013018619, -0.07635361, -0.040242743, 0.0054247873], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 165, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.4794017, 0.40945452, 0.20665437, 0.12353376, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 4.0, 6.0, 0.06535392, -0.013018619, -0.07635361, -0.040242743, 0.0054247873], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.336014, 25.472624, 21.863392, 18.281946, 7.190677, 10.180186, 11.683206, 8.06352, 10.218426], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.03763634, -0.027005186, 0.1257275, 0.04567854, -0.1247435, 0.24178421, -0.048637554, -0.08621364, 0.006643694, 0.1319322, 0.02114027], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 166, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.3045163, 0.5360683, 0.7981148, 0.0, 0.5133232, 0.48337245, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 7.0, 0.04567854, 4.0, 7.0, -0.048637554, -0.08621364, 0.006643694, 0.1319322, 0.02114027], "split_indices": [2, 2, 1, 0, 1, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.146996, 28.668343, 21.478651, 9.557478, 19.110867, 15.668752, 5.8099, 8.938517, 10.17235, 6.270838, 9.397913], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.06542544, -0.08215204, 0.10561314, 0.05325466, 0.09308247, 0.042341463, -0.0133425575], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 167, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.78281724, 0.0, 0.49926776, 0.3820513, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, -0.08215204, 8.0, 5.0, 0.09308247, 0.042341463, -0.0133425575], "split_indices": [2, 0, 2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.527916, 5.001635, 50.52628, 41.625957, 8.900322, 22.221943, 19.404016], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.012641638, 0.23634066, -0.13033228, -0.0, 0.1156581, -0.33799008, 0.006845463, -0.03338276, -0.12604155, 0.117020026, -0.04461773], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 168, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [1.7280484, 0.6949215, 1.188911, 0.0, 0.0, 0.20588303, 1.5770584, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 4.0, 6.0, -0.0, 0.1156581, 4.0, 7.0, -0.03338276, -0.12604155, 0.117020026, -0.04461773], "split_indices": [2, 0, 2, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.282543, 17.789612, 39.49293, 7.052825, 10.736786, 15.418186, 24.074745, 5.0508, 10.367386, 6.7017307, 17.373014], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.00986944, -0.0535524, 0.049172007, 0.0681216, -0.012648554, -0.020029858, 0.018936127], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 169, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.42808598, 0.0, 0.5690066, 0.0, 0.15746285, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.0535524, 3.0, 0.0681216, 7.0, -0.020029858, 0.018936127], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.38176, 8.85473, 48.52703, 12.242241, 36.28479, 22.005842, 14.278947], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.041529715, 0.018691463, 0.12346164, 0.11758757, -0.047428112, -0.03833303, 0.08363404, 0.085950434, -0.0005304033, -0.053363718, 0.00994132], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 170, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.121740445, 0.4043868, 0.6350445, 0.53100973, 0.39888763, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, 5.0, 3.0, 7.0, -0.03833303, 0.08363404, 0.085950434, -0.0005304033, -0.053363718, 0.00994132], "split_indices": [0, 2, 2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [72.89097, 58.916775, 13.974194, 23.90127, 35.015507, 5.1603904, 8.813803, 9.663195, 14.238075, 13.381592, 21.633913], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.053270023, 0.021552397, 0.07254087, -0.024088653, 0.047584128, -0.03375222, -0.050911114, 0.013450639], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 171, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.26019132, 0.0, 0.15426719, 0.5203017, 0.35330424, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.053270023, 5.0, 6.0, 7.0, 0.047584128, -0.03375222, -0.050911114, 0.013450639], "split_indices": [0, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.774124, 6.3173375, 62.456783, 30.25673, 32.200054, 21.11746, 9.13927, 10.341145, 21.85891], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04225678, -0.03751263, 0.080978096, 0.085697696, 0.038567383, 0.021090675, -0.0060248715], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 172, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.37467426, 0.0, 0.36752462, 0.0, 0.08233121, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.03751263, 3.0, 0.085697696, 8.0, 0.021090675, -0.0060248715], "split_indices": [2, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.954784, 9.612272, 46.34251, 6.5650587, 39.777454, 26.788769, 12.988685], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.05012014, -0.021058654, 0.09641472, 0.15743628, -0.13217099, -0.117097095, 0.22715297, 0.07911634, -0.0, -0.0, -0.05887405, 0.036132887, 0.11241184], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 173, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.1796691, 1.6187121, 0.93947697, 0.2689858, 0.0, 0.098766595, 0.23080063, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 4.0, 4.0, -0.13217099, 7.0, 8.0, 0.07911634, -0.0, -0.0, -0.05887405, 0.036132887, 0.11241184], "split_indices": [2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.621597, 19.312817, 31.308783, 13.819287, 5.4935293, 11.639493, 19.669289, 8.14896, 5.6703277, 5.5749373, 6.064556, 12.86172, 6.807569], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.025492584, -0.08114455, 0.14379172, 0.025342556, -0.11360402, 0.064394325, 0.012318082, -0.07209218, -0.012513246], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 174, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.50586736, 0.23326099, 0.06067577, 0.0, 0.2830092, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [8.0, 1.0, 5.0, 0.025342556, 5.0, 0.064394325, 0.012318082, -0.07209218, -0.012513246], "split_indices": [0, 2, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.12217, 40.062923, 12.059244, 5.797709, 34.265217, 5.9160223, 6.1432214, 11.052673, 23.212543], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.012955952, 0.11153779, -0.07869063, -0.12048494, 0.049520846, -0.07338671, -0.022577886], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 175, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [1.2086366, 0.0, 0.44193524, 0.15544623, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, 0.11153779, 9.0, 3.0, 0.049520846, -0.07338671, -0.022577886], "split_indices": [2, 0, 2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.790714, 5.926335, 40.864376, 35.665287, 5.199091, 7.755999, 27.909288], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.06007198, 0.06334966, 0.027528336, 0.07590686, -0.044974245, 0.03927817, 0.00018423749], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 176, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.22051261, 0.0, 0.3779115, 0.13314252, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, 0.06334966, 8.0, 5.0, -0.044974245, 0.03927817, 0.00018423749], "split_indices": [1, 0, 2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.882294, 7.322753, 42.55954, 34.3343, 8.225241, 18.643475, 15.6908245], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0445181, -0.10055033, 0.10259145, 0.1814715, -0.2106587, -0.0, 0.11572736, -0.02978909, -0.075411715], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 177, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1.2034897, 1.5665398, 0.0, 0.53974795, 0.10340667, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 3.0, 0.10259145, 5.0, 5.0, -0.0, 0.11572736, -0.02978909, -0.075411715], "split_indices": [1, 2, 0, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.100353, 48.122288, 5.9780655, 13.075512, 35.046776, 7.333442, 5.7420697, 10.960969, 24.085806], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.12354283, 0.002602631, 0.15945598, 0.03715106, -0.03205361, 0.09313647, 0.06085115, -0.029155655, 0.036855318], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 178, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.23533022, 0.21311094, 0.6001872, 0.0, 0.0, 0.0, 0.29185978, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [2.0, 1.0, 4.0, 0.03715106, -0.03205361, 0.09313647, 6.0, -0.029155655, 0.036855318], "split_indices": [1, 1, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.008537, 13.935769, 43.072765, 6.9829955, 6.9527736, 15.766322, 27.306444, 7.0797844, 20.22666], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.03495181, -0.122798055, 0.025667757, -0.02012059, -0.07004758, -0.046096824, 0.092037536, -0.010248977, -0.0, 0.07194781, -0.01921523], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 179, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.3221694, 0.24910858, 0.4188379, 0.0045983326, 0.0, 0.0, 0.63797086, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 4.0, 6.0, -0.07004758, -0.046096824, 7.0, -0.010248977, -0.0, 0.07194781, -0.01921523], "split_indices": [1, 1, 2, 2, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.083138, 23.562168, 33.520966, 13.305452, 10.256717, 8.29282, 25.228148, 7.90196, 5.4034925, 13.012118, 12.216028], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.025157435, 0.015391473, -0.10774872, -0.046800572, 0.06006015, -0.18834354, 0.00885326, 0.015915507, -0.07715265, -0.00024895417, -0.08480954], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 180, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.23575805, 0.543038, 0.2763959, 0.7513769, 0.0, 0.2388441, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 8.0, 9.0, 5.0, 0.06006015, 3.0, 0.00885326, 0.015915507, -0.07715265, -0.00024895417, -0.08480954], "split_indices": [0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.14387, 44.096947, 22.046917, 33.14772, 10.949227, 14.115238, 7.9316792, 22.6633, 10.484419, 5.417514, 8.697724], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0037317355, 0.10643127, -0.08107798, -0.04001133, 0.21453981, 0.04772922, -0.18663426, 0.09414034, 0.016337607, -0.10028264, 0.015680457], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 181, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.58378303, 0.8326473, 0.9461876, 0.0, 0.29299164, 0.0, 0.9552428, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 2.0, -0.04001133, 7.0, 0.04772922, 7.0, 0.09414034, 0.016337607, -0.10028264, 0.015680457], "split_indices": [2, 1, 1, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.74326, 29.701128, 35.042137, 8.869176, 20.831953, 10.254706, 24.78743, 11.8803625, 8.95159, 15.331786, 9.455644], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.049069427, -0.12416211, 0.019079065, -0.106976636, -0.0059937835, 0.14435063, -0.07287125, 0.028203297, -0.058533866, 0.062246483, 0.014209404, -0.058945943, 0.021691471], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 182, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.2992245, 0.7156006, 0.35749945, 0.0, 0.39728966, 0.046032935, 0.33562332, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 5.0, -0.106976636, 8.0, 8.0, 8.0, 0.028203297, -0.058533866, 0.062246483, 0.014209404, -0.058945943, 0.021691471], "split_indices": [1, 2, 2, 0, 2, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.955044, 26.452518, 28.502523, 7.9505677, 18.501951, 12.2842865, 16.218237, 12.085409, 6.416541, 6.125899, 6.158388, 8.940729, 7.277508], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.062070977, -0.104363225, 0.026263943, 0.022649035, -0.18362176, 0.06567496, -0.06474967, -0.07983622, -0.009502188], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 183, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.33657056, 0.4341313, 0.0, 0.80594844, 0.2865879, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 3.0, 0.026263943, 6.0, 6.0, 0.06567496, -0.06474967, -0.07983622, -0.009502188], "split_indices": [0, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.48069, 40.112858, 10.367833, 15.20281, 24.910046, 8.614515, 6.5882945, 15.230978, 9.679068], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.019722095, -0.03205003, 0.17148358, 0.04472174, -0.077573374, -0.030961972, 0.14147954, 0.005510019, -0.0513155], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 184, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.4285297, 0.3274159, 1.230819, 0.0, 0.309025, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [8.0, 2.0, 5.0, 0.04472174, 5.0, -0.030961972, 0.14147954, 0.005510019, -0.0513155], "split_indices": [0, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.06689, 38.174034, 12.892858, 6.769419, 31.404613, 6.9001603, 5.9926977, 15.366835, 16.037779], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.01645532, 0.11591092, -0.08305527, -0.014023894, -0.087211385, -0.022262804, 0.060420204], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 185, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [1.2937114, 0.0, 0.57265925, 0.43272728, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, 0.11591092, 8.0, 7.0, -0.087211385, -0.022262804, 0.060420204], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.15665, 5.7961574, 41.360497, 32.203735, 9.156759, 26.046553, 6.1571827], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.043525502, -0.038196303, 0.06641906, 0.06717464, 0.01245069, -0.05019223, 0.028898029], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 186, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.21234727, 0.0, 0.38594782, 0.0, 0.5769155, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, -0.038196303, 3.0, 0.06717464, 2.0, -0.05019223, 0.028898029], "split_indices": [0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.685947, 5.170541, 47.515404, 10.849947, 36.66546, 10.969455, 25.696003], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0011687505, 0.22225334, -0.0707543, 0.102751456, -0.0, 0.06817453, -0.13778476, -0.016221544, 0.053773466, 0.003168424, -0.105216876], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 187, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.9441843, 0.37803775, 0.42382523, 0.0, 0.0, 0.21805593, 0.99585015, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 7.0, 3.0, 0.102751456, -0.0, 7.0, 7.0, -0.016221544, 0.053773466, 0.003168424, -0.105216876], "split_indices": [1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.436924, 13.681504, 42.755417, 8.404632, 5.276872, 13.423874, 29.331545, 6.206126, 7.217747, 17.57115, 11.760393], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.007358403, 0.06111889, -0.030308606, 0.050929975, -0.12134882, -0.0028703273, 0.052731454, -0.080928124, 0.008402768], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 188, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.4694858, 0.0, 0.39065585, 0.22291632, 0.58457375, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.06111889, 4.0, 3.0, 7.0, -0.0028703273, 0.052731454, -0.080928124, 0.008402768], "split_indices": [2, 0, 1, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.292828, 9.260542, 50.032288, 26.09178, 23.940508, 17.65741, 8.434368, 11.939784, 12.000723], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.027913436, -0.061738446, -0.0, -0.037586458, 0.052710734, 0.030022444, -0.04099841], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 189, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.28020826, 0.0, 0.33237457, 0.5759935, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, -0.061738446, 8.0, 4.0, 0.052710734, 0.030022444, -0.04099841], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.490135, 6.63713, 47.853004, 39.96103, 7.891973, 16.297195, 23.663836], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.02191655, -0.0053059794, -0.051048826, -0.043103624, 0.06708014, -0.029962732, 0.012209107], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 190, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.15816592, 0.5638011, 0.0, 0.285806, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, -0.051048826, 6.0, 0.06708014, -0.029962732, 0.012209107], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [70.723434, 65.13553, 5.587908, 56.976048, 8.159481, 34.562553, 22.413494], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.047449596, -0.1267482, 0.14411826, -0.06336453, -0.08804196, 0.10998703, -0.0075676832, -0.038499076, 0.03575514, -0.0, -0.0048843347], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 191, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.9734635, 0.42604756, 0.6644414, 0.42704928, 0.0, 0.0, 0.0010796979, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [7.0, 8.0, 8.0, 6.0, -0.08804196, 0.10998703, 9.0, -0.038499076, 0.03575514, -0.0, -0.0048843347], "split_indices": [1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.993202, 44.45262, 17.540579, 33.642593, 10.810027, 6.8032675, 10.737311, 25.435394, 8.2072, 5.1282625, 5.6090493], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.054267805, 0.095438346, -0.064171046, 0.06750721, 0.058775634, 0.0044577513, 0.068397865], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 192, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.6243253, 0.09897146, 0.0, 0.31816086, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, -0.064171046, 8.0, 0.058775634, 0.0044577513, 0.068397865], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.771294, 48.363396, 6.4078975, 39.93069, 8.432705, 31.358946, 8.571746], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0066374787, -0.060768764, 0.08934085, -0.111409396, 0.02938054, -0.004595115, -0.13164611, 0.075661, -0.031497683], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 193, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.81900865, 0.21049026, 0.0, 0.85498047, 0.5131111, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 7.0, 0.08934085, 7.0, 8.0, -0.004595115, -0.13164611, 0.075661, -0.031497683], "split_indices": [0, 2, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.03809, 42.593567, 6.4445243, 27.83239, 14.761177, 22.56265, 5.2697387, 5.503064, 9.258113], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04993896, -0.0, 0.19128935, 0.056182764, -0.067326866, 0.08806269, 0.013685095, -0.07030067, 0.009198635], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 194, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.36080393, 0.52884376, 0.15317672, 0.0, 0.5212605, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [8.0, 2.0, 9.0, 0.056182764, 5.0, 0.08806269, 0.013685095, -0.07030067, 0.009198635], "split_indices": [2, 2, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.807453, 39.348076, 12.459378, 10.179573, 29.168503, 6.3209243, 6.1384544, 10.648566, 18.519938], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.00798743, 0.036524653, -0.04836018, -0.0, 0.09302827, 0.016473051, -0.029062921, 0.010992461, 0.033950277], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 195, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.230693, 0.09419477, 0.0, 0.15000549, 0.00082470477, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 6.0, -0.04836018, 4.0, 3.0, 0.016473051, -0.029062921, 0.010992461, 0.033950277], "split_indices": [0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.143562, 42.371815, 5.771747, 25.80186, 16.569956, 16.412489, 9.389371, 6.40943, 10.160525], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.024777172, 0.0965727, -0.08260082, -0.0066611166, 0.07756402, -0.011936166, -0.09928688, -0.060236778, 0.014235971], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 196, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.38174796, 0.35946894, 0.616525, 0.0, 0.0, 0.36577308, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 6.0, 8.0, -0.0066611166, 0.07756402, 5.0, -0.09928688, -0.060236778, 0.014235971], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.46644, 16.15781, 36.308628, 9.463622, 6.694188, 29.395443, 6.9131846, 6.865778, 22.529665], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.021833355, 0.07807582, -0.07586201, -0.15845388, 0.0050887973, -0.062385038, -0.0132587105, 0.047702923, -0.01116486], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 197, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.8652138, 0.0, 0.34137323, 0.10539919, 0.18039069, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.07807582, 6.0, 6.0, 2.0, -0.062385038, -0.0132587105, 0.047702923, -0.01116486], "split_indices": [1, 0, 1, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.684258, 8.039878, 47.64438, 23.720707, 23.923672, 15.368092, 8.352615, 5.072653, 18.851017], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.018640287, 0.005254827, -0.06340071, 0.048210286, -0.10650232, 0.05745558, -0.0026756767, -0.08901218, 0.00885903], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 198, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.3046308, 0.2641929, 0.0, 0.3572184, 0.4232102, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 7.0, -0.06340071, 3.0, 4.0, 0.05745558, -0.0026756767, -0.08901218, 0.00885903], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.442833, 54.178253, 6.2645764, 40.192936, 13.985317, 11.284321, 28.908617, 5.6455264, 8.339791], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.028753001, -0.06084878, -0.0, 0.085268095, -0.051316544, -0.085713625, 0.0047287373], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 199, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.27516958, 0.0, 0.72041297, 0.0, 0.691611, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.06084878, 3.0, 0.085268095, 5.0, -0.085713625, 0.0047287373], "split_indices": [2, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.33356, 6.8006334, 47.532925, 6.5490932, 40.983833, 8.725518, 32.258316], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0153618, -0.017302394, 0.09093774, 0.023896268, -0.07116012, 0.07956082, -0.0, -0.0047833333, 0.063158885, -0.0017432278, 0.0054237656], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 200, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.18644837, 0.48109242, 0.33900434, 0.34061247, 0.0, 0.0, 0.0028359094, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 3.0, 4.0, -0.07116012, 0.07956082, 5.0, -0.0047833333, 0.063158885, -0.0017432278, 0.0054237656], "split_indices": [1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [70.90693, 49.123474, 21.783457, 41.739925, 7.383548, 6.4988985, 15.284558, 34.70928, 7.030649, 8.599979, 6.684579], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.024058225, 0.057258863, -0.12845382, 0.25566763, -0.032519583, -0.05150965, -0.0030287595, -0.007357744, 0.16100724, -0.042099316, 0.030854907], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 201, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5722293, 0.6912474, 0.13167039, 1.0103228, 0.39870393, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 7.0, 3.0, 3.0, -0.05150965, -0.0030287595, -0.007357744, 0.16100724, -0.042099316, 0.030854907], "split_indices": [1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.7021, 36.086662, 28.615437, 11.01338, 25.073282, 20.009064, 8.606374, 5.793684, 5.2196956, 14.348136, 10.725146], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.053470362, -0.08588464, 0.044536725, 0.038267847, -0.13676403, 0.02746565, -0.002436728, -0.049027234, 0.07822808, -0.13740455, -0.011836921], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 202, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.18654417, 0.28892657, 0.04340872, 0.62727475, 0.91812176, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 3.0, 6.0, 5.0, 5.0, 0.02746565, -0.002436728, -0.049027234, 0.07822808, -0.13740455, -0.011836921], "split_indices": [2, 1, 1, 2, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.214653, 42.351574, 12.863082, 11.811755, 30.539818, 7.408768, 5.4543138, 6.098985, 5.71277, 5.999276, 24.54054], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.030897321, -0.043025345, 0.069201626, -0.021217637, -0.0, 0.13577007, 0.0036680724, 0.06258619, 0.002254027, 0.028142942, -0.011109729], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 203, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.1466231, 0.017615635, 0.13423531, 0.0, 0.0, 0.12745833, 0.078707844, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 6.0, 6.0, -0.021217637, -0.0, 7.0, 3.0, 0.06258619, 0.002254027, 0.028142942, -0.011109729], "split_indices": [1, 2, 1, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.805836, 15.7248535, 33.080982, 8.711211, 7.0136423, 15.122238, 17.958744, 8.747394, 6.3748446, 5.944721, 12.014023], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.060415547, -0.027329477, -0.13301142, 0.035064787, -0.003929717, 0.042910576], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 204, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.32541698, 0.0, 1.3345201, 0.0, 0.24125665, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.060415547, 3.0, -0.13301142, 8.0, -0.003929717, 0.042910576], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.35479, 6.1171093, 48.23768, 5.7035346, 42.534145, 29.380491, 13.153654], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0133441305, 0.05535982, -0.04971159, -0.104650795, 0.023721643, 0.04259687, -0.018491292], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 205, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.3387893, 0.0, 0.9521363, 0.0, 0.35982198, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.05535982, 3.0, -0.104650795, 6.0, 0.04259687, -0.018491292], "split_indices": [2, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.677143, 6.1874022, 40.489742, 7.5099516, 32.97979, 14.037913, 18.941875], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.013220891, -0.08776056, 0.021915821, 0.062110443, -0.023275262, -0.041320384, 0.029288543], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 206, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.5468096, 0.0, 0.4169966, 0.0, 0.5474929, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, -0.08776056, 2.0, 0.062110443, 6.0, -0.041320384, 0.029288543], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.305565, 5.232327, 46.07324, 8.771896, 37.301342, 19.514704, 17.786638], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.065822326, -0.032048136, 0.27059028, 0.05291157, -0.100873664, 0.025040103, 0.11699385, 0.039217837, -0.02801219], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 207, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [1.0665004, 0.96289206, 0.3026017, 0.33848566, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 7.0, 5.0, 4.0, -0.100873664, 0.025040103, 0.11699385, 0.039217837, -0.02801219], "split_indices": [1, 2, 2, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.593636, 34.47241, 16.121227, 27.335241, 7.137169, 7.267425, 8.853802, 18.359251, 8.975988], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.048368428, 0.08011559, -0.05667461, 0.08455024, 0.034621958, -0.021882694, 0.03950299], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 208, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.4528228, 0.4504189, 0.0, 0.0, 0.48813578, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 1.0, -0.05667461, 0.08455024, 6.0, -0.021882694, 0.03950299], "split_indices": [0, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.546345, 52.593452, 5.9528913, 8.318064, 44.275387, 20.62979, 23.6456], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.011102595, 0.07963038, -0.17426911, -0.037453976, 0.123597056, -0.088023104, -0.10701353, 0.072301805, -0.012470865, 0.006808778, -0.08967424], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 209, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.81630135, 0.3273869, 0.08760077, 0.0, 0.60155016, 0.0, 0.40272793, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 1.0, 7.0, -0.037453976, 6.0, -0.088023104, 7.0, 0.072301805, -0.012470865, 0.006808778, -0.08967424], "split_indices": [1, 1, 1, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.56181, 33.71359, 18.848219, 5.2381883, 28.4754, 5.051015, 13.797204, 16.729494, 11.745909, 8.447797, 5.349407], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.03656849, -0.11318998, 0.103568316, -0.21263978, 0.0059200204, 0.09693224, 0.054906297, -0.013564637, -0.10003027, -0.0072036595, 0.030496137], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 210, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.69316256, 0.30159786, 0.45695287, 0.20086443, 0.0, 0.0, 0.16089422, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 5.0, 5.0, 3.0, 0.0059200204, 0.09693224, 4.0, -0.013564637, -0.10003027, -0.0072036595, 0.030496137], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [67.01036, 20.038416, 46.971947, 11.549773, 8.488642, 7.1263843, 39.845566, 5.7635326, 5.78624, 14.207626, 25.637938], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.051000316, 0.024826491, 0.057822544, 0.14771418, -0.064087965, 0.08419235, 0.012070338, -0.054936238, 0.010072527], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 211, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.21569212, 0.63910294, 0.0, 0.3087126, 0.40723228, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 4.0, 0.057822544, 4.0, 7.0, 0.08419235, 0.012070338, -0.054936238, 0.010072527], "split_indices": [0, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.293655, 55.859386, 8.434269, 23.64427, 32.21512, 9.4345045, 14.2097645, 14.586198, 17.62892], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.018395524, 0.06968229, -0.08435477, -0.04375905, 0.15318723, 0.013727889, -0.09727807, 0.0020396293, -0.04213758, -0.005381576, 0.07530332], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 212, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.29812923, 0.38117936, 0.6158242, 0.09519078, 0.41006577, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 6.0, 2.0, 5.0, 0.013727889, -0.09727807, 0.0020396293, -0.04213758, -0.005381576, 0.07530332], "split_indices": [0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.736904, 37.376076, 17.360826, 15.541459, 21.834616, 11.510471, 5.8503556, 10.137033, 5.4044256, 7.867017, 13.967599], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0509858, -0.10656477, 0.11471768, -0.045188613, -0.21818852, -0.010814928, 0.07424482, -0.024769211, 0.019004818, -0.03285275, -0.09210816], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 213, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5088301, 0.24754146, 0.30220044, 0.123015985, 0.058483124, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, 4.0, 4.0, 5.0, -0.010814928, 0.07424482, -0.024769211, 0.019004818, -0.03285275, -0.09210816], "split_indices": [0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.010918, 40.415462, 12.595457, 27.561504, 12.853957, 5.8862805, 6.709177, 21.365028, 6.196475, 7.2918725, 5.562084], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.024862014, 0.07926223, -0.076821774, 0.006829442, 0.04073351, -0.15283756, 0.033865567, 0.0061946004, -0.06583747], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 214, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.2909632, 0.036597013, 0.5447854, 0.0, 0.0, 0.33323568, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [2.0, 7.0, 8.0, 0.006829442, 0.04073351, 3.0, 0.033865567, 0.0061946004, -0.06583747], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.699432, 16.39494, 35.304493, 9.785193, 6.6097465, 25.71996, 9.584535, 6.9113092, 18.80865], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.020434298, 0.0693804, -0.0734507, -0.0174313, -0.06335378, 0.036966745, -0.030876195], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 215, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.62059844, 0.0, 0.27766007, 0.3669802, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, 0.0693804, 8.0, 4.0, -0.06335378, 0.036966745, -0.030876195], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.416145, 6.988574, 38.427574, 28.633766, 9.793806, 10.215428, 18.418339], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.033261474, 0.0749089, -0.10410127, -0.012007921, 0.18972686, -0.043419152, -0.011364568, 0.033177927, -0.061742585, 0.0862634, 0.01259407], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 216, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.3164083, 0.44843906, 0.009965017, 0.6233515, 0.23029077, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, 5.0, 4.0, 8.0, -0.043419152, -0.011364568, 0.033177927, -0.061742585, 0.0862634, 0.01259407], "split_indices": [0, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.455536, 41.926407, 11.529131, 23.876472, 18.049934, 5.4744806, 6.05465, 14.571617, 9.304854, 9.8834305, 8.166505], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.03893338, -0.106445976, 0.070105135, 0.06707797, -0.17790042, 0.0010245042, 0.034857895, -0.09905645, -0.022576295], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 217, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1.0129385, 0.57556194, 0.0, 0.029279392, 0.43869138, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 2.0, 0.070105135, 1.0, 6.0, 0.0010245042, 0.034857895, -0.09905645, -0.022576295], "split_indices": [2, 1, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.375584, 43.563583, 9.812, 12.31614, 31.247444, 6.7161818, 5.5999584, 11.296603, 19.95084], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.01753407, 0.06342839, -0.064634144, -0.09390377, 0.004075904, 0.023292739, -0.03660914], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 218, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.6408795, 0.0, 0.89391947, 0.0, 0.37821063, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.06342839, 2.0, -0.09390377, 7.0, 0.023292739, -0.03660914], "split_indices": [2, 0, 1, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.522053, 9.02719, 49.49486, 10.269979, 39.224884, 25.516586, 13.708297], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.06192015, -0.025500322, 0.08508161, 0.22693224, 0.032462955, 0.009993222, 0.11685966, 0.0011698471, 0.050847854], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 219, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.20500463, 0.0, 0.34952077, 0.36020797, 0.1350127, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.025500322, 3.0, 6.0, 9.0, 0.009993222, 0.11685966, 0.0011698471, 0.050847854], "split_indices": [1, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.116325, 6.8264318, 50.289894, 12.196646, 38.09325, 6.397943, 5.7987027, 32.97743, 5.11582], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019826496, 0.06157908, -0.035249677, -0.10985546, 0.03769341, -0.0732627, -0.007884864, 0.08139455, -0.006853066], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 220, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.45474705, 0.0, 0.3342018, 0.30631563, 0.45622575, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.06157908, 5.0, 4.0, 3.0, -0.0732627, -0.007884864, 0.08139455, -0.006853066], "split_indices": [0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.37476, 8.049845, 58.324917, 29.261404, 29.063513, 10.025483, 19.23592, 5.604589, 23.458923], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.012594891, -0.061500747, 0.081818216, 0.01133928, -0.09233522, -0.049113806, 0.09485892, -0.011346858, 0.038030148, -0.03671583, 0.0020738856], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 221, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.31381673, 0.85140103, 0.7389005, 0.21714355, 0.0, 0.074615866, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [7.0, 7.0, 6.0, 5.0, -0.09233522, 3.0, 0.09485892, -0.011346858, 0.038030148, -0.03671583, 0.0020738856], "split_indices": [0, 2, 2, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.31487, 44.613117, 21.701756, 34.853012, 9.760103, 14.156961, 7.544795, 24.306543, 10.54647, 6.39455, 7.7624116], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.06474175, 0.1724718, 0.022454754, 0.016472267, 0.09900808, -0.12479428, 0.10579324, 0.0061611366, -0.09490793, 0.080591425, -0.0027652811], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 222, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.24064016, 0.23084816, 0.5442883, 0.0, 0.0, 0.47380176, 0.56613064, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 2.0, 6.0, 0.016472267, 0.09900808, 5.0, 4.0, 0.0061611366, -0.09490793, 0.080591425, -0.0027652811], "split_indices": [2, 2, 2, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.95214, 14.519352, 42.43279, 9.4506645, 5.0686874, 14.722517, 27.710274, 8.582313, 6.1402044, 11.276915, 16.43336], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.03323714, 0.08092107, -0.041716453, -0.00211404, 0.16733463, -0.016007632, 0.029621547, 0.016667672, 0.07341825], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 223, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.42637956, 0.31262365, 0.0, 0.11141622, 0.13631058, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, -0.041716453, 7.0, 4.0, -0.016007632, 0.029621547, 0.016667672, 0.07341825], "split_indices": [2, 2, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.236988, 40.217136, 10.019852, 20.416834, 19.8003, 14.507711, 5.9091234, 9.332172, 10.468129], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.014945973, 0.08428895, -0.066167414, 0.05508079, -0.13350703, -0.054134313, 0.028647618], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 224, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.77962613, 0.0, 0.77817845, 0.0, 0.41724873, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.08428895, 1.0, 0.05508079, 9.0, -0.054134313, 0.028647618], "split_indices": [2, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.627678, 6.5205464, 44.10713, 8.68436, 35.42277, 29.93528, 5.487491], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.04695898, 0.104430996, -0.13578871, 0.10607497, -0.014612459, -0.10845483, -0.032155745, 0.009644903, -0.03418976], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 225, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.64818513, 0.7152823, 0.6605146, 0.0, 0.0, 0.0, 0.1288814, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 3.0, 6.0, 0.10607497, -0.014612459, -0.10845483, 8.0, 0.009644903, -0.03418976], "split_indices": [0, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.911972, 16.496344, 29.415628, 6.0299387, 10.466404, 8.169003, 21.246626, 11.551472, 9.695154], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.03329804, 0.07046364, -0.033581875, 0.14898054, 0.019218814, -0.017522464, 0.11740559, 0.05112527, -0.008380714], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 226, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.2872824, 0.15328166, 0.0, 0.8632965, 0.21814156, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, -0.033581875, 5.0, 2.0, -0.017522464, 0.11740559, 0.05112527, -0.008380714], "split_indices": [0, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.528824, 41.987732, 9.54109, 15.05774, 26.929995, 8.301155, 6.7565846, 6.2847867, 20.645206], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.017317543, 0.050842185, -0.1703813, -0.057321426, 0.16153975, -0.022398869, -0.090268575, -0.07658603, 0.075318605, 0.07525672, -0.006737106], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 227, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.58523977, 0.47184277, 0.15680546, 1.2477485, 0.34699476, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 5.0, 5.0, 4.0, -0.022398869, -0.090268575, -0.07658603, 0.075318605, 0.07525672, -0.006737106], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.12011, 36.76366, 16.35645, 18.381624, 18.382036, 10.785026, 5.5714235, 11.497382, 6.884242, 12.496222, 5.885814], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0619166, 0.12281675, -0.15437081, -0.0, 0.118836485, -0.20438036, 0.034352284, 0.03652277, -0.05009382, 0.01255752, -0.09175682], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 228, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [1.0256101, 0.70372844, 0.56973004, 0.31766987, 0.0, 0.8979418, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [3.0, 7.0, 9.0, 1.0, 0.118836485, 3.0, 0.034352284, 0.03652277, -0.05009382, 0.01255752, -0.09175682], "split_indices": [2, 0, 2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.754803, 18.793455, 38.96135, 13.405862, 5.3875937, 33.367092, 5.594258, 7.565908, 5.839954, 9.621591, 23.7455], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.028577512, 0.09688274, -0.09545316, -0.017496958, 0.1764956, -0.1437127, 0.010157805, 0.112571895, -0.0, -0.07058713, -0.0154050635], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 229, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.50291425, 0.2746814, 0.26182184, 0.0, 0.52566373, 0.20840847, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [3.0, 4.0, 8.0, -0.017496958, 7.0, 5.0, 0.010157805, 0.112571895, -0.0, -0.07058713, -0.0154050635], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.855255, 19.44047, 38.414783, 6.2558923, 13.184578, 28.456684, 9.9581, 5.871381, 7.3131967, 12.968358, 15.488326], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.041831292, 0.0028922583, 0.115827195, -0.0627814, 0.07754287, 0.063535646, -0.014868867, 0.0045385812, -0.038755205, 0.049939167, 0.005396779], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 230, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.19009015, 0.24124172, 0.39644927, 0.14439785, 0.10345064, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 5.0, 2.0, 7.0, 0.063535646, -0.014868867, 0.0045385812, -0.038755205, 0.049939167, 0.005396779], "split_indices": [1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [69.20964, 46.913013, 22.296629, 24.329037, 22.583977, 14.315168, 7.981461, 10.856886, 13.472151, 7.6649256, 14.919051], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.04323396, 0.011018364, 0.11452613, 0.062184103, -0.053486772, -0.0, 0.0935738, 0.035737272, -0.03382298, -0.004359632, 0.0008167199], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 231, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.1365037, 0.45619264, 0.4592903, 0.3953185, 0.0, 0.0014253327, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [7.0, 7.0, 7.0, 5.0, -0.053486772, 3.0, 0.0935738, 0.035737272, -0.03382298, -0.004359632, 0.0008167199], "split_indices": [1, 2, 2, 1, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.44987, 46.151913, 18.297955, 37.27661, 8.8753, 11.9754095, 6.322545, 28.794472, 8.482139, 5.486481, 6.4889283], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.025599165, -0.08557629, 0.10245484, -0.016900832, -0.10121039, 0.22048974, -0.03782937, -0.026806917, 0.028756548, 0.032017596, 0.086803965], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 232, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.42868903, 0.62394357, 0.5075449, 0.2659506, 0.0, 0.024677277, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 9.0, 7.0, -0.10121039, 6.0, -0.03782937, -0.026806917, 0.028756548, 0.032017596, 0.086803965], "split_indices": [0, 0, 0, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.894806, 37.461475, 16.433332, 30.584042, 6.8774343, 11.14854, 5.284793, 19.285696, 11.298346, 5.7125607, 5.4359784], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.014273782, 0.077795774, -0.06377337, -0.007312999, 0.15009427, -0.011956525, -0.0525682, 0.020941164, -0.039231922, 0.010516975, 0.061994653, -0.039096363, 0.024805533], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 233, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.2676782, 0.20129703, 0.11486617, 0.15038674, 0.07930818, 0.21407807, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 6.0, 7.0, 4.0, 2.0, 4.0, -0.0525682, 0.020941164, -0.039231922, 0.010516975, 0.061994653, -0.039096363, 0.024805533], "split_indices": [1, 2, 2, 2, 1, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.708736, 29.2152, 22.493534, 13.196345, 16.018856, 16.723162, 5.770374, 7.891624, 5.304721, 6.396272, 9.622583, 7.732295, 8.990866], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.08296577, -0.25372234, -0.028274016, -0.0032292854, -0.13196385, 0.05683883, -0.12057057, -0.02099374, 0.07458317, -0.013931362, -0.071952306], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 234, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.45708838, 0.50304127, 0.33930194, 0.0, 0.0, 0.56326526, 0.14412653, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 7.0, -0.0032292854, -0.13196385, 7.0, 7.0, -0.02099374, 0.07458317, -0.013931362, -0.071952306], "split_indices": [0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.731884, 11.223312, 40.50857, 5.5607944, 5.662518, 20.710625, 19.797949, 12.534947, 8.175677, 13.583122, 6.2148266], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.026514571, 0.10655523, -0.10459376, -0.0, 0.053168345, -0.19952564, -0.026298933, -0.08975348, -0.020894065, 0.011684529, -0.027166514], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 235, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.5002947, 0.11934644, 0.20005703, 0.0, 0.0, 0.11220151, 0.08638171, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 6.0, 4.0, -0.0, 0.053168345, 7.0, 6.0, -0.08975348, -0.020894065, 0.011684529, -0.027166514], "split_indices": [1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.09785, 16.437714, 29.660135, 7.3346376, 9.103075, 12.12864, 17.531496, 5.68965, 6.4389896, 8.121894, 9.409602], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.048675075, 0.17016, -0.10953668, 0.11517332, 0.042479463, -0.08839741, 0.025192617, 0.0033521408, 0.026316749, 0.026512861, -0.011852188], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 236, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [1.0405247, 0.7774073, 0.6131727, 0.0, 0.018310532, 0.0, 0.06577351, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 4.0, 0.11517332, 8.0, -0.08839741, 7.0, 0.0033521408, 0.026316749, 0.026512861, -0.011852188], "split_indices": [0, 1, 1, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.85149, 29.644854, 22.206636, 10.04254, 19.602314, 9.16664, 13.039997, 13.629036, 5.9732776, 7.194129, 5.845868], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.038716093, -0.12897341, -0.04912339, 0.09459102, -0.05735528, -0.007977199, -0.03814036, 0.0046989415, 0.04954583, 0.006610093], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 237, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.28981557, 0.22114874, 0.059324637, 0.09780678, 0.118093014, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 6.0, 4.0, 7.0, -0.05735528, -0.007977199, -0.03814036, 0.0046989415, 0.04954583, 0.006610093], "split_indices": [2, 1, 1, 2, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.716873, 42.185463, 12.531408, 15.752501, 26.432964, 6.6478796, 5.883529, 7.4104977, 8.342003, 12.055072, 14.3778925], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.05001978, -0.004939668, 0.14748327, 0.027995925, -0.031607736, 0.11468502, -0.0, -0.021998076, 0.048980057, -0.042174615, 0.04903292], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 238, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.33135068, 0.13662216, 0.7474642, 0.41451207, 0.0, 0.0, 0.35921925, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [7.0, 7.0, 3.0, 4.0, -0.031607736, 0.11468502, 7.0, -0.021998076, 0.048980057, -0.042174615, 0.04903292], "split_indices": [0, 2, 2, 2, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.06197, 37.0774, 20.98457, 27.713356, 9.364045, 7.3868394, 13.597731, 15.684079, 12.029276, 7.266205, 6.3315263], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.04204522, 0.011162049, -0.173744, 0.060202174, -0.05136632, -0.09699064, -0.05931584, -0.08790425, 0.00056831393, -0.03581354, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 239, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.42690915, 0.52224034, 0.2400778, 0.0, 0.44069076, 0.0, 0.046339698, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 8.0, 0.060202174, 5.0, -0.09699064, 5.0, -0.08790425, 0.00056831393, -0.03581354, -0.0], "split_indices": [0, 2, 0, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.326275, 40.941532, 16.384743, 10.005791, 30.935741, 5.9321337, 10.45261, 5.1131306, 25.82261, 5.079074, 5.3735356], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.03441786, 0.09237808, -0.06875996, -0.09326212, -0.018842354, 0.032366112, -0.02869543], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 240, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.8125282, 0.0, 0.7359314, 0.0, 0.54012895, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.09237808, 3.0, -0.09326212, 6.0, 0.032366112, -0.02869543], "split_indices": [2, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.34448, 5.239121, 63.105362, 9.55073, 53.55463, 19.565853, 33.988777], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.021897947, -0.17948258, 0.06878685, -0.00830758, -0.08744724, -0.058217853, 0.1319191, -0.055098135, 0.009834352, 0.1439829, 0.015400545], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 241, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.6162103, 0.16698593, 0.44762766, 0.0, 0.0, 0.2275089, 0.9562283, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 4.0, 3.0, -0.00830758, -0.08744724, 6.0, 4.0, -0.055098135, 0.009834352, 0.1439829, 0.015400545], "split_indices": [0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.12396, 11.173781, 52.95018, 5.653002, 5.52078, 17.081259, 35.868923, 7.215416, 9.865842, 5.5787597, 30.290163], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.033640325, -0.09279793, -0.0, -0.034345847, 0.11463485, 0.017491348, -0.033820897, 0.06577703, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 242, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.54640615, 0.0, 0.21507931, 0.31882185, 0.16855869, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.09279793, 8.0, 4.0, 9.0, 0.017491348, -0.033820897, 0.06577703, -0.0], "split_indices": [0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.228806, 5.4111166, 52.81769, 41.242832, 11.57486, 18.434961, 22.807869, 5.943671, 5.6311884], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00072683964, -0.062905475, 0.058362782, 0.035120197, -0.16292535, -0.04103719, 0.15052953, 0.0062438017, -0.07465873, 0.10067916, -0.018759554], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 243, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.19137232, 0.494178, 0.48647207, 0.0, 0.3055175, 0.0, 0.7724399, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 4.0, 0.035120197, 2.0, -0.04103719, 8.0, 0.0062438017, -0.07465873, 0.10067916, -0.018759554], "split_indices": [2, 0, 0, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.963795, 25.138697, 24.8251, 8.562088, 16.576609, 7.4600573, 17.365042, 5.153126, 11.423483, 9.236412, 8.12863], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.016200112, -0.016325558, 0.07699455, 0.103154816, -0.07408111, -0.007970927, 0.061853368, 0.08938569, -0.054636735], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 244, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.42221373, 0.31491163, 0.0, 0.21724193, 1.3067229, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 4.0, 0.07699455, 3.0, 1.0, -0.007970927, 0.061853368, 0.08938569, -0.054636735], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.37775, 43.948948, 5.4287996, 13.501193, 30.447756, 5.8749804, 7.626213, 6.248412, 24.199343], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017972697, 0.07435301, -0.085416764, 0.11877529, -0.03975768, 0.012477165, 0.06806199], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 245, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [1.0109706, 0.34468794, 0.0, 0.21832135, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, -0.085416764, 6.0, -0.03975768, 0.012477165, 0.06806199], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [43.965, 34.943573, 9.02143, 29.515139, 5.428433, 18.567385, 10.947755], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.046558462, 0.007812093, -0.18757223, 0.069240645, -0.042477988, -0.08802299, 0.0002754941, -0.0, 0.065214954], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 246, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.42691895, 0.35871807, 0.33556777, 0.292142, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 7.0, 9.0, 6.0, -0.042477988, -0.08802299, 0.0002754941, -0.0, 0.065214954], "split_indices": [0, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.14697, 37.805706, 14.341262, 27.645172, 10.160533, 9.1675625, 5.1737, 19.556808, 8.088364], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.015195434, 0.043629263, -0.1701199, 0.083823636, -0.0011639085, 0.0003560477, -0.100802034, -0.019074038, 0.0745386], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 247, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.49326462, 0.4290794, 0.4590457, 0.0, 0.5043134, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 1.0, 4.0, 0.083823636, 9.0, 0.0003560477, -0.100802034, -0.019074038, 0.0745386], "split_indices": [0, 1, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.9229, 36.96154, 13.96136, 5.4210105, 31.54053, 7.060355, 6.9010053, 26.148327, 5.3922033], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.042606812, -0.2174505, 0.006564703, -0.021118494, -0.099352695, 0.041220278, -0.03459517, -5.4723518e-05, 0.07839084], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 248, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.52429354, 0.15873569, 0.19197044, 0.0, 0.0, 0.36560085, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [2.0, 1.0, 8.0, -0.021118494, -0.099352695, 7.0, -0.03459517, -5.4723518e-05, 0.07839084], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.36434, 12.286722, 45.077618, 6.447676, 5.839046, 36.25207, 8.825547, 30.970575, 5.2814965], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.055702902, 0.10250819, -0.14307809, 0.084124215, -0.006743902, -0.09348886, -0.070694216, -0.061510224, -0.003783495], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 249, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.79851496, 0.4822651, 0.39316154, 0.0, 0.0, 0.0, 0.19184826, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 4.0, 5.0, 0.084124215, -0.006743902, -0.09348886, 4.0, -0.061510224, -0.003783495], "split_indices": [1, 2, 1, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.409637, 19.244946, 36.164692, 7.7583594, 11.486587, 9.456877, 26.707817, 6.778352, 19.929464], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.029122798, 0.0060544475, -0.0746779, -0.038748246, 0.12950328, 0.0033784334, -0.07845102, 0.089209035, -0.0044167372], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 250, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.57172924, 0.35396162, 0.0, 0.5321831, 0.44875208, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 7.0, -0.0746779, 6.0, 8.0, 0.0033784334, -0.07845102, 0.089209035, -0.0044167372], "split_indices": [2, 2, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [69.60645, 60.44963, 9.156824, 44.261925, 16.187702, 36.55554, 7.706388, 7.3106337, 8.877069], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0043131146, -0.07513669, 0.048064023, 0.017810464, -0.1765541, 0.060296055, 0.009774975, -0.10116356, -0.003962574, -0.015379449, 0.032476183], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 251, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.24112101, 0.35122102, 0.24622846, 0.0, 0.339101, 0.0, 0.24179296, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [3.0, 4.0, 4.0, 0.017810464, 7.0, 0.060296055, 7.0, -0.10116356, -0.003962574, -0.015379449, 0.032476183], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.10726, 23.119741, 44.98752, 9.692993, 13.426747, 7.634627, 37.35289, 5.9238067, 7.502941, 22.72891, 14.62398], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.023931706, 0.032269742, -0.13564946, -0.044421177, 0.05932619, 0.041563146, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 252, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.4099656, 0.24600774, 0.0, 0.0, 0.19851449, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 1.0, -0.13564946, -0.044421177, 4.0, 0.041563146, -0.0], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.2333, 49.43638, 5.796919, 5.3836837, 44.052696, 17.603449, 26.449247], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.045611255, 0.023942387, -0.18995962, -0.028226357, 0.0698203, -0.06921763, -0.030262658, 0.0029521766, 0.045211088], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 253, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.57242244, 0.20706497, 0.010508597, 0.0, 0.11912216, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 8.0, -0.028226357, 4.0, -0.06921763, -0.030262658, 0.0029521766, 0.045211088], "split_indices": [1, 2, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.907883, 36.474274, 17.433609, 9.304929, 27.169346, 10.099474, 7.334135, 16.954561, 10.214786], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.033491306, 0.057275813, -0.10424294, -0.11695195, -0.0, 0.12848367, -0.034248926], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 254, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.830376, 0.0, 1.1760631, 0.0, 1.5279126, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.057275813, 5.0, -0.11695195, 6.0, 0.12848367, -0.034248926], "split_indices": [2, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.5916, 11.381097, 39.210503, 9.613675, 29.596827, 5.55031, 24.046516], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.017508319, -0.13575296, 0.0714113, -0.0, -0.07982904, 0.075814664, 0.02277442, -0.03700771, 0.025267525], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 255, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.37336445, 0.2163208, 0.26899105, 0.0, 0.0, 0.0, 0.26298252, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 5.0, 4.0, -0.0, -0.07982904, 0.075814664, 2.0, -0.03700771, 0.025267525], "split_indices": [0, 1, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [43.800095, 10.518733, 33.28136, 5.465729, 5.0530043, 5.717215, 27.564146, 7.379787, 20.184359], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.011081861, 0.19725214, -0.12149575, 0.02600944, 0.08382971, -0.17071104, 0.030017802, -0.07175152, -0.017878426], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 256, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [1.2400802, 0.107192874, 0.4134398, 0.0, 0.0, 0.1837166, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 6.0, 9.0, 0.02600944, 0.08382971, 6.0, 0.030017802, -0.07175152, -0.017878426], "split_indices": [0, 2, 0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.197075, 17.494843, 34.702232, 8.854722, 8.64012, 28.95184, 5.750393, 16.644482, 12.307359], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.021171091, -0.14529598, 0.108919345, 0.033738043, -0.08288995, 0.17879702, -0.0074157245, 0.034931812, 0.09512108, -0.098019116, 0.08293372], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 257, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.790284, 0.66950494, 0.30986616, 0.0, 0.0, 0.11779922, 1.3529716, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 3.0, 6.0, 0.033738043, -0.08288995, 5.0, 8.0, 0.034931812, 0.09512108, -0.098019116, 0.08293372], "split_indices": [1, 2, 2, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.268005, 17.42176, 34.846245, 5.661235, 11.760527, 21.971527, 12.874718, 16.91059, 5.0609374, 6.153704, 6.7210135], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.1531905, 0.084769085, -0.080481485, -0.08788532, 0.19197346, 0.015843296, -0.054515507, 0.016130961, 0.014725604, 0.083376825, -0.04118228, 0.029968059], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 258, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.7522625, 0.14858195, 0.2522207, 0.23201731, 0.0, 0.12512338, 0.32394388, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 3.0, 2.0, -0.08788532, 7.0, 6.0, -0.054515507, 0.016130961, 0.014725604, 0.083376825, -0.04118228, 0.029968059], "split_indices": [0, 0, 2, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.97456, 19.616556, 36.358006, 14.41083, 5.205726, 12.961757, 23.396248, 8.438665, 5.9721637, 5.8954015, 7.0663557, 7.6453714, 15.750876], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.037590202, -0.05884667, 0.10962193, 0.046996556, -0.07465018, 0.01092162, 0.12778427, 0.08029528, -0.089448065, 0.077693745, -0.0034690292], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 259, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.38542175, 0.4958051, 0.021154702, 1.2434933, 0.0, 0.0, 0.4655782, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 6.0, 6.0, -0.07465018, 0.01092162, 5.0, 0.08029528, -0.089448065, 0.077693745, -0.0034690292], "split_indices": [2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.824596, 22.070772, 30.753826, 14.319539, 7.751233, 7.8040943, 22.949732, 9.048941, 5.2705984, 11.748098, 11.2016325], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.026556417, -0.0035384458, 0.1276804, 0.065320894, -0.17430446, 0.06825863, -0.000111884256, 0.009010428, 0.030090924, -0.0711863, -0.012826627], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 260, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.23091066, 0.67402834, 0.24298963, 0.03309293, 0.09953046, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, 9.0, 6.0, 5.0, 0.06825863, -0.000111884256, 0.009010428, 0.030090924, -0.0711863, -0.012826627], "split_indices": [1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [70.64715, 54.425922, 16.221228, 38.808678, 15.617245, 9.116681, 7.104546, 21.668787, 17.139889, 9.5032835, 6.113961], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.026237402, -0.060466684, 0.028389, -0.05311946, -0.008082151, 0.061455432, -0.06098211, 0.043804068, -0.036139086, -0.05379252, 0.0117279515], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 261, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.13154954, 0.2431712, 0.43127906, 0.0, 0.5581692, 0.0, 0.22726549, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [7.0, 2.0, 8.0, -0.05311946, 5.0, 0.061455432, 4.0, 0.043804068, -0.036139086, -0.05379252, 0.0117279515], "split_indices": [0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.72042, 42.07785, 24.642569, 11.714503, 30.363348, 8.226401, 16.416168, 12.303663, 18.059687, 7.6172504, 8.798918], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0046761315, 0.082625024, -0.048168268, -0.018480923, 0.14299291, 0.083641596, -0.13050239, -0.0040529757, 0.114666365, -0.02361386, 0.0832147, -0.0845726, -0.0045759906], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 262, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.24717179, 0.23155563, 0.3905992, 0.0, 0.722152, 0.46406883, 0.3520762, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 4.0, -0.018480923, 3.0, 2.0, 6.0, -0.0040529757, 0.114666365, -0.02361386, 0.0832147, -0.0845726, -0.0045759906], "split_indices": [0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.202656, 23.648457, 33.5542, 6.482885, 17.165571, 12.427844, 21.126356, 10.694657, 6.470915, 6.803819, 5.624025, 8.130846, 12.995511], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.062387608, 0.13538718, -0.14770164, 0.008226214, 0.060790557, -0.08630116, -0.045290183, 0.09906914, -0.05495808], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 263, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.8484291, 0.079933256, 0.45655626, 0.0, 0.0, 0.0, 1.177747, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 5.0, 6.0, 0.008226214, 0.060790557, -0.08630116, 7.0, 0.09906914, -0.05495808], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.025223, 13.943157, 34.082066, 6.4624696, 7.480687, 13.223574, 20.85849, 5.0331297, 15.82536], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.030873531, -0.13637652, 0.10476742, -0.053071156, -0.21076211, -0.065402314, 0.3050571, -0.057122488, 0.010574042, -0.08859584, -0.01362355, -0.029430266, -0.0, 0.14055556, 0.025233187], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 264, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.802578, 0.15892577, 0.8671744, 0.22756016, 0.17315692, 0.020263046, 0.32217276, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 6.0, 6.0, 3.0, 9.0, 4.0, 8.0, -0.057122488, 0.010574042, -0.08859584, -0.01362355, -0.029430266, -0.0, 0.14055556, 0.025233187], "split_indices": [1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.863758, 30.709442, 23.154316, 15.928606, 14.780837, 12.612604, 10.541712, 6.22495, 9.703656, 8.85946, 5.921377, 7.155884, 5.456721, 5.1548944, 5.386818], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.0029761645, 0.05120975, -0.043782596, -0.13148464, 0.10769193, -0.09710497, -0.01338554, 0.04865552, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 265, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.32476428, 0.0, 0.534171, 0.3752219, 0.06974056, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.05120975, 7.0, 2.0, 5.0, -0.09710497, -0.01338554, 0.04865552, -0.0], "split_indices": [2, 0, 2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.561165, 7.596431, 37.964737, 24.545382, 13.4193535, 6.43344, 18.111942, 8.00889, 5.4104633], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0067762695, -0.2134482, 0.05758365, -0.0, -0.10690688, 0.0918434, 0.011116281, -0.088146485, 0.022034006], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 266, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.7516698, 0.42928082, 0.44895214, 0.0, 0.0, 0.0, 0.6968554, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 3.0, 4.0, -0.0, -0.10690688, 0.0918434, 5.0, -0.088146485, 0.022034006], "split_indices": [2, 1, 2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.4148, 12.502899, 40.9119, 5.2780275, 7.2248716, 5.2245517, 35.687347, 5.1540403, 30.533306], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.017506545, -0.036021136, 0.14699532, 0.12672739, -0.14959528, -0.0, 0.08221671, -0.017410312, 0.096023425, 0.0208125, -0.079893455], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 267, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.3879785, 0.7271032, 0.32738003, 0.6119064, 0.6339674, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 5.0, 5.0, 3.0, -0.0, 0.08221671, -0.017410312, 0.096023425, 0.0208125, -0.079893455], "split_indices": [0, 0, 2, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.61039, 37.18749, 15.422899, 14.875305, 22.312183, 7.3392687, 8.083631, 7.700002, 7.175303, 7.600946, 14.711239], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.027325733, 0.06359688, -0.08494137, 0.19834845, -0.08178642, -0.018973142, 0.094354324, -0.042178743, 0.028620174], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 268, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.66678494, 1.0943396, 0.0, 0.9232526, 0.29445204, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 6.0, -0.08494137, 3.0, 6.0, -0.018973142, 0.094354324, -0.042178743, 0.028620174], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.563087, 53.437317, 5.125773, 27.90059, 25.536726, 8.455199, 19.44539, 19.751856, 5.7848687], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007457317, -0.11972339, 0.07463652, 0.008335475, -0.18128231, 0.16682054, 0.019125808, -0.07576444, -0.0057237805, -0.00931656, 0.10571354, 0.057786386, -0.012474505], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 269, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.54511344, 0.23731989, 0.16442816, 0.0, 0.16992909, 0.51564366, 0.28136224, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 4.0, 0.008335475, 3.0, 2.0, 6.0, -0.07576444, -0.0057237805, -0.00931656, 0.10571354, 0.057786386, -0.012474505], "split_indices": [2, 0, 0, 0, 2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.279823, 22.890127, 35.389694, 6.4231277, 16.467, 11.870411, 23.519283, 10.6251955, 5.8418045, 5.8866806, 5.9837303, 5.960142, 17.559141], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.02440033, -0.07691132, 0.034942057, -0.03234218, -0.12096186, 0.08543455, -0.035423357, -0.035814397, 0.043935277, -0.010298126, -0.07062824, -0.0016230111, 0.05042918], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 270, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.22844952, 0.057186663, 0.2631351, 0.35471284, 0.1442872, 0.21253428, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 7.0, 7.0, 8.0, 2.0, -0.035423357, -0.035814397, 0.043935277, -0.010298126, -0.07062824, -0.0016230111, 0.05042918], "split_indices": [0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [70.10273, 37.929066, 32.17366, 20.844, 17.085066, 25.014673, 7.1589885, 14.6189, 6.2250986, 10.961913, 6.1231523, 11.78644, 13.228232], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.009402829, -0.079934336, 0.0140547445, 0.08382439, -0.059683587, 0.0071997005, 0.052045688, -0.09716981, 0.0032884057], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 271, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.43758118, 0.0, 0.32994357, 0.15484451, 0.6016402, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.079934336, 6.0, 7.0, 7.0, 0.0071997005, 0.052045688, -0.09716981, 0.0032884057], "split_indices": [0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.764626, 5.073558, 61.691067, 32.325104, 29.365963, 20.817707, 11.507396, 5.7376165, 23.628345], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.029632645, -0.027321931, 0.1960012, -0.09008049, 0.02163812, 0.017526686, 0.08165639, 0.04813614, -0.012707648], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 272, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.53521943, 0.57908297, 0.10173696, 0.0, 0.33117777, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [8.0, 2.0, 4.0, -0.09008049, 5.0, 0.017526686, 0.08165639, 0.04813614, -0.012707648], "split_indices": [1, 2, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.139515, 39.762074, 13.3774395, 5.577537, 34.184536, 5.865267, 7.5121727, 10.804303, 23.380234], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.017292924, 0.017543843, -0.09882235, 0.12078137, -0.032200374, -0.10321676, 0.029873842, -0.0, 0.06588717, -0.0, -0.03883361], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 273, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.15294446, 0.19395581, 0.84164584, 0.15755942, 0.0840457, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 4.0, 3.0, 8.0, -0.10321676, 0.029873842, -0.0, 0.06588717, -0.0, -0.03883361], "split_indices": [0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.37736, 34.285675, 15.091686, 11.337804, 22.947872, 6.6463685, 8.445317, 5.3398256, 5.9979777, 17.450155, 5.4977164], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.11609767, 0.14056511, -0.21845748, -0.0, 0.06586571, -0.35612518, -0.023694048, -0.0495255, -0.13015434, -0.049716894, 0.015405795], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 274, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [1.4052136, 0.17114922, 0.9687102, 0.0, 0.0, 0.192312, 0.20168932, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 6.0, -0.0, 0.06586571, 1.0, 6.0, -0.0495255, -0.13015434, -0.049716894, 0.015405795], "split_indices": [0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.02189, 14.161743, 36.860146, 5.396356, 8.765387, 20.809263, 16.050884, 7.2932634, 13.516, 5.592012, 10.458871], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0185003, 0.08352883, -0.060552493, -0.01844697, 0.14456907, 0.056944273, 0.0017351361], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 275, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.66643614, 0.34205836, 0.0, 0.0, 0.14644665, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, -0.060552493, -0.01844697, 7.0, 0.056944273, 0.0017351361], "split_indices": [2, 2, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.160255, 35.69925, 9.461008, 10.087602, 25.611649, 18.494171, 7.1174765], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.01968766, 0.038251285, -0.17950931, 0.1275009, -0.077578135, -0.086550444, 0.0036268758, 5.4404272e-06, 0.10646889, -0.034379993, -0.0022588938], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 276, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5208135, 0.4281922, 0.34429514, 0.64420986, 0.032394193, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 9.0, 4.0, 5.0, -0.086550444, 0.0036268758, 5.4404272e-06, 0.10646889, -0.034379993, -0.0022588938], "split_indices": [2, 0, 2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.976974, 38.9806, 13.996378, 22.447094, 16.533504, 8.931696, 5.064682, 15.306041, 7.141054, 9.466634, 7.0668697], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.050432187, -0.07646145, 0.121588714, 0.009159497, -0.05883883, 0.056226287, 0.09390632, 0.038265575, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 277, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.513129, 0.2783319, 0.3999588, 0.0, 0.0, 0.10756719, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 6.0, 7.0, 0.009159497, -0.05883883, 7.0, 0.09390632, 0.038265575, -0.0], "split_indices": [1, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.29984, 18.977428, 35.32241, 9.971394, 9.006036, 27.685574, 7.6368365, 11.185606, 16.499968], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00086248224, -0.06225381, 0.10148017, -0.05201636, -0.0069361012, 0.23094098, -0.036453977, 0.019891486, -0.06262345, 0.022561416, 0.107269056], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 278, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.3726665, 0.21478695, 0.67025393, 0.0, 0.42802435, 0.21503615, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 4.0, -0.05201636, 8.0, 2.0, -0.036453977, 0.019891486, -0.06262345, 0.022561416, 0.107269056], "split_indices": [0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.93275, 37.06671, 20.86604, 11.008448, 26.058264, 13.46286, 7.403179, 19.248474, 6.809789, 7.106307, 6.3565526], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.024260828, 0.03301032, -0.052394353, -0.0093803285, -0.14979196, -0.026769713, 0.062079687, -0.0, -0.07444349], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 279, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.21297604, 0.0, 0.1833489, 0.6113284, 0.2211361, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.03301032, 7.0, 5.0, 4.0, -0.026769713, 0.062079687, -0.0, -0.07444349], "split_indices": [0, 0, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.36943, 8.470399, 46.89903, 34.004528, 12.894502, 25.60295, 8.40158, 5.240213, 7.6542892], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.044634923, 0.17122197, -0.0063882917, 0.09477614, 0.0070456644, 0.022180634, -0.063588805, -0.052753676, 0.01750091], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 280, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.47698468, 0.4082545, 0.3237665, 0.0, 0.0, 0.32312766, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 5.0, 9.0, 0.09477614, 0.0070456644, 4.0, -0.063588805, -0.052753676, 0.01750091], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [70.12806, 20.032402, 50.095657, 9.203181, 10.829221, 44.339417, 5.756241, 5.822115, 38.517303], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.025272788, 0.08338513, -0.07430374, 0.02375152, 0.14076953, -0.03788749, -0.034618687, 0.04427561, -0.025686014, 0.072323926, 0.0026170728, 0.009196671, -0.034067173], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 281, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.36503977, 0.11765286, 0.026349679, 0.31347176, 0.2300303, 0.0, 0.090792574, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 4.0, 1.0, 7.0, -0.03788749, 8.0, 0.04427561, -0.025686014, 0.072323926, 0.0026170728, 0.009196671, -0.034067173], "split_indices": [1, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.85571, 39.165676, 21.69003, 20.754366, 18.411312, 7.354006, 14.336024, 9.998584, 10.755782, 9.563357, 8.847954, 7.488572, 6.847452], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.008739906, 0.09434856, -0.14594379, 0.020889632, 0.23677725, -0.06485965, -0.0, -0.055444565, 0.032385405, 0.09601264, 0.032569364], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 282, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.7280386, 0.34590405, 0.18044674, 0.46550593, 0.047173917, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 7.0, 4.0, 6.0, -0.06485965, -0.0, -0.055444565, 0.032385405, 0.09601264, 0.032569364], "split_indices": [1, 1, 2, 2, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.32822, 34.996296, 18.331928, 24.307993, 10.688301, 11.669755, 6.6621723, 6.553146, 17.754847, 5.100052, 5.5882487], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.019131625, 0.117252134, -0.026127545, 0.00038406553, 0.15944463, 0.005244266, -0.03868665, -0.0004744615, 0.09505882, -0.016883342, 0.026335156], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 283, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.2363181, 0.065986395, 0.12485989, 0.0, 0.32826987, 0.14404939, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 7.0, 0.00038406553, 5.0, 4.0, -0.03868665, -0.0004744615, 0.09505882, -0.016883342, 0.026335156], "split_indices": [0, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.47277, 15.81189, 33.66088, 5.125147, 10.686743, 25.773989, 7.8868923, 5.458171, 5.2285714, 14.2552595, 11.51873], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.102491185, -0.007132954, 0.13632125, 0.20362875, 0.05970777, 0.028570183, 0.088728696, 0.036734384, -0.0044827894], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 284, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.2265752, 0.0, 0.16741496, 0.13393164, 0.1099231, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.007132954, 6.0, 3.0, 7.0, 0.028570183, 0.088728696, 0.036734384, -0.0044827894], "split_indices": [0, 0, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.648224, 9.717972, 38.930252, 19.146383, 19.783867, 10.229934, 8.91645, 11.12941, 8.654457], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.015692165, -0.04362423, 0.023730151, 0.07897352, -0.04151931, -0.08959747, 0.05585987], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 285, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.25005743, 0.0, 0.32228792, 1.1587082, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.04362423, 8.0, 2.0, -0.04151931, -0.08959747, 0.05585987], "split_indices": [1, 0, 1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.843, 10.416098, 34.4269, 26.520134, 7.9067674, 5.263846, 21.256287], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.060560085, -0.18422566, 0.13445471, -0.10058933, -0.110479735, 0.016961578, 0.10290646, 0.013496445, -0.047129214, 0.037723847, -0.015765876], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 286, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [1.2939149, 0.42114627, 0.46313065, 0.21825965, 0.0, 0.123875625, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 7.0, 7.0, 1.0, -0.110479735, 3.0, 0.10290646, 0.013496445, -0.047129214, 0.037723847, -0.015765876], "split_indices": [1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.41895, 31.81321, 19.605742, 23.309992, 8.503216, 13.539336, 6.0664053, 6.0802073, 17.229786, 5.524137, 8.015199], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.02827924, 0.11921031, -0.08626374, -0.003280305, 0.09921051, -0.085669175, 0.017624468, -0.031492043, 0.032658365], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 287, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.473022, 0.4570228, 0.86379814, 0.0, 0.0, 0.0, 0.31411126, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [2.0, 6.0, 6.0, -0.003280305, 0.09921051, -0.085669175, 4.0, -0.031492043, 0.032658365], "split_indices": [1, 2, 1, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.493366, 14.311102, 39.182262, 9.153694, 5.157408, 13.156505, 26.025759, 10.572507, 15.453252], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.089891054, -0.052255724, -0.10286028, -0.10024611, 0.061770186, -0.006511767, -0.08950469, -0.00014358, 0.042273045], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 288, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.48507118, 0.28980315, 0.0, 0.53688985, 0.08872091, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 7.0, -0.10286028, 7.0, 6.0, -0.006511767, -0.08950469, -0.00014358, 0.042273045], "split_indices": [0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.980206, 50.12497, 5.8552365, 35.97987, 14.145102, 26.921053, 9.058814, 7.76656, 6.3785415], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.029284518, 0.06439732, -0.06020901, 0.057007305, 0.015179643, -0.084680624, 0.019871369, -0.032478202, 0.0343718], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 289, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.1802672, 0.23322424, 0.47536272, 0.0, 0.39927188, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 8.0, 0.057007305, 6.0, -0.084680624, 0.019871369, -0.032478202, 0.0343718], "split_indices": [1, 2, 1, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.999496, 40.505127, 14.4943695, 10.024676, 30.480452, 5.098745, 9.395624, 13.106999, 17.373453], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04225921, -0.014630155, 0.13758978, 0.08057558, -0.20722778, 0.06607008, 0.07921885, 0.11477037, -0.014307056, -0.0789157, -0.032189816, -0.003929206, 0.043284606], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 290, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.39968088, 0.84989333, 0.08435094, 1.2261227, 0.020817101, 0.0, 0.12478263, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 3.0, 2.0, 5.0, 0.06607008, 5.0, 0.11477037, -0.014307056, -0.0789157, -0.032189816, -0.003929206, 0.043284606], "split_indices": [2, 2, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [70.05863, 43.748734, 26.309895, 29.318367, 14.4303665, 9.033596, 17.276299, 8.381576, 20.936792, 7.5689073, 6.8614597, 6.8370867, 10.439212], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0806191, -0.036374465, -0.07932684, -0.07703533, 0.05515665, 0.017198794, -0.044869974], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 291, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.47984833, 0.4788854, 0.0, 0.46515647, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 7.0, -0.07932684, 3.0, 0.05515665, 0.017198794, -0.044869974], "split_indices": [1, 1, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.88655, 52.089653, 10.796898, 44.903446, 7.1862082, 15.331888, 29.571556], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.009386962, 0.0836972, -0.043372314, -0.08820303, -0.0038819762, -0.030906662, 0.025614722], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 292, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.85544664, 0.0, 0.4516471, 0.0, 0.39532602, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.0836972, 3.0, -0.08820303, 5.0, -0.030906662, 0.025614722], "split_indices": [0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.633495, 8.877294, 47.756203, 5.2783194, 42.477882, 20.583382, 21.8945], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.028346509, -0.023327107, 0.12572575, 0.009986018, -0.038722064, 0.017897924, 0.04800404, -0.009443436, 0.043893624], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 293, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.2539704, 0.12394044, 0.007941037, 0.15240048, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 4.0, 5.0, -0.038722064, 0.017897924, 0.04800404, -0.009443436, 0.043893624], "split_indices": [2, 2, 1, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.85388, 30.436502, 16.417381, 23.114643, 7.32186, 7.5419426, 8.875439, 17.751125, 5.3635173], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.062202197, 0.07030973, 0.013247602, 0.12679918, -0.19068679, -0.0150727285, 0.0647516, -0.07888584, -0.014786747], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 294, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.3901722, 0.0, 0.9471046, 0.4481895, 0.1033417, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.07030973, 7.0, 3.0, 8.0, -0.0150727285, 0.0647516, -0.07888584, -0.014786747], "split_indices": [2, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.59997, 9.46906, 39.130913, 25.693336, 13.437575, 8.36782, 17.325518, 7.8557415, 5.5818343], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018619029, 0.049123235, -0.031079672, -0.12870945, 0.046189725, -0.05580521, -0.0, -0.016363172, 0.04289165], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 295, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.20776404, 0.0, 0.31058782, 0.13620141, 0.23205753, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.049123235, 6.0, 6.0, 5.0, -0.05580521, -0.0, -0.016363172, 0.04289165], "split_indices": [0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [43.628548, 5.3391967, 38.28935, 17.193647, 21.095701, 11.646489, 5.5471587, 10.044408, 11.051293], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04329052, 0.16892777, -0.02980753, 0.0009619106, 0.06618861, -0.09198133, 0.03569666, -0.085331604, -0.006339547], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 296, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.4921553, 0.14488637, 0.31280252, 0.0, 0.0, 0.29418796, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 3.0, 7.0, 0.0009619106, 0.06618861, 5.0, 0.03569666, -0.085331604, -0.006339547], "split_indices": [1, 2, 2, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.52616, 18.60532, 31.92084, 5.1366363, 13.468684, 23.256939, 8.6639, 5.066915, 18.190023], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.02662258, 0.007301977, -0.05478241, 0.03280134, -0.017425032, -0.058954805, 0.0132692335], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 297, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.29754937, 0.12133208, 0.0, 0.0, 0.41306853, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, -0.05478241, 0.03280134, 4.0, -0.058954805, 0.0132692335], "split_indices": [0, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.664257, 42.691566, 8.972691, 8.4490385, 34.242527, 8.624791, 25.617737], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.01980143, -0.027609592, 0.08994995, 0.007843374, -0.096894324, 0.11422305, 0.007847033, -0.02552195, 0.03150054, -0.006220293, -0.0494775, 0.05860858, -0.004700098], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 298, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.20647018, 0.09660665, 0.021786883, 0.222029, 0.045424595, 0.19728892, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 6.0, 7.0, 7.0, 8.0, 0.007847033, -0.02552195, 0.03150054, -0.006220293, -0.0494775, 0.05860858, -0.004700098], "split_indices": [1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.459946, 34.394634, 24.06531, 22.38415, 12.010485, 15.72421, 8.3411, 10.979321, 11.40483, 6.9758067, 5.034678, 9.841999, 5.8822107], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.044702113, 0.017236207, -0.059631232, -0.10486147, 0.0019226026, -0.01442985, -0.055056177, 0.03443443, -0.035753086], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 299, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.09277196, 0.0, 0.15619841, 0.106963545, 0.31682155, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.017236207, 6.0, 7.0, 8.0, -0.01442985, -0.055056177, 0.03443443, -0.035753086], "split_indices": [0, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.440838, 6.1068, 51.334038, 30.121525, 21.212511, 19.170507, 10.951017, 11.434272, 9.778239], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0030439438, -0.14189062, 0.037054814, 0.0051475666, -0.07305139, -0.042812232, 0.072830126, 0.04097368, 0.0077596717], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 300, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.4418462, 0.31636047, 0.3868087, 0.0, 0.0, 0.0, 0.13245875, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 1.0, 1.0, 0.0051475666, -0.07305139, -0.042812232, 4.0, 0.04097368, 0.0077596717], "split_indices": [2, 2, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [75.53323, 16.915754, 58.617477, 6.534613, 10.381141, 8.769359, 49.848118, 19.43289, 30.41523], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.022760797, 0.07565171, -0.11594153, -0.023213293, 0.07933549, -0.17841864, 0.015652437, 0.031533863, -0.028037326, 0.0064462414, -0.117168374], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 301, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.60336083, 0.6164421, 0.37901008, 0.1996039, 0.0, 1.124395, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 9.0, 1.0, 0.07933549, 5.0, 0.015652437, 0.031533863, -0.028037326, 0.0064462414, -0.117168374], "split_indices": [0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.400326, 30.404087, 32.996235, 20.182774, 10.221314, 24.445206, 8.551031, 6.4604697, 13.722303, 12.805584, 11.639622], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.007534226, -0.029236984, 0.046864323, 0.00054964336, -0.039099194, -0.022132134, 0.018000588], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 302, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.32297257, 0.14895618, 0.0, 0.15811536, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 6.0, 0.046864323, 3.0, -0.039099194, -0.022132134, 0.018000588], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.91718, 44.133068, 10.784108, 34.09312, 10.039948, 14.297013, 19.796108], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.06912159, 0.04298014, 0.05674368, 0.08158672, -0.021809151, 0.010114752, 0.0822032], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 303, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.13249797, 0.20714849, 0.0, 0.2801067, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 7.0, 0.05674368, 6.0, -0.021809151, 0.010114752, 0.0822032], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.21824, 43.8573, 7.3609405, 33.75197, 10.10533, 28.38524, 5.366727], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.049761277, -0.08334718, 0.104863495, -0.05379035, 0.013375324, 0.22775668, 0.026166447, 0.08181584, 0.03516531, -0.02670818, 0.03043891], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 304, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.4041456, 0.21447502, 0.34479907, 0.0, 0.0, 0.0013909936, 0.23003328, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 6.0, 6.0, -0.05379035, 0.013375324, 6.0, 8.0, 0.08181584, 0.03516531, -0.02670818, 0.03043891], "split_indices": [2, 0, 2, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.616478, 14.723074, 37.893402, 8.630198, 6.092876, 13.5574, 24.336004, 7.951298, 5.6061015, 9.02244, 15.313563], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.012953729, 0.0542834, -0.032750826, -0.064992584, 0.014178209, 0.04647333, -0.017028527], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 305, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.36546803, 0.0, 0.3311779, 0.0, 0.30739564, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.0542834, 2.0, -0.064992584, 5.0, 0.04647333, -0.017028527], "split_indices": [1, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [43.89773, 9.223808, 34.673923, 6.7476816, 27.92624, 9.434915, 18.491325], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.015651902, 0.1374331, -0.04232837, 0.08892178, 0.023873413, -0.093513474, 0.05820962, -0.006604727, 0.02307253, 0.06520705, -0.010891657], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 306, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.40766367, 0.29872957, 1.068622, 0.0, 0.037288845, 0.0, 0.44921404, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [3.0, 3.0, 2.0, 0.08892178, 6.0, -0.093513474, 5.0, -0.006604727, 0.02307253, 0.06520705, -0.010891657], "split_indices": [0, 1, 1, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.56451, 17.713541, 36.85097, 6.343089, 11.370453, 9.714904, 27.136066, 5.3985934, 5.9718595, 10.02091, 17.115156], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.039797973, 0.054558136, -0.08051084, -0.09198025, -0.010690294, 0.030092778, -0.013216306], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 307, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.50960684, 0.0, 0.7283899, 0.0, 0.14072028, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.054558136, 3.0, -0.09198025, 5.0, 0.030092778, -0.013216306], "split_indices": [1, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.9708, 7.610861, 47.359936, 10.036535, 37.323402, 7.469437, 29.853964], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0089545315, 0.110892296, -0.10241714, 0.087489486, 0.058543794, -0.09960869, -0.0073281894, -0.017447622, 0.04234361, -0.028717348, 0.027291898], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 308, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.6611888, 0.23927516, 0.56119514, 0.0, 0.26289266, 0.0, 0.18892117, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 6.0, 0.087489486, 7.0, -0.09960869, 5.0, -0.017447622, 0.04234361, -0.028717348, 0.027291898], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.1642, 29.795118, 26.36908, 5.2359953, 24.559124, 6.6845183, 19.68456, 9.799421, 14.759702, 10.913871, 8.77069], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0047372207, 0.043767177, -0.1393193, 0.15671718, -0.0, -0.09186846, -0.0018120365, 0.0039151837, 0.07586834, -0.021834152, 0.0154938055], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 309, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.39394855, 0.19935979, 0.32592237, 0.12840286, 0.12372552, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 5.0, 2.0, 6.0, -0.09186846, -0.0018120365, 0.0039151837, 0.07586834, -0.021834152, 0.0154938055], "split_indices": [0, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.95129, 41.813236, 15.138054, 10.456361, 31.356874, 5.8118176, 9.326236, 5.0653462, 5.391014, 12.21538, 19.141495], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.013923641, -0.07539264, 0.11163919, -0.13431722, 0.024334155, 0.18558373, -0.031958397, -0.013815978, -0.08149478, -0.034177274, 0.06746758, 0.035969827, 0.10840874], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 310, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6584681, 0.24202777, 0.6134089, 0.2605399, 0.4418823, 0.22980237, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 8.0, 2.0, 2.0, 7.0, -0.031958397, -0.013815978, -0.08149478, -0.034177274, 0.06746758, 0.035969827, 0.10840874], "split_indices": [1, 2, 2, 1, 1, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [73.06705, 37.758865, 35.308186, 24.147161, 13.611704, 26.824224, 8.483959, 15.955797, 8.191364, 8.017818, 5.5938854, 21.166, 5.658226], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.014152772, 0.04724585, -0.12871218, 0.09660003, -0.096525684, -0.057477463, -0.009102042, 0.014029856, 0.086966366, -0.07634632, 0.018289542], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 311, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.326507, 0.4189437, 0.050988734, 0.37651965, 0.40143037, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 8.0, 9.0, 7.0, 4.0, -0.057477463, -0.009102042, 0.014029856, 0.086966366, -0.07634632, 0.018289542], "split_indices": [1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.497375, 56.72656, 11.770818, 42.99187, 13.73469, 5.9825835, 5.7882338, 35.578312, 7.413557, 6.8666067, 6.8680825], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.035315298, 0.04011949, -0.24328226, 0.07799819, -0.046414353, -0.04306637, -0.100673154, -0.004411659, 0.053245466, -0.051818836, 0.017536305], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 312, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.92154586, 0.14511842, 0.04768449, 0.30048025, 0.18939684, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, 9.0, 3.0, 4.0, -0.04306637, -0.100673154, -0.004411659, 0.053245466, -0.051818836, 0.017536305], "split_indices": [1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.82512, 41.205208, 14.619912, 29.532236, 11.672972, 8.776013, 5.843899, 15.208072, 14.324163, 5.4340687, 6.2389035], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.03448025, -0.059758913, 0.09591339, 0.04428377, -0.06704238, 0.112779066, -0.0, -0.008307271, 0.039437562, 0.019041982, 0.04659198], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 313, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.300385, 0.3652841, 0.04199913, 0.092916615, 0.0, 0.022898018, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 6.0, 7.0, 3.0, -0.06704238, 4.0, -0.0, -0.008307271, 0.039437562, 0.019041982, 0.04659198], "split_indices": [2, 1, 1, 2, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.270752, 18.812151, 30.4586, 11.5749, 7.2372527, 25.055159, 5.403441, 6.08562, 5.4892793, 13.846113, 11.209045], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0054858658, -0.022810534, 0.06391032, 0.020526785, -0.105626866, -0.01085669, 0.06508724, -0.045091197, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 314, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.32296306, 0.17324403, 0.0, 0.3576939, 0.06545943, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 6.0, 0.06391032, 4.0, 8.0, -0.01085669, 0.06508724, -0.045091197, -0.0], "split_indices": [1, 1, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.925854, 44.295105, 5.63075, 28.779568, 15.515535, 22.569672, 6.209897, 10.053989, 5.461546], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.09146259, -0.0, 0.16030318, 0.07734458, -0.0349213, 0.10404755, 0.08166701, -0.019465886, 0.07053366, 0.08080277, -0.001322616], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 315, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.28258613, 0.19571203, 0.30881655, 0.3316459, 0.0, 0.0, 0.34929967, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 5.0, 2.0, -0.0349213, 0.10404755, 3.0, -0.019465886, 0.07053366, 0.08080277, -0.001322616], "split_indices": [1, 2, 1, 1, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.80203, 19.894884, 24.907146, 12.341153, 7.5537314, 5.9979806, 18.909164, 6.466418, 5.8747354, 5.641448, 13.267717], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.092792675, 0.04594183, -0.17507516, 0.069757745, -0.06150511, -0.09212896, -0.11306262, -0.09733384, 0.060519505, 0.004927337, -0.06907943], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 316, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.6332855, 0.43856645, 0.20433009, 0.0, 1.000963, 0.0, 0.41150555, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 2.0, 0.069757745, 7.0, -0.09212896, 6.0, -0.09733384, 0.060519505, 0.004927337, -0.06907943], "split_indices": [2, 1, 1, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.64051, 19.309523, 33.33099, 6.9709344, 12.338589, 8.87399, 24.456999, 6.246484, 6.092105, 11.622682, 12.834317], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.055787582, -0.043272052, 0.09692968, -0.084709674, 0.030148653, 0.06736662, -0.012814682, 0.010141731, -0.024745306], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 317, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.22745, 0.5850923, 0.56744003, 0.0, 0.0, 0.0, 0.07711269, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 6.0, 4.0, -0.084709674, 0.030148653, 0.06736662, 8.0, 0.010141731, -0.024745306], "split_indices": [0, 1, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.46307, 14.6668005, 37.79627, 5.406693, 9.260107, 17.41212, 20.38415, 11.660671, 8.723479], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.03342873, -0.10122299, 0.096481174, -0.047000293, -0.09315924, -0.0021808166, 0.0738432, 0.025693862, -0.037633125], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 318, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.4904946, 0.3675887, 0.31612003, 0.33526736, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 6.0, 3.0, -0.09315924, -0.0021808166, 0.0738432, 0.025693862, -0.037633125], "split_indices": [1, 1, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.530075, 35.82511, 17.704964, 29.805975, 6.019136, 10.623624, 7.08134, 10.568389, 19.237587], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.067226, -0.07207073, -0.032746088, -0.07526136, 0.03995869, 0.009501316, -0.038404245, 0.055099603, -0.024548601], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 319, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.29504532, 0.0, 0.1542773, 0.19067006, 0.33011848, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.07207073, 6.0, 5.0, 8.0, 0.009501316, -0.038404245, 0.055099603, -0.024548601], "split_indices": [2, 0, 1, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.354618, 7.5588655, 46.795753, 30.374212, 16.421541, 9.540058, 20.834154, 7.676475, 8.745066], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.019964278, -0.052590657, 0.055183046, 0.0065980717, -0.1415697, -0.0427234, 0.020880079, -0.025521144, -0.07431136], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 320, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.46438935, 0.34003323, 0.0, 0.35381338, 0.09806126, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 6.0, 0.055183046, 2.0, 7.0, -0.0427234, 0.020880079, -0.025521144, -0.07431136], "split_indices": [2, 2, 0, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [69.3242, 60.824455, 8.499748, 36.407936, 24.41652, 9.972901, 26.435034, 17.72475, 6.691767], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.021642875, 0.10568891, -0.20665194, 0.029389216, 0.18344218, -0.0831556, -0.0, -0.021825552, 0.046359368, 0.13353601, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 321, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [1.5700755, 0.20479247, 0.38029718, 0.2946476, 0.9139761, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 7.0, 4.0, 6.0, -0.0831556, -0.0, -0.021825552, 0.046359368, 0.13353601, -0.0], "split_indices": [0, 2, 2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.29284, 38.016415, 26.27643, 20.532179, 17.484236, 19.110682, 7.1657476, 11.095539, 9.436639, 6.7523613, 10.7318735], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.031910937, 0.0575641, -0.020252064, 0.03923163, -0.13917328, -0.018796982, 0.026436029, -0.021145772, -0.05819207], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 322, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.5104945, 0.0, 0.33140847, 0.15746441, 0.018874645, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, 0.0575641, 7.0, 6.0, 7.0, -0.018796982, 0.026436029, -0.021145772, -0.05819207], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.655003, 13.990041, 43.664963, 28.992365, 14.672598, 8.646059, 20.346306, 8.335638, 6.3369594], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0031393953, 0.15799089, -0.06423441, -0.0, 0.0698306, -0.078146756, 0.04208023, -0.029282147, 0.034150045], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 323, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.5360585, 0.17707995, 0.75807375, 0.0, 0.0, 0.0, 0.24403587, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 1.0, 6.0, -0.0, 0.0698306, -0.078146756, 3.0, -0.029282147, 0.034150045], "split_indices": [0, 0, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.50998, 14.749378, 33.7606, 5.2254014, 9.523977, 11.683508, 22.077093, 6.8967314, 15.180362], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.017085282, -0.05615057, 0.1098141, -0.14653297, 0.03433299, -0.017189506, 0.17262943, -0.0, -0.06624314, 0.08513507, 0.017731687], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 324, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.381922, 0.49074847, 0.28394142, 0.23409936, 0.0, 0.0, 0.18692791, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 7.0, 3.0, 2.0, 0.03433299, -0.017189506, 7.0, -0.0, -0.06624314, 0.08513507, 0.017731687], "split_indices": [1, 2, 2, 2, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.540646, 29.532423, 24.008223, 19.780005, 9.75242, 6.1480236, 17.860199, 6.827504, 12.952499, 7.821409, 10.038789], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.014237905, -0.12894782, 0.119136415, -0.0885646, 0.014125662, 0.1721975, -0.0, -0.005438547, 0.07890079], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 325, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.73593634, 0.63549995, 0.18227845, 0.0, 0.0, 0.37250465, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 7.0, 7.0, -0.0885646, 0.014125662, 3.0, -0.0, -0.005438547, 0.07890079], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.03972, 19.389977, 27.649744, 9.915548, 9.474428, 18.861006, 8.788737, 5.9948993, 12.866107], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.024122706, -0.0050437204, 0.06464449, 0.09201072, -0.068975136, -0.0058550136, 0.09379346, -0.03589145, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 326, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.3003054, 0.27564472, 0.0, 0.45841634, 0.08618404, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 3.0, 0.06464449, 2.0, 5.0, -0.0058550136, 0.09379346, -0.03589145, -0.0], "split_indices": [0, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.84089, 42.77266, 6.068232, 16.190474, 26.582186, 11.05874, 5.131734, 14.189252, 12.392933], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.010151645, 0.075940035, -0.03903988, -0.07963195, 0.022187637, -0.010413484, 0.065268494], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 327, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.6369316, 0.0, 0.62160593, 0.0, 0.40554252, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.075940035, 3.0, -0.07963195, 8.0, -0.010413484, 0.065268494], "split_indices": [1, 0, 1, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.669945, 8.006885, 41.66306, 8.5065365, 33.15652, 25.918661, 7.2378607], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.04872855, -0.011917317, -0.09760795, 0.04907738, -0.074692406, 0.036786932, -0.033115737, -0.050893433, -0.000467149], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 328, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.5506594, 0.20663859, 0.0, 0.32370052, 0.1701036, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, -0.09760795, 7.0, 7.0, 0.036786932, -0.033115737, -0.050893433, -0.000467149], "split_indices": [0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.76849, 51.341118, 5.427374, 25.34254, 25.998577, 17.930826, 7.4117146, 10.1429405, 15.855637], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.028928408, -0.05896284, 0.08901554, 0.02946896, -0.14082158, -0.013075103, 0.14922784, -0.08106281, 0.0046187174, 0.1023006, 0.007677657], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 329, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.3047183, 0.31044635, 0.29185176, 0.0, 0.34838048, 0.0, 0.5334553, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 4.0, 0.02946896, 7.0, -0.013075103, 6.0, -0.08106281, 0.0046187174, 0.1023006, 0.007677657], "split_indices": [0, 2, 2, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.13651, 21.720194, 33.416317, 6.950875, 14.76932, 10.003042, 23.413273, 8.029379, 6.739941, 8.17143, 15.241843], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.078961514, -0.03990044, -0.16169477, -0.10989029, 0.0312882, 0.008656704, -0.3420886, -0.006472082, -0.055870116, 0.021923441, -0.015471734, -0.14013582, -0.04124406], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 330, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.21410882, 0.2741122, 0.822887, 0.16080403, 0.09671204, 0.0, 0.18305206, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 3.0, 5.0, 7.0, 0.008656704, 6.0, -0.006472082, -0.055870116, 0.021923441, -0.015471734, -0.14013582, -0.04124406], "split_indices": [0, 0, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [73.48828, 51.74755, 21.740726, 26.527668, 25.219881, 10.711667, 11.02906, 13.5013275, 13.0263405, 17.645472, 7.5744104, 5.7460628, 5.2829976], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.016071783, -0.16316341, 0.055255942, -0.097575, -0.029915115, 0.13297932, -0.010690141, 0.054765325, -0.06868511, -0.0, 0.11716813, -0.061465085, 0.028630242], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 331, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.7032588, 0.42955536, 0.23998608, 0.0, 0.61754334, 0.71043444, 0.52755004, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 4.0, -0.097575, 6.0, 3.0, 6.0, 0.054765325, -0.06868511, -0.0, 0.11716813, -0.061465085, 0.028630242], "split_indices": [0, 2, 2, 0, 2, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.14213, 20.983839, 43.158287, 8.47871, 12.505128, 20.039658, 23.11863, 5.8455334, 6.659595, 13.9431095, 6.096547, 8.173638, 14.944991], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.08232262, -0.21282317, 0.021306084, -0.14437461, -0.0, 0.1846309, -0.10863825, 0.041336574, -0.036294356, 0.0073854444, 0.086968675, -0.06832831, -0.002066262], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 332, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.782045, 1.4772797, 0.7012433, 0.0, 0.26783082, 0.21026582, 0.19270474, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 6.0, 5.0, -0.14437461, 8.0, 2.0, 7.0, 0.041336574, -0.036294356, 0.0073854444, 0.086968675, -0.06832831, -0.002066262], "split_indices": [1, 2, 2, 0, 2, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.076694, 24.338404, 30.738293, 10.333967, 14.0044365, 13.77431, 16.963982, 6.7215085, 7.2829275, 6.332356, 7.4419546, 6.791611, 10.172371], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0169798, -0.065184236, 0.03715131, 0.11296867, -0.036584985, 0.0684605, -0.0011014191, -0.041891597, 0.019408418], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 333, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.55003417, 0.0, 0.22702509, 0.29072437, 0.22116493, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.065184236, 5.0, 6.0, 5.0, 0.0684605, -0.0011014191, -0.041891597, 0.019408418], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.32357, 9.820019, 37.50355, 18.897167, 18.606384, 9.456059, 9.441109, 9.524094, 9.082291], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.015926063, 0.106532805, -0.026922518, -0.0029742466, 0.08831919, 0.07299428, -0.14326654, 0.08893966, -0.05539094, -0.13620353, 0.022952862], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 334, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.21675141, 0.42471826, 0.43666774, 0.0, 0.0, 1.1943195, 1.2649186, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 7.0, 6.0, -0.0029742466, 0.08831919, 7.0, 7.0, 0.08893966, -0.05539094, -0.13620353, 0.022952862], "split_indices": [1, 2, 2, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.040348, 16.980745, 35.0596, 10.739094, 6.2416506, 18.599417, 16.460186, 10.087148, 8.512269, 6.565843, 9.894342], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.02594613, -0.04751614, 0.07247326, 0.0024482333, 0.16344605, 0.023009285, -0.019171879, -0.0060799667, 0.083197355], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 335, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.39247048, 0.0, 0.22119205, 0.11832984, 0.35823148, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.04751614, 7.0, 6.0, 5.0, 0.023009285, -0.019171879, -0.0060799667, 0.083197355], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.424488, 8.035523, 36.388966, 21.763193, 14.6257715, 10.908253, 10.854941, 5.5753427, 9.050428], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.045539506, -0.04715335, 0.12709033, -0.10393107, 0.0071638008, 0.19162926, -0.0350163, -0.0, -0.05953711, 0.017188787, 0.07454342], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 336, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.400535, 0.10763836, 0.4711966, 0.1277958, 0.0, 0.12991321, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 8.0, 2.0, 0.0071638008, 6.0, -0.0350163, -0.0, -0.05953711, 0.017188787, 0.07454342], "split_indices": [1, 2, 2, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.136314, 23.088673, 27.04764, 13.35691, 9.731763, 21.88, 5.167642, 7.1746583, 6.1822515, 7.626217, 14.253781], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.063955896, -0.031722054, -0.008002194, 0.045236275, 0.031499676, -0.07594182, -0.0008881177, 0.04129733], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 337, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.107400544, 0.15129107, 0.54909164, 0.0, 0.0, 0.12134169, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 7.0, 8.0, -0.008002194, 0.045236275, 8.0, -0.07594182, -0.0008881177, 0.04129733], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.20905, 16.13665, 36.0724, 7.671127, 8.465523, 28.376192, 7.6962094, 21.445024, 6.931166], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0391963, 0.067386866, -0.051414523, -0.039790634, 0.107103154, 0.06300746, 0.014282396], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 338, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.33297384, 0.41231024, 0.0, 0.0, 0.22784498, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 2.0, -0.051414523, -0.039790634, 3.0, 0.06300746, 0.014282396], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.897385, 49.43437, 5.4630175, 7.3848367, 42.049534, 13.860718, 28.188814], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.027750801, -0.03484235, -0.032051917, 0.057150003, 0.014783882, -0.0649847], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 339, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.1788573, 0.45033163, 0.0, 0.51666385, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 6.0, -0.03484235, 7.0, 0.057150003, 0.014783882, -0.0649847], "split_indices": [0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.76081, 43.013954, 9.746855, 31.593454, 11.4205, 22.06241, 9.531044], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.022243101, -0.043562267, 0.17807758, 0.0016559202, -0.16626155, 0.07077397, -0.0, -0.029214354, 0.009254455, -0.08608106, -0.015211543], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 340, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.73592675, 0.29057246, 0.22398537, 0.101758875, 0.14546737, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 8.0, 3.0, 5.0, 0.07077397, -0.0, -0.029214354, 0.009254455, -0.08608106, -0.015211543], "split_indices": [1, 1, 2, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.6963, 48.355583, 20.340717, 35.459316, 12.896267, 15.184231, 5.156486, 6.7609224, 28.698395, 5.15962, 7.7366467], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.035307482, 0.0699773, -0.0, -0.051029574, -0.01195729, 0.05677998], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 341, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.5457249, 0.26527655, 0.0, 0.35229737, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, 0.0699773, 7.0, -0.051029574, -0.01195729, 0.05677998], "split_indices": [0, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.779976, 56.057243, 7.722733, 45.430412, 10.626829, 38.340374, 7.0900373], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.01872614, 0.052095868, -0.064909175, 0.04570151, -0.0033430425, -0.041376233, 0.009483971, -0.03521156, 0.0424407], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 342, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.16809158, 0.259504, 0.13164507, 0.0, 0.4867571, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, 9.0, 0.04570151, 8.0, -0.041376233, 0.009483971, -0.03521156, 0.0424407], "split_indices": [2, 1, 2, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.476208, 42.92307, 15.553139, 15.30503, 27.618038, 9.20068, 6.35246, 15.943637, 11.674401], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.05274178, -0.103318654, 0.030077916, -0.02777206, -0.23257707, 0.042055618, -0.024976658, -0.09331776, -0.039706204], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 343, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.3953318, 0.341929, 0.0, 0.24662301, 0.020831943, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 7.0, 0.030077916, 2.0, 4.0, 0.042055618, -0.024976658, -0.09331776, -0.039706204], "split_indices": [0, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.709286, 37.343246, 11.366043, 24.823412, 12.519832, 5.3373704, 19.486042, 5.282106, 7.2377257], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.005424394, 0.08471554, -0.029065011, -0.122878104, 0.0581946, -0.0042090756, -0.061913516, -0.012622549, 0.045891367], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 344, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.54759556, 0.0, 0.41016662, 0.19517529, 0.25723302, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.08471554, 6.0, 5.0, 5.0, -0.0042090756, -0.061913516, -0.012622549, 0.045891367], "split_indices": [0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.815407, 5.373818, 47.441586, 23.217833, 24.223753, 11.10604, 12.111793, 11.528268, 12.695486], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.03722193, 0.06268509, -0.03776143, -0.0, 0.10011248, 0.04192576, -0.022947209, -0.047042713, 0.05626379], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 345, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.19985414, 0.097055614, 0.0, 0.19570519, 0.6242788, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 4.0, -0.03776143, 4.0, 3.0, 0.04192576, -0.022947209, -0.047042713, 0.05626379], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.74587, 41.49604, 5.2498274, 16.151297, 25.344746, 5.5427966, 10.6085, 5.9432955, 19.40145], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.03225033, 0.04123977, -0.10891093, 0.04834431, -0.03022102, -0.048370875, -0.082244106, -0.03630615, 0.013288873, -0.059086572, 0.024255287], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 346, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.29051897, 0.23532596, 0.20659032, 0.0, 0.1227395, 0.41348505, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 7.0, 0.04834431, 2.0, 4.0, -0.082244106, -0.03630615, 0.013288873, -0.059086572, 0.024255287], "split_indices": [1, 2, 2, 0, 1, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.56879, 24.367077, 24.201714, 9.153029, 15.2140465, 19.101234, 5.1004777, 7.1814017, 8.032645, 9.001107, 10.100128], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.041482724, 0.06554956, -0.04145592, 0.039179567, 0.06061385, 0.026068581, -0.04278122], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 347, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.23685852, 0.144726, 0.0, 0.37990808, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, -0.04145592, 8.0, 0.06061385, 0.026068581, -0.04278122], "split_indices": [2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.361813, 48.109276, 5.252536, 42.012478, 6.0968, 34.126762, 7.885716], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0039659915, 0.051508646, -0.018286359, 0.01773627, -0.09198224, 0.014559009, -0.016222078], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 348, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.23232414, 0.0, 0.5636083, 0.10365217, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, 0.051508646, 9.0, 7.0, -0.09198224, 0.014559009, -0.016222078], "split_indices": [1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.29422, 6.333424, 49.960796, 44.93793, 5.022864, 32.65252, 12.285415], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.03832489, 0.007960957, 0.07164679, 0.054731816, -0.06129399, 0.037553854, -0.009155634, -0.08570467, 0.0109298], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 349, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.3088934, 0.16246745, 0.0, 0.19845334, 0.46279114, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 6.0, 0.07164679, 5.0, 3.0, 0.037553854, -0.009155634, -0.08570467, 0.0109298], "split_indices": [2, 2, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.10489, 48.324898, 5.779991, 29.831821, 18.493074, 16.719847, 13.111975, 5.3375697, 13.155504], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.036486715, 0.05416496, -0.061807364, -0.10986989, 0.035525825, -0.00902149, -0.063657016, 0.042481013, -0.00026069797], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 350, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.3860036, 0.0, 0.31155574, 0.32778233, 0.09635921, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.05416496, 7.0, 3.0, 4.0, -0.00902149, -0.063657016, 0.042481013, -0.00026069797], "split_indices": [2, 0, 1, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [69.36994, 6.1944213, 63.175518, 42.893547, 20.281971, 25.375061, 17.518486, 5.1445074, 15.137463], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.037224483, -0.07690172, 0.07863767, -0.023877345, -0.042054743, 0.0009893312], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 351, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.5936603, 0.7666171, 0.0, 0.0, 0.1491528, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 2.0, -0.07690172, 0.07863767, 3.0, -0.042054743, 0.0009893312], "split_indices": [0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.502113, 52.59777, 6.904341, 10.883793, 41.71398, 7.8099594, 33.904022], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.027996402, 0.03519421, -0.0631691, -0.14718823, 0.07001663, -0.07275565, 0.00047062148, 0.042190626, -0.013981498], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 352, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.28788522, 0.0, 0.5354508, 0.4376551, 0.16505313, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.03519421, 7.0, 8.0, 6.0, -0.07275565, 0.00047062148, 0.042190626, -0.013981498], "split_indices": [0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.93648, 9.6968775, 45.239605, 28.13596, 17.103645, 17.172766, 10.963193, 11.05873, 6.0449157], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.009614629, -0.029486638, 0.027794585, 0.022925807, -0.112383045, -0.01403214, 0.081486486, -0.09724001, 0.005300147], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 353, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.10327375, 0.20966642, 0.0, 0.5210816, 0.5478594, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, 0.027794585, 4.0, 4.0, -0.01403214, 0.081486486, -0.09724001, 0.005300147], "split_indices": [0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.583065, 44.604877, 6.9781866, 27.031155, 17.573723, 21.433905, 5.5972505, 6.4112926, 11.16243], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.034779955, 0.056296814, 0.008502415, 0.04083436, -0.017919738, -0.055322118, 0.004294374], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 354, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.19037302, 0.0, 0.17121467, 0.0, 0.22786991, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.056296814, 1.0, 0.04083436, 2.0, -0.055322118, 0.004294374], "split_indices": [0, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.66953, 6.1197176, 45.549812, 7.75043, 37.79938, 5.8688354, 31.930548], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.010836264, 0.04635489, -0.040601134, 0.078086, -0.035730183, -0.0, 0.04594864], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 355, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.23888217, 0.20847231, 0.0, 0.20137344, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 9.0, -0.040601134, 4.0, -0.035730183, -0.0, 0.04594864], "split_indices": [1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.516277, 37.71815, 7.7981257, 32.598793, 5.1193614, 16.281525, 16.317266], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.06290627, 0.09167278, -0.027210945, 0.05426327, 0.061166514, -0.015538326, 0.033138227], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 356, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.23502113, 0.08512133, 0.0, 0.0, 0.2082177, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, -0.027210945, 0.05426327, 2.0, -0.015538326, 0.033138227], "split_indices": [2, 2, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.28289, 43.2396, 7.043289, 8.927391, 34.31221, 9.792563, 24.519648], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.014291298, -0.1541406, 0.041349057, -0.087989986, -0.0, 0.18196142, -0.07830394, 0.10577322, -0.0, -0.06604593, 0.03705382], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 357, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.4567949, 0.3502836, 0.7085375, 0.0, 0.0, 0.63341206, 0.67458236, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 5.0, 5.0, -0.087989986, -0.0, 6.0, 7.0, 0.10577322, -0.0, -0.06604593, 0.03705382], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.435833, 15.785843, 39.649986, 7.707993, 8.07785, 18.373293, 21.276695, 9.25543, 9.117863, 12.732297, 8.544398], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.024463363, -0.10114891, 0.072040215, 0.043764032, -0.08510622, -0.024459355, 0.15089928, 0.029417636, -0.091459386, 0.022277847, 0.082164496], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 358, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.35239303, 0.7692424, 0.34658468, 0.0, 0.0, 0.7310127, 0.17662966, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 5.0, 5.0, 0.043764032, -0.08510622, 4.0, 8.0, 0.029417636, -0.091459386, 0.022277847, 0.082164496], "split_indices": [2, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.233494, 14.835905, 42.39759, 6.1568484, 8.679057, 18.806225, 23.591366, 13.292131, 5.514093, 16.023092, 7.5682735], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.013467376, -0.059843823, 0.04832691, 0.005477641, 0.14000235, 0.016024275, -0.053302813, 0.011557365, 0.07134635], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 359, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.4070891, 0.0, 0.17704159, 0.30571225, 0.11024684, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.059843823, 8.0, 8.0, 6.0, 0.016024275, -0.053302813, 0.011557365, 0.07134635], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.577477, 6.6097007, 47.967777, 34.15447, 13.813306, 28.063734, 6.0907364, 7.965737, 5.8475695], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.010093294, -0.04397498, 0.039660357, 0.005219089, 0.070034534, 0.024770644, -0.03478807], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 360, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.32528803, 0.0, 0.3839265, 0.507652, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.04397498, 9.0, 5.0, 0.070034534, 0.024770644, -0.03478807], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [70.27236, 9.990846, 60.281517, 52.494766, 7.78675, 32.74025, 19.754517], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.027106078, -0.040183347, 0.06286761, 0.053499985, -0.0493691, 0.0017000708, 0.116182506, -0.06161501, 0.084506765, 0.03405527, -0.039592154], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 361, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.15658261, 0.2707777, 0.8047362, 0.80840874, 0.0, 0.567106, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [3.0, 2.0, 9.0, 5.0, -0.0493691, 5.0, 0.116182506, -0.06161501, 0.084506765, 0.03405527, -0.039592154], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.07748, 20.5293, 41.54818, 11.576075, 8.953226, 36.062107, 5.486074, 5.264804, 6.311271, 20.125834, 15.936272], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.01593925, -0.042142287, 0.14731891, -0.005056071, -0.05511681, 0.14496447, -0.0069385096, 0.008146069, -0.029731838], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 362, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.4354579, 0.18130359, 1.0632626, 0.1066409, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 4.0, 8.0, -0.05511681, 0.14496447, -0.0069385096, 0.008146069, -0.029731838], "split_indices": [0, 0, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.874397, 37.30177, 16.57263, 30.939388, 6.36238, 5.1585402, 11.414089, 22.784372, 8.155017], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.019562444, 0.102425896, -0.15261605, 0.038667664, 0.05619585, -0.018210387, -0.08637685, 0.03392367, -0.013665966], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 363, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.7401388, 0.16236249, 0.15439042, 0.1511078, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 7.0, 7.0, 5.0, 0.05619585, -0.018210387, -0.08637685, 0.03392367, -0.013665966], "split_indices": [1, 2, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.132717, 34.51012, 15.622597, 21.212315, 13.297805, 10.605734, 5.0168624, 11.710504, 9.501812], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.02823048, 0.0685386, -0.025890268, -0.031674117, 0.14815511, -0.027686464, 0.015714297, 0.051608015, -0.088983566, 0.064751, 0.010740658, 0.046743423, -0.036002275], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 364, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.12235528, 0.27079684, 0.07106056, 0.83995515, 0.107822806, 0.0, 0.28308094, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 8.0, 2.0, 8.0, -0.027686464, 7.0, 0.051608015, -0.088983566, 0.064751, 0.010740658, 0.046743423, -0.036002275], "split_indices": [1, 2, 1, 2, 2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.31911, 30.89237, 21.426743, 13.387619, 17.504751, 8.7106, 12.716142, 7.553362, 5.834257, 9.793827, 7.710925, 6.5469356, 6.1692066], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.0054138317, 0.056137986, -0.09108575, -0.038996458, 0.10180374, 0.010154541, 0.05086687], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 365, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.7546379, 0.37502837, 0.0, 0.0, 0.13708463, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 2.0, -0.09108575, -0.038996458, 6.0, 0.010154541, 0.05086687], "split_indices": [0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.632866, 41.89777, 5.7350965, 7.4591503, 34.43862, 18.67913, 15.759491], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.004090989, 0.0694672, -0.034586154, -0.13434915, 0.18517943, -0.014304272, -0.08910146, 0.0015198117, 0.105851084], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 366, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.47933018, 0.0, 0.99254745, 0.39413357, 0.38398167, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.0694672, 7.0, 7.0, 6.0, -0.014304272, -0.08910146, 0.0015198117, 0.105851084], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.33409, 6.9657855, 43.368305, 30.408161, 12.960142, 21.082602, 9.32556, 7.0321836, 5.927959], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.09784437, -0.004222026, -0.27710518, 0.083365045, -0.09762892, -0.11864664, -0.041001786, -0.024980973, 0.07106492, 0.036975406, -0.0606459], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 367, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8775443, 0.3112712, 0.19458091, 0.52161264, 0.46821168, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 5.0, 3.0, 1.0, -0.11864664, -0.041001786, -0.024980973, 0.07106492, 0.036975406, -0.0606459], "split_indices": [1, 2, 2, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.03426, 35.8778, 17.15646, 18.018698, 17.8591, 7.937176, 9.219284, 8.5472975, 9.4714, 5.3717127, 12.487388], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.00477156, -0.048115697, 0.0663053, -0.013145557, -0.062278964, 0.015763097, -0.020515924], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 368, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.68078977, 0.23351064, 0.0, 0.14876385, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, 0.0663053, 3.0, -0.062278964, 0.015763097, -0.020515924], "split_indices": [2, 1, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.08528, 45.317142, 10.768143, 38.56932, 6.7478204, 16.813845, 21.755476], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.010851243, 0.040847268, -0.10969971, 0.08263973, -0.009670306, -0.09317395, -0.0, 0.086293526, 0.00052250404, 0.034950476, -0.042019855], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 369, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.2818267, 0.1140365, 0.43408656, 0.35697383, 0.0, 0.0, 0.23115104, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 7.0, 5.0, 2.0, -0.009670306, -0.09317395, 8.0, 0.086293526, 0.00052250404, 0.034950476, -0.042019855], "split_indices": [0, 2, 2, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.9244, 33.814354, 18.110048, 22.299631, 11.514724, 5.884289, 12.225759, 5.236908, 17.062723, 6.9289083, 5.29685], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0035481043, 0.051412325, -0.11429706, 0.11495347, -0.0, -0.0541627, -0.014520293, 0.006817535, 0.06297227, -0.041131325, 0.02214126], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 370, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.4041885, 0.1622646, 0.057531804, 0.17112753, 0.31464297, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 5.0, 4.0, 7.0, -0.0541627, -0.014520293, 0.006817535, 0.06297227, -0.041131325, 0.02214126], "split_indices": [0, 2, 2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [70.73051, 51.304764, 19.42574, 21.741873, 29.562891, 8.062837, 11.362904, 12.192163, 9.54971, 9.690041, 19.87285], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.03624296, 0.08920838, -0.009035778, -0.014918345, 0.15212828, 0.11892422, -0.14455381, 0.059291046, 0.013150349, -0.032710638, 0.073853426, -0.09971186, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 371, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.16811514, 0.2951143, 0.6388093, 0.0, 0.078449786, 0.57810915, 0.47851104, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 4.0, -0.014918345, 7.0, 1.0, 7.0, 0.059291046, 0.013150349, -0.032710638, 0.073853426, -0.09971186, -0.0], "split_indices": [2, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.4212, 30.722029, 34.699177, 9.1462145, 21.575813, 17.530714, 17.168463, 13.96067, 7.6151423, 6.050077, 11.480637, 6.768102, 10.40036], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.05538055, 0.18774445, -0.0, 0.03324041, 0.0808709, 0.07346461, -0.04410398, 0.008733806, 0.035835482], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 372, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.39478922, 0.033260107, 0.44631085, 0.0, 0.0, 0.03937912, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 6.0, 7.0, 0.03324041, 0.0808709, 4.0, -0.04410398, 0.008733806, 0.035835482], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.94898, 15.068583, 39.880394, 9.609074, 5.459509, 27.296133, 12.584261, 15.833341, 11.462793], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.055216584, -0.09425826, 0.049219124, -0.056706924, -0.019131899], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 373, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.44916955, 0.09185296, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [8.0, 2.0, 0.049219124, -0.056706924, -0.019131899], "split_indices": [1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [50.475536, 43.71797, 6.757565, 8.576572, 35.1414], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.034561276, -0.021698702, 0.0630712, 0.12079056, -0.05154568, 0.04826041, -0.0, -0.0, -0.034373313], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 374, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.1599467, 0.0, 0.28945237, 0.1438714, 0.056894083, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.021698702, 8.0, 8.0, 5.0, 0.04826041, -0.0, -0.0, -0.034373313], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.292526, 9.527437, 40.765087, 27.656363, 13.108726, 20.716007, 6.9403553, 6.919511, 6.189214], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.032723222, 0.03543327, -0.13033906, -0.023078118, 0.03723621, -0.07055032, -0.047617074, 0.005150505, -0.028595138, -0.0320856, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 375, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.33608764, 0.16179878, 0.14015049, 0.06016724, 0.0, 0.0, 0.03845799, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 6.0, 4.0, 3.0, 0.03723621, -0.07055032, 7.0, 0.005150505, -0.028595138, -0.0320856, -0.0], "split_indices": [1, 2, 2, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.394905, 27.680592, 19.714315, 16.443949, 11.236642, 7.349276, 12.365039, 10.161003, 6.282946, 5.0113053, 7.353734], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.009933308, -0.06807311, 0.11654088, -0.0, -0.091687955, 0.14776522, 0.012278771, 0.044301607, -0.023683233, 0.075772315, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 376, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.461629, 0.4752744, 0.03107506, 0.30352536, 0.0, 0.22974914, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 8.0, 3.0, -0.091687955, 5.0, 0.012278771, 0.044301607, -0.023683233, 0.075772315, -0.0], "split_indices": [2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.973656, 30.257334, 22.71632, 24.618479, 5.6388555, 14.465769, 8.25055, 7.836964, 16.781515, 7.894865, 6.5709043], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.05273499, 0.06940005, -0.14891782, -0.0055714864, 0.13598414, -0.09230414, -0.09032741, -0.0, 0.09147447, 0.054676995, -0.060694296], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 377, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.62175006, 0.14814591, 0.20694947, 0.0, 0.33801806, 0.0, 0.7542239, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 1.0, 6.0, -0.0055714864, 7.0, -0.09230414, 7.0, -0.0, 0.09147447, 0.054676995, -0.060694296], "split_indices": [1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.370407, 21.85598, 28.514425, 9.079396, 12.776585, 6.0803466, 22.434078, 7.400866, 5.375719, 6.0900416, 16.344036], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.022249075, 0.048312154, -0.0, -0.06373748, 0.034789387, 0.017388921, -0.01700478], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 378, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.16523111, 0.0, 0.3679837, 0.0, 0.09732395, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.048312154, 2.0, -0.06373748, 8.0, 0.017388921, -0.01700478], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.721043, 6.45388, 49.267166, 5.96443, 43.302734, 35.714355, 7.588381], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.028343664, -0.019002873, 0.04397305, 0.11769463, -0.0, -0.009883552, 0.05911811, -0.027031679, 0.019503767], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 379, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.0807579, 0.0, 0.15217416, 0.23629555, 0.19362694, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.019002873, 4.0, 1.0, 6.0, -0.009883552, 0.05911811, -0.027031679, 0.019503767], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.763584, 6.5992775, 48.164307, 16.848633, 31.315676, 5.5808415, 11.26779, 12.5685005, 18.747175], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.010352697, -0.032580268, 0.041455314, 0.02989318, -0.12823269, -0.019227311, 0.038543534, -0.09442228, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 380, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.22106029, 0.37956667, 0.0, 0.35951388, 0.60102725, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 6.0, 0.041455314, 2.0, 8.0, -0.019227311, 0.038543534, -0.09442228, -0.0], "split_indices": [0, 1, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [67.7901, 60.230854, 7.5592484, 36.217896, 24.012959, 18.201456, 18.01644, 9.21918, 14.793778], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.005390082, 0.10282327, -0.02531209, -0.014396094, 0.07613468, 0.06599775, -0.10817455, -0.06325953, 0.015909106], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 381, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.19828756, 0.39772606, 0.98408824, 0.0, 0.0, 0.0, 0.6345146, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [2.0, 5.0, 2.0, -0.014396094, 0.07613468, 0.06599775, 7.0, -0.06325953, 0.015909106], "split_indices": [1, 2, 2, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.82447, 15.03117, 46.7933, 7.529138, 7.5020313, 11.092759, 35.700542, 21.999023, 13.701518], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.042598967, -0.2622342, 0.032146085, -0.031436346, -0.13894106, -0.03572842, 0.17941342, 0.006730226, -0.03201211, 0.12173797, 0.00044686464], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 382, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.970498, 0.368286, 0.45288157, 0.0, 0.0, 0.13450421, 0.533401, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 7.0, 8.0, -0.031436346, -0.13894106, 4.0, 4.0, 0.006730226, -0.03201211, 0.12173797, 0.00044686464], "split_indices": [0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.20166, 13.972765, 42.228893, 8.965715, 5.0070486, 28.95985, 13.269043, 15.518484, 13.441367, 5.028424, 8.240619], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.003989962, -0.038008247, 0.037836757, 0.06544874, -0.07701126, -0.041595228, 0.040123776], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 383, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.25728765, 0.8393694, 0.0, 0.36086378, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.037836757, 2.0, -0.07701126, -0.041595228, 0.040123776], "split_indices": [0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.5075, 34.510315, 11.997186, 23.59287, 10.917442, 5.289979, 18.302893], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.045035575, 0.041562732, -0.15372434, -0.04885642, 0.09447466, -0.090107314, -0.036444664, 0.05198287, -0.0153978085, 0.03244712, -0.068926215], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 384, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.51921165, 0.332336, 0.37424564, 0.0, 0.3060447, 0.0, 0.45770353, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 1.0, 8.0, -0.04885642, 7.0, -0.090107314, 9.0, 0.05198287, -0.0153978085, 0.03244712, -0.068926215], "split_indices": [2, 1, 2, 0, 1, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.292847, 28.935715, 23.357132, 5.133894, 23.80182, 9.307, 14.050132, 15.7738695, 8.027951, 7.993743, 6.056389], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.045660708, 0.0753874, -0.10354098, -0.19394645, -0.040790632, -0.016930114, -0.09925024, -0.02654775, -0.004261651], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 385, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.8338261, 0.0, 0.20144615, 0.24183273, 0.021630183, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.0753874, 4.0, 2.0, 4.0, -0.016930114, -0.09925024, -0.02654775, -0.004261651], "split_indices": [2, 0, 1, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.858868, 6.816721, 40.04215, 14.896349, 25.1458, 8.500162, 6.396187, 6.8194566, 18.326342], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.021480609, 0.024561726, -0.07605545, 0.0685797, -0.027180586, -0.069109105, 0.018219352], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 386, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.5670435, 0.4757381, 0.0, 0.0, 0.64644593, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, -0.07605545, 0.0685797, 2.0, -0.069109105, 0.018219352], "split_indices": [1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.350395, 41.56583, 7.7845674, 8.107978, 33.45785, 9.958564, 23.499287], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.03352625, -0.024545917, -0.11173303, 0.03552428, 0.029931013, -0.09391878, -0.023506077, 0.025136808], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 387, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.14103918, 0.0, 0.2400045, 0.849315, 0.149472, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.03352625, 5.0, 5.0, 3.0, 0.029931013, -0.09391878, -0.023506077, 0.025136808], "split_indices": [0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.801704, 8.18909, 42.612614, 17.711075, 24.901537, 8.641087, 9.069989, 6.610131, 18.291407], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.073441096, -0.049550038, -0.042694718, 0.07467026, -0.12104882, 0.06517896, -0.004804364, -0.09434033, -0.0144506935], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 388, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.13714164, 0.0, 0.43648905, 0.26301223, 0.3498196, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.049550038, 4.0, 5.0, 6.0, 0.06517896, -0.004804364, -0.09434033, -0.0144506935], "split_indices": [1, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.498554, 12.520831, 44.977722, 17.51425, 27.46347, 6.6945868, 10.819664, 6.2333956, 21.230076], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.018137163, 0.0386008, -0.18030438, -0.058216333, 0.17106587, -0.0073937536, -0.07985389, -0.056703035, 0.06553736, -0.0, 0.07251754], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 389, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.53302205, 0.5545178, 0.16557318, 0.91889024, 0.21616411, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 4.0, 5.0, 2.0, -0.0073937536, -0.07985389, -0.056703035, 0.06553736, -0.0, 0.07251754], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.634136, 40.58119, 14.052948, 23.332985, 17.248201, 5.868005, 8.184944, 16.303856, 7.0291295, 5.409631, 11.838571], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.040337242, -0.058749992, 0.07985655, -0.049125344, 0.14268093, 0.036040436, -0.049574047, 0.01116643, 0.07279805], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 390, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.65174407, 0.0, 0.50678927, 0.41836247, 0.40029508, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.058749992, 2.0, 5.0, 7.0, 0.036040436, -0.049574047, 0.01116643, 0.07279805], "split_indices": [2, 0, 1, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.39705, 8.874792, 59.522255, 19.04603, 40.476227, 7.385339, 11.660691, 20.852848, 19.623379], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.040314138, -0.20194796, 0.037960645, -0.015583594, -0.07978737, -0.02637187, 0.11543474, 0.032785904, -0.06091258], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 391, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.8605696, 0.16704285, 1.0493585, 0.0, 0.0, 0.9573139, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 4.0, 9.0, -0.015583594, -0.07978737, 5.0, 0.11543474, 0.032785904, -0.06091258], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.11718, 21.147053, 43.97013, 7.3340406, 13.813013, 37.62638, 6.3437467, 21.179123, 16.44726], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.057303358, -0.044297077, 0.041109372, -0.19711143, -0.028476764, 0.031179758, -0.030960167, -0.09230732], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 392, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.48981428, 0.0, 0.6282853, 0.26819864, 0.105773926, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.057303358, 7.0, 4.0, 6.0, -0.028476764, 0.031179758, -0.030960167, -0.09230732], "split_indices": [0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.257042, 9.933644, 45.3234, 29.123278, 16.200119, 8.536451, 20.586826, 10.294775, 5.905345], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.010361038, 0.13737586, -0.049350142, 0.017098421, 0.06820437, 0.010305635, -0.03273943, -0.027054904, 0.044629063], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 393, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.38445956, 0.07687497, 0.12970954, 0.0, 0.0, 0.25213176, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 2.0, 6.0, 0.017098421, 0.06820437, 7.0, -0.03273943, -0.027054904, 0.044629063], "split_indices": [1, 1, 2, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.637592, 15.353148, 32.284443, 9.558501, 5.7946477, 15.736165, 16.548279, 8.901871, 6.8342943], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.083048366, -0.045503493, -0.08101155, -0.18102367, 0.055411432, -0.14257415, -0.0106683355, 0.06396707, -0.007354193], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 394, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.31918958, 0.6340677, 0.0, 0.77690566, 0.35166925, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, -0.08101155, 2.0, 2.0, -0.14257415, -0.0106683355, 0.06396707, -0.007354193], "split_indices": [2, 2, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.710064, 43.71156, 6.998505, 18.741041, 24.970518, 5.22267, 13.518371, 8.25482, 16.715698], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0054204194, 0.09501707, -0.09101498, -0.0044270535, 0.1668986, -0.15650809, 0.029245643, 0.017505694, 0.07376699, -0.070094936, -0.00516611], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 395, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.38976333, 0.17841437, 0.32989317, 0.0, 0.06619099, 0.17866337, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 9.0, -0.0044270535, 6.0, 7.0, 0.029245643, 0.017505694, 0.07376699, -0.070094936, -0.00516611], "split_indices": [2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [43.378258, 19.370428, 24.00783, 7.4289036, 11.9415245, 18.336996, 5.6708326, 6.295381, 5.6461434, 10.903123, 7.433874], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.03284533, -0.0024587293, 0.1408668, 0.06615772, -0.18366581, 0.09613785, -0.013866131, -0.011099886, 0.048534255, -0.08847928, -0.008055962], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 396, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.21147227, 0.5122324, 0.49440232, 0.30277342, 0.1597175, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 5.0, 2.0, 6.0, 0.09613785, -0.013866131, -0.011099886, 0.048534255, -0.08847928, -0.008055962], "split_indices": [1, 1, 2, 1, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.732033, 38.274494, 12.457542, 27.822273, 10.452219, 6.265069, 6.192473, 13.175722, 14.646551, 5.2258034, 5.2264156], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.066420324, -0.052053686, 0.099249884, 0.04116595, 0.20393553, -0.0236048, 0.038292296, 0.09677077, 0.032739576], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 397, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.42230582, 0.0, 0.25330248, 0.34803566, 0.10078281, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.052053686, 7.0, 5.0, 8.0, -0.0236048, 0.038292296, 0.09677077, 0.032739576], "split_indices": [0, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.369835, 5.3004656, 46.06937, 31.140053, 14.9293165, 12.60461, 18.535442, 5.095584, 9.833733], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.047184408, 0.0012822296, -0.15451367, -0.06664968, 0.07920473, -0.2813564, 0.028124144, -0.035815936, 0.047722694, -0.04567019, -0.1037553], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 398, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.30767792, 0.73414594, 0.6158021, 0.39019984, 0.0, 0.0017251968, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 9.0, 8.0, 0.07920473, 6.0, 0.028124144, -0.035815936, 0.047722694, -0.04567019, -0.1037553], "split_indices": [2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.163578, 38.041523, 17.122055, 30.51685, 7.5246725, 11.510529, 5.6115265, 25.478384, 5.038464, 5.4700546, 6.040474], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.031424284, -0.06473531, 0.038874008, -0.18692699, -0.008338617, -0.0006022819, -0.10306018, -0.059817925, 0.011380059], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 399, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.30489212, 0.30962795, 0.0, 0.38607252, 0.32819617, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, 0.038874008, 4.0, 2.0, -0.0006022819, -0.10306018, -0.059817925, 0.011380059], "split_indices": [0, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.49372, 46.990616, 8.503105, 13.54839, 33.442226, 6.9698467, 6.5785437, 6.2964816, 27.145744], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.052543797, -0.20011707, 0.009419301, -0.39995527, 0.020558547, 0.093769856, -0.042080257, -0.060290866, -0.14978519, 0.047017325, -0.009979898, -0.064594425, 0.0042717247], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 400, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.64666843, 1.1749278, 0.22054118, 0.072007895, 0.0, 0.17348279, 0.31756675, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [2.0, 6.0, 5.0, 3.0, 0.020558547, 7.0, 7.0, -0.060290866, -0.14978519, 0.047017325, -0.009979898, -0.064594425, 0.0042717247], "split_indices": [1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [67.428154, 19.757446, 47.67071, 11.262804, 8.494642, 18.49649, 29.174221, 5.0920024, 6.170801, 12.737648, 5.7588415, 6.9006796, 22.273542], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.008971838, -0.14127786, 0.05555471, -0.024885284, -0.10773756, 0.069784746, -0.0, 0.002406281, -0.022440974, 0.02561719, -0.036763966], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 401, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.5885045, 0.52970713, 0.4557534, 0.033899274, 0.0, 0.0, 0.381014, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 5.0, 3.0, -0.10773756, 0.069784746, 5.0, 0.002406281, -0.022440974, 0.02561719, -0.036763966], "split_indices": [0, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.01027, 21.7579, 44.25237, 15.206542, 6.5513587, 10.003801, 34.24857, 8.4634285, 6.743113, 20.143217, 14.1053505], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.019942842, -0.07779697, 0.10736217, -0.013639708, -0.14218463, 0.08802677, -0.008596801, 0.041699775, -0.029324224, -0.06176761, -0.008558255], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 402, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.41005117, 0.14315103, 0.4663058, 0.28376597, 0.10484636, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 7.0, 4.0, 2.0, 4.0, 0.08802677, -0.008596801, 0.041699775, -0.029324224, -0.06176761, -0.008558255], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.942104, 37.895016, 16.04709, 20.366411, 17.528605, 6.598675, 9.448415, 6.581215, 13.785196, 10.148681, 7.379924], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.03751671, 0.114435464, -0.068352245, 0.23088768, -0.004739731, 0.010491468, 0.15754075, 0.042129003, -0.07378808], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 403, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.9684591, 0.5397481, 0.0, 1.0354078, 0.7080636, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, -0.068352245, 6.0, 6.0, 0.010491468, 0.15754075, 0.042129003, -0.07378808], "split_indices": [2, 1, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.72618, 36.18337, 9.542813, 18.251047, 17.93232, 11.831863, 6.4191847, 11.197058, 6.735262], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.050756197, -0.059158504, 0.14207871, 0.03279477, -0.09237291, 0.004772587, 0.29062262, -0.010681642, 0.04592259, -0.017660039, 0.02673919, 0.11437939, 0.029113973], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 404, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5491965, 0.59305704, 0.57619137, 0.16808285, 0.0, 0.09865133, 0.15961432, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 6.0, 7.0, -0.09237291, 8.0, 8.0, -0.010681642, 0.04592259, -0.017660039, 0.02673919, 0.11437939, 0.029113973], "split_indices": [1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.050007, 23.283474, 28.766533, 17.339804, 5.943671, 15.83491, 12.9316225, 10.972586, 6.3672185, 8.5224, 7.312511, 7.7952957, 5.136327], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.03394906, -0.06871634, 0.0077020363, 0.059425127, -0.03713741, -0.01765499, 0.039382562], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 405, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.40201235, 0.0, 0.2654114, 0.26253113, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.06871634, 8.0, 4.0, -0.03713741, -0.01765499, 0.039382562], "split_indices": [0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.529224, 7.6405034, 37.88872, 28.131666, 9.757056, 10.173626, 17.95804], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.013239705, 0.08273876, -0.08672238, -0.0, 0.21974653, 0.07605213, -0.027476387, 0.0040830937, 0.12649302], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 406, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1.1233808, 0.5154468, 0.0, 0.674192, 0.65690273, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, -0.08672238, 1.0, 5.0, 0.07605213, -0.027476387, 0.0040830937, 0.12649302], "split_indices": [0, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.31113, 43.39268, 8.918449, 27.42497, 15.967711, 6.6482487, 20.776722, 8.674146, 7.293566], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.005392172, 0.038945857, -0.14466435, 0.10919996, -0.0050797043, -0.06856244, -0.0032687862, 0.06086014, 0.0019645847, -0.01999889, 0.037970483], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 407, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.3490076, 0.13816042, 0.12807986, 0.13889793, 0.20805925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 6.0, 4.0, 7.0, -0.06856244, -0.0032687862, 0.06086014, 0.0019645847, -0.01999889, 0.037970483], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.89454, 40.12339, 12.77115, 15.762037, 24.361353, 6.954442, 5.8167076, 7.221829, 8.540208, 17.451153, 6.9101996], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.06054208, -0.12107923, 0.033228584, -0.06468566, -0.06705727, 0.039005864, -0.01430993, -0.054530844, 0.047221843], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 408, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.32438296, 0.15440422, 0.18417582, 0.65788424, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 7.0, 8.0, 5.0, -0.06705727, 0.039005864, -0.01430993, -0.054530844, 0.047221843], "split_indices": [0, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.887466, 33.217728, 20.669739, 23.105976, 10.111751, 9.698871, 10.970869, 15.543482, 7.5624943], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.007115202, -0.028537365, 0.06898794, 0.03531218, -0.042501453, 0.16329008, -0.018578008, -0.017591018, 0.037193798, 0.08380727, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 409, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.125644, 0.2618747, 0.28127596, 0.19728145, 0.0, 0.24218392, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 5.0, 5.0, -0.042501453, 8.0, -0.018578008, -0.017591018, 0.037193798, 0.08380727, -0.0], "split_indices": [0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.231384, 33.087894, 20.14349, 21.025625, 12.06227, 12.009328, 8.134161, 9.825602, 11.200023, 6.54812, 5.461208], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.025859581, 0.034907605, -0.07270863, 0.037531413, -0.02056066, -0.11163742, 0.016413486, -0.032445926, 0.018632075, -0.010815512, -0.0745558], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 410, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.20647934, 0.16489129, 0.21494806, 0.0, 0.14866947, 0.29677433, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 9.0, 0.037531413, 7.0, 6.0, 0.016413486, -0.032445926, 0.018632075, -0.010815512, -0.0745558], "split_indices": [2, 1, 2, 0, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [69.30883, 29.354252, 39.954575, 11.386323, 17.96793, 31.30048, 8.654096, 9.16175, 8.806179, 21.44987, 9.85061], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.040249653, -0.047517378, 0.062519744, 0.01987972, 0.16027391, 0.04349255, -0.010155545, -0.0011787459, 0.08166498], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 411, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.29496738, 0.0, 0.23331887, 0.3142576, 0.35309362, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.047517378, 7.0, 3.0, 5.0, 0.04349255, -0.010155545, -0.0011787459, 0.08166498], "split_indices": [2, 0, 1, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.60683, 5.544987, 60.06184, 43.370235, 16.691605, 13.080272, 30.289963, 6.7953844, 9.896219], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.023074986, -0.10859378, 0.0111975465, -0.0046551293, -0.1456646, 0.04711568, -0.037656255, -0.1324623, 0.041133575, 0.045407485, -0.0113290185], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 412, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.17805587, 0.041105628, 0.19829515, 0.0, 1.0482625, 0.31433392, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 8.0, -0.0046551293, 6.0, 7.0, -0.037656255, -0.1324623, 0.041133575, 0.045407485, -0.0113290185], "split_indices": [1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.012405, 16.23962, 39.772785, 5.7980676, 10.441553, 32.596077, 7.1767054, 5.0010324, 5.4405212, 14.806582, 17.789495], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.06961175, 0.13174886, -0.062443208, 0.04857101, 0.16571604, 0.044737577, -0.058216672, -0.010125121, 0.043706074, 0.08357926, 0.025382293], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 413, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.41556555, 0.066639185, 0.4727588, 0.11310746, 0.14965558, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 3.0, 5.0, 4.0, 0.044737577, -0.058216672, -0.010125121, 0.043706074, 0.08357926, 0.025382293], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.797462, 33.019875, 14.777585, 11.132612, 21.887264, 5.3248897, 9.452696, 5.821081, 5.311531, 7.6176744, 14.269589], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.044524778, -0.024065042, 0.13005589, 0.11234809, -0.15576227, 0.050578434, 0.19677569, -0.0054295976, 0.058430735, -0.0, -0.06827218, 0.041654967, -0.003646787, 0.08704844, 0.014220707], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 414, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.3350133, 0.57135516, 0.09882218, 0.18496835, 0.16274089, 0.08922785, 0.12905207, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 6.0, 1.0, 3.0, 8.0, 8.0, -0.0054295976, 0.058430735, -0.0, -0.06827218, 0.041654967, -0.003646787, 0.08704844, 0.014220707], "split_indices": [0, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.765347, 29.587648, 24.177696, 14.232798, 15.354852, 12.52027, 11.657427, 5.343242, 8.889555, 5.5508037, 9.804048, 5.359634, 7.160637, 6.1811094, 5.4763174], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.031389445, 0.054990787, -0.013596109, 0.021839352, 0.05197517, 0.031959765, -0.011912653], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 415, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.09006141, 0.12579782, 0.0, 0.1751494, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, -0.013596109, 3.0, 0.05197517, 0.031959765, -0.011912653], "split_indices": [2, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.83449, 37.028625, 9.805867, 30.50696, 6.5216637, 13.215575, 17.291386], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006973316, 0.03652735, -0.12442247, -0.028125498, 0.065233454, -0.004631404, -0.055918988, 0.010173902, -0.037473127], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 416, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.25720283, 0.50292724, 0.06538291, 0.19693479, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 3.0, 6.0, 0.065233454, -0.004631404, -0.055918988, 0.010173902, -0.037473127], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.9501, 39.855473, 12.094627, 29.577448, 10.278024, 5.4221683, 6.6724596, 17.79034, 11.787107], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0042866827, 0.043886263, -0.14815228, 0.13070369, -0.021676673, -0.05776449, -0.020188915, 0.011929846, 0.05572519, -0.028548377, 0.035507984], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 417, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.376799, 0.23658478, 0.010505408, 0.05838287, 0.24055816, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 6.0, 1.0, 5.0, -0.05776449, -0.020188915, 0.011929846, 0.05572519, -0.028548377, 0.035507984], "split_indices": [2, 1, 1, 1, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.926155, 38.151203, 12.774955, 16.64364, 21.50756, 6.5566907, 6.218264, 7.6103616, 9.033279, 14.784113, 6.723448], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.025409818, 0.09970797, -0.017092083, 0.02152061, 0.055306293, 0.057706174, -0.054951116, 0.055456508, -0.03183438, -0.0008951679, 0.07580332], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 418, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.18821332, 0.11801991, 0.46912813, 0.29735377, 0.0, 0.32272902, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [3.0, 5.0, 7.0, 1.0, 0.055306293, 6.0, -0.054951116, 0.055456508, -0.03183438, -0.0008951679, 0.07580332], "split_indices": [1, 2, 2, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.446514, 20.542595, 34.90392, 11.94484, 8.597755, 24.100128, 10.803792, 5.3866873, 6.5581527, 18.738937, 5.3611913], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.04110338, -0.034107476, 0.06647124, -0.017431572, -0.0, 0.056475725, 0.039450027, -0.044490863, 0.029513724], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 419, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.11516767, 0.0066160597, 0.11562674, 0.0, 0.0, 0.0, 0.43229315, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [2.0, 5.0, 3.0, -0.017431572, -0.0, 0.056475725, 4.0, -0.044490863, 0.029513724], "split_indices": [1, 2, 1, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.477802, 13.163634, 43.31417, 5.8398705, 7.3237634, 6.1099176, 37.20425, 8.119919, 29.084333], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.037576552, 0.011836709, 0.05414489, 0.07982327, -0.0959593, -0.053146955, 0.03973898, -0.006302639, -0.06590252], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 420, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.22164392, 0.42850626, 0.0, 0.51226866, 0.17698377, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, 0.05414489, 1.0, 6.0, -0.053146955, 0.03973898, -0.006302639, -0.06590252], "split_indices": [0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.06486, 56.64477, 8.420087, 35.46457, 21.180202, 5.304371, 30.160198, 14.42142, 6.758783], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0013787156, 0.065941975, -0.028258594, 0.043156955, -0.043007188, -0.086932175, 0.03973538, -0.0011761889, -0.06564575], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 421, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.13277413, 0.38378444, 0.432974, 0.0, 0.0, 0.3508077, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 6.0, 7.0, 0.043156955, -0.043007188, 4.0, 0.03973538, -0.0011761889, -0.06564575], "split_indices": [0, 2, 2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.44558, 21.266605, 44.178974, 16.073488, 5.193117, 33.155533, 11.023443, 21.445427, 11.710105], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04010911, 0.062448885, -0.039302204, -0.012578509, 0.09893289, 0.06596931, 0.008928919], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 422, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.20808798, 0.19658335, 0.0, 0.0, 0.28094295, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 3.0, -0.039302204, -0.012578509, 3.0, 0.06596931, 0.008928919], "split_indices": [2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.088047, 48.036217, 5.0518284, 11.693131, 36.343086, 11.902709, 24.440376], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.061050586, -0.13097861, 0.03087781, -0.04158652, -0.21892205, -0.02166069, 0.03055107, 0.035736576, -0.07255626, -0.09014781, -0.030948058, 0.004653399, -0.019104287], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 423, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.33267576, 0.19250926, 0.09076973, 0.5611854, 0.058942854, 0.02521825, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 7.0, 6.0, 6.0, 8.0, 0.03055107, 0.035736576, -0.07255626, -0.09014781, -0.030948058, 0.004653399, -0.019104287], "split_indices": [0, 0, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.564854, 27.959587, 20.605267, 15.159917, 12.79967, 11.428801, 9.176466, 8.358413, 6.8015046, 6.065818, 6.733853, 5.1362348, 6.2925663], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.015340748, -0.09953652, 0.056214705, -0.0, -0.15616345, 0.11316897, -0.002329122, -0.0894632, 0.017985864, -0.004011369, 0.071949586, 0.012441378, -0.021857422], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 424, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.3235513, 0.15289582, 0.10516772, 0.0, 0.54495317, 0.2686518, 0.049375717, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 6.0, -0.0, 7.0, 3.0, 8.0, -0.0894632, 0.017985864, -0.004011369, 0.071949586, 0.012441378, -0.021857422], "split_indices": [0, 0, 2, 0, 2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.056606, 23.918898, 27.13771, 8.5257435, 15.393154, 14.148461, 12.989248, 9.338243, 6.0549116, 7.1301556, 7.018306, 7.3924665, 5.5967817], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.085803494, 0.12574184, -0.0135405585, 0.20573755, 0.06819447, 0.092641845, 0.01721834, -0.009518435, 0.044494644], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 425, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.25122485, 0.12268627, 0.0, 0.15500546, 0.20014964, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 3.0, -0.0135405585, 4.0, 6.0, 0.092641845, 0.01721834, -0.009518435, 0.044494644], "split_indices": [2, 1, 0, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.4135, 34.648556, 9.764945, 12.692338, 21.956217, 6.452991, 6.2393465, 9.4840765, 12.472141], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.03362816, -0.122815914, 0.031045787, -0.0297915, -0.05861078, -0.03089236, 0.088388026, -0.049223594, 0.027613996, 0.019188061, -0.060473565], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 426, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.31713775, 0.12700221, 0.536892, 0.21649797, 0.0, 0.43896997, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 8.0, 1.0, -0.05861078, 5.0, 0.088388026, -0.049223594, 0.027613996, 0.019188061, -0.060473565], "split_indices": [0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.72907, 22.01575, 29.713318, 10.868979, 11.146772, 24.467237, 5.24608, 5.3958583, 5.473121, 15.752674, 8.714562], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.023088615, 0.049676847, -0.061021302, -0.0, -0.12404852, -0.026018295, 0.07012424, -0.044977937, -0.018869635], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 427, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.37249607, 0.0, 0.17309555, 0.49914765, 0.0041452944, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.049676847, 5.0, 4.0, 8.0, -0.026018295, 0.07012424, -0.044977937, -0.018869635], "split_indices": [0, 0, 2, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.095135, 7.5482683, 43.546867, 22.518702, 21.028166, 16.838923, 5.6797795, 12.606222, 8.421944], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.027532578, -0.08169568, 0.09314114, -0.0919686, 0.045083117, 0.07899202, 0.024380842, 0.02376191, -0.020257926], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 428, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.38977596, 1.1003948, 0.3656281, 0.0, 0.0, 0.0, 0.13711402, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 7.0, 4.0, -0.0919686, 0.045083117, 0.07899202, 6.0, 0.02376191, -0.020257926], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.16854, 18.912582, 33.25596, 9.659013, 9.25357, 8.3379135, 24.918045, 16.375687, 8.542357], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0155056445, -0.09037016, 0.060428534, 0.12168922, -0.0019849844, -0.0, 0.054012265, -0.017248359, 0.011595955], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 429, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.7970637, 0.0, 0.20541526, 0.18501753, 0.06096438, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.09037016, 5.0, 2.0, 7.0, -0.0, 0.054012265, -0.017248359, 0.011595955], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.430485, 5.8999405, 49.530544, 25.325409, 24.205135, 8.591142, 16.734268, 11.0953865, 13.109749], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0568922, 0.16031566, -0.006965514, 0.21661694, -0.0136047425, 0.023475118, -0.040877327, 0.026569564, 0.12862642, 0.034791898, -0.0015971145], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 430, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.45860064, 0.33027846, 0.17880094, 0.4840346, 0.0, 0.10231128, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [3.0, 9.0, 7.0, 6.0, -0.0136047425, 5.0, -0.040877327, 0.026569564, 0.12862642, 0.034791898, -0.0015971145], "split_indices": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.94776, 25.195993, 40.75177, 20.120838, 5.0751567, 33.022038, 7.7297335, 13.724976, 6.3958616, 7.9459867, 25.07605], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.025266873, 0.05034364, -0.094953366, -0.0, 0.052596044, -0.046281993, 0.014448243, -0.008878489, 0.009545969, -0.016285734, 0.031086782], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 431, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.3560621, 0.21508014, 0.24521887, 0.022330128, 0.0, 0.0, 0.088574894, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 7.0, 2.0, 0.052596044, -0.046281993, 8.0, -0.008878489, 0.009545969, -0.016285734, 0.031086782], "split_indices": [2, 2, 0, 2, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.8225, 30.534666, 34.287834, 21.937021, 8.597645, 22.559492, 11.728341, 12.950434, 8.986588, 6.2109175, 5.517424], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.007728388, 0.007202104, -0.042958092, -0.09440488, 0.06332131, -0.09102512, 0.009347643, 0.0013410605, 0.04598788], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 432, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.12637007, 0.28825498, 0.0, 0.4979247, 0.16078173, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 4.0, -0.042958092, 5.0, 6.0, -0.09102512, 0.009347643, 0.0013410605, 0.04598788], "split_indices": [1, 2, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.246555, 49.04917, 5.197386, 16.57502, 32.47415, 5.987782, 10.587237, 20.938967, 11.535185], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0010375482, -0.16398883, 0.08693165, -0.005745815, -0.06851222, 0.075307816, -0.07529787, -0.06268287, 0.0062800893], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 433, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.67880213, 0.12178072, 0.8948225, 0.0, 0.0, 0.0, 0.2376503, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 4.0, 7.0, -0.005745815, -0.06851222, 0.075307816, 4.0, -0.06268287, 0.0062800893], "split_indices": [0, 1, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.42588, 15.182558, 31.243322, 5.549485, 9.633073, 15.56672, 15.676601, 6.5072827, 9.169319], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.026742052, 0.09377273, -0.079477824, -0.0059163226, 0.050064072, -0.11209652, 0.02794187, -0.07765321, -0.008637719], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 434, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.34292972, 0.15090798, 0.22625913, 0.0, 0.0, 0.36592537, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 3.0, 9.0, -0.0059163226, 0.050064072, 6.0, 0.02794187, -0.07765321, -0.008637719], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.010696, 15.017484, 36.993214, 5.633814, 9.38367, 31.895918, 5.097297, 10.352118, 21.5438], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.072297215, 0.035527434, -0.10872367, -0.15042087, 0.038624983, 0.0074200355, -0.05905248], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 435, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.34650075, 0.0, 0.43147504, 0.31507862, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, 0.035527434, 9.0, 3.0, 0.038624983, 0.0074200355, -0.05905248], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.161728, 6.761818, 40.39991, 35.035774, 5.3641367, 6.9866753, 28.049099], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.014384031, -0.15726809, 0.033568397, -0.0646976, -0.017803207, -0.052865665, 0.15975124, 0.019274537, -0.039177418, 0.09759811, -0.022075526], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 436, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.3783434, 0.039339185, 0.4522919, 0.0, 0.0, 0.23213722, 0.70066524, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 5.0, 6.0, -0.0646976, -0.017803207, 2.0, 8.0, 0.019274537, -0.039177418, 0.09759811, -0.022075526], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.6435, 12.927904, 38.7156, 6.705824, 6.22208, 22.851246, 15.864352, 8.671773, 14.179473, 9.299639, 6.5647135], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.005776951, 0.053844742, -0.055800978, 0.08023844, -0.0003634475, -0.027220244, 0.041380927], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 437, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.46398473, 0.5098594, 0.0, 0.0, 0.43145147, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, -0.055800978, 0.08023844, 5.0, -0.027220244, 0.041380927], "split_indices": [0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.679367, 40.715183, 8.964186, 7.817956, 32.89723, 20.54155, 12.355677], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0028594257, 0.030779514, -0.047934737, -0.026272887, 0.08561999, 0.023429876, -0.053575166, -0.007729488, 0.050135966, -0.02390134, 0.04520428], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 438, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.08880568, 0.21287617, 0.2601715, 0.0, 0.22474954, 0.24304856, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 4.0, -0.026272887, 3.0, 8.0, -0.053575166, -0.007729488, 0.050135966, -0.02390134, 0.04520428], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.463467, 30.672068, 24.791399, 8.945544, 21.726522, 16.069176, 8.722225, 8.950646, 12.775876, 8.649366, 7.4198084], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.031776708, -0.047715366, -0.0, 0.061527487, 0.024987536, -0.03629813], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 439, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.28584108, 0.2445798, 0.0, 0.43010658, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 9.0, -0.047715366, 6.0, 0.061527487, 0.024987536, -0.03629813], "split_indices": [0, 2, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.390274, 47.00724, 8.383035, 41.040104, 5.9671345, 24.964613, 16.07549], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.041430708, -0.019830052, -0.065285146, -0.05168136, 0.038480632, -0.06743708, -0.0022747421], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 440, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.24476105, 0.30777106, 0.0, 0.39602983, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, -0.065285146, 3.0, 0.038480632, -0.06743708, -0.0022747421], "split_indices": [1, 1, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [70.6867, 64.46353, 6.223169, 54.14495, 10.318583, 9.700575, 44.444374], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.017897163, 0.060094297, -0.123058096, 0.15239665, -0.0138126975, -0.02048361, -0.053324144, -0.01006205, 0.06529424, -0.012206583, 0.022905704], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 441, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.3995112, 0.3759418, 0.0088418275, 0.31870025, 0.07131707, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, 5.0, 3.0, 7.0, -0.02048361, -0.053324144, -0.01006205, 0.06529424, -0.012206583, 0.022905704], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.983635, 51.768917, 14.214717, 23.149094, 28.619825, 9.138314, 5.0764027, 5.741556, 17.407537, 23.325033, 5.2947917], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0026803378, -0.08860205, 0.03914339, 0.06662101, 0.010342337, -0.007584957, 0.029283008], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 442, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.60763514, 0.0, 0.24924482, 0.0, 0.15452334, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, -0.08860205, 2.0, 0.06662101, 8.0, -0.007584957, 0.029283008], "split_indices": [0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.210754, 5.054774, 51.155983, 5.57356, 45.58242, 32.05919, 13.5232315], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0070812055, -0.048130404, 0.033144034, 0.015655069, -0.08460853, -0.011581223, -0.059500378], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 443, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.24821404, 0.15253232, 0.0, 0.0, 0.12637053, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, 0.033144034, 0.015655069, 7.0, -0.011581223, -0.059500378], "split_indices": [0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.62495, 38.59118, 12.033772, 9.4452, 29.145979, 22.331282, 6.814696], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0041108886, 0.08822773, -0.053283915, -0.066206194, -0.01628559, 0.013970557, -0.035864532], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 444, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.76976967, 0.0, 0.26189777, 0.0, 0.27124318, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.08822773, 3.0, -0.066206194, 7.0, 0.013970557, -0.035864532], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.06898, 6.377723, 45.691257, 6.882579, 38.808678, 23.863842, 14.944837], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.03603364, 0.1751514, -0.03226894, 0.007118757, 0.10339798, -0.04237576, -0.0010149387, -0.007992407, 0.008301358], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 445, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.45766875, 0.36071092, 0.092104554, 0.0, 0.0, 0.0, 0.019756991, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 3.0, 2.0, 0.007118757, 0.10339798, -0.04237576, 7.0, -0.007992407, 0.008301358], "split_indices": [0, 0, 2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.0665, 14.807178, 30.259323, 8.734534, 6.072643, 5.2462864, 25.013037, 15.08992, 9.923118], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.057723656, -0.05830152, 0.10392868, 0.015933946, -0.058946356, 0.06885966, 0.052381694, 0.03526109, -0.034913782], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 446, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.29282153, 0.25596982, 0.20985076, 0.0, 0.0, 0.0, 0.33536163, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 5.0, 5.0, 0.015933946, -0.058946356, 0.06885966, 7.0, 0.03526109, -0.034913782], "split_indices": [2, 1, 2, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.639896, 14.016662, 37.623234, 7.7002263, 6.316436, 9.378347, 28.244886, 21.024014, 7.220872], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.021648146, 0.103524745, -0.1104761, 0.08685379, 0.0554637, -0.03442121, 0.050901584], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 447, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.6953571, 0.34794164, 0.0, 0.0, 0.7459388, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 1.0, -0.1104761, 0.08685379, 6.0, -0.03442121, 0.050901584], "split_indices": [0, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.741367, 43.580692, 8.160674, 7.4902377, 36.090454, 14.139122, 21.951332], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.023805551, 0.07606591, -0.10447165, -0.022815451, 0.2093253, -0.068810284, 0.03210515, -0.03626939, 0.008369117, 0.004971896, 0.11992051], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 448, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.38410896, 0.5676662, 0.4655556, 0.13294701, 0.61685973, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 9.0, 2.0, 4.0, -0.068810284, 0.03210515, -0.03626939, 0.008369117, 0.004971896, 0.11992051], "split_indices": [2, 1, 2, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.535408, 40.302628, 15.23278, 23.180538, 17.12209, 9.821146, 5.411634, 8.100794, 15.079744, 9.309887, 7.8122034], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.03909634, -0.13804153, -0.0, -0.013213638, -0.06269878, -0.042895015, 0.04593808, -0.003315472, -0.04740551], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 449, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.20894319, 0.06816697, 0.2710205, 0.0, 0.0, 0.10178062, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 3.0, 8.0, -0.013213638, -0.06269878, 9.0, 0.04593808, -0.003315472, -0.04740551], "split_indices": [2, 1, 1, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.227825, 14.395073, 39.832752, 7.495546, 6.899527, 31.852278, 7.980475, 26.489765, 5.362512], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.049176324, -0.014606811, 0.06695271, 0.036845643, 0.07089627, 0.042042576, -0.01204677], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 450, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.13119856, 0.0, 0.2833552, 0.45202985, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.014606811, 9.0, 4.0, 0.07089627, 0.042042576, -0.01204677], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [71.49092, 9.857463, 61.633457, 53.86613, 7.767325, 23.20957, 30.65656], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.043167032, -0.19772083, 0.012416556, 0.005150021, -0.10310131, 0.06950448, -0.04916956, 0.061407346, -0.00024274476, -0.0746385, 0.015312867], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 451, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.57957625, 0.59474766, 0.1752259, 0.0, 0.0, 0.27070475, 0.4920337, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 4.0, 6.0, 0.005150021, -0.10310131, 5.0, 4.0, 0.061407346, -0.00024274476, -0.0746385, 0.015312867], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.02307, 16.734264, 47.288807, 6.8483763, 9.885889, 25.316536, 21.97227, 8.494975, 16.82156, 7.1815886, 14.790682], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0140867755, -0.1598622, 0.022655122, -0.0, -0.09046711, 0.059219036, -0.054426752, 0.027804855, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 452, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.3164329, 0.275548, 0.3337719, 0.0, 0.0, 0.08387633, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [2.0, 6.0, 9.0, -0.0, -0.09046711, 7.0, -0.054426752, 0.027804855, -0.0], "split_indices": [1, 2, 1, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.841446, 10.913257, 43.92819, 5.5351644, 5.3780923, 38.27111, 5.6570783, 24.796638, 13.474472], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.012886615, 0.047135066, -0.046259753, -0.12012943, -0.0, 0.03202114, -0.07546398, -0.047160532, 0.026335096], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 453, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.27344024, 0.0, 0.15348107, 0.54460585, 0.37028074, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.047135066, 4.0, 5.0, 6.0, 0.03202114, -0.07546398, -0.047160532, 0.026335096], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.868473, 6.7548447, 41.11363, 15.880423, 25.233206, 5.614657, 10.265766, 8.433055, 16.800152], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0023425207, -0.17195319, 0.06526538, -0.010672121, -0.090232715, 0.115293816, -0.042137466, 0.08462277, 0.021285113], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 454, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.5842437, 0.20111325, 0.4279397, 0.0, 0.0, 0.1910891, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 1.0, 9.0, -0.010672121, -0.090232715, 1.0, -0.042137466, 0.08462277, 0.021285113], "split_indices": [2, 2, 2, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.22149, 12.970718, 39.25077, 7.297521, 5.6731977, 32.317394, 6.933376, 5.1880617, 27.129333], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.028154638, 0.062254976, -0.079001345, -0.057025727, -0.047966965, 0.008509262, -0.03308091], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 455, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.553674, 0.0, 0.10095811, 0.0, 0.16538101, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.062254976, 3.0, -0.057025727, 5.0, 0.008509262, -0.03308091], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.908924, 7.0739064, 37.83502, 6.4392257, 31.395792, 13.6329, 17.762892], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.09179451, 0.07576239, -0.05183124, -0.03624778, -0.032913607, 0.14838403, -0.031181296, 0.015137819, 0.07152683, -0.015524287], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 456, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.3638695, 0.08061212, 0.40022215, 0.0, 0.10573294, 0.0, 0.41104603, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 1.0, 3.0, -0.05183124, 6.0, -0.032913607, 8.0, -0.031181296, 0.015137819, 0.07152683, -0.015524287], "split_indices": [1, 1, 2, 0, 2, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.178738, 22.96116, 27.217575, 7.7510247, 15.210135, 7.127171, 20.090405, 9.077877, 6.132259, 14.069095, 6.02131], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.013429022, -0.05188549, 0.058211237, 0.0482642, -0.17115355, -0.011485685, 0.07492051, -0.068185695, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 457, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.41844094, 0.56856275, 0.0, 0.47121927, 0.20598829, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, 0.058211237, 3.0, 8.0, -0.011485685, 0.07492051, -0.068185695, -0.0], "split_indices": [1, 1, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.966373, 44.87935, 7.0870256, 24.262745, 20.616606, 17.1912, 7.0715446, 15.245358, 5.3712473], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.010604574, 0.073043615, -0.04643988, -0.12077567, 0.0530755, 0.022722386, -0.05432206, 0.00089233404, 0.04790406], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 458, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.49324232, 0.0, 0.37912875, 0.37022293, 0.09687101, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.073043615, 6.0, 1.0, 8.0, 0.022722386, -0.05432206, 0.00089233404, 0.04790406], "split_indices": [1, 0, 1, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.093983, 5.625134, 48.46885, 28.197311, 20.271538, 6.170961, 22.026352, 15.127845, 5.1436934], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.022875192, 0.011503477, -0.0897858, -0.026420891, 0.03628837, 0.022844229, -0.014646478], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 459, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.55727065, 0.12087605, 0.0, 0.0, 0.14680377, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 2.0, -0.0897858, -0.026420891, 7.0, 0.022844229, -0.014646478], "split_indices": [0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.253048, 48.782337, 5.470714, 8.409433, 40.372902, 28.36782, 12.005082], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.009217949, 0.05586049, -0.078665294, 0.06620703, 0.024584448, 0.019416433, -0.049060605], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 460, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.88241506, 0.2846207, 0.0, 0.0, 0.4003078, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 1.0, -0.078665294, 0.06620703, 7.0, 0.019416433, -0.049060605], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [69.17155, 60.02046, 9.151091, 8.096598, 51.923862, 43.77221, 8.15165], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.02772663, -0.015824296, 0.10900717, 0.025887929, -0.08468367, -0.0023153077, 0.0827805, -0.017967615, 0.016738934, 0.022433061, -0.01925584], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 461, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.24271904, 0.5073858, 0.46955124, 0.099141434, 0.0, 0.07474845, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [7.0, 8.0, 6.0, 3.0, -0.08468367, 8.0, 0.0827805, -0.017967615, 0.016738934, 0.022433061, -0.01925584], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.448044, 41.686832, 22.761215, 36.50041, 5.1864243, 13.887111, 8.8741045, 8.31275, 28.187658, 5.3705006, 8.51661], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.045006923, 0.041879404, -0.08706834, 0.052898332, -0.15044406, 0.05770863, -0.027841311, -0.07346213, -0.026187286], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 462, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.43448856, 0.0, 0.42492267, 0.323582, 0.13833141, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.041879404, 3.0, 5.0, 6.0, 0.05770863, -0.027841311, -0.07346213, -0.026187286], "split_indices": [0, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.06774, 9.1616125, 44.90613, 13.555638, 31.350492, 7.1131563, 6.4424815, 10.776305, 20.574186], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.038454413, 0.06952521, -0.018946083, 0.011992936, 0.13295275, 0.01305783, -0.015230322, 0.022236357, 0.06332398], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 463, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.16580841, 0.13071916, 0.0, 0.04704658, 0.039530396, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, -0.018946083, 5.0, 5.0, 0.01305783, -0.015230322, 0.022236357, 0.06332398], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.927563, 39.231007, 10.696556, 22.02175, 17.209255, 15.921872, 6.099877, 11.658685, 5.550571], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.02439966, 0.06307038, -0.06408458, -0.0006134095, 0.08963971, 0.050238732, -0.057504635, 0.08681826, 0.011812498], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 464, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.47221702, 0.085801795, 0.0, 0.4601885, 0.28867844, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 3.0, -0.06408458, 4.0, 1.0, 0.050238732, -0.057504635, 0.08681826, 0.011812498], "split_indices": [2, 2, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.47631, 44.41524, 6.0610685, 12.254857, 32.160385, 6.3070035, 5.9478536, 5.1092205, 27.051163], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.024926119, -0.06539542, 0.03798221, 0.034312636, -0.12920302, -0.06735369, 0.01431064], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 465, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.28334498, 0.44489893, 0.0, 0.0, 0.5082326, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 3.0, 0.03798221, 0.034312636, 7.0, -0.06735369, 0.01431064], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.85222, 36.367966, 8.484252, 8.9087715, 27.459194, 18.037495, 9.4217], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.025772639, -0.062574975, 0.06337662, -0.0, -0.13688204, 0.041700535, -0.014741241, -0.054909013, -0.008516983], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 466, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.4651972, 0.2235516, 0.0, 0.19047606, 0.083057374, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, 0.06337662, 2.0, 6.0, 0.041700535, -0.014741241, -0.054909013, -0.008516983], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.517822, 46.461792, 6.056032, 25.611923, 20.849869, 6.322843, 19.28908, 13.499138, 7.35073], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.036339965, -0.057617284, 0.10136546, 0.0441266, -0.050298467, 0.17310454, -0.023384603, 0.0651023, -0.040224638, 0.010671894, 0.064711034, 0.015022892, -0.035936937], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 467, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.34100896, 0.2677915, 0.31425768, 0.401872, 0.0, 0.09358758, 0.10090488, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 7.0, 5.0, -0.050298467, 3.0, 6.0, 0.0651023, -0.040224638, 0.010671894, 0.064711034, 0.015022892, -0.035936937], "split_indices": [2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.14312, 21.153654, 31.989464, 10.830427, 10.323227, 20.572807, 11.4166565, 5.671705, 5.1587224, 5.854373, 14.718435, 6.163406, 5.25325], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0038717196, -0.058635816, 0.051701963, 0.057846364, -0.14721163, 0.054192144, -0.02368039, -0.08249371, -0.019368665], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 468, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.5116243, 0.4479639, 0.0, 0.33102405, 0.20671666, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 3.0, 0.051701963, 4.0, 3.0, 0.054192144, -0.02368039, -0.08249371, -0.019368665], "split_indices": [2, 2, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.148785, 40.72635, 11.422437, 17.254137, 23.47221, 9.308207, 7.945931, 7.830338, 15.641871], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0736891, -0.0, -0.2082535, -0.0237083, 0.025048722, -0.09237885, -0.13568972, 0.010274993, -0.04851233, -0.007612652, -0.06099494], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 469, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.51903605, 0.070664, 0.04801941, 0.24343345, 0.0, 0.0, 0.06972182, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 3.0, 3.0, 0.025048722, -0.09237885, 5.0, 0.010274993, -0.04851233, -0.007612652, -0.06099494], "split_indices": [1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.56566, 34.01641, 17.549246, 27.149942, 6.8664684, 5.4463263, 12.102921, 19.138958, 8.010984, 5.6843762, 6.418544], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.02067997, -0.10001253, 0.07779715, -0.20531671, 0.041077927, 0.031253535, 0.049894214, -0.08612678, -0.03276195, -0.0040391176, 0.032770947], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 470, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.48116815, 0.5848001, 0.17261606, 0.05436468, 0.0, 0.12752369, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [2.0, 8.0, 6.0, 4.0, 0.041077927, 8.0, 0.049894214, -0.08612678, -0.03276195, -0.0040391176, 0.032770947], "split_indices": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [67.992676, 21.037407, 46.95527, 14.949032, 6.088375, 32.40107, 14.5542, 6.4411836, 8.507848, 20.303553, 12.097516], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.032722212, 0.040856533, 0.0102587575, -0.07176481, 0.051432624, 0.02379778, -0.09117984, -0.0033022256, 0.091077745], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 471, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.13418001, 0.0, 0.18733682, 0.67730045, 0.63089186, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, 0.040856533, 3.0, 2.0, 9.0, 0.02379778, -0.09117984, -0.0033022256, 0.091077745], "split_indices": [0, 0, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.63354, 9.653835, 53.979706, 16.980568, 36.999138, 10.424253, 6.556314, 30.104427, 6.8947115], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [5.142536e-05, 0.048830517, -0.037595168, -0.1407405, 0.0060279137, -0.0674209, -0.005180094, 0.010084004, -0.009712033], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 472, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.358947, 0.0, 0.21872953, 0.119451284, 0.035326667, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.048830517, 4.0, 8.0, 8.0, -0.0674209, -0.005180094, 0.010084004, -0.009712033], "split_indices": [0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.554176, 10.124881, 44.429295, 13.225088, 31.204206, 6.900708, 6.3243804, 19.747465, 11.45674], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.068699814, -0.00043639794, 0.091438636, 0.11440375, 0.0017644599, 0.005581512, 0.043890804], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 473, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.086140424, 0.0, 0.061077654, 0.06827089, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.00043639794, 7.0, 3.0, 0.0017644599, 0.005581512, 0.043890804], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.27456, 10.724694, 36.549866, 27.628021, 8.921843, 8.080089, 19.547934], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.054050162, -0.011893268, -0.08269598, -0.07389804, 0.13033675, 0.047549535, -0.04103325, -0.013790441, 0.1080834], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 474, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.47044194, 0.41507086, 0.0, 0.51035297, 0.61651766, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 8.0, -0.08269598, 1.0, 6.0, 0.047549535, -0.04103325, -0.013790441, 0.1080834], "split_indices": [0, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.7861, 45.613663, 7.1724358, 32.589764, 13.023897, 6.277544, 26.312222, 7.578421, 5.445476], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.03145286, 0.10841377, -0.122238785, -0.0, 0.05931462, -0.17856397, -0.019998848, -0.078763515, -0.020060368, 0.022067267, -0.039329473], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 475, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.6066615, 0.17017074, 0.1445885, 0.0, 0.0, 0.118869424, 0.14138013, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 7.0, -0.0, 0.05931462, 7.0, 8.0, -0.078763515, -0.020060368, 0.022067267, -0.039329473], "split_indices": [2, 2, 0, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.623898, 17.447802, 28.176096, 8.5479765, 8.899825, 17.055445, 11.120653, 8.453604, 8.60184, 5.731768, 5.3888845], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.06497204, 0.047684103, -0.12650439, -0.0, 0.047750365, 0.012034221, -0.16704373, 0.010541453, -0.01485635, -0.0995904, -0.030736245], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 476, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.37213498, 0.11080367, 0.2508704, 0.026195008, 0.0, 0.0, 0.22336483, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 1.0, 2.0, 0.047750365, 0.012034221, 3.0, 0.010541453, -0.01485635, -0.0995904, -0.030736245], "split_indices": [2, 1, 1, 2, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.676403, 17.406927, 33.269478, 12.381255, 5.0256715, 6.029935, 27.239542, 6.45105, 5.9302053, 6.0650473, 21.174494], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011145148, -0.13252859, 0.051703416, -0.008929735, -0.06253369, 0.056180257, 0.01634781, -0.041894782, 0.017179418], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 477, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.38287997, 0.09496191, 0.15865731, 0.0, 0.0, 0.0, 0.20280968, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 7.0, 4.0, -0.008929735, -0.06253369, 0.056180257, 5.0, -0.041894782, 0.017179418], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.010265, 15.021082, 36.98918, 7.533244, 7.487838, 6.177539, 30.811644, 5.3931417, 25.4185], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.007025757, 0.099061966, -0.046214297, 0.051598445, 0.05465058, -0.13637333, 0.062137455, 0.034099076, -0.005773434, -0.069607675, -0.018142918, -0.0, 0.047744777], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 478, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.29119104, 0.054189354, 0.37115893, 0.08283191, 0.0, 0.10675073, 0.08971084, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 2.0, 5.0, 4.0, 0.05465058, 2.0, 8.0, 0.034099076, -0.005773434, -0.069607675, -0.018142918, -0.0, 0.047744777], "split_indices": [2, 2, 1, 1, 0, 1, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.41377, 21.047142, 35.366627, 15.19384, 5.853302, 19.666512, 15.700114, 8.528886, 6.664954, 7.20784, 12.458671, 10.61246, 5.087654], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.060340457, -0.0, -0.10762669, 0.049048025, -0.06210932, -0.04861224, 0.040832225], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 479, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.9409983, 0.4668243, 0.0, 0.0, 0.6969765, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 3.0, -0.10762669, 0.049048025, 6.0, -0.04861224, 0.040832225], "split_indices": [0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.327946, 44.5081, 7.819847, 11.565999, 32.9421, 22.357512, 10.58459], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.022938386, -0.15730059, 0.013442668, -0.10440677, -0.0, 0.07060044, -0.10222952, 0.005134854, 0.061638463, -0.052221652, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 480, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.371478, 0.501361, 0.38409466, 0.0, 0.0, 0.26015577, 0.12868407, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 4.0, 8.0, -0.10440677, -0.0, 7.0, 9.0, 0.005134854, 0.061638463, -0.052221652, -0.0], "split_indices": [2, 0, 2, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [71.61873, 15.137613, 56.481117, 6.436128, 8.701485, 38.641254, 17.839865, 29.006178, 9.635075, 9.744722, 8.095142], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.054825336, -0.01179605, -0.06635201, 0.025439197, -0.06687561, -0.027906187, 0.017440023], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 481, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.44938093, 0.45219377, 0.0, 0.18257028, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 9.0, -0.06635201, 3.0, -0.06687561, -0.027906187, 0.017440023], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.40091, 53.288754, 12.112155, 45.644398, 7.644358, 8.8120775, 36.83232], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.067995064, -0.13476136, 0.051277086, -0.06272019, -0.07766868, 0.03353688, -0.014916311, 0.012475058, -0.040435247], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 482, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.4429335, 0.2667839, 0.13070309, 0.19630772, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 5.0, 4.0, -0.07766868, 0.03353688, -0.014916311, 0.012475058, -0.040435247], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.705326, 34.244473, 18.460855, 23.131166, 11.113307, 12.096542, 6.3643126, 9.057238, 14.073928], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.008932354, 0.09718065, -0.05231133, 0.0073514353, 0.04470031, -0.07540777, 0.012063961, -0.041913137, 0.031943947], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 483, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.2795319, 0.059902236, 0.4045526, 0.0, 0.0, 0.0, 0.3388612, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 2.0, 5.0, 0.0073514353, 0.04470031, -0.07540777, 4.0, -0.041913137, 0.031943947], "split_indices": [1, 1, 1, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.949818, 20.482237, 28.46758, 9.941846, 10.540391, 6.626694, 21.840885, 7.7859755, 14.054911], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.02219485, -0.12200071, 0.08627555, -0.0, -0.15739536, 0.0064693093, 0.16881315, 1.2378885e-05, -0.08002176, 0.015459782, -0.0064436537, 0.10160039, -0.009241224], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 484, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5858993, 0.11367193, 0.1487935, 0.0, 0.39942557, 0.022471668, 0.4426055, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 1.0, 6.0, -0.0, 3.0, 8.0, 8.0, 1.2378885e-05, -0.08002176, 0.015459782, -0.0064436537, 0.10160039, -0.009241224], "split_indices": [0, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.817486, 27.401745, 24.415741, 6.7490644, 20.65268, 13.577502, 10.83824, 8.499669, 12.153011, 6.2439747, 7.333527, 5.749958, 5.088281], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.025641246, 0.09285451, -0.059728183, -0.022428451, 0.15303674, 0.091247305, 0.020408295], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 485, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.6634055, 0.3665983, 0.0, 0.0, 0.27741748, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, -0.059728183, -0.022428451, 4.0, 0.091247305, 0.020408295], "split_indices": [2, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [42.398518, 33.431652, 8.966864, 8.329355, 25.102299, 7.6854916, 17.416807], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.06302493, -0.09715975, 0.021876885, -0.0038481336, -0.17442773, 0.009728209, -0.010730951, -0.0, -0.067103684], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 486, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.24154827, 0.27699533, 0.0, 0.024854489, 0.17408961, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 3.0, 0.021876885, 3.0, 2.0, 0.009728209, -0.010730951, -0.0, -0.067103684], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.002502, 39.98393, 9.018575, 19.173414, 20.810513, 7.50093, 11.672485, 5.067814, 15.742698], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.009262652, -0.04200972, 0.13236512, -0.04561167, -0.0071087163, -0.0, 0.06726656, 0.012263742, -0.028949201], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 487, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.3510392, 0.12822455, 0.1907373, 0.0, 0.1401189, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 2.0, 5.0, -0.04561167, 5.0, -0.0, 0.06726656, 0.012263742, -0.028949201], "split_indices": [0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.30419, 36.81648, 15.48771, 7.3676457, 29.448833, 6.9480495, 8.539661, 18.78706, 10.661774], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.034489594, 0.0414502, -0.10933367, -0.038381156, 0.10259754, 0.009332355, -0.18324779, 0.04308424, -0.0, -0.024085341, -0.0734484], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 488, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.32530838, 0.29525232, 0.31947017, 0.0, 0.10022813, 0.0, 0.07118863, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 3.0, -0.038381156, 7.0, 0.009332355, 7.0, 0.04308424, -0.0, -0.024085341, -0.0734484], "split_indices": [2, 1, 1, 0, 1, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.244164, 26.48326, 27.760904, 6.3009644, 20.182295, 9.305698, 18.455206, 14.670212, 5.512083, 8.376749, 10.078457], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.02468876, 0.081851855, -0.060687594, 0.051127497, -0.0027962574, -0.17608823, 0.011155171, -0.08015385, -0.015978156, 0.023259753, -0.035860773], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 489, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.20224991, 0.1215043, 0.35120893, 0.0, 0.0, 0.13266498, 0.22317603, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 3.0, 6.0, 0.051127497, -0.0027962574, 5.0, 7.0, -0.08015385, -0.015978156, 0.023259753, -0.035860773], "split_indices": [2, 1, 2, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.98992, 11.854436, 39.135487, 6.1283693, 5.726067, 14.993085, 24.1424, 7.4664454, 7.5266395, 16.787851, 7.354551], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.02570968, 0.14642069, -0.11060986, 0.096643455, 0.07444839, -0.0986631, -0.051390342, 0.04263397, -0.0125388345, -0.037183717, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 490, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [1.1401718, 0.12856537, 0.3607219, 0.18653399, 0.0, 0.0, 0.09923938, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 6.0, 3.0, 0.07444839, -0.0986631, 2.0, 0.04263397, -0.0125388345, -0.037183717, -0.0], "split_indices": [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [67.08732, 35.9304, 31.156914, 26.095707, 9.834695, 5.317157, 25.839756, 20.167572, 5.928136, 10.073839, 15.765917], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.028922882, 0.05749327, -0.0, -0.06767177, 0.066430174, 0.008118038, 0.07509485], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 491, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.31643784, 0.0, 0.8466783, 0.0, 0.27402002, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.05749327, 3.0, -0.06767177, 9.0, 0.008118038, 0.07509485], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.625813, 9.065016, 53.560795, 11.9436245, 41.617172, 35.670975, 5.946195], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.028341753, 0.06856751, -0.029257989, 0.020877836, 0.1072355, -0.027578643, 0.07531839, 0.06470538, -0.051294066], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 492, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.2882159, 0.06564556, 0.0, 0.6100901, 0.7282467, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, -0.029257989, 7.0, 8.0, -0.027578643, 0.07531839, 0.06470538, -0.051294066], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.17543, 42.759747, 12.415686, 20.993843, 21.765903, 14.200672, 6.793171, 16.081491, 5.68441], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0480358, -0.08130674, -0.0, -0.18095513, 0.07136283, -0.013042938, -0.086773485, 0.060139343, -0.0149657], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 493, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.5427258, 0.0, 0.57494646, 0.14221126, 0.5208985, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.08130674, 4.0, 5.0, 7.0, -0.013042938, -0.086773485, 0.060139343, -0.0149657], "split_indices": [0, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.308735, 8.00053, 42.308205, 11.631955, 30.676249, 6.149644, 5.4823117, 14.940008, 15.73624], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0024565223, 0.04656935, -0.051677953, 0.0337238, -0.110833, -0.07646793, 0.012833185], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 494, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.37899956, 0.0, 0.38772583, 0.0, 0.68225956, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.04656935, 1.0, 0.0337238, 7.0, -0.07646793, 0.012833185], "split_indices": [2, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.13062, 10.454099, 37.67652, 9.2969265, 28.379595, 14.64074, 13.738854], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.007428702, 0.08946893, -0.050726455, 0.0502242, 0.0057809115, -0.065824725, 0.058023736, 0.052736863, -0.025153372], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 495, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.2297591, 0.08928943, 0.5255369, 0.0, 0.0, 0.0, 0.30614898, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 3.0, 4.0, 0.0502242, 0.0057809115, -0.065824725, 7.0, 0.052736863, -0.025153372], "split_indices": [2, 1, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.393528, 19.31985, 26.073677, 7.8236146, 11.496236, 10.181868, 15.89181, 8.938051, 6.953759], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.010248657, -0.15326522, 0.033045277, -0.0616292, -0.019879298, 0.089083456, -0.04622735, -0.042531572, 0.050254926], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 496, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.3414089, 0.01769948, 0.43187687, 0.0, 0.0, 0.60948855, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [2.0, 5.0, 8.0, -0.0616292, -0.019879298, 2.0, -0.04622735, -0.042531572, 0.050254926], "split_indices": [0, 1, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.37974, 11.885857, 39.493885, 5.8609757, 6.024881, 31.207195, 8.286689, 7.3857746, 23.82142], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.07356229, 0.023630682, 0.08085096, 0.057021964, -0.05195416, -0.0027513043, 0.06978916, -0.062392928, 0.036447465], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 497, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.47968867, 0.113212496, 0.0, 0.39125443, 0.3837901, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 7.0, 0.08085096, 5.0, 5.0, -0.0027513043, 0.06978916, -0.062392928, 0.036447465], "split_indices": [2, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.676754, 42.48202, 9.194732, 30.553112, 11.928909, 22.463982, 8.08913, 6.4785886, 5.4503207], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.06102412, -0.039656542, 0.089029975, 0.016556552, -0.04786323, 0.15742883, 0.03443451, 0.08198648, 0.026580116, -0.0015716325, 0.035267122], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 498, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.16704395, 0.1574417, 0.14375415, 0.0, 0.0, 0.09047243, 0.101433285, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 1.0, 4.0, 0.016556552, -0.04786323, 4.0, 8.0, 0.08198648, 0.026580116, -0.0015716325, 0.035267122], "split_indices": [1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.239643, 11.154104, 44.08554, 6.0630627, 5.091041, 17.919828, 26.165712, 5.0297527, 12.890075, 17.531902, 8.633809], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0034732253, -0.037383277, 0.07121023, 0.013773369, -0.06617288, 0.05247047, -0.023437725], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 499, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.52777916, 0.44491884, 0.0, 0.55120265, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, 0.07121023, 3.0, -0.06617288, 0.05247047, -0.023437725], "split_indices": [0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.20825, 43.94465, 7.263599, 34.648106, 9.296542, 12.651314, 21.996792], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "5E-1", "boost_from_average": "1", "num_class": "10", "num_feature": "3", "num_target": "1"}, "objective": {"name": "multi:softprob", "softmax_multiclass_param": {"num_class": "10"}}}, "version": [3, 0, 2]}