#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急内存优化修复脚本
专门解决OOM错误-536870904
"""

import sys
import gc
import os
import psutil
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('EmergencyMemoryFix')

def force_memory_cleanup():
    """强制内存清理"""
    logger.info("🧹 开始强制内存清理...")
    
    # 多次垃圾回收
    for i in range(3):
        collected = gc.collect()
        logger.info(f"🗑️ 垃圾回收第{i+1}次: 清理了{collected}个对象")
    
    # 获取内存状态
    memory = psutil.virtual_memory()
    logger.info(f"💾 当前内存使用: {memory.percent:.1f}% ({memory.used/1024/1024/1024:.1f}GB/{memory.total/1024/1024/1024:.1f}GB)")
    
    return memory.percent

def optimize_python_environment():
    """优化Python环境设置"""
    logger.info("⚙️ 优化Python环境设置...")
    
    # 设置环境变量优化内存使用
    os.environ['PYTHONHASHSEED'] = '0'  # 固定哈希种子
    os.environ['PYTHONOPTIMIZE'] = '1'  # 启用优化
    os.environ['PYTHONDONTWRITEBYTECODE'] = '1'  # 不生成.pyc文件
    
    # TensorFlow内存优化
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # 减少TensorFlow日志
    os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'  # 禁用oneDNN优化
    
    # 设置内存增长策略
    try:
        import tensorflow as tf
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
        logger.info("✅ TensorFlow内存增长策略已设置")
    except ImportError:
        logger.info("ℹ️ TensorFlow未安装，跳过GPU内存优化")
    
    logger.info("✅ Python环境优化完成")

def check_memory_requirements():
    """检查内存需求"""
    logger.info("🔍 检查系统内存需求...")
    
    memory = psutil.virtual_memory()
    available_gb = memory.available / 1024 / 1024 / 1024
    
    # 训练所需最小内存（估算）
    min_required_gb = 4.0  # 4GB最小需求
    recommended_gb = 8.0   # 8GB推荐
    
    logger.info(f"💾 可用内存: {available_gb:.1f}GB")
    logger.info(f"📊 最小需求: {min_required_gb}GB")
    logger.info(f"🎯 推荐配置: {recommended_gb}GB")
    
    if available_gb < min_required_gb:
        logger.error(f"❌ 内存不足！可用{available_gb:.1f}GB < 需求{min_required_gb}GB")
        return False
    elif available_gb < recommended_gb:
        logger.warning(f"⚠️ 内存偏低！可用{available_gb:.1f}GB < 推荐{recommended_gb}GB")
        return True
    else:
        logger.info(f"✅ 内存充足！可用{available_gb:.1f}GB >= 推荐{recommended_gb}GB")
        return True

def apply_emergency_fixes():
    """应用紧急修复"""
    logger.info("🚨 应用紧急内存修复...")
    
    fixes_applied = []
    
    # 修复1: 强制垃圾回收
    initial_memory = force_memory_cleanup()
    fixes_applied.append("强制垃圾回收")
    
    # 修复2: 优化Python环境
    optimize_python_environment()
    fixes_applied.append("Python环境优化")
    
    # 修复3: 检查内存需求
    memory_ok = check_memory_requirements()
    if memory_ok:
        fixes_applied.append("内存需求检查通过")
    else:
        logger.error("❌ 内存需求检查失败，建议关闭其他程序释放内存")
        return False
    
    # 修复4: 设置进程优先级
    try:
        current_process = psutil.Process()
        current_process.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS if os.name == 'nt' else 10)
        fixes_applied.append("进程优先级调整")
        logger.info("✅ 进程优先级已调整为低优先级")
    except Exception as e:
        logger.warning(f"⚠️ 无法调整进程优先级: {e}")
    
    # 最终内存检查
    final_memory = psutil.virtual_memory().percent
    memory_freed = initial_memory - final_memory
    
    logger.info("🎉 紧急修复完成!")
    logger.info(f"📊 应用的修复: {', '.join(fixes_applied)}")
    logger.info(f"💾 内存释放: {memory_freed:.1f}%")
    logger.info(f"🧠 当前内存使用: {final_memory:.1f}%")
    
    return True

def create_memory_safe_training_script():
    """创建内存安全的训练脚本"""
    logger.info("📝 创建内存安全的训练脚本...")
    
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存安全的训练脚本包装器
"""

import sys
import gc
import os
import psutil
import subprocess
from pathlib import Path

def run_with_memory_safety(script_path, args):
    """以内存安全模式运行训练脚本"""
    print(f"🧠 内存安全模式启动: {script_path}")
    
    # 强制垃圾回收
    gc.collect()
    
    # 获取初始内存
    initial_memory = psutil.virtual_memory().percent
    print(f"💾 初始内存使用: {initial_memory:.1f}%")
    
    # 检查内存是否充足
    if initial_memory > 80:
        print("❌ 内存使用过高，无法安全启动训练")
        return False
    
    try:
        # 启动训练进程
        cmd = [sys.executable, script_path] + args
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1
        )
        
        # 监控内存使用
        while process.poll() is None:
            memory = psutil.virtual_memory().percent
            if memory > 90:
                print(f"⚠️ 内存使用过高: {memory:.1f}%，终止训练")
                process.terminate()
                return False
            time.sleep(5)
        
        # 获取结果
        stdout, stderr = process.communicate()
        
        if process.returncode == 0:
            print("✅ 训练完成")
            return True
        else:
            print(f"❌ 训练失败: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 训练异常: {e}")
        return False
    finally:
        # 清理内存
        gc.collect()

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法: python memory_safe_train.py <训练脚本> [参数...]")
        sys.exit(1)
    
    script_path = sys.argv[1]
    args = sys.argv[2:]
    
    success = run_with_memory_safety(script_path, args)
    sys.exit(0 if success else 1)
'''
    
    script_path = Path("scripts/memory_safe_train.py")
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    logger.info(f"✅ 内存安全训练脚本已创建: {script_path}")
    return script_path

def main():
    """主函数"""
    logger.info("🚨 启动紧急内存优化修复...")
    
    try:
        # 应用紧急修复
        if not apply_emergency_fixes():
            logger.error("❌ 紧急修复失败")
            return False
        
        # 创建内存安全训练脚本
        create_memory_safe_training_script()
        
        logger.info("🎉 紧急内存优化修复完成!")
        logger.info("💡 建议:")
        logger.info("   1. 使用单模型训练: --model xgb")
        logger.info("   2. 使用内存安全脚本: python scripts/memory_safe_train.py")
        logger.info("   3. 监控内存使用: python scripts/memory_monitor.py")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 紧急修复异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
