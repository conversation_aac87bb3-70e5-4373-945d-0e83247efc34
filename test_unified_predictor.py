#!/usr/bin/env python3
"""
测试统一预测器接口
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.predictors.unified_predictor_interface import UnifiedPredictorInterface

def test_unified_predictor():
    """测试统一预测器接口"""
    print("开始测试统一预测器接口...")
    
    try:
        # 初始化统一预测器接口
        db_path = "data/fucai3d.db"
        interface = UnifiedPredictorInterface(db_path)
        
        print("✅ 统一预测器接口初始化成功")
        
        # 获取预测器状态
        status = interface.get_predictor_status()
        print(f"📊 预测器状态: {status}")
        
        # 计算总体状态
        loaded_count = sum(1 for p in status.values() if p.get('loaded', False))
        total_count = len(status)
        
        print(f"📈 已加载预测器: {loaded_count}/{total_count}")
        
        if loaded_count == total_count:
            print("✅ 所有预测器已成功加载")
        else:
            print("⚠️ 部分预测器未加载")
        
        # 测试生成预测
        print("\n开始测试预测生成...")
        prediction = interface.generate_unified_prediction()
        print(f"🎯 预测结果: {prediction}")
        
        print("\n✅ 统一预测器接口测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_unified_predictor()
    sys.exit(0 if success else 1)
