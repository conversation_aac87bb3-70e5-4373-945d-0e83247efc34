[ULTRA_LIGHT] 超轻量级训练模式启动
[POSITION] 位置: units
[ISSUE] 期号: 2025230
[LIMIT] 数据限制: 500条
[START] 启动超轻量级units位XGBoost训练
[DATA] 加载数据: 500条
[RANGE] 数据范围: 2024070 到 2025217
[FEATURE] 特征维度: (499, 3)
[TARGET] 目标维度: (499,)
[TRAIN] 开始训练...
[PARAMS] 模型参数: 深度3, 学习率0.3, 轮次50
[RESULT] 训练完成，测试准确率: 0.050
[METRICS] 正确预测: 5/100
[SAVE] 模型已保存: models\units\ultra_light_xgb_2025230.json
[TRAINING_INFO] {"training_mode": "ultra_light", "position": "units", "target_issue": "2025230", "data_limit": 500, "model_type": "XGBoost", "start_time": "2025-08-16T17:19:46.602097", "end_time": "2025-08-16T17:19:48.205590", "data_range": {"total_records": 500, "latest_issue": "2025217", "earliest_issue": "2024070", "data_source": "lottery_data table"}, "model_params": {"algorithm": "XGBoost", "objective": "multi:softprob", "num_class": 10, "max_depth": 3, "learning_rate": 0.3, "num_boost_round": 50, "train_test_split": "80/20", "feature_count": 3, "training_samples": 399, "test_samples": 100}, "training_results": {"accuracy": 0.05, "test_samples": 100, "correct_predictions": 5, "model_file": "ultra_light_xgb_2025230.json", "training_duration_seconds": 1.603493}}
[SUCCESS] 超轻量级训练成功完成！
[INFO] 提示: 这是应急模式，模型精度可能较低
