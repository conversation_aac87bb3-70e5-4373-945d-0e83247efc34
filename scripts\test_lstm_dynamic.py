#!/usr/bin/env python3
"""
测试LSTM模型动态期号保存功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_lstm_filename_generation():
    """测试LSTM模型文件名生成"""
    try:
        from src.utils.dynamic_issue_manager import issue_manager
        
        print("🔍 测试LSTM模型文件名生成")
        print("=" * 40)
        
        # 获取最新期号
        latest_issue = issue_manager.get_latest_issue()
        print(f"最新期号: {latest_issue}")
        
        # 测试LSTM主文件名生成
        lstm_main_filename = issue_manager.generate_model_filename('lstm', 'hundreds', latest_issue, 'model')
        print(f"✅ LSTM主文件名: {lstm_main_filename}")
        
        # 测试LSTM辅助文件名生成
        lstm_comp_filename = issue_manager.generate_model_filename('lstm', 'hundreds', latest_issue, 'components')
        print(f"✅ LSTM辅助文件名: {lstm_comp_filename}")
        
        # 测试文件路径生成
        lstm_main_filepath = issue_manager.get_model_filepath('lstm', 'hundreds', latest_issue, 'model')
        lstm_comp_filepath = issue_manager.get_model_filepath('lstm', 'hundreds', latest_issue, 'components')
        
        print(f"✅ LSTM主文件路径: {lstm_main_filepath}")
        print(f"✅ LSTM辅助文件路径: {lstm_comp_filepath}")
        
        # 验证文件名格式
        expected_main = f"lstm_hundreds_model_{latest_issue}.h5"
        expected_comp = f"lstm_hundreds_model_{latest_issue}_components.pkl"
        
        if lstm_main_filename == expected_main:
            print("✅ LSTM主文件名格式正确")
        else:
            print(f"❌ LSTM主文件名格式错误: 期望 {expected_main}, 实际 {lstm_main_filename}")
            return False
        
        if lstm_comp_filename == expected_comp:
            print("✅ LSTM辅助文件名格式正确")
        else:
            print(f"❌ LSTM辅助文件名格式错误: 期望 {expected_comp}, 实际 {lstm_comp_filename}")
            return False
        
        print("\n🎉 LSTM文件名生成测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_lstm_save_logic():
    """测试LSTM保存逻辑"""
    try:
        from src.utils.dynamic_issue_manager import issue_manager
        
        print("\n🔍 测试LSTM保存逻辑")
        print("=" * 40)
        
        latest_issue = issue_manager.get_latest_issue()
        
        # 模拟LSTM模型保存路径生成
        model_dir = Path('models/hundreds/')
        model_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成LSTM模型的基础文件名（不含扩展名）
        filename_base = f"lstm_hundreds_model_{latest_issue}"
        filepath_base = model_dir / filename_base
        
        # LSTM模型会生成两个文件
        h5_file = Path(f"{filepath_base}.h5")
        pkl_file = Path(f"{filepath_base}_components.pkl")
        
        print(f"✅ LSTM基础路径: {filepath_base}")
        print(f"✅ LSTM主文件: {h5_file}")
        print(f"✅ LSTM辅助文件: {pkl_file}")
        
        # 验证路径格式
        expected_h5 = f"models/hundreds/lstm_hundreds_model_{latest_issue}.h5"
        expected_pkl = f"models/hundreds/lstm_hundreds_model_{latest_issue}_components.pkl"
        
        if str(h5_file) == expected_h5:
            print("✅ LSTM主文件路径格式正确")
        else:
            print(f"❌ LSTM主文件路径格式错误: 期望 {expected_h5}, 实际 {h5_file}")
            return False
        
        if str(pkl_file) == expected_pkl:
            print("✅ LSTM辅助文件路径格式正确")
        else:
            print(f"❌ LSTM辅助文件路径格式错误: 期望 {expected_pkl}, 实际 {pkl_file}")
            return False
        
        print("\n🎉 LSTM保存逻辑测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_all_positions():
    """测试所有位置的LSTM文件名"""
    try:
        from src.utils.dynamic_issue_manager import issue_manager
        
        print("\n🔍 测试所有位置的LSTM文件名")
        print("=" * 40)
        
        latest_issue = issue_manager.get_latest_issue()
        positions = ['hundreds', 'tens', 'units']
        
        for position in positions:
            print(f"\n{position.upper()} 位LSTM模型:")
            
            # 主文件
            main_filename = issue_manager.generate_model_filename('lstm', position, latest_issue, 'model')
            print(f"  主文件: {main_filename}")
            
            # 辅助文件
            comp_filename = issue_manager.generate_model_filename('lstm', position, latest_issue, 'components')
            print(f"  辅助文件: {comp_filename}")
            
            # 验证格式
            expected_main = f"lstm_{position}_model_{latest_issue}.h5"
            expected_comp = f"lstm_{position}_model_{latest_issue}_components.pkl"
            
            if main_filename != expected_main or comp_filename != expected_comp:
                print(f"❌ {position} 位文件名格式错误")
                return False
        
        print("\n✅ 所有位置的LSTM文件名格式正确")
        print("\n🎉 所有位置测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 LSTM模型动态期号保存功能测试")
    print("=" * 50)
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 文件名生成
    if test_lstm_filename_generation():
        success_count += 1
    
    # 测试2: 保存逻辑
    if test_lstm_save_logic():
        success_count += 1
    
    # 测试3: 所有位置
    if test_all_positions():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有LSTM测试通过！")
        return True
    else:
        print("⚠️ 部分LSTM测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
