[2025-08-15T13:50:23.267378] [INFO] [P9SystemAdapter] [P9系统API适配器日志系统初始化成功] [req:None]
INFO:src.data.feature_importance:SHAP库导入成功
2025-08-15 13:50:28.056911: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-15 13:50:29.918451: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
INFO:src.core.optimization_comparator:\u2705 优化对比数据库初始化完成
INFO:__main__:\u2705 优化对比路由已加载
INFO:src.config.feature_config:功能配置管理器初始化完成
INFO:src.core.data_flow_manager:后台任务启动完成
INFO:src.core.data_flow_manager:数据流管理器初始化完成
INFO:src.database.feedback_data_manager:用户反馈数据管理器初始化完成
INFO:src.analytics.feedback_analytics:反馈分析引擎初始化完成
INFO:src.analytics.feedback_analytics:个性化优化引擎初始化完成
INFO:src.database.lottery_validator:期号验证器初始化完成
INFO:src.core.auto_optimization_engine:自动优化迭代引擎初始化完成
INFO:src.core.auto_optimization_scheduler:自动优化调度器初始化完成，检查间隔: 3600秒
INFO:src.core.optimization_validation_manager:\u2705 优化验证管理器数据库初始化完成
INFO:__main__:\u2705 优化验证管理路由已加载
INFO:backtest.real_data_manager:数据库验证通过
INFO:backtest.real_data_manager:真实数据管理器初始化完成，数据库路径: data/lottery.db
INFO:backtest.parameter_snapshot_manager:数据库表结构初始化完成
INFO:backtest.parameter_snapshot_manager:参数快照管理器初始化完成，数据库路径: data/fucai3d.db
INFO:backtest.real_data_manager:数据库验证通过
INFO:backtest.real_data_manager:真实数据管理器初始化完成，数据库路径: data/lottery.db
INFO:backtest.parameter_snapshot_manager:数据库表结构初始化完成
INFO:backtest.parameter_snapshot_manager:参数快照管理器初始化完成，数据库路径: data/fucai3d.db
INFO:backtest.time_series_backtest_engine:时间序列回测引擎初始化完成
启动方式检查通过
当前目录: D:\github\fucai3d
使用正确的启动方式: python src/web/app.py
TTL缓存工具导入成功
2025-08-15 13:50:32 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:32 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:32 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:32 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:32 - HundredsPredictor - INFO - XGBoost百位预测模型初始化完成
2025-08-15 13:50:32 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:32 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:32 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:32 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:32 - HundredsPredictor - INFO - LightGBM百位预测模型初始化完成
2025-08-15 13:50:32 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:32 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:32 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:32 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:32 - HundredsPredictor - INFO - LSTM百位预测模型初始化完成
2025-08-15 13:50:32 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:32 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:32 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:32 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:32 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:32 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:32 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:32 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:32 - HundredsPredictor - INFO - XGBoost百位预测模型初始化完成
2025-08-15 13:50:32 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:33 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:33 - HundredsPredictor - INFO - LightGBM百位预测模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:33 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:33 - HundredsPredictor - INFO - LSTM百位预测模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - 基础模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - 集成百位预测模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - 所有模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - P3-百位预测器初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:33 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:33 - HundredsPredictor - INFO - XGBoost百位预测模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:33 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:33 - HundredsPredictor - INFO - LightGBM百位预测模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:33 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:33 - HundredsPredictor - INFO - LSTM百位预测模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:33 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:33 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:33 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:33 - HundredsPredictor - INFO - XGBoost百位预测模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:33 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:33 - HundredsPredictor - INFO - LightGBM百位预测模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:33 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:33 - HundredsPredictor - INFO - LSTM百位预测模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - 基础模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - 集成百位预测模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - 所有模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - P3-百位预测器初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:33 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:33 - HundredsPredictor - INFO - XGBoost百位预测模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:33 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:33 - HundredsPredictor - INFO - LightGBM百位预测模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:33 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:33 - HundredsPredictor - INFO - LSTM百位预测模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:33 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:33 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:33 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:33 - HundredsPredictor - INFO - XGBoost百位预测模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:33 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:33 - HundredsPredictor - INFO - LightGBM百位预测模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:33 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:33 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:33 - HundredsPredictor - INFO - LSTM百位预测模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - 基础模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - 集成百位预测模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - 所有模型初始化完成
2025-08-15 13:50:33 - HundredsPredictor - INFO - P3-百位预测器初始化完成
D:\github\fucai3d\src\web\app.py:355: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("startup")
INFO:     Will watch for changes in these directories: ['D:\\github\\fucai3d']
INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [50904] using WatchFiles
[2025-08-15T13:50:34.773950] [INFO] [P9SystemAdapter] [P9系统API适配器日志系统初始化成功] [req:None]
INFO:src.data.feature_importance:SHAP库导入成功
2025-08-15 13:50:38.296499: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-15 13:50:39.547035: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
INFO:src.core.optimization_comparator:\u2705 优化对比数据库初始化完成
INFO:__mp_main__:\u2705 优化对比路由已加载
INFO:src.config.feature_config:功能配置管理器初始化完成
INFO:src.core.data_flow_manager:后台任务启动完成
INFO:src.core.data_flow_manager:数据流管理器初始化完成
INFO:src.database.feedback_data_manager:用户反馈数据管理器初始化完成
INFO:src.analytics.feedback_analytics:反馈分析引擎初始化完成
INFO:src.analytics.feedback_analytics:个性化优化引擎初始化完成
INFO:src.database.lottery_validator:期号验证器初始化完成
INFO:src.core.auto_optimization_engine:自动优化迭代引擎初始化完成
INFO:src.core.auto_optimization_scheduler:自动优化调度器初始化完成，检查间隔: 3600秒
INFO:src.core.optimization_validation_manager:\u2705 优化验证管理器数据库初始化完成
INFO:__mp_main__:\u2705 优化验证管理路由已加载
INFO:backtest.real_data_manager:数据库验证通过
INFO:backtest.real_data_manager:真实数据管理器初始化完成，数据库路径: data/lottery.db
INFO:backtest.parameter_snapshot_manager:数据库表结构初始化完成
INFO:backtest.parameter_snapshot_manager:参数快照管理器初始化完成，数据库路径: data/fucai3d.db
INFO:backtest.real_data_manager:数据库验证通过
INFO:backtest.real_data_manager:真实数据管理器初始化完成，数据库路径: data/lottery.db
INFO:backtest.parameter_snapshot_manager:数据库表结构初始化完成
INFO:backtest.parameter_snapshot_manager:参数快照管理器初始化完成，数据库路径: data/fucai3d.db
INFO:backtest.time_series_backtest_engine:时间序列回测引擎初始化完成
TTL缓存工具导入成功
2025-08-15 13:50:41 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:41 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:41 - HundredsPredictor - INFO - XGBoost百位预测模型初始化完成
2025-08-15 13:50:41 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:41 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:41 - HundredsPredictor - INFO - LightGBM百位预测模型初始化完成
2025-08-15 13:50:41 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:41 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:41 - HundredsPredictor - INFO - LSTM百位预测模型初始化完成
2025-08-15 13:50:41 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:41 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:41 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:41 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:41 - HundredsPredictor - INFO - XGBoost百位预测模型初始化完成
2025-08-15 13:50:41 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:41 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:41 - HundredsPredictor - INFO - LightGBM百位预测模型初始化完成
2025-08-15 13:50:41 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:41 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:41 - HundredsPredictor - INFO - LSTM百位预测模型初始化完成
2025-08-15 13:50:41 - HundredsPredictor - INFO - 基础模型初始化完成
2025-08-15 13:50:41 - HundredsPredictor - INFO - 集成百位预测模型初始化完成
2025-08-15 13:50:41 - HundredsPredictor - INFO - 所有模型初始化完成
2025-08-15 13:50:41 - HundredsPredictor - INFO - P3-百位预测器初始化完成
2025-08-15 13:50:41 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:41 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:41 - HundredsPredictor - INFO - XGBoost百位预测模型初始化完成
2025-08-15 13:50:41 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:41 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:41 - HundredsPredictor - INFO - LightGBM百位预测模型初始化完成
2025-08-15 13:50:41 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:41 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:41 - HundredsPredictor - INFO - LSTM百位预测模型初始化完成
2025-08-15 13:50:41 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:41 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:41 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:41 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:41 - HundredsPredictor - INFO - XGBoost百位预测模型初始化完成
2025-08-15 13:50:41 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:41 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:41 - HundredsPredictor - INFO - LightGBM百位预测模型初始化完成
2025-08-15 13:50:41 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:41 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:41 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:41 - HundredsPredictor - INFO - LSTM百位预测模型初始化完成
2025-08-15 13:50:41 - HundredsPredictor - INFO - 基础模型初始化完成
2025-08-15 13:50:41 - HundredsPredictor - INFO - 集成百位预测模型初始化完成
2025-08-15 13:50:41 - HundredsPredictor - INFO - 所有模型初始化完成
2025-08-15 13:50:41 - HundredsPredictor - INFO - P3-百位预测器初始化完成
2025-08-15 13:50:42 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:42 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:42 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:42 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:42 - HundredsPredictor - INFO - XGBoost百位预测模型初始化完成
2025-08-15 13:50:42 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:42 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:42 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:42 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:42 - HundredsPredictor - INFO - LightGBM百位预测模型初始化完成
2025-08-15 13:50:42 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:42 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:42 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:42 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:42 - HundredsPredictor - INFO - LSTM百位预测模型初始化完成
2025-08-15 13:50:42 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:42 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:42 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:42 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:42 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:42 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:42 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:42 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:42 - HundredsPredictor - INFO - XGBoost百位预测模型初始化完成
2025-08-15 13:50:42 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:42 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:42 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:42 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:42 - HundredsPredictor - INFO - LightGBM百位预测模型初始化完成
2025-08-15 13:50:42 - HundredsPredictor - INFO - P2特征接口初始化成功: hundreds
2025-08-15 13:50:42 - HundredsPredictor - INFO - 缓存优化器初始化成功: hundreds
2025-08-15 13:50:42 - HundredsPredictor - INFO - 配置重载信号注册成功
2025-08-15 13:50:42 - HundredsPredictor - INFO - 初始化hundreds位独立预测器
2025-08-15 13:50:42 - HundredsPredictor - INFO - LSTM百位预测模型初始化完成
2025-08-15 13:50:42 - HundredsPredictor - INFO - 基础模型初始化完成
2025-08-15 13:50:42 - HundredsPredictor - INFO - 集成百位预测模型初始化完成
2025-08-15 13:50:42 - HundredsPredictor - INFO - 所有模型初始化完成
2025-08-15 13:50:42 - HundredsPredictor - INFO - P3-百位预测器初始化完成
INFO:     Started server process [40440]
INFO:     Waiting for application startup.
2025-08-15 13:50:42 - P9SystemAdapter - INFO - {"message": "P9系统组件初始化成功", "context": {"timestamp": "2025-08-15T13:50:42.397522", "component": "P9SystemAdapter", "request_id": null}}
[2025-08-15T13:50:42.397522] [INFO] [P9SystemAdapter] [P9系统组件初始化成功] [req:None]
ERROR:    Traceback (most recent call last):
  File "D:\github\fucai3d\src\web\app.py", line 384, in startup_event
    print("\u2705 自动优化调度器已启动")
UnicodeEncodeError: 'gbk' codec can't encode character '\u2705' in position 0: illegal multibyte sequence

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\starlette\routing.py", line 692, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Program Files\Python311\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\fastapi\routing.py", line 133, in merged_lifespan
    async with original_context(app) as maybe_original_state:
  File "C:\Program Files\Python311\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\fastapi\routing.py", line 133, in merged_lifespan
    async with original_context(app) as maybe_original_state:
  File "C:\Program Files\Python311\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\fastapi\routing.py", line 133, in merged_lifespan
    async with original_context(app) as maybe_original_state:
  File "C:\Program Files\Python311\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\fastapi\routing.py", line 133, in merged_lifespan
    async with original_context(app) as maybe_original_state:
  File "C:\Program Files\Python311\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\fastapi\routing.py", line 133, in merged_lifespan
    async with original_context(app) as maybe_original_state:
  File "C:\Program Files\Python311\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\fastapi\routing.py", line 133, in merged_lifespan
    async with original_context(app) as maybe_original_state:
  File "C:\Program Files\Python311\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\fastapi\routing.py", line 133, in merged_lifespan
    async with original_context(app) as maybe_original_state:
  File "C:\Program Files\Python311\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\fastapi\routing.py", line 133, in merged_lifespan
    async with original_context(app) as maybe_original_state:
  File "C:\Program Files\Python311\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\fastapi\routing.py", line 133, in merged_lifespan
    async with original_context(app) as maybe_original_state:
  File "C:\Program Files\Python311\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\fastapi\routing.py", line 133, in merged_lifespan
    async with original_context(app) as maybe_original_state:
  File "C:\Program Files\Python311\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\fastapi\routing.py", line 133, in merged_lifespan
    async with original_context(app) as maybe_original_state:
  File "C:\Program Files\Python311\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\fastapi\routing.py", line 133, in merged_lifespan
    async with original_context(app) as maybe_original_state:
  File "C:\Program Files\Python311\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\fastapi\routing.py", line 133, in merged_lifespan
    async with original_context(app) as maybe_original_state:
  File "C:\Program Files\Python311\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\fastapi\routing.py", line 133, in merged_lifespan
    async with original_context(app) as maybe_original_state:
  File "C:\Program Files\Python311\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\fastapi\routing.py", line 133, in merged_lifespan
    async with original_context(app) as maybe_original_state:
  File "C:\Program Files\Python311\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\fastapi\routing.py", line 133, in merged_lifespan
    async with original_context(app) as maybe_original_state:
  File "C:\Program Files\Python311\Lib\site-packages\starlette\routing.py", line 569, in __aenter__
    await self._router.startup()
  File "C:\Program Files\Python311\Lib\site-packages\starlette\routing.py", line 669, in startup
    await handler()
  File "D:\github\fucai3d\src\web\app.py", line 386, in startup_event
    print(f"\u26a0\ufe0f 自动优化调度器启动失败: {e}")
UnicodeEncodeError: 'gbk' codec can't encode character '\u26a0' in position 0: illegal multibyte sequence

ERROR:    Application startup failed. Exiting.
