#!/usr/bin/env python3
"""
简单测试动态期号功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_basic_functionality():
    """测试基本功能"""
    try:
        from src.utils.dynamic_issue_manager import issue_manager
        
        print("🔍 测试动态期号管理器")
        print("=" * 40)
        
        # 测试获取最新期号
        latest_issue = issue_manager.get_latest_issue()
        print(f"✅ 最新期号: {latest_issue}")
        
        # 测试文件名生成
        xgb_filename = issue_manager.generate_model_filename('xgb', 'hundreds', latest_issue)
        print(f"✅ XGBoost文件名: {xgb_filename}")
        
        lgb_filename = issue_manager.generate_model_filename('lgb', 'hundreds', latest_issue)
        print(f"✅ LightGBM文件名: {lgb_filename}")
        
        lstm_filename = issue_manager.generate_model_filename('lstm', 'hundreds', latest_issue, 'model')
        print(f"✅ LSTM主文件名: {lstm_filename}")
        
        lstm_comp_filename = issue_manager.generate_model_filename('lstm', 'hundreds', latest_issue, 'components')
        print(f"✅ LSTM辅助文件名: {lstm_comp_filename}")
        
        ensemble_filename = issue_manager.generate_model_filename('ensemble', 'hundreds', latest_issue)
        print(f"✅ 集成模型文件名: {ensemble_filename}")
        
        # 测试期号检测
        new_issue = issue_manager.detect_new_issue()
        if new_issue:
            print(f"✅ 检测到新期号: {new_issue}")
        else:
            print("✅ 没有新期号需要训练")
        
        print("\n🎉 所有基本功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_basic_functionality()
    sys.exit(0 if success else 1)
