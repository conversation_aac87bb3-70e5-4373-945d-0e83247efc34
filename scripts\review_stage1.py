#!/usr/bin/env python3
"""
阶段1评审验证脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def review_stage1():
    """评审阶段1功能"""
    print("📋 阶段1：模型保存机制扩展 - 功能验证")
    print("=" * 50)
    
    try:
        from src.utils.dynamic_issue_manager import issue_manager
        
        # 1. 验证期号获取
        latest_issue = issue_manager.get_latest_issue()
        print(f"✅ 最新期号获取: {latest_issue}")
        
        # 2. 验证文件名生成
        test_issue = "2025217"
        
        # XGBoost/LightGBM
        xgb_file = issue_manager.generate_model_filename('xgb', 'hundreds', test_issue)
        lgb_file = issue_manager.generate_model_filename('lgb', 'hundreds', test_issue)
        print(f"✅ XGBoost文件名: {xgb_file}")
        print(f"✅ LightGBM文件名: {lgb_file}")
        
        # LSTM
        lstm_main = issue_manager.generate_model_filename('lstm', 'hundreds', test_issue, 'model')
        lstm_comp = issue_manager.generate_model_filename('lstm', 'hundreds', test_issue, 'components')
        print(f"✅ LSTM主文件: {lstm_main}")
        print(f"✅ LSTM辅助文件: {lstm_comp}")
        
        # 集成模型
        ensemble_file = issue_manager.generate_model_filename('ensemble', 'hundreds', test_issue)
        print(f"✅ 集成模型文件: {ensemble_file}")
        
        # 3. 验证期号检测
        new_issue = issue_manager.detect_new_issue()
        if new_issue:
            print(f"✅ 检测到新期号: {new_issue}")
        else:
            print("✅ 没有新期号需要训练")
        
        # 4. 验证文件名格式
        expected_formats = {
            'xgb': f"xgb_hundreds_model_{test_issue}.pkl",
            'lgb': f"lgb_hundreds_model_{test_issue}.pkl",
            'lstm_main': f"lstm_hundreds_model_{test_issue}.h5",
            'lstm_comp': f"lstm_hundreds_model_{test_issue}_components.pkl",
            'ensemble': f"ensemble_hundreds_model_{test_issue}_ensemble.pkl"
        }
        
        actual_formats = {
            'xgb': xgb_file,
            'lgb': lgb_file,
            'lstm_main': lstm_main,
            'lstm_comp': lstm_comp,
            'ensemble': ensemble_file
        }
        
        print("\n📊 文件名格式验证:")
        all_correct = True
        for key in expected_formats:
            expected = expected_formats[key]
            actual = actual_formats[key]
            if expected == actual:
                print(f"  ✅ {key}: {actual}")
            else:
                print(f"  ❌ {key}: 期望 {expected}, 实际 {actual}")
                all_correct = False
        
        if all_correct:
            print("\n🎉 阶段1所有功能验证通过！")
            return True
        else:
            print("\n⚠️ 部分功能验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    success = review_stage1()
    sys.exit(0 if success else 1)
