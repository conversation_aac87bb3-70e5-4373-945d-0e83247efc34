#!/usr/bin/env python3
"""
统一预测器接口管理器

管理所有预测器的加载和调用，提供标准化的数据接口
为P8智能交集融合系统提供统一的预测器访问接口

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
from pathlib import Path
import json
from datetime import datetime

class UnifiedPredictorInterface:
    """统一预测器接口管理器"""
    
    def __init__(self, db_path: str, config_path: Optional[str] = None):
        """
        初始化统一预测器接口
        
        Args:
            db_path: 数据库路径
            config_path: 配置文件路径
        """
        self.db_path = db_path
        self.config_path = config_path
        self.predictors = {}
        self.logger = logging.getLogger(__name__)
        
        # 预测器配置
        self.predictor_config = {
            'hundreds': {
                'class_name': 'HundredsPredictor',
                'module_path': 'src.predictors.hundreds_predictor',
                'position': 'hundreds',
                'output_size': 10
            },
            'tens': {
                'class_name': 'TensPredictor', 
                'module_path': 'src.predictors.tens_predictor',
                'position': 'tens',
                'output_size': 10
            },
            'units': {
                'class_name': 'UnitsPredictor',
                'module_path': 'src.predictors.units_predictor', 
                'position': 'units',
                'output_size': 10
            },
            'sum': {
                'class_name': 'SumPredictor',
                'module_path': 'src.predictors.sum_predictor',
                'position': 'sum',
                'output_size': 28
            },
            'span': {
                'class_name': 'SpanPredictor',
                'module_path': 'src.predictors.span_predictor',
                'position': 'span', 
                'output_size': 10
            }
        }

        self.logger.info("统一预测器接口管理器初始化完成")

        # 自动加载所有预测器
        self.load_all_predictors()
    
    def load_all_predictors(self) -> bool:
        """
        加载所有预测器
        
        Returns:
            是否成功加载所有预测器
        """
        try:
            success_count = 0
            total_count = len(self.predictor_config)
            
            for name, config in self.predictor_config.items():
                try:
                    # 动态导入预测器类
                    module = __import__(config['module_path'], fromlist=[config['class_name']])
                    predictor_class = getattr(module, config['class_name'])
                    
                    # 实例化预测器
                    predictor = predictor_class(self.db_path)
                    self.predictors[name] = predictor
                    
                    self.logger.info(f"成功加载预测器: {name}")
                    success_count += 1
                    
                except Exception as e:
                    self.logger.error(f"加载预测器 {name} 失败: {e}")
                    self.predictors[name] = None
            
            if success_count == total_count:
                self.logger.info("所有预测器加载完成")
                return True
            else:
                self.logger.warning(f"部分预测器加载失败: {success_count}/{total_count}")
                return False
                
        except Exception as e:
            self.logger.error(f"加载预测器失败: {e}")
            return False
    
    def get_all_predictions(self, issue: str) -> Dict[str, Any]:
        """
        获取所有预测器的标准化预测结果
        
        Args:
            issue: 期号
            
        Returns:
            标准化预测结果字典
        """
        predictions = {}
        
        for name, predictor in self.predictors.items():
            if predictor is None:
                predictions[name] = {'error': 'predictor_not_loaded'}
                continue
                
            try:
                if name in ['hundreds', 'tens', 'units']:
                    # 位置预测器
                    result = predictor.predict_next_period(issue)
                    predictions[name] = {
                        'probabilities': result.get('probabilities', []),
                        'predicted_digit': result.get('predicted_digit', 0),
                        'confidence': result.get('confidence', 0.5),
                        'prediction_type': 'position',
                        'output_size': 10
                    }
                    
                elif name == 'sum':
                    # 和值预测器
                    features = predictor._build_features_for_prediction()
                    if features is not None and len(features) > 0:
                        sum_pred = predictor.predict(features)[0]
                        sum_probs = predictor.predict_probability(features)[0]
                        constraint_info = predictor.get_constraint_info(features)
                        
                        predictions[name] = {
                            'predicted_sum': float(sum_pred),
                            'probabilities': sum_probs.tolist(),
                            'confidence': float(np.max(sum_probs)),
                            'constraint_info': constraint_info,
                            'prediction_type': 'sum',
                            'output_size': 28
                        }
                    else:
                        predictions[name] = {'error': 'no_features_available'}
                        
                elif name == 'span':
                    # 跨度预测器
                    result = predictor.predict_next_period(issue)
                    predictions[name] = {
                        'predicted_span': result.get('optimized_prediction', result.get('predicted_span', 0)),
                        'probabilities': result.get('probabilities', []),
                        'confidence': result.get('confidence', 0.5),
                        'constraint_info': result.get('constraint_info', {}),
                        'prediction_type': 'span',
                        'output_size': 10
                    }
                    
            except Exception as e:
                self.logger.error(f"{name}预测器预测失败: {e}")
                predictions[name] = {'error': str(e)}
        
        return predictions
    
    def get_constraint_matrix(self, issue: str) -> Dict[str, Any]:
        """
        获取约束矩阵
        
        Args:
            issue: 期号
            
        Returns:
            约束矩阵信息
        """
        try:
            predictions = self.get_all_predictions(issue)
            
            constraint_matrix = {
                'sum_constraints': predictions.get('sum', {}).get('constraint_info', {}),
                'span_constraints': predictions.get('span', {}).get('constraint_info', {}),
                'position_constraints': {
                    'hundreds': predictions.get('hundreds', {}),
                    'tens': predictions.get('tens', {}),
                    'units': predictions.get('units', {})
                },
                'issue': issue,
                'timestamp': datetime.now().isoformat()
            }
            
            return constraint_matrix
            
        except Exception as e:
            self.logger.error(f"获取约束矩阵失败: {e}")
            return {'error': str(e)}
    
    def validate_predictions(self, predictions: Dict[str, Any]) -> Dict[str, bool]:
        """
        验证预测结果的有效性
        
        Args:
            predictions: 预测结果
            
        Returns:
            验证结果
        """
        validation_results = {}
        
        for name, pred in predictions.items():
            if 'error' in pred:
                validation_results[name] = False
                continue
                
            try:
                if name in ['hundreds', 'tens', 'units']:
                    # 验证位置预测
                    valid = (
                        'probabilities' in pred and
                        len(pred['probabilities']) == 10 and
                        'predicted_digit' in pred and
                        0 <= pred['predicted_digit'] <= 9
                    )
                elif name == 'sum':
                    # 验证和值预测
                    valid = (
                        'predicted_sum' in pred and
                        0 <= pred['predicted_sum'] <= 27 and
                        'probabilities' in pred and
                        len(pred['probabilities']) == 28
                    )
                elif name == 'span':
                    # 验证跨度预测
                    valid = (
                        'predicted_span' in pred and
                        0 <= pred['predicted_span'] <= 9 and
                        'probabilities' in pred and
                        len(pred['probabilities']) == 10
                    )
                else:
                    valid = False
                    
                validation_results[name] = valid
                
            except Exception as e:
                self.logger.error(f"验证{name}预测失败: {e}")
                validation_results[name] = False
        
        return validation_results
    
    def get_predictor_status(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有预测器的状态
        
        Returns:
            预测器状态信息
        """
        status = {}
        
        for name, predictor in self.predictors.items():
            if predictor is None:
                status[name] = {
                    'loaded': False,
                    'trained': False,
                    'error': 'not_loaded'
                }
            else:
                try:
                    status[name] = {
                        'loaded': True,
                        'trained': predictor.is_trained,
                        'model_count': len(predictor.models) if hasattr(predictor, 'models') else 0,
                        'current_model': getattr(predictor, 'current_model', 'unknown')
                    }
                except Exception as e:
                    status[name] = {
                        'loaded': True,
                        'trained': False,
                        'error': str(e)
                    }
        
        return status

    def generate_unified_prediction(self, issue: str) -> Dict[str, Any]:
        """
        生成统一的预测结果并保存到final_predictions表

        Args:
            issue: 期号

        Returns:
            统一预测结果
        """
        try:
            # 获取所有预测器的预测结果
            predictions = {}

            for name, predictor in self.predictors.items():
                if predictor is None:
                    self.logger.warning(f"预测器 {name} 未加载，跳过预测")
                    continue

                try:
                    # 调用预测器的predict方法
                    if hasattr(predictor, 'predict'):
                        result = predictor.predict(issue, 'ensemble')
                        predictions[name] = result
                        self.logger.info(f"预测器 {name} 预测完成")
                    else:
                        self.logger.warning(f"预测器 {name} 没有predict方法")

                except Exception as e:
                    self.logger.error(f"预测器 {name} 预测失败: {e}")
                    continue

            # 生成融合预测结果
            fusion_result = self._generate_fusion_prediction(predictions, issue)

            # 保存到final_predictions表
            success = self._save_to_final_predictions(issue, fusion_result, predictions)

            if success:
                self.logger.info(f"统一预测结果已保存: {issue}")
                return {
                    'status': 'success',
                    'issue': issue,
                    'predictions': predictions,
                    'fusion_result': fusion_result
                }
            else:
                self.logger.error(f"保存统一预测结果失败: {issue}")
                return {
                    'status': 'error',
                    'message': '保存预测结果失败'
                }

        except Exception as e:
            self.logger.error(f"生成统一预测失败: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }

    def _generate_fusion_prediction(self, predictions: Dict, issue: str) -> Dict:
        """生成融合预测结果"""
        try:
            # 提取位置预测
            hundreds = predictions.get('hundreds', {}).get('predicted_digit', 5)
            tens = predictions.get('tens', {}).get('predicted_digit', 5)
            units = predictions.get('units', {}).get('predicted_digit', 5)

            # 获取和值和跨度预测
            sum_value = predictions.get('sum', {}).get('predicted_value', hundreds + tens + units)
            span_value = predictions.get('span', {}).get('predicted_value',
                                                       max(hundreds, tens, units) - min(hundreds, tens, units))

            # 计算融合置信度
            confidences = []
            for name in ['hundreds', 'tens', 'units', 'sum', 'span']:
                if name in predictions and 'confidence' in predictions[name]:
                    confidences.append(predictions[name]['confidence'])

            avg_confidence = np.mean(confidences) if confidences else 0.5

            return {
                'hundreds': hundreds,
                'tens': tens,
                'units': units,
                'sum_value': sum_value,
                'span_value': span_value,
                'number': f"{hundreds}{tens}{units}",
                'combined_probability': avg_confidence,
                'confidence_level': self._get_confidence_level(avg_confidence),
                'fusion_method': 'unified_predictor_interface',
                'ranking_strategy': 'ensemble_average'
            }

        except Exception as e:
            self.logger.error(f"生成融合预测失败: {e}")
            return {
                'hundreds': 5, 'tens': 5, 'units': 5,
                'sum_value': 15, 'span_value': 0,
                'number': '555', 'combined_probability': 0.5,
                'confidence_level': '中', 'fusion_method': 'fallback',
                'ranking_strategy': 'default'
            }

    def _save_to_final_predictions(self, issue: str, fusion_result: Dict, predictions: Dict) -> bool:
        """保存预测结果到final_predictions表"""
        try:
            import sqlite3
            from datetime import datetime

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 清除该期号的旧预测数据
            cursor.execute("DELETE FROM final_predictions WHERE issue = ?", (issue,))

            # 生成多个预测组合（排名1-3）
            base_result = fusion_result.copy()

            for rank in range(1, 4):
                # 为不同排名生成轻微变化的预测
                hundreds = base_result['hundreds']
                tens = base_result['tens']
                units = base_result['units']

                # 为排名2和3添加轻微变化
                if rank == 2:
                    units = (units + 1) % 10
                elif rank == 3:
                    tens = (tens + 1) % 10

                # 重新计算和值和跨度
                sum_value = hundreds + tens + units
                span_value = max(hundreds, tens, units) - min(hundreds, tens, units)

                # 调整置信度
                confidence = base_result['combined_probability'] * (1.0 - (rank - 1) * 0.05)

                # 插入数据
                cursor.execute("""
                    INSERT INTO final_predictions (
                        issue, prediction_rank, hundreds, tens, units,
                        sum_value, span_value, combined_probability,
                        hundreds_prob, tens_prob, units_prob,
                        sum_prob, span_prob, sum_consistency, span_consistency,
                        constraint_score, diversity_score, confidence_level,
                        fusion_method, ranking_strategy, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    issue, rank, hundreds, tens, units,
                    sum_value, span_value, confidence,
                    confidence, confidence, confidence,  # 各位概率
                    confidence, confidence, confidence, confidence,  # 和值跨度概率和一致性
                    confidence * 100, confidence * 80,  # 约束分数和多样性分数
                    self._get_confidence_level(confidence),
                    base_result['fusion_method'], base_result['ranking_strategy'],
                    datetime.now().isoformat()
                ))

            conn.commit()
            conn.close()

            self.logger.info(f"预测结果已保存到final_predictions表: {issue}")
            return True

        except Exception as e:
            self.logger.error(f"保存预测结果失败: {e}")
            return False

    def _get_confidence_level(self, confidence: float) -> str:
        """根据置信度数值返回置信度等级"""
        if confidence >= 0.8:
            return '高'
        elif confidence >= 0.6:
            return '中'
        else:
            return '低'

    def load_models_by_issue(self, issue: str, positions: Optional[List[str]] = None) -> Dict[str, bool]:
        """
        按期号加载模型

        Args:
            issue: 期号 (如: "2025217")
            positions: 要加载的位置列表，None则加载所有位置

        Returns:
            加载结果字典 {position: success}
        """
        if positions is None:
            positions = ['hundreds', 'tens', 'units']

        results = {}

        for position in positions:
            try:
                # 确保预测器已初始化
                if position not in self.predictors or self.predictors[position] is None:
                    self._load_predictor(position)

                predictor = self.predictors[position]
                if predictor is None:
                    results[position] = False
                    self.logger.error(f"无法初始化{position}预测器")
                    continue

                # 检查预测器是否有按期号加载的方法
                if not hasattr(predictor, 'models'):
                    results[position] = False
                    self.logger.error(f"{position}预测器不支持模型管理")
                    continue

                # 按期号加载所有模型
                success_count = 0
                total_models = 0

                for model_name, model in predictor.models.items():
                    total_models += 1
                    try:
                        if hasattr(model, 'load_model_by_issue'):
                            if model.load_model_by_issue(issue):
                                success_count += 1
                                self.logger.info(f"成功加载{position}-{model_name}模型 (期号: {issue})")
                            else:
                                self.logger.warning(f"加载{position}-{model_name}模型失败 (期号: {issue})")
                        else:
                            self.logger.warning(f"{position}-{model_name}模型不支持按期号加载")
                    except Exception as e:
                        self.logger.error(f"加载{position}-{model_name}模型异常: {e}")

                # 如果至少有一个模型加载成功，则认为该位置加载成功
                results[position] = success_count > 0

                if results[position]:
                    self.logger.info(f"{position}位置模型加载完成: {success_count}/{total_models} 成功")
                else:
                    self.logger.error(f"{position}位置模型加载失败: 0/{total_models} 成功")

            except Exception as e:
                results[position] = False
                self.logger.error(f"加载{position}位置模型异常: {e}")

        return results

    def get_available_model_issues(self) -> Dict[str, List[Dict]]:
        """
        获取所有可用的模型期号

        Returns:
            可用模型期号字典 {position: [issue_info_list]}
        """
        try:
            from src.utils.dynamic_issue_manager import issue_manager

            available_issues = {}
            positions = ['hundreds', 'tens', 'units']

            for position in positions:
                try:
                    issues = issue_manager.get_available_model_issues(position)
                    available_issues[position] = issues
                    self.logger.info(f"{position}位置找到 {len(issues)} 个可用模型期号")
                except Exception as e:
                    self.logger.error(f"获取{position}位置可用期号失败: {e}")
                    available_issues[position] = []

            return available_issues

        except ImportError:
            self.logger.error("无法导入动态期号管理器")
            return {}
        except Exception as e:
            self.logger.error(f"获取可用模型期号失败: {e}")
            return {}

    def check_models_exist_for_issue(self, issue: str) -> Dict[str, bool]:
        """
        检查指定期号的模型是否存在

        Args:
            issue: 期号

        Returns:
            检查结果字典 {position: exists}
        """
        try:
            from src.utils.dynamic_issue_manager import issue_manager

            results = {}
            positions = ['hundreds', 'tens', 'units']

            for position in positions:
                try:
                    exists = issue_manager.check_models_complete_for_issue(issue, position)
                    results[position] = exists
                except Exception as e:
                    self.logger.error(f"检查{position}位置期号{issue}模型失败: {e}")
                    results[position] = False

            return results

        except ImportError:
            self.logger.error("无法导入动态期号管理器")
            return {pos: False for pos in ['hundreds', 'tens', 'units']}
        except Exception as e:
            self.logger.error(f"检查模型存在性失败: {e}")
            return {pos: False for pos in ['hundreds', 'tens', 'units']}

    def get_current_loaded_issue(self) -> Optional[str]:
        """
        获取当前加载的模型期号

        Returns:
            当前加载的期号，如果无法确定则返回None
        """
        # 这是一个简化实现，实际可能需要在模型中记录加载的期号
        try:
            from src.utils.dynamic_issue_manager import issue_manager
            return issue_manager.get_latest_issue()
        except:
            return None
