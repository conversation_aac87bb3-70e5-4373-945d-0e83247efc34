#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练任务管理器
负责训练任务的队列管理、状态跟踪和调度
"""

import uuid
import time
import json
import threading
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
from dataclasses import dataclass, asdict
from pathlib import Path
import logging

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待中
    RUNNING = "running"      # 运行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 失败
    CANCELLED = "cancelled"  # 已取消

class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4

@dataclass
class TrainingTask:
    """训练任务数据类"""
    task_id: str
    position: str  # hundreds/tens/units
    model_type: str  # xgb/lgb/lstm/ensemble/all
    issue: Optional[str] = None
    train_until: Optional[str] = None
    priority: TaskPriority = TaskPriority.NORMAL
    status: TaskStatus = TaskStatus.PENDING
    created_at: str = None
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    progress: float = 0.0
    message: str = ""
    result: Optional[Dict] = None
    error: Optional[str] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now().isoformat()
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        data = asdict(self)
        data['priority'] = self.priority.value
        data['status'] = self.status.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'TrainingTask':
        """从字典创建任务"""
        data['priority'] = TaskPriority(data['priority'])
        data['status'] = TaskStatus(data['status'])
        return cls(**data)

class TaskManager:
    """训练任务管理器"""
    
    def __init__(self, max_concurrent_tasks: int = 1):
        """
        初始化任务管理器
        
        Args:
            max_concurrent_tasks: 最大并发任务数
        """
        self.max_concurrent_tasks = max_concurrent_tasks
        self.tasks: Dict[str, TrainingTask] = {}
        self.task_queue: List[str] = []  # 任务ID队列
        self.running_tasks: List[str] = []  # 运行中的任务ID
        self.lock = threading.RLock()
        
        # 设置日志
        self.logger = logging.getLogger('TaskManager')
        self.logger.setLevel(logging.INFO)
        
        # 持久化文件
        self.data_dir = Path("data/training")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.tasks_file = self.data_dir / "tasks.json"
        
        # 加载已有任务
        self._load_tasks()
        
    def create_task(self, position: str, model_type: str, issue: str = None,
                   train_until: str = None, priority: TaskPriority = TaskPriority.NORMAL) -> str:
        """
        创建新的训练任务
        
        Args:
            position: 位置 (hundreds/tens/units)
            model_type: 模型类型 (xgb/lgb/lstm/ensemble/all)
            issue: 训练期号
            train_until: 训练数据截止期号
            priority: 任务优先级
            
        Returns:
            任务ID
        """
        with self.lock:
            task_id = str(uuid.uuid4())
            
            task = TrainingTask(
                task_id=task_id,
                position=position,
                model_type=model_type,
                issue=issue,
                train_until=train_until,
                priority=priority
            )
            
            self.tasks[task_id] = task
            self._insert_task_by_priority(task_id)
            
            self.logger.info(f"📝 创建任务: {task_id} - {position} {model_type}")
            self._save_tasks()
            
            return task_id
    
    def _insert_task_by_priority(self, task_id: str):
        """按优先级插入任务到队列"""
        task = self.tasks[task_id]
        
        # 找到合适的插入位置
        insert_index = len(self.task_queue)
        for i, existing_task_id in enumerate(self.task_queue):
            existing_task = self.tasks[existing_task_id]
            if task.priority.value > existing_task.priority.value:
                insert_index = i
                break
                
        self.task_queue.insert(insert_index, task_id)
    
    def get_task(self, task_id: str) -> Optional[TrainingTask]:
        """获取任务信息"""
        with self.lock:
            return self.tasks.get(task_id)
    
    def get_all_tasks(self) -> List[TrainingTask]:
        """获取所有任务"""
        with self.lock:
            return list(self.tasks.values())
    
    def get_pending_tasks(self) -> List[TrainingTask]:
        """获取等待中的任务"""
        with self.lock:
            return [self.tasks[task_id] for task_id in self.task_queue 
                   if self.tasks[task_id].status == TaskStatus.PENDING]
    
    def get_running_tasks(self) -> List[TrainingTask]:
        """获取运行中的任务"""
        with self.lock:
            return [self.tasks[task_id] for task_id in self.running_tasks]
    
    def start_task(self, task_id: str) -> bool:
        """
        启动任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功启动
        """
        with self.lock:
            if task_id not in self.tasks:
                return False
                
            task = self.tasks[task_id]
            
            if task.status != TaskStatus.PENDING:
                return False
                
            if len(self.running_tasks) >= self.max_concurrent_tasks:
                return False
                
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now().isoformat()
            task.message = "任务已启动"
            
            # 从队列移除，添加到运行列表
            if task_id in self.task_queue:
                self.task_queue.remove(task_id)
            self.running_tasks.append(task_id)
            
            self.logger.info(f"🚀 启动任务: {task_id}")
            self._save_tasks()
            
            return True
    
    def update_task_progress(self, task_id: str, progress: float, message: str = ""):
        """更新任务进度"""
        with self.lock:
            if task_id in self.tasks:
                self.tasks[task_id].progress = progress
                if message:
                    self.tasks[task_id].message = message
                self._save_tasks()
    
    def complete_task(self, task_id: str, result: Dict = None):
        """完成任务"""
        with self.lock:
            if task_id not in self.tasks:
                return
                
            task = self.tasks[task_id]
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now().isoformat()
            task.progress = 100.0
            task.message = "任务已完成"
            task.result = result
            
            # 从运行列表移除
            if task_id in self.running_tasks:
                self.running_tasks.remove(task_id)
                
            self.logger.info(f"✅ 完成任务: {task_id}")
            self._save_tasks()
    
    def fail_task(self, task_id: str, error: str):
        """任务失败"""
        with self.lock:
            if task_id not in self.tasks:
                return
                
            task = self.tasks[task_id]
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.now().isoformat()
            task.message = "任务失败"
            task.error = error
            
            # 从运行列表移除
            if task_id in self.running_tasks:
                self.running_tasks.remove(task_id)
                
            self.logger.error(f"❌ 任务失败: {task_id} - {error}")
            self._save_tasks()
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self.lock:
            if task_id not in self.tasks:
                return False
                
            task = self.tasks[task_id]
            
            if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                return False
                
            task.status = TaskStatus.CANCELLED
            task.completed_at = datetime.now().isoformat()
            task.message = "任务已取消"
            
            # 从队列和运行列表移除
            if task_id in self.task_queue:
                self.task_queue.remove(task_id)
            if task_id in self.running_tasks:
                self.running_tasks.remove(task_id)
                
            self.logger.info(f"⏹️ 取消任务: {task_id}")
            self._save_tasks()
            
            return True
    
    def get_next_task(self) -> Optional[str]:
        """获取下一个待执行的任务"""
        with self.lock:
            if len(self.running_tasks) >= self.max_concurrent_tasks:
                return None
                
            for task_id in self.task_queue:
                task = self.tasks[task_id]
                if task.status == TaskStatus.PENDING:
                    return task_id
                    
            return None
    
    def get_queue_status(self) -> Dict:
        """获取队列状态"""
        with self.lock:
            pending_count = len([t for t in self.tasks.values() if t.status == TaskStatus.PENDING])
            running_count = len(self.running_tasks)
            completed_count = len([t for t in self.tasks.values() if t.status == TaskStatus.COMPLETED])
            failed_count = len([t for t in self.tasks.values() if t.status == TaskStatus.FAILED])
            
            return {
                'total_tasks': len(self.tasks),
                'pending': pending_count,
                'running': running_count,
                'completed': completed_count,
                'failed': failed_count,
                'queue_length': len(self.task_queue),
                'max_concurrent': self.max_concurrent_tasks
            }
    
    def _save_tasks(self):
        """保存任务到文件"""
        try:
            tasks_data = {
                'tasks': {task_id: task.to_dict() for task_id, task in self.tasks.items()},
                'task_queue': self.task_queue,
                'running_tasks': self.running_tasks
            }
            
            with open(self.tasks_file, 'w', encoding='utf-8') as f:
                json.dump(tasks_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            self.logger.error(f"保存任务失败: {e}")
    
    def _load_tasks(self):
        """从文件加载任务"""
        try:
            if not self.tasks_file.exists():
                return
                
            with open(self.tasks_file, 'r', encoding='utf-8') as f:
                tasks_data = json.load(f)
                
            # 恢复任务
            for task_id, task_data in tasks_data.get('tasks', {}).items():
                self.tasks[task_id] = TrainingTask.from_dict(task_data)
                
            # 恢复队列（过滤掉已完成的任务）
            self.task_queue = [task_id for task_id in tasks_data.get('task_queue', [])
                              if task_id in self.tasks and 
                              self.tasks[task_id].status == TaskStatus.PENDING]
            
            # 恢复运行中的任务（重置为等待状态）
            for task_id in tasks_data.get('running_tasks', []):
                if task_id in self.tasks and self.tasks[task_id].status == TaskStatus.RUNNING:
                    self.tasks[task_id].status = TaskStatus.PENDING
                    if task_id not in self.task_queue:
                        self.task_queue.append(task_id)
                        
            self.running_tasks = []
            
            self.logger.info(f"📂 加载任务: {len(self.tasks)} 个")
            
        except Exception as e:
            self.logger.error(f"加载任务失败: {e}")

# 全局任务管理器实例
task_manager = TaskManager()

if __name__ == "__main__":
    # 测试代码
    import time

    # 创建测试任务
    task_id = task_manager.create_task("hundreds", "xgb", "2025217")
    print(f"创建任务: {task_id}")

    # 获取任务信息
    task = task_manager.get_task(task_id)
    print(f"任务信息: {task.to_dict()}")

    # 获取队列状态
    status = task_manager.get_queue_status()
    print(f"队列状态: {status}")
