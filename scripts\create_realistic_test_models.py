#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建符合实际格式的测试模型文件
"""

import sys
import pickle
import numpy as np
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def create_realistic_xgb_model(position, issue):
    """创建符合XGBoost模型格式的测试文件"""
    # 模拟XGBoost模型数据结构
    model_data = {
        'xgb_model': 'mock_xgb_model_object',  # 实际应该是XGBoost模型对象
        'label_encoder': 'mock_label_encoder',  # 必需的label_encoder
        'feature_names': [f'feature_{i}' for i in range(10)],
        'feature_importance_': np.random.random(10).tolist(),
        'training_metrics': {
            'accuracy': 0.85,
            'precision': 0.82,
            'recall': 0.88
        },
        'xgb_config': {
            'n_estimators': 100,
            'max_depth': 6,
            'learning_rate': 0.1
        },
        'created_at': datetime.now().isoformat(),
        'issue': issue,
        'position': position
    }
    return model_data

def create_realistic_lgb_model(position, issue):
    """创建符合LightGBM模型格式的测试文件"""
    model_data = {
        'lgb_model': 'mock_lgb_model_object',  # 实际应该是LightGBM模型对象
        'label_encoder': 'mock_label_encoder',  # 必需的label_encoder
        'feature_names': [f'feature_{i}' for i in range(10)],
        'feature_importance_': np.random.random(10).tolist(),
        'training_metrics': {
            'accuracy': 0.83,
            'precision': 0.80,
            'recall': 0.86
        },
        'lgb_config': {
            'n_estimators': 100,
            'max_depth': 6,
            'learning_rate': 0.1
        },
        'created_at': datetime.now().isoformat(),
        'issue': issue,
        'position': position
    }
    return model_data

def create_realistic_lstm_components(position, issue):
    """创建符合LSTM组件格式的测试文件"""
    components_data = {
        'scaler': 'mock_scaler_object',  # 实际应该是StandardScaler对象
        'label_encoder': 'mock_label_encoder',  # 必需的label_encoder
        'sequence_length': 10,
        'feature_names': [f'feature_{i}' for i in range(10)],
        'training_metrics': {
            'accuracy': 0.81,
            'loss': 0.45
        },
        'model_config': {
            'units': 50,
            'dropout': 0.2,
            'epochs': 100
        },
        'created_at': datetime.now().isoformat(),
        'issue': issue,
        'position': position
    }
    return components_data

def create_realistic_ensemble_model(position, issue):
    """创建符合集成模型格式的测试文件"""
    model_data = {
        'model_weights': {  # 必需的model_weights
            'xgb': 0.3,
            'lgb': 0.3,
            'lstm': 0.4
        },
        'ensemble_config': {
            'voting_method': 'weighted',
            'models': ['xgb', 'lgb', 'lstm']
        },
        'base_models': {
            'xgb': 'mock_xgb_model',
            'lgb': 'mock_lgb_model',
            'lstm': 'mock_lstm_model'
        },
        'training_metrics': {
            'accuracy': 0.87,
            'precision': 0.85,
            'recall': 0.89
        },
        'created_at': datetime.now().isoformat(),
        'issue': issue,
        'position': position
    }
    return model_data

def create_mock_h5_file(filepath):
    """创建模拟的.h5文件"""
    # 由于我们无法创建真正的HDF5文件，创建一个简单的二进制文件
    # 实际应用中，这应该是Keras模型的.h5文件
    mock_h5_content = b'\x89HDF\r\n\x1a\n'  # HDF5文件头
    mock_h5_content += b'MOCK_KERAS_MODEL_DATA' * 100  # 模拟数据
    
    with open(filepath, 'wb') as f:
        f.write(mock_h5_content)

def create_realistic_test_models():
    """创建符合实际格式的测试模型文件"""
    print("🔧 创建符合实际格式的测试模型文件...")
    
    # 创建模型目录
    positions = ['hundreds', 'tens', 'units']
    for position in positions:
        model_dir = Path(f'models/{position}')
        model_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建测试期号的模型
    test_issues = ['2025215', '2025216', '2025217']
    
    for issue in test_issues:
        for position in positions:
            model_dir = Path(f'models/{position}')
            
            # 创建XGBoost模型文件
            xgb_file = model_dir / f"xgb_{position}_model_{issue}.pkl"
            xgb_data = create_realistic_xgb_model(position, issue)
            with open(xgb_file, 'wb') as f:
                pickle.dump(xgb_data, f)
            print(f"   ✅ 创建: {xgb_file}")
            
            # 创建LightGBM模型文件
            lgb_file = model_dir / f"lgb_{position}_model_{issue}.pkl"
            lgb_data = create_realistic_lgb_model(position, issue)
            with open(lgb_file, 'wb') as f:
                pickle.dump(lgb_data, f)
            print(f"   ✅ 创建: {lgb_file}")
            
            # 创建LSTM模型文件
            lstm_h5_file = model_dir / f"lstm_{position}_model_{issue}.h5"
            lstm_pkl_file = model_dir / f"lstm_{position}_model_{issue}_components.pkl"
            
            # 创建模拟的.h5文件
            create_mock_h5_file(lstm_h5_file)
            print(f"   ✅ 创建: {lstm_h5_file}")
            
            # 创建LSTM组件文件
            lstm_components = create_realistic_lstm_components(position, issue)
            with open(lstm_pkl_file, 'wb') as f:
                pickle.dump(lstm_components, f)
            print(f"   ✅ 创建: {lstm_pkl_file}")
            
            # 创建集成模型文件
            ensemble_file = model_dir / f"ensemble_{position}_model_{issue}_ensemble.pkl"
            ensemble_data = create_realistic_ensemble_model(position, issue)
            with open(ensemble_file, 'wb') as f:
                pickle.dump(ensemble_data, f)
            print(f"   ✅ 创建: {ensemble_file}")
    
    print(f"\n🎉 成功创建符合实际格式的测试模型文件")
    print(f"   期号: {', '.join(test_issues)}")
    print(f"   位置: {', '.join(positions)}")
    print(f"   每个期号包含: XGBoost, LightGBM, LSTM, 集成模型")
    print(f"   所有模型文件都包含必要的组件和数据结构")

def main():
    """主函数"""
    print("🔧 创建符合实际格式的测试模型文件")
    print("=" * 50)
    
    # 删除旧的测试文件
    print("🗑️ 清理旧的测试文件...")
    for position in ['hundreds', 'tens', 'units']:
        model_dir = Path(f'models/{position}')
        if model_dir.exists():
            for file in model_dir.glob('*_model_202521*.pkl'):
                file.unlink()
                print(f"   🗑️ 删除: {file}")
            for file in model_dir.glob('*_model_202521*.h5'):
                file.unlink()
                print(f"   🗑️ 删除: {file}")
    
    # 创建新的符合格式的测试模型
    create_realistic_test_models()
    
    print("\n🎉 符合实际格式的测试模型文件创建完成！")
    print("\n📋 现在可以测试以下功能:")
    print("   ✅ 模型加载应该不会出现格式错误")
    print("   ✅ API端点应该正常工作")
    print("   ✅ 前端期号选择器应该能正常预测")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
