2025-08-07 02:18:41 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - load_all_predictors - 96 - 加载预测器 sum 失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-07 02:18:41 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 139 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-07 02:18:41 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - get_all_predictions - 171 - hundreds预测器预测失败: 'HundredsPredictor' object has no attribute 'predict_next_period'
2025-08-07 02:18:41 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - get_all_predictions - 171 - tens预测器预测失败: 'TensPredictor' object has no attribute 'predict_next_period'
2025-08-07 02:18:41 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - get_all_predictions - 171 - units预测器预测失败: 'UnitsPredictor' object has no attribute 'predict_next_period'
2025-08-07 02:18:41 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - get_all_predictions - 171 - span预测器预测失败: span位预测器尚未训练
2025-08-07 02:18:41 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - get_all_predictions - 171 - hundreds预测器预测失败: 'HundredsPredictor' object has no attribute 'predict_next_period'
2025-08-07 02:18:41 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - get_all_predictions - 171 - tens预测器预测失败: 'TensPredictor' object has no attribute 'predict_next_period'
2025-08-07 02:18:41 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - get_all_predictions - 171 - units预测器预测失败: 'UnitsPredictor' object has no attribute 'predict_next_period'
2025-08-07 02:18:41 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - get_all_predictions - 171 - span预测器预测失败: span位预测器尚未训练
2025-08-07 14:25:14 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 139 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:22:36 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器sum失败: SumPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:22:36 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器span失败: SpanPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:22:36 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_fusion_predictor - 137 - 加载融合预测器失败: FusionPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:22:36 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_weight_adjuster - 150 - 加载权重调整器失败: DynamicWeightAdjuster.__init__() missing 2 required positional arguments: 'db_path' and 'config'
2025-08-14 22:22:37 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器sum失败: SumPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:22:37 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器span失败: SpanPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:22:37 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_fusion_predictor - 137 - 加载融合预测器失败: FusionPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:22:37 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_weight_adjuster - 150 - 加载权重调整器失败: DynamicWeightAdjuster.__init__() missing 2 required positional arguments: 'db_path' and 'config'
2025-08-14 22:22:37 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器sum失败: SumPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:22:37 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器span失败: SpanPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:22:37 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_fusion_predictor - 137 - 加载融合预测器失败: FusionPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:22:37 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_weight_adjuster - 150 - 加载权重调整器失败: DynamicWeightAdjuster.__init__() missing 2 required positional arguments: 'db_path' and 'config'
2025-08-14 22:22:39 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:22:40 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:22:43 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:22:43 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:22:43.236784"}
2025-08-14 22:22:43 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:22:43.237783"}
2025-08-14 22:22:43 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:22:43 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:22:43 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:22:43 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:22:43 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:22:43 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:22:43 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:22:43 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:22:43 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:22:43 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:22:45 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:22:57 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器sum失败: SumPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:22:57 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器span失败: SpanPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:22:57 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_fusion_predictor - 137 - 加载融合预测器失败: FusionPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:22:57 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_weight_adjuster - 150 - 加载权重调整器失败: DynamicWeightAdjuster.__init__() missing 2 required positional arguments: 'db_path' and 'config'
2025-08-14 22:22:58 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器sum失败: SumPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:22:58 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器span失败: SpanPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:22:58 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_fusion_predictor - 137 - 加载融合预测器失败: FusionPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:22:58 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_weight_adjuster - 150 - 加载权重调整器失败: DynamicWeightAdjuster.__init__() missing 2 required positional arguments: 'db_path' and 'config'
2025-08-14 22:22:58 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器sum失败: SumPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:22:58 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器span失败: SpanPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:22:58 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_fusion_predictor - 137 - 加载融合预测器失败: FusionPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:22:58 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_weight_adjuster - 150 - 加载权重调整器失败: DynamicWeightAdjuster.__init__() missing 2 required positional arguments: 'db_path' and 'config'
2025-08-14 22:22:59 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:23:01 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:23:04 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:23:04 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:23:04.085433"}
2025-08-14 22:23:04 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:23:04.086432"}
2025-08-14 22:23:04 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:23:04 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:23:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:23:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:23:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:23:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:23:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:23:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:23:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:23:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:23:06 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:23:09 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:23:09 - src.core.execution_verifier - ERROR - execution_verifier - _verify_prediction_review - 211 - ❌ 预测复盘验证失败: no such table: prediction_reviews
2025-08-14 22:23:12 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:23:17 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:23:17 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 22:23:20 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:23:23 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:23:26 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:23:29 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:24:13 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器sum失败: SumPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:24:13 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器span失败: SpanPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:24:13 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_fusion_predictor - 137 - 加载融合预测器失败: FusionPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:24:13 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_weight_adjuster - 150 - 加载权重调整器失败: DynamicWeightAdjuster.__init__() missing 2 required positional arguments: 'db_path' and 'config'
2025-08-14 22:24:13 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器sum失败: SumPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:24:13 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器span失败: SpanPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:24:13 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_fusion_predictor - 137 - 加载融合预测器失败: FusionPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:24:13 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_weight_adjuster - 150 - 加载权重调整器失败: DynamicWeightAdjuster.__init__() missing 2 required positional arguments: 'db_path' and 'config'
2025-08-14 22:24:13 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器sum失败: SumPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:24:13 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器span失败: SpanPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:24:13 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_fusion_predictor - 137 - 加载融合预测器失败: FusionPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:24:13 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_weight_adjuster - 150 - 加载权重调整器失败: DynamicWeightAdjuster.__init__() missing 2 required positional arguments: 'db_path' and 'config'
2025-08-14 22:24:14 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器sum失败: SumPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:24:14 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器span失败: SpanPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:24:14 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_fusion_predictor - 137 - 加载融合预测器失败: FusionPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:24:14 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_weight_adjuster - 150 - 加载权重调整器失败: DynamicWeightAdjuster.__init__() missing 2 required positional arguments: 'db_path' and 'config'
2025-08-14 22:24:14 - HundredsPredictor - ERROR - base_independent_predictor - load_training_data - 263 - 加载训练数据失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - HundredsPredictor - ERROR - hundreds_predictor - train_model - 148 - ensemble 模型训练失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - train_all_predictors - 196 - 训练预测器hundreds失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - TensPredictor - ERROR - base_independent_predictor - load_training_data - 263 - 加载训练数据失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - TensPredictor - ERROR - tens_predictor - train_model - 148 - ensemble 模型训练失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - train_all_predictors - 196 - 训练预测器tens失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - UnitsPredictor - ERROR - base_independent_predictor - load_training_data - 263 - 加载训练数据失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - UnitsPredictor - ERROR - units_predictor - train_model - 148 - ensemble 模型训练失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - train_all_predictors - 196 - 训练预测器units失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - HundredsPredictor - ERROR - hundreds_predictor - predict - 215 - 预测失败: ensemble 模型尚未训练
2025-08-14 22:24:14 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 264 - 预测器hundreds预测失败: ensemble 模型尚未训练
2025-08-14 22:24:14 - TensPredictor - ERROR - tens_predictor - predict - 215 - 预测失败: ensemble 模型尚未训练
2025-08-14 22:24:14 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 264 - 预测器tens预测失败: ensemble 模型尚未训练
2025-08-14 22:24:14 - UnitsPredictor - ERROR - units_predictor - predict - 215 - 预测失败: ensemble 模型尚未训练
2025-08-14 22:24:14 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 264 - 预测器units预测失败: ensemble 模型尚未训练
2025-08-14 22:24:14 - HundredsPredictor - ERROR - base_independent_predictor - load_training_data - 263 - 加载训练数据失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - HundredsPredictor - ERROR - hundreds_predictor - train_model - 148 - ensemble 模型训练失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - train_all_predictors - 196 - 训练预测器hundreds失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - TensPredictor - ERROR - base_independent_predictor - load_training_data - 263 - 加载训练数据失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - TensPredictor - ERROR - tens_predictor - train_model - 148 - ensemble 模型训练失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - train_all_predictors - 196 - 训练预测器tens失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - UnitsPredictor - ERROR - base_independent_predictor - load_training_data - 263 - 加载训练数据失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - UnitsPredictor - ERROR - units_predictor - train_model - 148 - ensemble 模型训练失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - train_all_predictors - 196 - 训练预测器units失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - HundredsPredictor - ERROR - hundreds_predictor - predict - 215 - 预测失败: ensemble 模型尚未训练
2025-08-14 22:24:14 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 264 - 预测器hundreds预测失败: ensemble 模型尚未训练
2025-08-14 22:24:14 - TensPredictor - ERROR - tens_predictor - predict - 215 - 预测失败: ensemble 模型尚未训练
2025-08-14 22:24:14 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 264 - 预测器tens预测失败: ensemble 模型尚未训练
2025-08-14 22:24:14 - UnitsPredictor - ERROR - units_predictor - predict - 215 - 预测失败: ensemble 模型尚未训练
2025-08-14 22:24:14 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 264 - 预测器units预测失败: ensemble 模型尚未训练
2025-08-14 22:24:14 - HundredsPredictor - ERROR - base_independent_predictor - load_training_data - 263 - 加载训练数据失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - HundredsPredictor - ERROR - hundreds_predictor - train_model - 148 - ensemble 模型训练失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - train_all_predictors - 196 - 训练预测器hundreds失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - TensPredictor - ERROR - base_independent_predictor - load_training_data - 263 - 加载训练数据失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - TensPredictor - ERROR - tens_predictor - train_model - 148 - ensemble 模型训练失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - train_all_predictors - 196 - 训练预测器tens失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - UnitsPredictor - ERROR - base_independent_predictor - load_training_data - 263 - 加载训练数据失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - UnitsPredictor - ERROR - units_predictor - train_model - 148 - ensemble 模型训练失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - train_all_predictors - 196 - 训练预测器units失败: 'PredictorFeatureInterface' object has no attribute 'get_feature_names'
2025-08-14 22:24:14 - HundredsPredictor - ERROR - hundreds_predictor - predict - 215 - 预测失败: ensemble 模型尚未训练
2025-08-14 22:24:14 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 264 - 预测器hundreds预测失败: ensemble 模型尚未训练
2025-08-14 22:24:14 - TensPredictor - ERROR - tens_predictor - predict - 215 - 预测失败: ensemble 模型尚未训练
2025-08-14 22:24:14 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 264 - 预测器tens预测失败: ensemble 模型尚未训练
2025-08-14 22:24:14 - UnitsPredictor - ERROR - units_predictor - predict - 215 - 预测失败: ensemble 模型尚未训练
2025-08-14 22:24:14 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 264 - 预测器units预测失败: ensemble 模型尚未训练
2025-08-14 22:26:41 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器sum失败: SumPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:26:41 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器span失败: SpanPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:26:41 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_fusion_predictor - 137 - 加载融合预测器失败: FusionPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:26:41 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_weight_adjuster - 150 - 加载权重调整器失败: DynamicWeightAdjuster.__init__() missing 2 required positional arguments: 'db_path' and 'config'
2025-08-14 22:26:41 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器sum失败: SumPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:26:41 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器span失败: SpanPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:26:41 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_fusion_predictor - 137 - 加载融合预测器失败: FusionPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:26:41 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_weight_adjuster - 150 - 加载权重调整器失败: DynamicWeightAdjuster.__init__() missing 2 required positional arguments: 'db_path' and 'config'
2025-08-14 22:26:42 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器sum失败: SumPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:26:42 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 124 - 加载预测器span失败: SpanPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:26:42 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_fusion_predictor - 137 - 加载融合预测器失败: FusionPredictor.__init__() missing 1 required positional argument: 'db_path'
2025-08-14 22:26:42 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_weight_adjuster - 150 - 加载权重调整器失败: DynamicWeightAdjuster.__init__() missing 2 required positional arguments: 'db_path' and 'config'
2025-08-14 22:26:43 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:26:45 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:26:48 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:26:48 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:26:48.139778"}
2025-08-14 22:26:48 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:26:48.144777"}
2025-08-14 22:26:48 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:26:48 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:26:48 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:26:48 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:26:48 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:26:48 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:26:48 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:26:48 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:26:48 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:26:48 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:26:50 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:26:53 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:26:53 - src.core.execution_verifier - ERROR - execution_verifier - _verify_prediction_review - 211 - ❌ 预测复盘验证失败: no such table: prediction_reviews
2025-08-14 22:26:56 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:27:01 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:27:01 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 22:27:04 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:27:07 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:27:10 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:27:13 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:29:48 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:29:48 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:29:48 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:29:48 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:29:48 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:29:48 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:29:51 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:29:52 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:29:52 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:29:54 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:29:54.266461"}
2025-08-14 22:29:54 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:29:54.267461"}
2025-08-14 22:29:54 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:29:54 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:29:54 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:29:54 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:29:54 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:29:54 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:29:54 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:29:54 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:29:54 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:29:54 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:29:56 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:29:59 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:29:59 - src.core.execution_verifier - ERROR - execution_verifier - _verify_prediction_review - 211 - ❌ 预测复盘验证失败: no such table: prediction_reviews
2025-08-14 22:30:02 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:30:07 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:30:08 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 22:30:11 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:30:14 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:30:17 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:30:20 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:30:40 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:30:40 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:30:40 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:30:40 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:30:40 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:30:40 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:30:44 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:30:57 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:30:57 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:30:57 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:30:57 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:30:57 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:30:57 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:31:00 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:31:01 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:31:03 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:31:03 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:31:03.347237"}
2025-08-14 22:31:03 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:31:03.356237"}
2025-08-14 22:31:03 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:31:03 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:31:03 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:31:03 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:31:03 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:31:03 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:31:03 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:31:03 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:31:03 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:31:03 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:31:05 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:31:08 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:31:08 - src.core.execution_verifier - ERROR - execution_verifier - _verify_prediction_review - 211 - ❌ 预测复盘验证失败: no such table: prediction_reviews
2025-08-14 22:31:10 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:31:10 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:31:10 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:31:10 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:31:11 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:31:11 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:31:11 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:31:11 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:31:12 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:31:15 - HundredsPredictor - ERROR - xgb_hundreds_model - train - 194 - XGBoost训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:15 - HundredsPredictor - ERROR - ensemble_hundreds_model - train - 178 - 基础模型 xgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:15 - HundredsPredictor - ERROR - lgb_hundreds_model - train - 197 - LightGBM训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:15 - HundredsPredictor - ERROR - ensemble_hundreds_model - train - 178 - 基础模型 lgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:15 - HundredsPredictor - ERROR - lstm_hundreds_model - train - 235 - LSTM训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:31:15 - HundredsPredictor - ERROR - ensemble_hundreds_model - train - 178 - 基础模型 lstm 训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:31:15 - HundredsPredictor - ERROR - ensemble_hundreds_model - _evaluate_ensemble - 358 - 集成模型评估失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:16 - TensPredictor - ERROR - xgb_tens_model - train - 191 - XGBoost训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:16 - TensPredictor - ERROR - ensemble_tens_model - train - 178 - 基础模型 xgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:16 - TensPredictor - ERROR - lgb_tens_model - train - 197 - LightGBM训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:16 - TensPredictor - ERROR - ensemble_tens_model - train - 178 - 基础模型 lgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:16 - TensPredictor - ERROR - lstm_tens_model - train - 234 - LSTM训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:31:16 - TensPredictor - ERROR - ensemble_tens_model - train - 178 - 基础模型 lstm 训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:31:16 - TensPredictor - ERROR - ensemble_tens_model - _evaluate_ensemble - 358 - 集成模型评估失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:17 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:31:17 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 22:31:18 - UnitsPredictor - ERROR - xgb_units_model - train - 191 - XGBoost训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:18 - UnitsPredictor - ERROR - ensemble_units_model - train - 178 - 基础模型 xgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:18 - UnitsPredictor - ERROR - lgb_units_model - train - 197 - LightGBM训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:18 - UnitsPredictor - ERROR - ensemble_units_model - train - 178 - 基础模型 lgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:18 - UnitsPredictor - ERROR - lstm_units_model - train - 235 - LSTM训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:31:18 - UnitsPredictor - ERROR - ensemble_units_model - train - 178 - 基础模型 lstm 训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:31:18 - UnitsPredictor - ERROR - ensemble_units_model - _evaluate_ensemble - 358 - 集成模型评估失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:18 - HundredsPredictor - ERROR - base_independent_predictor - _get_prediction_features - 367 - 获取预测特征失败: 'PredictorFeatureInterface' object has no attribute 'get_prediction_features'
2025-08-14 22:31:18 - HundredsPredictor - ERROR - ensemble_hundreds_model - predict_probability - 258 - 集成模型预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:18 - HundredsPredictor - ERROR - base_independent_predictor - predict_next_period - 350 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:18 - HundredsPredictor - ERROR - hundreds_predictor - predict - 215 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:18 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器hundreds预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:18 - TensPredictor - ERROR - base_independent_predictor - _get_prediction_features - 367 - 获取预测特征失败: 'PredictorFeatureInterface' object has no attribute 'get_prediction_features'
2025-08-14 22:31:18 - TensPredictor - ERROR - ensemble_tens_model - predict_probability - 258 - 集成模型预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:18 - TensPredictor - ERROR - base_independent_predictor - predict_next_period - 350 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:18 - TensPredictor - ERROR - tens_predictor - predict - 215 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:18 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器tens预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:18 - UnitsPredictor - ERROR - base_independent_predictor - _get_prediction_features - 367 - 获取预测特征失败: 'PredictorFeatureInterface' object has no attribute 'get_prediction_features'
2025-08-14 22:31:18 - UnitsPredictor - ERROR - ensemble_units_model - predict_probability - 258 - 集成模型预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:18 - UnitsPredictor - ERROR - base_independent_predictor - predict_next_period - 350 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:18 - UnitsPredictor - ERROR - units_predictor - predict - 215 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:18 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器units预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:18 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器span预测失败: 模型尚未训练
2025-08-14 22:31:20 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:31:21 - HundredsPredictor - ERROR - xgb_hundreds_model - train - 194 - XGBoost训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:21 - HundredsPredictor - ERROR - ensemble_hundreds_model - train - 178 - 基础模型 xgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:21 - HundredsPredictor - ERROR - lgb_hundreds_model - train - 197 - LightGBM训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:21 - HundredsPredictor - ERROR - ensemble_hundreds_model - train - 178 - 基础模型 lgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:21 - HundredsPredictor - ERROR - lstm_hundreds_model - train - 235 - LSTM训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:31:21 - HundredsPredictor - ERROR - ensemble_hundreds_model - train - 178 - 基础模型 lstm 训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:31:21 - HundredsPredictor - ERROR - ensemble_hundreds_model - _evaluate_ensemble - 358 - 集成模型评估失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:23 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:31:23 - TensPredictor - ERROR - xgb_tens_model - train - 191 - XGBoost训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:23 - TensPredictor - ERROR - ensemble_tens_model - train - 178 - 基础模型 xgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:23 - TensPredictor - ERROR - lgb_tens_model - train - 197 - LightGBM训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:23 - TensPredictor - ERROR - ensemble_tens_model - train - 178 - 基础模型 lgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:23 - TensPredictor - ERROR - lstm_tens_model - train - 234 - LSTM训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:31:23 - TensPredictor - ERROR - ensemble_tens_model - train - 178 - 基础模型 lstm 训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:31:23 - TensPredictor - ERROR - ensemble_tens_model - _evaluate_ensemble - 358 - 集成模型评估失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:24 - UnitsPredictor - ERROR - xgb_units_model - train - 191 - XGBoost训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:24 - UnitsPredictor - ERROR - ensemble_units_model - train - 178 - 基础模型 xgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:24 - UnitsPredictor - ERROR - lgb_units_model - train - 197 - LightGBM训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:24 - UnitsPredictor - ERROR - ensemble_units_model - train - 178 - 基础模型 lgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:24 - UnitsPredictor - ERROR - lstm_units_model - train - 235 - LSTM训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:31:24 - UnitsPredictor - ERROR - ensemble_units_model - train - 178 - 基础模型 lstm 训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:31:24 - UnitsPredictor - ERROR - ensemble_units_model - _evaluate_ensemble - 358 - 集成模型评估失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:24 - HundredsPredictor - ERROR - base_independent_predictor - _get_prediction_features - 367 - 获取预测特征失败: 'PredictorFeatureInterface' object has no attribute 'get_prediction_features'
2025-08-14 22:31:24 - HundredsPredictor - ERROR - ensemble_hundreds_model - predict_probability - 258 - 集成模型预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:24 - HundredsPredictor - ERROR - base_independent_predictor - predict_next_period - 350 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:24 - HundredsPredictor - ERROR - hundreds_predictor - predict - 215 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:24 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器hundreds预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:24 - TensPredictor - ERROR - base_independent_predictor - _get_prediction_features - 367 - 获取预测特征失败: 'PredictorFeatureInterface' object has no attribute 'get_prediction_features'
2025-08-14 22:31:24 - TensPredictor - ERROR - ensemble_tens_model - predict_probability - 258 - 集成模型预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:24 - TensPredictor - ERROR - base_independent_predictor - predict_next_period - 350 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:24 - TensPredictor - ERROR - tens_predictor - predict - 215 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:24 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器tens预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:24 - UnitsPredictor - ERROR - base_independent_predictor - _get_prediction_features - 367 - 获取预测特征失败: 'PredictorFeatureInterface' object has no attribute 'get_prediction_features'
2025-08-14 22:31:24 - UnitsPredictor - ERROR - ensemble_units_model - predict_probability - 258 - 集成模型预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:24 - UnitsPredictor - ERROR - base_independent_predictor - predict_next_period - 350 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:24 - UnitsPredictor - ERROR - units_predictor - predict - 215 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:24 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器units预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:24 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器span预测失败: 模型尚未训练
2025-08-14 22:31:26 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:31:28 - HundredsPredictor - ERROR - xgb_hundreds_model - train - 194 - XGBoost训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:28 - HundredsPredictor - ERROR - ensemble_hundreds_model - train - 178 - 基础模型 xgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:28 - HundredsPredictor - ERROR - lgb_hundreds_model - train - 197 - LightGBM训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:28 - HundredsPredictor - ERROR - ensemble_hundreds_model - train - 178 - 基础模型 lgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:28 - HundredsPredictor - ERROR - lstm_hundreds_model - train - 235 - LSTM训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:31:28 - HundredsPredictor - ERROR - ensemble_hundreds_model - train - 178 - 基础模型 lstm 训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:31:28 - HundredsPredictor - ERROR - ensemble_hundreds_model - _evaluate_ensemble - 358 - 集成模型评估失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:29 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:31:29 - TensPredictor - ERROR - xgb_tens_model - train - 191 - XGBoost训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:29 - TensPredictor - ERROR - ensemble_tens_model - train - 178 - 基础模型 xgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:29 - TensPredictor - ERROR - lgb_tens_model - train - 197 - LightGBM训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:29 - TensPredictor - ERROR - ensemble_tens_model - train - 178 - 基础模型 lgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:29 - TensPredictor - ERROR - lstm_tens_model - train - 234 - LSTM训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:31:29 - TensPredictor - ERROR - ensemble_tens_model - train - 178 - 基础模型 lstm 训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:31:29 - TensPredictor - ERROR - ensemble_tens_model - _evaluate_ensemble - 358 - 集成模型评估失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:30 - UnitsPredictor - ERROR - xgb_units_model - train - 191 - XGBoost训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:30 - UnitsPredictor - ERROR - ensemble_units_model - train - 178 - 基础模型 xgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:30 - UnitsPredictor - ERROR - lgb_units_model - train - 197 - LightGBM训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:30 - UnitsPredictor - ERROR - ensemble_units_model - train - 178 - 基础模型 lgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:30 - UnitsPredictor - ERROR - lstm_units_model - train - 235 - LSTM训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:31:30 - UnitsPredictor - ERROR - ensemble_units_model - train - 178 - 基础模型 lstm 训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:31:30 - UnitsPredictor - ERROR - ensemble_units_model - _evaluate_ensemble - 358 - 集成模型评估失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:31:30 - HundredsPredictor - ERROR - ensemble_hundreds_model - predict_probability - 258 - 集成模型预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:30 - HundredsPredictor - ERROR - base_independent_predictor - predict_next_period - 350 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:30 - HundredsPredictor - ERROR - hundreds_predictor - predict - 215 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:30 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器hundreds预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:30 - TensPredictor - ERROR - ensemble_tens_model - predict_probability - 258 - 集成模型预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:30 - TensPredictor - ERROR - base_independent_predictor - predict_next_period - 350 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:30 - TensPredictor - ERROR - tens_predictor - predict - 215 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:30 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器tens预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:30 - UnitsPredictor - ERROR - ensemble_units_model - predict_probability - 258 - 集成模型预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:30 - UnitsPredictor - ERROR - base_independent_predictor - predict_next_period - 350 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:30 - UnitsPredictor - ERROR - units_predictor - predict - 215 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:30 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器units预测失败: 没有可用的基础模型进行预测
2025-08-14 22:31:30 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器span预测失败: 模型尚未训练
2025-08-14 22:32:15 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:32:15 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:32:15 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:32:15 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:32:15 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:32:16 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:32:19 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:32:19 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:32:19 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:32:21 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:32:21.982113"}
2025-08-14 22:32:21 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:32:21.983110"}
2025-08-14 22:32:21 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:32:21 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:32:22 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:32:22 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:32:22 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:32:22 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:32:22 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:32:22 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:32:22 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:32:22 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:32:24 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:32:27 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:32:27 - src.core.execution_verifier - ERROR - execution_verifier - _verify_prediction_review - 211 - ❌ 预测复盘验证失败: no such table: prediction_reviews
2025-08-14 22:32:30 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:32:35 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:32:35 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 22:32:38 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:32:41 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:32:44 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:32:47 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:32:51 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:32:51 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:32:52 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:32:52 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:32:52 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:32:52 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:32:52 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:32:52 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:32:57 - HundredsPredictor - ERROR - xgb_hundreds_model - train - 194 - XGBoost训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:32:57 - HundredsPredictor - ERROR - ensemble_hundreds_model - train - 178 - 基础模型 xgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:32:57 - HundredsPredictor - ERROR - lgb_hundreds_model - train - 197 - LightGBM训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:32:57 - HundredsPredictor - ERROR - ensemble_hundreds_model - train - 178 - 基础模型 lgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:32:57 - HundredsPredictor - ERROR - lstm_hundreds_model - train - 235 - LSTM训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:32:57 - HundredsPredictor - ERROR - ensemble_hundreds_model - train - 178 - 基础模型 lstm 训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:32:57 - HundredsPredictor - ERROR - ensemble_hundreds_model - _evaluate_ensemble - 358 - 集成模型评估失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:32:59 - TensPredictor - ERROR - xgb_tens_model - train - 191 - XGBoost训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:32:59 - TensPredictor - ERROR - ensemble_tens_model - train - 178 - 基础模型 xgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:32:59 - TensPredictor - ERROR - lgb_tens_model - train - 197 - LightGBM训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:32:59 - TensPredictor - ERROR - ensemble_tens_model - train - 178 - 基础模型 lgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:32:59 - TensPredictor - ERROR - lstm_tens_model - train - 234 - LSTM训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:32:59 - TensPredictor - ERROR - ensemble_tens_model - train - 178 - 基础模型 lstm 训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:32:59 - TensPredictor - ERROR - ensemble_tens_model - _evaluate_ensemble - 358 - 集成模型评估失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:00 - UnitsPredictor - ERROR - xgb_units_model - train - 191 - XGBoost训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:00 - UnitsPredictor - ERROR - ensemble_units_model - train - 178 - 基础模型 xgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:00 - UnitsPredictor - ERROR - lgb_units_model - train - 197 - LightGBM训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:00 - UnitsPredictor - ERROR - ensemble_units_model - train - 178 - 基础模型 lgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:01 - UnitsPredictor - ERROR - lstm_units_model - train - 235 - LSTM训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:33:01 - UnitsPredictor - ERROR - ensemble_units_model - train - 178 - 基础模型 lstm 训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:33:01 - UnitsPredictor - ERROR - ensemble_units_model - _evaluate_ensemble - 358 - 集成模型评估失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:01 - HundredsPredictor - ERROR - base_independent_predictor - _get_prediction_features - 367 - 获取预测特征失败: PredictorFeatureInterface.get_prediction_features() takes 2 positional arguments but 3 were given
2025-08-14 22:33:01 - HundredsPredictor - ERROR - ensemble_hundreds_model - predict_probability - 258 - 集成模型预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:01 - HundredsPredictor - ERROR - base_independent_predictor - predict_next_period - 350 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:01 - HundredsPredictor - ERROR - hundreds_predictor - predict - 215 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:01 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器hundreds预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:01 - TensPredictor - ERROR - base_independent_predictor - _get_prediction_features - 367 - 获取预测特征失败: PredictorFeatureInterface.get_prediction_features() takes 2 positional arguments but 3 were given
2025-08-14 22:33:01 - TensPredictor - ERROR - ensemble_tens_model - predict_probability - 258 - 集成模型预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:01 - TensPredictor - ERROR - base_independent_predictor - predict_next_period - 350 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:01 - TensPredictor - ERROR - tens_predictor - predict - 215 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:01 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器tens预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:01 - UnitsPredictor - ERROR - base_independent_predictor - _get_prediction_features - 367 - 获取预测特征失败: PredictorFeatureInterface.get_prediction_features() takes 2 positional arguments but 3 were given
2025-08-14 22:33:01 - UnitsPredictor - ERROR - ensemble_units_model - predict_probability - 258 - 集成模型预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:01 - UnitsPredictor - ERROR - base_independent_predictor - predict_next_period - 350 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:01 - UnitsPredictor - ERROR - units_predictor - predict - 215 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:01 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器units预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:01 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器span预测失败: 模型尚未训练
2025-08-14 22:33:05 - HundredsPredictor - ERROR - xgb_hundreds_model - train - 194 - XGBoost训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:05 - HundredsPredictor - ERROR - ensemble_hundreds_model - train - 178 - 基础模型 xgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:05 - HundredsPredictor - ERROR - lgb_hundreds_model - train - 197 - LightGBM训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:05 - HundredsPredictor - ERROR - ensemble_hundreds_model - train - 178 - 基础模型 lgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:05 - HundredsPredictor - ERROR - lstm_hundreds_model - train - 235 - LSTM训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:33:05 - HundredsPredictor - ERROR - ensemble_hundreds_model - train - 178 - 基础模型 lstm 训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:33:05 - HundredsPredictor - ERROR - ensemble_hundreds_model - _evaluate_ensemble - 358 - 集成模型评估失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:09 - TensPredictor - ERROR - xgb_tens_model - train - 191 - XGBoost训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:09 - TensPredictor - ERROR - ensemble_tens_model - train - 178 - 基础模型 xgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:09 - TensPredictor - ERROR - lgb_tens_model - train - 197 - LightGBM训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:09 - TensPredictor - ERROR - ensemble_tens_model - train - 178 - 基础模型 lgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:09 - TensPredictor - ERROR - lstm_tens_model - train - 234 - LSTM训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:33:09 - TensPredictor - ERROR - ensemble_tens_model - train - 178 - 基础模型 lstm 训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:33:09 - TensPredictor - ERROR - ensemble_tens_model - _evaluate_ensemble - 358 - 集成模型评估失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:11 - UnitsPredictor - ERROR - xgb_units_model - train - 191 - XGBoost训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:11 - UnitsPredictor - ERROR - ensemble_units_model - train - 178 - 基础模型 xgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:11 - UnitsPredictor - ERROR - lgb_units_model - train - 197 - LightGBM训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:11 - UnitsPredictor - ERROR - ensemble_units_model - train - 178 - 基础模型 lgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:11 - UnitsPredictor - ERROR - lstm_units_model - train - 235 - LSTM训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:33:11 - UnitsPredictor - ERROR - ensemble_units_model - train - 178 - 基础模型 lstm 训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:33:11 - UnitsPredictor - ERROR - ensemble_units_model - _evaluate_ensemble - 358 - 集成模型评估失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:11 - HundredsPredictor - ERROR - base_independent_predictor - _get_prediction_features - 367 - 获取预测特征失败: PredictorFeatureInterface.get_prediction_features() takes 2 positional arguments but 3 were given
2025-08-14 22:33:11 - HundredsPredictor - ERROR - ensemble_hundreds_model - predict_probability - 258 - 集成模型预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:11 - HundredsPredictor - ERROR - base_independent_predictor - predict_next_period - 350 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:11 - HundredsPredictor - ERROR - hundreds_predictor - predict - 215 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:11 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器hundreds预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:11 - TensPredictor - ERROR - base_independent_predictor - _get_prediction_features - 367 - 获取预测特征失败: PredictorFeatureInterface.get_prediction_features() takes 2 positional arguments but 3 were given
2025-08-14 22:33:11 - TensPredictor - ERROR - ensemble_tens_model - predict_probability - 258 - 集成模型预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:11 - TensPredictor - ERROR - base_independent_predictor - predict_next_period - 350 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:11 - TensPredictor - ERROR - tens_predictor - predict - 215 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:11 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器tens预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:11 - UnitsPredictor - ERROR - base_independent_predictor - _get_prediction_features - 367 - 获取预测特征失败: PredictorFeatureInterface.get_prediction_features() takes 2 positional arguments but 3 were given
2025-08-14 22:33:11 - UnitsPredictor - ERROR - ensemble_units_model - predict_probability - 258 - 集成模型预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:11 - UnitsPredictor - ERROR - base_independent_predictor - predict_next_period - 350 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:11 - UnitsPredictor - ERROR - units_predictor - predict - 215 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:11 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器units预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:11 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器span预测失败: 模型尚未训练
2025-08-14 22:33:39 - HundredsPredictor - ERROR - xgb_hundreds_model - train - 194 - XGBoost训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:39 - HundredsPredictor - ERROR - ensemble_hundreds_model - train - 178 - 基础模型 xgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:39 - HundredsPredictor - ERROR - lgb_hundreds_model - train - 197 - LightGBM训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:39 - HundredsPredictor - ERROR - ensemble_hundreds_model - train - 178 - 基础模型 lgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:39 - HundredsPredictor - ERROR - lstm_hundreds_model - train - 235 - LSTM训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:33:39 - HundredsPredictor - ERROR - ensemble_hundreds_model - train - 178 - 基础模型 lstm 训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:33:39 - HundredsPredictor - ERROR - ensemble_hundreds_model - _evaluate_ensemble - 358 - 集成模型评估失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:41 - TensPredictor - ERROR - xgb_tens_model - train - 191 - XGBoost训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:41 - TensPredictor - ERROR - ensemble_tens_model - train - 178 - 基础模型 xgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:41 - TensPredictor - ERROR - lgb_tens_model - train - 197 - LightGBM训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:41 - TensPredictor - ERROR - ensemble_tens_model - train - 178 - 基础模型 lgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:41 - TensPredictor - ERROR - lstm_tens_model - train - 234 - LSTM训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:33:41 - TensPredictor - ERROR - ensemble_tens_model - train - 178 - 基础模型 lstm 训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:33:41 - TensPredictor - ERROR - ensemble_tens_model - _evaluate_ensemble - 358 - 集成模型评估失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:42 - UnitsPredictor - ERROR - xgb_units_model - train - 191 - XGBoost训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:42 - UnitsPredictor - ERROR - ensemble_units_model - train - 178 - 基础模型 xgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:42 - UnitsPredictor - ERROR - lgb_units_model - train - 197 - LightGBM训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:42 - UnitsPredictor - ERROR - ensemble_units_model - train - 178 - 基础模型 lgb 训练失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:42 - UnitsPredictor - ERROR - lstm_units_model - train - 235 - LSTM训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:33:42 - UnitsPredictor - ERROR - ensemble_units_model - train - 178 - 基础模型 lstm 训练失败: Expected 2D array, got 1D array instead:
array=[].
Reshape your data either using array.reshape(-1, 1) if your data has a single feature or array.reshape(1, -1) if it contains a single sample.
2025-08-14 22:33:42 - UnitsPredictor - ERROR - ensemble_units_model - _evaluate_ensemble - 358 - 集成模型评估失败: With n_samples=0, test_size=0.2 and train_size=None, the resulting train set will be empty. Adjust any of the aforementioned parameters.
2025-08-14 22:33:42 - HundredsPredictor - ERROR - ensemble_hundreds_model - predict_probability - 258 - 集成模型预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:42 - HundredsPredictor - ERROR - base_independent_predictor - predict_next_period - 350 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:42 - HundredsPredictor - ERROR - hundreds_predictor - predict - 215 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:42 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器hundreds预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:42 - TensPredictor - ERROR - ensemble_tens_model - predict_probability - 258 - 集成模型预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:42 - TensPredictor - ERROR - base_independent_predictor - predict_next_period - 350 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:42 - TensPredictor - ERROR - tens_predictor - predict - 215 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:42 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器tens预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:42 - UnitsPredictor - ERROR - ensemble_units_model - predict_probability - 258 - 集成模型预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:42 - UnitsPredictor - ERROR - base_independent_predictor - predict_next_period - 350 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:42 - UnitsPredictor - ERROR - units_predictor - predict - 215 - 预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:42 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器units预测失败: 没有可用的基础模型进行预测
2025-08-14 22:33:42 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - predict_all_dimensions - 274 - 预测器span预测失败: 模型尚未训练
2025-08-14 22:34:02 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:34:02 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:34:02 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:34:02 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:34:03 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:34:03 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:34:06 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:34:07 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:34:09 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:34:09 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:34:09.186545"}
2025-08-14 22:34:09 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:34:09 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:34:09.212543"}
2025-08-14 22:34:09 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:34:09 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:34:09 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:34:09 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:34:09 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:34:09 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:34:09 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:34:09 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:34:09 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:34:11 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:34:14 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:34:14 - src.core.execution_verifier - ERROR - execution_verifier - _verify_prediction_review - 211 - ❌ 预测复盘验证失败: no such table: prediction_reviews
2025-08-14 22:34:17 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:34:22 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:34:22 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 22:34:25 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:34:28 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:34:31 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:34:34 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:34:58 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:34:58 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:34:58 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:34:58 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:34:58 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:34:58 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:35:00 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:35:02 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:35:03 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:35:03 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:35:03 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:35:03 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:35:04 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:35:04 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:35:04 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 125 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:35:04 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:35:04 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:35:04 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:35:04.804835"}
2025-08-14 22:35:04 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:35:04.823836"}
2025-08-14 22:35:04 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:35:04 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:35:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:35:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:35:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:35:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:35:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:35:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:35:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:35:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:35:07 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:35:10 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:35:10 - src.core.execution_verifier - ERROR - execution_verifier - _verify_prediction_review - 211 - ❌ 预测复盘验证失败: no such table: prediction_reviews
2025-08-14 22:35:13 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:35:18 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:35:18 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 22:35:21 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:35:24 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:35:27 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:35:30 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:40:06 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:40:06 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:40:09 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:40:09.008569"}
2025-08-14 22:40:09 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:40:09 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:40:09.039569"}
2025-08-14 22:40:09 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:40:09 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:40:09 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:40:09 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:40:09 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:40:09 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:40:09 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:40:09 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:40:09 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:42:00 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:42:00 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:42:00 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:42:00 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:42:00 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:42:00 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:42:00 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:42:00 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:42:00 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:42:00 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:42:00 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:42:00 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:42:04 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:42:06 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:42:06 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:42:07 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:42:07.027188"}
2025-08-14 22:42:07 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:42:07.028665"}
2025-08-14 22:42:07 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:42:07 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:42:07 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:42:07 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:42:07 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:42:07 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:42:07 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:42:07 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:42:07 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:42:07 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:42:09 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:42:12 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:42:12 - src.core.execution_verifier - ERROR - execution_verifier - _verify_prediction_review - 211 - ❌ 预测复盘验证失败: no such table: prediction_reviews
2025-08-14 22:42:15 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:42:20 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:42:20 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 22:42:23 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:42:26 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:42:29 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:42:32 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:42:35 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:42:35 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:42:35 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:42:35 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:42:35 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:42:35 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:42:35 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:42:35 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:42:35 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:42:35 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:42:35 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:42:35 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:42:35 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:42:35 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:42:35 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:42:35 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:42:35 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:45:45 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:45:45 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:45:45 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:45:45 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:45:45 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:45:45 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:45:45 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:45:45 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:45:45 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:45:45 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:45:45 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:45:45 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:45:45 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:45:45 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:45:45 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:45:48 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:45:49 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:45:51 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:45:51 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:45:51.151944"}
2025-08-14 22:45:51 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:45:51 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:45:51.163945"}
2025-08-14 22:45:51 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:45:51 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:45:51 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:45:51 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:45:51 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:45:51 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:45:51 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:45:51 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:45:51 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:45:53 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:45:56 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:45:56 - src.core.execution_verifier - ERROR - execution_verifier - _verify_prediction_review - 211 - ❌ 预测复盘验证失败: no such table: prediction_reviews
2025-08-14 22:45:59 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:46:04 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:46:04 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 22:46:07 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:46:10 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:46:13 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:46:25 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:46:25 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:46:25 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:46:25 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:46:25 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:46:25 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:46:25 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:46:25 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:46:25 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:46:25 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:46:25 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:46:25 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:46:25 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:46:25 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:46:25 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:46:28 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:46:29 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:46:30 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:46:30 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:46:30 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:46:30 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:46:30 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:46:30 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:46:30 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:46:30 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:46:30 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:46:30 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:46:30 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:46:30 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:46:30 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:46:30 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:46:30 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:46:30 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:46:30 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:46:30 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:46:30 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:46:31 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:46:31 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:46:31 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:46:31 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:46:31.537514"}
2025-08-14 22:46:31 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:46:31.538535"}
2025-08-14 22:46:31 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:46:31 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:46:31 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:46:31 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:46:31 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:46:31 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:46:31 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:46:31 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:46:31 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:46:31 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:46:33 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:46:36 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:46:36 - src.core.execution_verifier - ERROR - execution_verifier - _verify_prediction_review - 211 - ❌ 预测复盘验证失败: no such table: prediction_reviews
2025-08-14 22:46:39 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:46:44 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:46:44 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 22:46:47 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:46:50 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:46:54 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:46:57 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:47:17 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:47:17 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:47:17 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:47:17 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:47:17 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:47:17 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:47:17 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:47:17 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:47:17 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:47:17 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:47:17 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:47:17 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:47:17 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:47:17 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:47:17 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:47:21 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:47:21 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:47:21 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:47:21 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:47:21 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:47:21 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:47:21 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:47:21 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:47:21 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:47:21 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:47:21 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:47:21 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:47:21 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:47:21 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:47:21 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:47:21 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:47:22 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:47:22 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:47:22 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:47:22 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:47:22 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:47:22 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:47:22 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:47:22 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:47:22 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:47:22 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器sum失败: SumPredictor._load_config() missing 1 required positional argument: 'config_path'
2025-08-14 22:47:22 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:47:22 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:47:23 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:47:23 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:47:23.921470"}
2025-08-14 22:47:23 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:47:23.928471"}
2025-08-14 22:47:23 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:47:23 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:47:23 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:47:23 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:47:23 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:47:23 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:47:23 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:47:23 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:47:23 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:47:23 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:47:26 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:47:29 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:47:29 - src.core.execution_verifier - ERROR - execution_verifier - _verify_prediction_review - 211 - ❌ 预测复盘验证失败: no such table: prediction_reviews
2025-08-14 22:47:32 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:47:37 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:47:37 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 22:47:40 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:47:43 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:47:46 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:47:49 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:48:24 - SumPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SumPredictor' object has no attribute 'cache_config'
2025-08-14 22:48:24 - SumPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SumPredictor' object has no attribute 'cache_config'
2025-08-14 22:48:24 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:48:24 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:48:24 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:48:24 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:48:24 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:48:24 - SumPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SumPredictor' object has no attribute 'cache_config'
2025-08-14 22:48:24 - SumPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SumPredictor' object has no attribute 'cache_config'
2025-08-14 22:48:24 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:48:24 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:48:24 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:48:24 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:48:24 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:48:24 - SumPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SumPredictor' object has no attribute 'cache_config'
2025-08-14 22:48:24 - SumPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SumPredictor' object has no attribute 'cache_config'
2025-08-14 22:48:24 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:48:24 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:48:26 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:48:28 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:48:30 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:48:30 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:48:30.869487"}
2025-08-14 22:48:30 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:48:30.871486"}
2025-08-14 22:48:30 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:48:30 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:48:30 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:48:30 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:48:30 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:48:30 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:48:30 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:48:30 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:48:30 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:48:30 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:48:33 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:48:36 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:48:36 - src.core.execution_verifier - ERROR - execution_verifier - _verify_prediction_review - 211 - ❌ 预测复盘验证失败: no such table: prediction_reviews
2025-08-14 22:48:39 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:48:44 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:48:44 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 22:48:47 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:48:50 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:48:53 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:48:56 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:49:10 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:49:10 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:49:10 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:10 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:10 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:10 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:49:10 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:49:10 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:10 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:10 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:10 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:49:11 - SpanPredictor - ERROR - base_independent_predictor - _init_p2_components - 154 - P2系统组件初始化失败: 'SpanPredictor' object has no attribute 'cache_config'
2025-08-14 22:49:12 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:49:14 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:49:16 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:49:16 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:49:16.914967"}
2025-08-14 22:49:16 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:49:16.920968"}
2025-08-14 22:49:16 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:49:16 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:49:16 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:49:16 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:49:16 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:49:16 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:49:16 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:49:16 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:49:16 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:49:16 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:49:19 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:49:22 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:49:22 - src.core.execution_verifier - ERROR - execution_verifier - _verify_prediction_review - 211 - ❌ 预测复盘验证失败: no such table: prediction_reviews
2025-08-14 22:49:35 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:35 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:35 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:35 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:35 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:35 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:38 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:49:39 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:49:41 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:49:41 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:49:41.282549"}
2025-08-14 22:49:41 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:49:41.283548"}
2025-08-14 22:49:41 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:49:41 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:49:41 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:49:41 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:49:41 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:49:41 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:49:41 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:49:41 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:49:41 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:49:41 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:49:42 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:42 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:42 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:42 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:42 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:42 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:42 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:42 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:42 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:42 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器hundreds失败: HundredsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:42 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器tens失败: TensPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:42 - src.backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:49:43 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:49:46 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:49:46 - src.core.execution_verifier - ERROR - execution_verifier - _verify_prediction_review - 211 - ❌ 预测复盘验证失败: no such table: prediction_reviews
2025-08-14 22:49:49 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:49:54 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:49:54 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 22:49:57 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:50:00 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:50:03 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:50:06 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:51:18 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:51:18 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:51:18 - backtest.multi_predictor_coordinator - ERROR - multi_predictor_coordinator - _load_predictor - 126 - 加载预测器units失败: UnitsPredictor.__init__() takes from 1 to 2 positional arguments but 3 were given
2025-08-14 22:51:20 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:51:20 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:51:22 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:51:24 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:51:24.843111"}
2025-08-14 22:51:24 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:51:24.844414"}
2025-08-14 22:51:24 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:51:24 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:51:24 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:51:24 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:51:24 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:51:24 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:51:24 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:51:24 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:51:24 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:51:24 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:51:27 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:51:43 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:51:44 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:51:46 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:51:46 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:51:46.375914"}
2025-08-14 22:51:46 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:51:46.377997"}
2025-08-14 22:51:46 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:51:46 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:51:46 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:51:46 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:51:46 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:51:46 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:51:46 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:51:46 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:51:46 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:51:46 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:51:48 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:51:51 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:51:51 - src.core.execution_verifier - ERROR - execution_verifier - _verify_prediction_review - 211 - ❌ 预测复盘验证失败: no such table: prediction_reviews
2025-08-14 22:51:55 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:52:00 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 22:52:00 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 22:52:03 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:52:06 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:52:09 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:52:12 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 22:56:48 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:56:48 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:56:48.511562"}
2025-08-14 22:56:50 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 22:56:50 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:56:50 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T22:56:50.589016"}
2025-08-14 22:56:50 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 22:56:50 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:56:50 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:56:50 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:56:50 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 22:56:50 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 22:56:50 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 22:56:50 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 22:56:50 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 23:01:52 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 23:01:54 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:01:54.844023"}
2025-08-14 23:01:54 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:01:54.845995"}
2025-08-14 23:01:54 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 23:01:54 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:01:54 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:01:54 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 23:01:54 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:01:54 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 23:01:54 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 23:01:54 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 23:01:54 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:01:54 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 23:01:54 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 23:02:29 - app - ERROR - app - websocket_endpoint - 505 - WebSocket消息处理错误: (1001, '')
2025-08-14 23:02:38 - app - ERROR - app - websocket_endpoint - 505 - WebSocket消息处理错误: (1001, '')
2025-08-14 23:02:38 - app - ERROR - app - websocket_endpoint - 505 - WebSocket消息处理错误: (1001, '')
2025-08-14 23:02:44 - app - ERROR - app - websocket_endpoint - 505 - WebSocket消息处理错误: (1001, '')
2025-08-14 23:02:47 - app - ERROR - app - websocket_endpoint - 505 - WebSocket消息处理错误: (1001, '')
2025-08-14 23:03:19 - app - ERROR - app - websocket_endpoint - 505 - WebSocket消息处理错误: (<CloseCode.ABNORMAL_CLOSURE: 1006>, '')
2025-08-14 23:06:54 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 23:06:57 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 23:06:59 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:06:59.132179"}
2025-08-14 23:06:59 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 760 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:06:59.133180"}
2025-08-14 23:06:59 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:06:59 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:06:59 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 23:06:59 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:06:59 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 23:06:59 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 23:06:59 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 23:06:59 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:06:59 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 23:06:59 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 23:08:16 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 23:08:16 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 23:08:18 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/dashboard - 500
2025-08-14 23:08:19 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 777 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:08:19.044268"}
2025-08-14 23:08:19 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 777 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:08:19.045884"}
2025-08-14 23:08:19 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:08:19 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:08:19 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 23:08:19 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:08:19 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 23:08:19 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 23:08:19 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 3 个关键API故障:
2025-08-14 23:08:19 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:08:19 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 反馈仪表板: 状态码不匹配: 期望200, 实际500
2025-08-14 23:08:19 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 23:08:21 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 23:08:37 - src.web.websocket_manager - ERROR - websocket_manager - send_to_websocket - 55 - 发送WebSocket消息失败: 
2025-08-14 23:08:38 - app - ERROR - app - websocket_endpoint - 505 - WebSocket消息处理错误: WebSocket is not connected. Need to call "accept" first.
2025-08-14 23:08:39 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 23:08:42 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:08:42.286998"}
2025-08-14 23:08:42 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:08:42.289000"}
2025-08-14 23:08:42 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:08:42 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:08:42 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 2 个关键API故障:
2025-08-14 23:08:42 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:08:42 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 23:08:42 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 2 个关键API故障:
2025-08-14 23:08:42 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:08:42 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 预测状态: 状态码不匹配: 期望200, 实际404
2025-08-14 23:08:44 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 23:08:47 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 23:08:47 - src.core.execution_verifier - ERROR - execution_verifier - _verify_prediction_review - 211 - ❌ 预测复盘验证失败: no such table: prediction_reviews
2025-08-14 23:08:50 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 23:08:55 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 23:08:55 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 23:08:58 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 23:09:02 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 23:09:05 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 23:09:08 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 23:09:41 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 23:09:41 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:09:41.678748"}
2025-08-14 23:09:43 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:09:43 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:09:43.745451"}
2025-08-14 23:09:43 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:09:43 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:09:43 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:09:43 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:09:43 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:09:46 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 23:09:49 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 23:09:49 - src.core.execution_verifier - ERROR - execution_verifier - _verify_prediction_review - 211 - ❌ 预测复盘验证失败: no such table: prediction_reviews
2025-08-14 23:09:52 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 23:09:57 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 23:09:57 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 23:10:00 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 23:10:03 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 23:10:06 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 23:10:09 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 23:14:47 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:14:47.868652"}
2025-08-14 23:14:47 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:14:47 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:14:47.901624"}
2025-08-14 23:14:47 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:14:47 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:14:47 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:14:47 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:14:47 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:18:01 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 23:18:04 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:18:04.022946"}
2025-08-14 23:18:04 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:18:04.023947"}
2025-08-14 23:18:04 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:18:04 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:18:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:18:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:18:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:18:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:18:06 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 23:18:09 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 23:18:09 - src.core.execution_verifier - ERROR - execution_verifier - _verify_prediction_review - 211 - ❌ 预测复盘验证失败: no such table: prediction_reviews
2025-08-14 23:18:12 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 23:18:17 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 23:18:17 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 23:18:20 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 23:18:23 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 23:18:26 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 23:18:29 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _trigger_detection_task - 1225 - 触发检测任务失败: cannot import name 'TaskQueueManager' from 'src.optimization.task_queue_manager' (D:\github\fucai3d\src\optimization\task_queue_manager.py)
2025-08-14 23:18:38 - app - ERROR - app - websocket_endpoint - 505 - WebSocket消息处理错误: (1001, '')
2025-08-14 23:18:38 - app - ERROR - app - websocket_endpoint - 505 - WebSocket消息处理错误: (1001, '')
2025-08-14 23:23:08 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:23:08.255259"}
2025-08-14 23:23:08 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:23:08 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:23:08.264259"}
2025-08-14 23:23:08 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:23:08 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:23:08 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:23:08 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:23:08 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:28:12 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:28:12.462078"}
2025-08-14 23:28:12 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:28:12.464078"}
2025-08-14 23:28:12 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:28:12 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:28:12 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:28:12 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:28:12 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:28:12 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:33:16 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:33:16.621245"}
2025-08-14 23:33:16 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:33:16.631245"}
2025-08-14 23:33:16 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:33:16 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:33:16 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:33:16 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:33:16 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:33:16 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:34:06 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 23:34:08 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:34:08.824726"}
2025-08-14 23:34:08 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:34:08.827726"}
2025-08-14 23:34:08 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:34:08 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:34:08 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:34:08 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:34:08 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:34:08 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:34:11 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 23:34:33 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 23:34:35 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:34:35.854217"}
2025-08-14 23:34:35 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:34:35.855216"}
2025-08-14 23:34:35 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:34:35 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:34:35 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:34:35 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:34:35 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:34:35 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:34:38 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 23:34:41 - src.core.execution_verifier - ERROR - execution_verifier - _verify_prediction_review - 211 - ❌ 预测复盘验证失败: no such table: prediction_reviews
2025-08-14 23:34:44 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 23:34:49 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: accuracy
2025-08-14 23:34:49 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 23:35:06 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:35:09 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:35:09.368303"}
2025-08-14 23:35:09 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:35:09.374303"}
2025-08-14 23:35:09 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:35:09 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:35:09 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:35:09 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:35:09 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:35:09 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:35:11 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:35:17 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:35:22 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:35:22 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 23:35:57 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:36:00 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:36:00.447751"}
2025-08-14 23:36:00 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:36:00.462752"}
2025-08-14 23:36:00 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:36:00 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:36:00 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:36:00 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:36:00 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:36:00 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:36:02 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:36:09 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:36:14 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:36:14 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 23:36:28 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:36:31 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:36:31.418099"}
2025-08-14 23:36:31 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:36:31.420099"}
2025-08-14 23:36:31 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:36:31 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:36:31 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:36:31 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:36:31 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:36:31 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:36:33 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:36:40 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:36:45 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:36:45 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 23:37:27 - src.optimization.optimization_detection_engine - ERROR - optimization_detection_engine - _create_result_from_comprehensive_evaluation - 324 - 从综合评估创建检测结果失败: DetectionResult.__init__() got an unexpected keyword argument 'status'
2025-08-14 23:41:35 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:41:35.610276"}
2025-08-14 23:41:35 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:41:35 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:41:35.633305"}
2025-08-14 23:41:35 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:41:35 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:41:35 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:41:35 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:41:35 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:46:39 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:46:39.796817"}
2025-08-14 23:46:39 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:46:39.799822"}
2025-08-14 23:46:39 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:46:39 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:46:39 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:46:39 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:46:39 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:46:39 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:47:27 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:47:30 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:47:30.036452"}
2025-08-14 23:47:30 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:47:30.038011"}
2025-08-14 23:47:30 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:47:30 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:47:30 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:47:30 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:47:30 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:47:30 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:47:32 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:47:38 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:47:43 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:47:43 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 23:48:14 - app - ERROR - app - websocket_endpoint - 505 - WebSocket消息处理错误: (1001, '')
2025-08-14 23:48:15 - src.web.websocket_manager - ERROR - websocket_manager - send_to_websocket - 55 - 发送WebSocket消息失败: 
2025-08-14 23:48:15 - app - ERROR - app - websocket_endpoint - 505 - WebSocket消息处理错误: WebSocket is not connected. Need to call "accept" first.
2025-08-14 23:48:26 - src.optimization.optimization_detection_engine - ERROR - optimization_detection_engine - _create_result_from_comprehensive_evaluation - 324 - 从综合评估创建检测结果失败: DetectionResult.__init__() got an unexpected keyword argument 'status'
2025-08-14 23:49:40 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:49:43 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:49:43.412755"}
2025-08-14 23:49:43 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:49:43.413752"}
2025-08-14 23:49:43 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:49:43 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:49:43 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:49:43 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:49:43 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:49:43 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:49:45 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:49:52 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:49:57 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:49:57 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 23:50:21 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:50:24 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:50:24.083534"}
2025-08-14 23:50:24 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:50:24.086534"}
2025-08-14 23:50:24 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:50:24 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:50:24 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:50:24 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:50:24 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:50:24 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:50:26 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:50:32 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:50:37 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:50:37 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 23:51:20 - src.optimization.optimization_detection_engine - ERROR - optimization_detection_engine - _create_result_from_comprehensive_evaluation - 324 - 从综合评估创建检测结果失败: DetectionResult.__init__() got an unexpected keyword argument 'status'
2025-08-14 23:51:41 - app - ERROR - app - websocket_endpoint - 517 - WebSocket消息处理错误: (1001, '')
2025-08-14 23:51:41 - src.web.websocket_manager - ERROR - websocket_manager - send_to_websocket - 55 - 发送WebSocket消息失败: 
2025-08-14 23:51:42 - app - ERROR - app - websocket_endpoint - 517 - WebSocket消息处理错误: WebSocket is not connected. Need to call "accept" first.
2025-08-14 23:53:51 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:53:54 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:53:54.032167"}
2025-08-14 23:53:54 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:53:54.034167"}
2025-08-14 23:53:54 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:53:54 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:53:54 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:53:54 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:53:54 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:53:54 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:53:56 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:54:02 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:54:07 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-14 23:54:07 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-14 23:54:50 - src.optimization.optimization_detection_engine - ERROR - optimization_detection_engine - _create_result_from_comprehensive_evaluation - 324 - 从综合评估创建检测结果失败: DetectionResult.__init__() got an unexpected keyword argument 'status'
2025-08-14 23:55:35 - app - ERROR - app - websocket_endpoint - 517 - WebSocket消息处理错误: (1001, '')
2025-08-14 23:55:35 - app - ERROR - app - websocket_endpoint - 517 - WebSocket消息处理错误: (1001, '')
2025-08-14 23:55:49 - app - ERROR - app - websocket_endpoint - 517 - WebSocket消息处理错误: (1001, '')
2025-08-14 23:58:15 - app - ERROR - app - websocket_endpoint - 517 - WebSocket消息处理错误: (1001, '')
2025-08-14 23:58:38 - app - ERROR - app - websocket_endpoint - 517 - WebSocket消息处理错误: (1001, '')
2025-08-14 23:58:38 - app - ERROR - app - websocket_endpoint - 517 - WebSocket消息处理错误: (1001, '')
2025-08-14 23:58:56 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:58:56.193913"}
2025-08-14 23:58:58 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:58:58 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-14T23:58:58.250483"}
2025-08-14 23:58:58 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-14 23:58:58 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:58:58 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-14 23:58:58 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-14 23:58:58 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 00:03:56 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-15 00:03:59 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T00:03:59.482366"}
2025-08-15 00:03:59 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T00:03:59.484364"}
2025-08-15 00:03:59 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 00:03:59 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 00:03:59 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 00:03:59 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 00:03:59 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 00:03:59 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 00:04:01 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-15 00:04:08 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-15 00:04:13 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-15 00:04:13 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-15 00:04:21 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-15 00:04:23 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T00:04:23.464123"}
2025-08-15 00:04:23 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T00:04:23.473119"}
2025-08-15 00:04:23 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 00:04:23 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 00:04:23 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 00:04:23 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 00:04:23 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 00:04:23 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 00:04:26 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-15 00:04:32 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-15 00:04:37 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-15 00:04:37 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-15 00:04:55 - src.optimization.optimization_detection_engine - ERROR - optimization_detection_engine - _create_result_from_comprehensive_evaluation - 324 - 从综合评估创建检测结果失败: DetectionResult.__init__() got an unexpected keyword argument 'status'
2025-08-15 00:05:19 - src.optimization.optimization_detection_engine - ERROR - optimization_detection_engine - _create_result_from_comprehensive_evaluation - 324 - 从综合评估创建检测结果失败: DetectionResult.__init__() got an unexpected keyword argument 'status'
2025-08-15 12:59:50 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-15 12:59:52 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T12:59:52.849538"}
2025-08-15 12:59:52 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 12:59:52 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T12:59:52.865539"}
2025-08-15 12:59:52 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 12:59:52 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 12:59:52 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 12:59:52 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 12:59:52 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 12:59:55 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-15 13:00:01 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-15 13:00:06 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-15 13:00:06 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-15 13:00:18 - app - ERROR - app - websocket_endpoint - 517 - WebSocket消息处理错误: (<CloseCode.ABNORMAL_CLOSURE: 1006>, '')
2025-08-15 13:00:49 - src.optimization.optimization_detection_engine - ERROR - optimization_detection_engine - _create_result_from_comprehensive_evaluation - 324 - 从综合评估创建检测结果失败: DetectionResult.__init__() got an unexpected keyword argument 'status'
2025-08-15 13:05:09 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-15 13:05:11 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T13:05:11.679674"}
2025-08-15 13:05:11 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T13:05:11.685673"}
2025-08-15 13:05:11 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 13:05:11 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 13:05:11 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 13:05:11 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 13:05:11 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 13:05:11 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 13:05:14 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-15 13:05:20 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-15 13:05:25 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-15 13:05:25 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-15 13:06:01 - app - ERROR - app - websocket_endpoint - 517 - WebSocket消息处理错误: (1001, '')
2025-08-15 13:06:01 - app - ERROR - app - websocket_endpoint - 517 - WebSocket消息处理错误: (1001, '')
2025-08-15 13:06:07 - src.optimization.optimization_detection_engine - ERROR - optimization_detection_engine - _create_result_from_comprehensive_evaluation - 324 - 从综合评估创建检测结果失败: DetectionResult.__init__() got an unexpected keyword argument 'status'
2025-08-15 13:09:25 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-15 13:09:27 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T13:09:27.817927"}
2025-08-15 13:09:27 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T13:09:27.820926"}
2025-08-15 13:09:27 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 13:09:27 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 13:09:27 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 13:09:27 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 13:09:27 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 13:09:27 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 13:09:30 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-15 13:09:36 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-15 13:09:41 - src.core.execution_verifier - ERROR - execution_verifier - _get_current_metrics - 437 - ❌ 获取当前指标失败: no such column: created_at
2025-08-15 13:09:41 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-15 13:10:24 - src.optimization.optimization_detection_engine - ERROR - optimization_detection_engine - _create_result_from_comprehensive_evaluation - 324 - 从综合评估创建检测结果失败: DetectionResult.__init__() got an unexpected keyword argument 'status'
2025-08-15 13:11:23 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T13:11:23.177547"}
2025-08-15 13:11:23 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T13:11:23.178547"}
2025-08-15 13:11:23 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 13:11:23 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 13:11:23 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 13:11:23 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 13:11:23 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 13:11:23 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 13:11:25 - src.optimization.optimization_detection_engine - ERROR - optimization_detection_engine - _collect_accuracy_metrics - 389 - 收集准确率指标失败: no such column: created_at
2025-08-15 13:11:55 - src.optimization.optimization_detection_engine - ERROR - optimization_detection_engine - _collect_accuracy_metrics - 389 - 收集准确率指标失败: no such column: created_at
2025-08-15 13:11:55 - src.optimization.optimization_detection_engine - ERROR - optimization_detection_engine - _create_result_from_comprehensive_evaluation - 324 - 从综合评估创建检测结果失败: DetectionResult.__init__() got an unexpected keyword argument 'status'
2025-08-15 13:12:07 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-15 13:12:26 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T13:12:26.514018"}
2025-08-15 13:12:26 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T13:12:26.515017"}
2025-08-15 13:12:26 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 13:12:26 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 13:12:26 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 13:12:26 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 13:12:47 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T13:12:47.285152"}
2025-08-15 13:12:47 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T13:12:47.292153"}
2025-08-15 13:12:47 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 13:12:47 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 13:12:47 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 13:12:47 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 13:12:47 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 13:12:47 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 13:13:01 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-15 13:13:26 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T13:13:26.871591"}
2025-08-15 13:13:26 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T13:13:26.882221"}
2025-08-15 13:13:26 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 13:13:26 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 13:13:26 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 13:13:26 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 13:13:26 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 13:13:26 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 13:13:40 - src.core.auto_optimization_engine - ERROR - auto_optimization_engine - _execute_single_action - 829 - 开奖号码提取失败: 'LotteryQueryEngine' object has no attribute 'get_latest_lottery_data'
2025-08-15 13:14:05 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T13:14:05.814892"}
2025-08-15 13:14:05 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T13:14:05.820894"}
2025-08-15 13:14:05 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 13:14:05 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 13:14:05 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 13:14:05 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 13:14:05 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 13:14:05 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 13:14:52 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T13:14:52.428975"}
2025-08-15 13:14:52 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T13:14:52.430976"}
2025-08-15 13:14:52 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 13:14:52 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 13:14:52 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 13:14:52 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 13:14:52 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 13:14:52 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 13:19:56 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T13:19:56.581363"}
2025-08-15 13:19:56 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T13:19:56.586363"}
2025-08-15 13:19:56 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 13:19:56 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 13:19:56 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 13:19:56 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 13:19:56 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 13:19:56 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 13:25:00 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T13:25:00.738725"}
2025-08-15 13:25:00 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T13:25:00.739726"}
2025-08-15 13:25:00 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 13:25:00 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 13:25:00 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 13:25:00 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 13:25:00 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 13:25:00 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 13:30:04 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T13:30:04.885636"}
2025-08-15 13:30:04 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 13:30:04 - src.web.routes.feedback_routes - ERROR - feedback_routes - trigger_optimization_detection - 780 - 触发检测失败 - 详细信息: {"action_id": "unknown", "success_criteria": "unknown", "error_type": "HTTPException", "error_message": "400: 缺少action_id参数", "timestamp": "2025-08-15T13:30:04.904636"}
2025-08-15 13:30:04 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/feedback/auto-optimize/detection/trigger - 500
2025-08-15 13:30:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 13:30:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 13:30:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 155 - 发现 1 个关键API故障:
2025-08-15 13:30:04 - src.monitoring.api_health_checker - ERROR - api_health_checker - check_all_endpoints - 157 -   - 触发检测: 状态码不匹配: 期望400, 实际500
2025-08-15 16:44:29 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/prediction/unified/generate - 503
2025-08-15 16:54:04 - HundredsPredictor - ERROR - hundreds_predictor - predict - 215 - 预测失败: ensemble 模型尚未训练
2025-08-15 16:54:04 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - generate_unified_prediction - 325 - 预测器 hundreds 预测失败: ensemble 模型尚未训练
2025-08-15 16:54:04 - TensPredictor - ERROR - tens_predictor - predict - 215 - 预测失败: ensemble 模型尚未训练
2025-08-15 16:54:04 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - generate_unified_prediction - 325 - 预测器 tens 预测失败: ensemble 模型尚未训练
2025-08-15 16:54:04 - UnitsPredictor - ERROR - units_predictor - predict - 215 - 预测失败: ensemble 模型尚未训练
2025-08-15 16:54:04 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - generate_unified_prediction - 325 - 预测器 units 预测失败: ensemble 模型尚未训练
2025-08-15 16:54:04 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - generate_unified_prediction - 325 - 预测器 sum 预测失败: 模型尚未训练
2025-08-15 16:54:04 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - generate_unified_prediction - 325 - 预测器 span 预测失败: 模型尚未训练
2025-08-15 17:40:57 - HundredsPredictor - ERROR - hundreds_predictor - predict - 215 - 预测失败: ensemble 模型尚未训练
2025-08-15 17:40:57 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - generate_unified_prediction - 325 - 预测器 hundreds 预测失败: ensemble 模型尚未训练
2025-08-15 17:40:57 - TensPredictor - ERROR - tens_predictor - predict - 215 - 预测失败: ensemble 模型尚未训练
2025-08-15 17:40:57 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - generate_unified_prediction - 325 - 预测器 tens 预测失败: ensemble 模型尚未训练
2025-08-15 17:40:57 - UnitsPredictor - ERROR - units_predictor - predict - 215 - 预测失败: ensemble 模型尚未训练
2025-08-15 17:40:57 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - generate_unified_prediction - 325 - 预测器 units 预测失败: ensemble 模型尚未训练
2025-08-15 17:40:57 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - generate_unified_prediction - 325 - 预测器 sum 预测失败: 模型尚未训练
2025-08-15 17:40:57 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - generate_unified_prediction - 325 - 预测器 span 预测失败: 模型尚未训练
2025-08-15 17:47:56 - HundredsPredictor - ERROR - hundreds_predictor - predict - 215 - 预测失败: ensemble 模型尚未训练
2025-08-15 17:47:56 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - generate_unified_prediction - 325 - 预测器 hundreds 预测失败: ensemble 模型尚未训练
2025-08-15 17:47:56 - TensPredictor - ERROR - tens_predictor - predict - 215 - 预测失败: ensemble 模型尚未训练
2025-08-15 17:47:56 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - generate_unified_prediction - 325 - 预测器 tens 预测失败: ensemble 模型尚未训练
2025-08-15 17:47:56 - UnitsPredictor - ERROR - units_predictor - predict - 215 - 预测失败: ensemble 模型尚未训练
2025-08-15 17:47:56 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - generate_unified_prediction - 325 - 预测器 units 预测失败: ensemble 模型尚未训练
2025-08-15 17:47:56 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - generate_unified_prediction - 325 - 预测器 sum 预测失败: 模型尚未训练
2025-08-15 17:47:56 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - generate_unified_prediction - 325 - 预测器 span 预测失败: 模型尚未训练
2025-08-15 20:14:58 - app - ERROR - app - websocket_endpoint - 470 - WebSocket消息处理错误: (1001, '')
2025-08-15 20:14:58 - app - ERROR - app - websocket_endpoint - 470 - WebSocket消息处理错误: (1001, '')
2025-08-15 20:17:24 - app - ERROR - app - websocket_endpoint - 470 - WebSocket消息处理错误: (1001, '')
2025-08-15 20:17:25 - app - ERROR - app - websocket_endpoint - 470 - WebSocket消息处理错误: (1001, '')
2025-08-15 20:17:43 - app - ERROR - app - websocket_endpoint - 470 - WebSocket消息处理错误: (1001, '')
2025-08-15 20:25:24 - app - ERROR - app - websocket_endpoint - 470 - WebSocket消息处理错误: (1001, '')
2025-08-15 20:25:25 - app - ERROR - app - websocket_endpoint - 470 - WebSocket消息处理错误: (<CloseCode.ABNORMAL_CLOSURE: 1006>, '')
2025-08-16 12:57:23 - app - ERROR - app - websocket_endpoint - 470 - WebSocket消息处理错误: (1001, '')
2025-08-16 12:57:23 - app - ERROR - app - websocket_endpoint - 470 - WebSocket消息处理错误: (1001, '')
2025-08-16 13:00:39 - src.web.websocket_manager - ERROR - websocket_manager - send_to_websocket - 55 - 发送WebSocket消息失败: 
2025-08-16 13:00:39 - src.web.websocket_manager - ERROR - websocket_manager - send_to_websocket - 55 - 发送WebSocket消息失败: 
2025-08-16 13:00:39 - app - ERROR - app - websocket_endpoint - 470 - WebSocket消息处理错误: WebSocket is not connected. Need to call "accept" first.
2025-08-16 13:00:39 - app - ERROR - app - websocket_endpoint - 470 - WebSocket消息处理错误: WebSocket is not connected. Need to call "accept" first.
2025-08-16 13:01:48 - HundredsPredictor - ERROR - xgb_hundreds_model - load_model - 410 - XGBoost模型加载失败: 'label_encoder'
2025-08-16 13:01:48 - HundredsPredictor - ERROR - lgb_hundreds_model - load_model - 413 - LightGBM模型加载失败: 'label_encoder'
2025-08-16 13:01:48 - HundredsPredictor - ERROR - lstm_hundreds_model - load_model - 415 - LSTM模型加载失败: Unable to synchronously open file (file signature not found)
2025-08-16 13:01:48 - HundredsPredictor - ERROR - ensemble_hundreds_model - load_model - 461 - 集成模型加载失败: 'model_weights'
2025-08-16 13:01:48 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - load_models_by_issue - 532 - hundreds位置模型加载失败: 0/4 成功
2025-08-16 13:01:48 - TensPredictor - ERROR - xgb_tens_model - load_model - 407 - XGBoost模型加载失败: 'label_encoder'
2025-08-16 13:01:48 - TensPredictor - ERROR - lgb_tens_model - load_model - 350 - LightGBM模型加载失败: 'label_encoder'
2025-08-16 13:01:48 - TensPredictor - ERROR - lstm_tens_model - load_model - 414 - LSTM模型加载失败: Unable to synchronously open file (file signature not found)
2025-08-16 13:01:48 - TensPredictor - ERROR - ensemble_tens_model - load_model - 461 - 集成模型加载失败: 'model_weights'
2025-08-16 13:01:48 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - load_models_by_issue - 532 - tens位置模型加载失败: 0/4 成功
2025-08-16 13:01:48 - UnitsPredictor - ERROR - xgb_units_model - load_model - 407 - XGBoost模型加载失败: 'label_encoder'
2025-08-16 13:01:48 - UnitsPredictor - ERROR - lgb_units_model - load_model - 350 - LightGBM模型加载失败: 'label_encoder'
2025-08-16 13:01:48 - UnitsPredictor - ERROR - lstm_units_model - load_model - 415 - LSTM模型加载失败: Unable to synchronously open file (file signature not found)
2025-08-16 13:01:48 - UnitsPredictor - ERROR - ensemble_units_model - load_model - 461 - 集成模型加载失败: 'model_weights'
2025-08-16 13:01:48 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - load_models_by_issue - 532 - units位置模型加载失败: 0/4 成功
2025-08-16 13:01:49 - HundredsPredictor - ERROR - xgb_hundreds_model - load_model - 410 - XGBoost模型加载失败: 'label_encoder'
2025-08-16 13:01:49 - HundredsPredictor - ERROR - lgb_hundreds_model - load_model - 413 - LightGBM模型加载失败: 'label_encoder'
2025-08-16 13:01:49 - HundredsPredictor - ERROR - lstm_hundreds_model - load_model - 415 - LSTM模型加载失败: Unable to synchronously open file (file signature not found)
2025-08-16 13:01:49 - HundredsPredictor - ERROR - ensemble_hundreds_model - load_model - 461 - 集成模型加载失败: 'model_weights'
2025-08-16 13:01:49 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - load_models_by_issue - 532 - hundreds位置模型加载失败: 0/4 成功
2025-08-16 13:01:49 - TensPredictor - ERROR - xgb_tens_model - load_model - 407 - XGBoost模型加载失败: 'label_encoder'
2025-08-16 13:01:49 - TensPredictor - ERROR - lgb_tens_model - load_model - 350 - LightGBM模型加载失败: 'label_encoder'
2025-08-16 13:01:49 - TensPredictor - ERROR - lstm_tens_model - load_model - 414 - LSTM模型加载失败: Unable to synchronously open file (file signature not found)
2025-08-16 13:01:49 - TensPredictor - ERROR - ensemble_tens_model - load_model - 461 - 集成模型加载失败: 'model_weights'
2025-08-16 13:01:49 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - load_models_by_issue - 532 - tens位置模型加载失败: 0/4 成功
2025-08-16 13:01:49 - UnitsPredictor - ERROR - xgb_units_model - load_model - 407 - XGBoost模型加载失败: 'label_encoder'
2025-08-16 13:01:49 - UnitsPredictor - ERROR - lgb_units_model - load_model - 350 - LightGBM模型加载失败: 'label_encoder'
2025-08-16 13:01:49 - UnitsPredictor - ERROR - lstm_units_model - load_model - 415 - LSTM模型加载失败: Unable to synchronously open file (file signature not found)
2025-08-16 13:01:49 - UnitsPredictor - ERROR - ensemble_units_model - load_model - 461 - 集成模型加载失败: 'model_weights'
2025-08-16 13:01:49 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - load_models_by_issue - 532 - units位置模型加载失败: 0/4 成功
2025-08-16 13:01:49 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/prediction/predict/with-model/2025216/quick - 500
2025-08-16 13:03:33 - app - ERROR - app - websocket_endpoint - 470 - WebSocket消息处理错误: (1001, '')
2025-08-16 13:03:33 - src.web.websocket_manager - ERROR - websocket_manager - send_to_websocket - 55 - 发送WebSocket消息失败: 
2025-08-16 13:03:33 - app - ERROR - app - websocket_endpoint - 470 - WebSocket消息处理错误: WebSocket is not connected. Need to call "accept" first.
2025-08-16 13:04:04 - HundredsPredictor - ERROR - xgb_hundreds_model - load_model - 410 - XGBoost模型加载失败: 'model'
2025-08-16 13:04:04 - HundredsPredictor - ERROR - lgb_hundreds_model - load_model - 413 - LightGBM模型加载失败: 'model'
2025-08-16 13:04:04 - HundredsPredictor - ERROR - lstm_hundreds_model - load_model - 415 - LSTM模型加载失败: Unable to synchronously open file (bad superblock version number)
2025-08-16 13:04:04 - HundredsPredictor - ERROR - ensemble_hundreds_model - load_model - 461 - 集成模型加载失败: 'label_encoder'
2025-08-16 13:04:04 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - load_models_by_issue - 532 - hundreds位置模型加载失败: 0/4 成功
2025-08-16 13:04:04 - TensPredictor - ERROR - xgb_tens_model - load_model - 407 - XGBoost模型加载失败: 'model'
2025-08-16 13:04:04 - TensPredictor - ERROR - lgb_tens_model - load_model - 350 - LightGBM模型加载失败: 'model'
2025-08-16 13:04:04 - TensPredictor - ERROR - lstm_tens_model - load_model - 414 - LSTM模型加载失败: Unable to synchronously open file (bad superblock version number)
2025-08-16 13:04:04 - TensPredictor - ERROR - ensemble_tens_model - load_model - 461 - 集成模型加载失败: 'label_encoder'
2025-08-16 13:04:04 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - load_models_by_issue - 532 - tens位置模型加载失败: 0/4 成功
2025-08-16 13:04:04 - UnitsPredictor - ERROR - xgb_units_model - load_model - 407 - XGBoost模型加载失败: 'model'
2025-08-16 13:04:04 - UnitsPredictor - ERROR - lgb_units_model - load_model - 350 - LightGBM模型加载失败: 'model'
2025-08-16 13:04:04 - UnitsPredictor - ERROR - lstm_units_model - load_model - 415 - LSTM模型加载失败: Unable to synchronously open file (bad superblock version number)
2025-08-16 13:04:04 - UnitsPredictor - ERROR - ensemble_units_model - load_model - 461 - 集成模型加载失败: 'label_encoder'
2025-08-16 13:04:04 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - load_models_by_issue - 532 - units位置模型加载失败: 0/4 成功
2025-08-16 13:04:04 - HundredsPredictor - ERROR - xgb_hundreds_model - load_model - 410 - XGBoost模型加载失败: 'model'
2025-08-16 13:04:04 - HundredsPredictor - ERROR - lgb_hundreds_model - load_model - 413 - LightGBM模型加载失败: 'model'
2025-08-16 13:04:04 - HundredsPredictor - ERROR - lstm_hundreds_model - load_model - 415 - LSTM模型加载失败: Unable to synchronously open file (bad superblock version number)
2025-08-16 13:04:04 - HundredsPredictor - ERROR - ensemble_hundreds_model - load_model - 461 - 集成模型加载失败: 'label_encoder'
2025-08-16 13:04:04 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - load_models_by_issue - 532 - hundreds位置模型加载失败: 0/4 成功
2025-08-16 13:04:04 - TensPredictor - ERROR - xgb_tens_model - load_model - 407 - XGBoost模型加载失败: 'model'
2025-08-16 13:04:04 - TensPredictor - ERROR - lgb_tens_model - load_model - 350 - LightGBM模型加载失败: 'model'
2025-08-16 13:04:04 - TensPredictor - ERROR - lstm_tens_model - load_model - 414 - LSTM模型加载失败: Unable to synchronously open file (bad superblock version number)
2025-08-16 13:04:04 - TensPredictor - ERROR - ensemble_tens_model - load_model - 461 - 集成模型加载失败: 'label_encoder'
2025-08-16 13:04:04 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - load_models_by_issue - 532 - tens位置模型加载失败: 0/4 成功
2025-08-16 13:04:04 - UnitsPredictor - ERROR - xgb_units_model - load_model - 407 - XGBoost模型加载失败: 'model'
2025-08-16 13:04:04 - UnitsPredictor - ERROR - lgb_units_model - load_model - 350 - LightGBM模型加载失败: 'model'
2025-08-16 13:04:04 - UnitsPredictor - ERROR - lstm_units_model - load_model - 415 - LSTM模型加载失败: Unable to synchronously open file (bad superblock version number)
2025-08-16 13:04:04 - UnitsPredictor - ERROR - ensemble_units_model - load_model - 461 - 集成模型加载失败: 'label_encoder'
2025-08-16 13:04:04 - src.predictors.unified_predictor_interface - ERROR - unified_predictor_interface - load_models_by_issue - 532 - units位置模型加载失败: 0/4 成功
2025-08-16 13:04:04 - src.monitoring.performance_monitor - ERROR - performance_monitor - _check_api_alerts - 241 - 服务器错误: /api/prediction/predict/with-model/2025217/quick - 500
2025-08-16 15:07:08 - app - ERROR - app - websocket_endpoint - 473 - WebSocket消息处理错误: (1001, '')
2025-08-16 15:07:09 - app - ERROR - app - websocket_endpoint - 473 - WebSocket消息处理错误: (1001, '')
2025-08-16 15:07:52 - app - ERROR - app - websocket_endpoint - 473 - WebSocket消息处理错误: (1001, '')
2025-08-16 15:07:52 - app - ERROR - app - websocket_endpoint - 473 - WebSocket消息处理错误: (1001, '')
2025-08-16 15:08:25 - app - ERROR - app - websocket_endpoint - 473 - WebSocket消息处理错误: (1001, '')
2025-08-16 15:08:25 - src.web.websocket_manager - ERROR - websocket_manager - send_to_websocket - 55 - 发送WebSocket消息失败: 
2025-08-16 15:08:25 - app - ERROR - app - websocket_endpoint - 473 - WebSocket消息处理错误: WebSocket is not connected. Need to call "accept" first.
2025-08-16 15:09:35 - app - ERROR - app - websocket_endpoint - 473 - WebSocket消息处理错误: (1012, None)
2025-08-16 15:09:35 - app - ERROR - app - websocket_endpoint - 473 - WebSocket消息处理错误: (1012, None)
2025-08-16 15:10:05 - TaskManager - ERROR - task_manager - fail_task - 250 - ❌ 任务失败: ccbf2018-7dcd-4f17-a8d9-afef24e3dfad - 'utf-8' codec can't decode byte 0xbf in position 37: invalid start byte
2025-08-16 15:10:05 - TrainingWorker-worker-1 - ERROR - training_worker - _execute_task - 158 - ❌ 任务失败: ccbf2018-7dcd-4f17-a8d9-afef24e3dfad - 'utf-8' codec can't decode byte 0xbf in position 37: invalid start byte
2025-08-16 15:10:15 - TaskManager - ERROR - task_manager - fail_task - 250 - ❌ 任务失败: d7d431ff-c137-4884-8ffd-52803982e495 - 'utf-8' codec can't decode byte 0xbf in position 37: invalid start byte
2025-08-16 15:10:15 - TrainingWorker-worker-1 - ERROR - training_worker - _execute_task - 158 - ❌ 任务失败: d7d431ff-c137-4884-8ffd-52803982e495 - 'utf-8' codec can't decode byte 0xbf in position 37: invalid start byte
2025-08-16 15:11:45 - TaskManager - ERROR - task_manager - fail_task - 250 - ❌ 任务失败: 1c6c36ba-fbe6-4002-a864-73521d52326e - 'utf-8' codec can't decode byte 0xbf in position 37: invalid start byte
2025-08-16 15:11:45 - TrainingWorker-worker-1 - ERROR - training_worker - _execute_task - 158 - ❌ 任务失败: 1c6c36ba-fbe6-4002-a864-73521d52326e - 'utf-8' codec can't decode byte 0xbf in position 37: invalid start byte
2025-08-16 15:12:43 - app - ERROR - app - websocket_endpoint - 473 - WebSocket消息处理错误: (1012, None)
2025-08-16 15:12:43 - app - ERROR - app - websocket_endpoint - 473 - WebSocket消息处理错误: (1012, None)
2025-08-16 15:13:31 - app - ERROR - app - websocket_endpoint - 473 - WebSocket消息处理错误: (1012, None)
2025-08-16 15:13:31 - app - ERROR - app - websocket_endpoint - 473 - WebSocket消息处理错误: (1012, None)
2025-08-16 15:13:52 - app - ERROR - app - websocket_endpoint - 473 - WebSocket消息处理错误: (1012, None)
2025-08-16 15:13:52 - app - ERROR - app - websocket_endpoint - 473 - WebSocket消息处理错误: (1012, None)
2025-08-16 15:16:05 - TaskManager - ERROR - task_manager - fail_task - 250 - ❌ 任务失败: 9f8bb35f-8287-49aa-b6f8-cc24af071c51 - INFO:src.data.feature_importance:SHAP�⵼��ɹ�
2025-08-16 15:15:58.139549: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-16 15:15:59.536389: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
Traceback (most recent call last):
  File "D:\github\fucai3d\scripts\train_tens_predictor.py", line 369, in <module>
    main()
  File "D:\github\fucai3d\scripts\train_tens_predictor.py", line 275, in main
    args = parse_arguments()
           ^^^^^^^^^^^^^^^^^
  File "D:\github\fucai3d\scripts\train_tens_predictor.py", line 110, in parse_arguments
    parser.add_argument(
  File "C:\Program Files\Python311\Lib\argparse.py", line 1473, in add_argument
    return self._add_action(action)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\argparse.py", line 1855, in _add_action
    self._optionals._add_action(action)
  File "C:\Program Files\Python311\Lib\argparse.py", line 1675, in _add_action
    action = super(_ArgumentGroup, self)._add_action(action)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\argparse.py", line 1487, in _add_action
    self._check_conflict(action)
  File "C:\Program Files\Python311\Lib\argparse.py", line 1624, in _check_conflict
    conflict_handler(action, confl_optionals)
  File "C:\Program Files\Python311\Lib\argparse.py", line 1633, in _handle_conflict_error
    raise ArgumentError(action, message % conflict_string)
argparse.ArgumentError: argument --model/-m: conflicting option strings: --model, -m
2025-08-16 15:16:05 - TrainingWorker-worker-1 - ERROR - training_worker - _execute_task - 158 - ❌ 任务失败: 9f8bb35f-8287-49aa-b6f8-cc24af071c51 - INFO:src.data.feature_importance:SHAP�⵼��ɹ�
2025-08-16 15:15:58.139549: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-16 15:15:59.536389: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
Traceback (most recent call last):
  File "D:\github\fucai3d\scripts\train_tens_predictor.py", line 369, in <module>
    main()
  File "D:\github\fucai3d\scripts\train_tens_predictor.py", line 275, in main
    args = parse_arguments()
           ^^^^^^^^^^^^^^^^^
  File "D:\github\fucai3d\scripts\train_tens_predictor.py", line 110, in parse_arguments
    parser.add_argument(
  File "C:\Program Files\Python311\Lib\argparse.py", line 1473, in add_argument
    return self._add_action(action)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\argparse.py", line 1855, in _add_action
    self._optionals._add_action(action)
  File "C:\Program Files\Python311\Lib\argparse.py", line 1675, in _add_action
    action = super(_ArgumentGroup, self)._add_action(action)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\argparse.py", line 1487, in _add_action
    self._check_conflict(action)
  File "C:\Program Files\Python311\Lib\argparse.py", line 1624, in _check_conflict
    conflict_handler(action, confl_optionals)
  File "C:\Program Files\Python311\Lib\argparse.py", line 1633, in _handle_conflict_error
    raise ArgumentError(action, message % conflict_string)
argparse.ArgumentError: argument --model/-m: conflicting option strings: --model, -m
2025-08-16 15:18:20 - app - ERROR - app - websocket_endpoint - 473 - WebSocket消息处理错误: (1012, None)
2025-08-16 15:18:20 - app - ERROR - app - websocket_endpoint - 473 - WebSocket消息处理错误: (1012, None)
2025-08-16 15:18:50 - app - ERROR - app - websocket_endpoint - 473 - WebSocket消息处理错误: (1012, None)
2025-08-16 15:18:50 - app - ERROR - app - websocket_endpoint - 473 - WebSocket消息处理错误: (1012, None)
2025-08-16 15:19:22 - app - ERROR - app - websocket_endpoint - 473 - WebSocket消息处理错误: (1012, None)
2025-08-16 15:19:22 - app - ERROR - app - websocket_endpoint - 473 - WebSocket消息处理错误: (1012, None)
2025-08-16 15:21:05 - app - ERROR - app - websocket_endpoint - 473 - WebSocket消息处理错误: (1001, '')
2025-08-16 15:21:05 - app - ERROR - app - websocket_endpoint - 473 - WebSocket消息处理错误: (1001, '')
2025-08-16 15:21:08 - TaskManager - ERROR - task_manager - fail_task - 250 - ❌ 任务失败: 1cd37adc-239c-47a2-8f3f-45713b78074b - INFO:src.data.feature_importance:SHAP�⵼��ɹ�
2025-08-16 15:21:01.385358: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-16 15:21:02.959005: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
Traceback (most recent call last):
  File "D:\github\fucai3d\scripts\train_units_predictor.py", line 362, in <module>
    main()
  File "D:\github\fucai3d\scripts\train_units_predictor.py", line 277, in main
    print_banner()
  File "D:\github\fucai3d\scripts\train_units_predictor.py", line 116, in print_banner
    print("\U0001f680 P5-��λԤ����ѵ��ϵͳ")
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f680' in position 0: illegal multibyte sequence
2025-08-16 15:21:08 - TrainingWorker-worker-1 - ERROR - training_worker - _execute_task - 158 - ❌ 任务失败: 1cd37adc-239c-47a2-8f3f-45713b78074b - INFO:src.data.feature_importance:SHAP�⵼��ɹ�
2025-08-16 15:21:01.385358: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-16 15:21:02.959005: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
Traceback (most recent call last):
  File "D:\github\fucai3d\scripts\train_units_predictor.py", line 362, in <module>
    main()
  File "D:\github\fucai3d\scripts\train_units_predictor.py", line 277, in main
    print_banner()
  File "D:\github\fucai3d\scripts\train_units_predictor.py", line 116, in print_banner
    print("\U0001f680 P5-��λԤ����ѵ��ϵͳ")
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f680' in position 0: illegal multibyte sequence
