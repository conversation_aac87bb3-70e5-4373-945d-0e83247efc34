#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前端集成和API接口
"""

import sys
import requests
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_api_endpoints():
    """测试API端点"""
    base_url = "http://localhost:8000/api/prediction"
    
    print("🔍 测试API端点")
    print("=" * 40)
    
    # 测试1: 获取可用模型
    print("\n📋 测试获取可用模型...")
    try:
        response = requests.get(f"{base_url}/available-models", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                models = data.get('data', {}).get('available_models', {})
                total_models = sum(len(models.get(pos, [])) for pos in ['hundreds', 'tens', 'units'])
                print(f"   ✅ 获取可用模型成功: {total_models} 个模型")
                
                # 返回第一个可用期号用于后续测试
                hundreds = models.get('hundreds', [])
                if hundreds:
                    return hundreds[0]['issue']
            else:
                print(f"   ❌ API返回错误: {data}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"   ❌ 请求失败: {e}")
    except Exception as e:
        print(f"   ❌ 其他错误: {e}")
    
    return None

def test_model_check(issue):
    """测试模型检查"""
    if not issue:
        return False
        
    base_url = "http://localhost:8000/api/prediction"
    
    print(f"\n📋 测试检查模型存在性 (期号: {issue})...")
    try:
        response = requests.get(f"{base_url}/models/check/{issue}", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                models_exist = data.get('data', {}).get('models_exist', {})
                completeness = data.get('data', {}).get('completeness', {})
                print(f"   ✅ 模型检查成功: {completeness.get('percentage', 0):.1f}% 完整")
                return True
            else:
                print(f"   ❌ API返回错误: {data}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"   ❌ 请求失败: {e}")
    except Exception as e:
        print(f"   ❌ 其他错误: {e}")
    
    return False

def test_model_load(issue):
    """测试模型加载"""
    if not issue:
        return False
        
    base_url = "http://localhost:8000/api/prediction"
    
    print(f"\n📋 测试加载模型 (期号: {issue})...")
    try:
        response = requests.post(f"{base_url}/models/load/{issue}", timeout=30)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                statistics = data.get('data', {}).get('statistics', {})
                success_rate = statistics.get('success_rate', 0)
                print(f"   ✅ 模型加载成功: {success_rate:.1f}% 成功率")
                return True
            else:
                print(f"   ❌ API返回错误: {data}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"   ❌ 请求失败: {e}")
    except Exception as e:
        print(f"   ❌ 其他错误: {e}")
    
    return False

def test_prediction_with_model(issue):
    """测试使用指定模型预测"""
    if not issue:
        return False
        
    base_url = "http://localhost:8000/api/prediction"
    
    print(f"\n📋 测试使用指定模型预测 (期号: {issue})...")
    try:
        response = requests.get(f"{base_url}/predict/with-model/{issue}/quick", timeout=30)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                result = data.get('data', {})
                predicted_number = result.get('predicted_number', '')
                confidence = result.get('confidence', 0)
                print(f"   ✅ 预测成功: {predicted_number} (置信度: {confidence:.1%})")
                return True
            else:
                print(f"   ❌ API返回错误: {data}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"   ❌ 请求失败: {e}")
    except Exception as e:
        print(f"   ❌ 其他错误: {e}")
    
    return False

def test_frontend_files():
    """测试前端文件"""
    print("\n📋 测试前端文件...")
    
    # 检查ModelIssueSelector组件
    selector_file = project_root / 'web-frontend' / 'src' / 'components' / 'ModelIssueSelector.tsx'
    if selector_file.exists():
        print("   ✅ ModelIssueSelector.tsx 文件存在")
        
        # 检查文件内容
        with open(selector_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        required_elements = [
            'ModelIssueSelector',
            'available-models',
            'models/check',
            'models/load',
            'predict/with-model'
        ]
        
        missing_elements = []
        for element in required_elements:
            if element not in content:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"   ❌ 缺少必要元素: {missing_elements}")
            return False
        else:
            print("   ✅ ModelIssueSelector 组件内容完整")
    else:
        print("   ❌ ModelIssueSelector.tsx 文件不存在")
        return False
    
    # 检查Dashboard集成
    dashboard_file = project_root / 'web-frontend' / 'src' / 'components' / 'Dashboard.tsx'
    if dashboard_file.exists():
        with open(dashboard_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'ModelIssueSelector' in content:
            print("   ✅ Dashboard 已集成 ModelIssueSelector")
            return True
        else:
            print("   ❌ Dashboard 未集成 ModelIssueSelector")
            return False
    else:
        print("   ❌ Dashboard.tsx 文件不存在")
        return False

def main():
    """主函数"""
    print("🔍 前端集成测试")
    print("=" * 50)
    
    # 测试前端文件
    frontend_ok = test_frontend_files()
    
    # 测试API端点（需要后端运行）
    print("\n" + "=" * 50)
    print("📡 API端点测试 (需要后端运行)")
    print("=" * 50)
    
    # 获取可用模型
    test_issue = test_api_endpoints()
    
    if test_issue:
        # 测试模型检查
        check_ok = test_model_check(test_issue)
        
        # 测试模型加载
        load_ok = test_model_load(test_issue)
        
        # 测试预测
        predict_ok = test_prediction_with_model(test_issue)
        
        api_tests_passed = check_ok and load_ok and predict_ok
    else:
        print("\n⚠️ 无法获取测试期号，跳过API测试")
        api_tests_passed = False
    
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    if frontend_ok:
        print("✅ 前端文件: 通过")
    else:
        print("❌ 前端文件: 失败")
    
    if api_tests_passed:
        print("✅ API端点: 通过")
    elif test_issue:
        print("❌ API端点: 失败")
    else:
        print("⚠️ API端点: 跳过 (后端未运行)")
    
    overall_success = frontend_ok and (api_tests_passed or not test_issue)
    
    if overall_success:
        print("\n🎉 前端集成测试通过！")
    else:
        print("\n⚠️ 前端集成测试部分失败")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
