#!/usr/bin/env python3
"""
独立位置预测器基类

基于独立位置预测理念设计的抽象基类，为P3、P4、P5提供统一的接口和功能。

设计原则：
- 每个位置作为完全独立的随机变量进行预测
- 基于P2系统的特征工程和缓存优化
- 统一的接口和性能标准
- 支持多种机器学习模型

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Optional, Any
import numpy as np
import logging
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入P2系统组件
try:
    from src.interfaces.predictor_feature_interface import PredictorFeatureInterface
    from src.data.cache_optimizer import CacheOptimizer, CacheConfig
except ImportError as e:
    print(f"警告: 无法导入P2系统组件: {e}")
    print("请确保P2系统已正确安装和配置")
    PredictorFeatureInterface = None
    CacheOptimizer = None

# 导入配置加载器
try:
    from config import get_config, setup_logging
except ImportError:
    # 尝试添加项目根目录到路径
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent
    sys.path.insert(0, str(project_root))
    try:
        from config import get_config, setup_logging
    except ImportError:
        print("警告: 无法导入配置加载器，将使用默认配置")
        get_config = None
        setup_logging = None

class BaseIndependentPredictor(ABC):
    """
    独立位置预测器基类
    
    设计理念：
    - 每个位置完全独立预测，不依赖其他位置信息
    - 专注于单位置特征的深度优化
    - 避免复杂的关联性分析，确保预测稳定性
    - 基于P2系统的特征工程和缓存优化
    """
    
    def __init__(self, position: str, db_path: str):
        """
        初始化独立预测器
        
        Args:
            position: 位置类型 ('hundreds', 'tens', 'units')
            db_path: 数据库路径
        """
        self.position = position
        self.db_path = db_path
        self.model = None
        self.is_trained = False
        self.feature_names = []
        self.training_history = {}
        
        # 设置日志
        self._setup_logging()
        
        # 加载配置
        self._load_config()
        
        # 初始化P2系统组件
        self._init_p2_components()

        # 🆕 注册配置重载信号
        self._register_config_reload()

        self.logger.info(f"初始化{position}位独立预测器")
    
    def _setup_logging(self):
        """设置日志"""
        try:
            setup_logging()
            self.logger = logging.getLogger(f"{self.position.title()}Predictor")
        except:
            # 如果配置加载失败，使用基础日志配置
            logging.basicConfig(level=logging.INFO)
            self.logger = logging.getLogger(f"{self.position.title()}Predictor")
    
    def _load_config(self):
        """加载配置"""
        try:
            self.config_loader = get_config()
            self.predictor_config = self.config_loader.load_predictor_config()
            self.data_config = self.config_loader.get_data_config()
            self.training_config = self.config_loader.get_training_config()
            self.cache_config = self.config_loader.get_cache_config()
        except Exception as e:
            self.logger.warning(f"配置加载失败，使用默认配置: {e}")
            self._set_default_config()
    
    def _set_default_config(self):
        """设置默认配置"""
        self.data_config = {
            'feature_types': [self.position, 'common'],
            'window_size': 20,
            'lag_features': [1, 2, 3, 5, 7],
            'validation_split': 0.2,
            'max_training_samples': 8359
        }
        self.training_config = {
            'save_model': True,
            'model_save_path': f'models/{self.position}/'
        }
        self.cache_config = {
            'memory_size': 200,
            'db_cache_enabled': True
        }
    
    def _init_p2_components(self):
        """初始化P2系统组件"""
        try:
            # 初始化特征接口
            self.feature_interface = PredictorFeatureInterface(self.db_path, self.position)
            self.logger.info(f"P2特征接口初始化成功: {self.position}")
            
            # 配置缓存优化器
            cache_config = CacheConfig(
                memory_size=self.cache_config.get('memory_size', 200),
                db_cache_enabled=self.cache_config.get('db_cache_enabled', True),
                db_cache_path=f"cache/{self.position}_predictor_cache.db"
            )
            self.cache_optimizer = CacheOptimizer(cache_config)
            self.logger.info(f"缓存优化器初始化成功: {self.position}")
            
        except Exception as e:
            self.logger.error(f"P2系统组件初始化失败: {e}")
            self.feature_interface = None
            self.cache_optimizer = None
    
    @abstractmethod
    def build_model(self) -> Any:
        """
        构建模型
        
        Returns:
            构建好的模型对象
        """
        pass
    
    @abstractmethod
    def train(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """
        训练模型
        
        Args:
            X: 特征矩阵
            y: 目标向量
            
        Returns:
            训练结果字典
        """
        pass
    
    @abstractmethod
    def predict_probability(self, X: np.ndarray) -> np.ndarray:
        """
        预测概率分布（独立预测，无依赖）
        
        Args:
            X: 特征矩阵
            
        Returns:
            概率分布数组，shape: (n_samples, 10)
        """
        pass
    
    def load_training_data(self, limit: Optional[int] = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        基于P2系统加载训练数据（独立加载）
        
        Args:
            limit: 限制训练样本数量，None表示使用全部数据
            
        Returns:
            X: 特征矩阵
            y: 目标向量（位置数字）
        """
        if self.feature_interface is None:
            raise RuntimeError("P2特征接口未初始化，无法加载训练数据")
        
        try:
            # 使用P2系统的特征接口
            if self.feature_interface is None:
                # 初始化特征接口
                self.feature_interface = PredictorFeatureInterface(self.db_path, self.position)
            
            # 获取历史期号
            import sqlite3
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            query = "SELECT issue FROM lottery_data ORDER BY issue"
            if limit:
                query += f" LIMIT {limit}"
                
            cursor.execute(query)
            issues = [row[0] for row in cursor.fetchall()]
            conn.close()
            
            self.logger.info(f"加载 {len(issues)} 期数据进行训练")
            
            # 获取特征名称
            self.feature_names = self.feature_interface.get_feature_names()

            # 创建训练数据集
            X_list = []
            y_list = []

            for issue in issues:
                try:
                    # 获取特征
                    features = self.feature_interface.get_prediction_features(issue, len(self.feature_names))

                    # 获取目标值（下一期的对应位置数字）
                    target = self._get_target_value(issue)
                    if target is not None:
                        X_list.append(features)
                        y_list.append(target)

                except Exception as e:
                    self.logger.warning(f"跳过期号 {issue}: {e}")
                    continue

            # 转换为numpy数组
            X = np.array(X_list)
            y = np.array(y_list)

            self.logger.info(f"特征矩阵形状: {X.shape}")
            self.logger.info(f"目标向量形状: {y.shape}")
            self.logger.info(f"特征数量: {len(self.feature_names)}")

            return X, y
            
        except Exception as e:
            self.logger.error(f"加载训练数据失败: {e}")
            raise

    def _get_target_value(self, issue: str) -> Optional[int]:
        """获取指定期号的目标值（下一期对应位置的数字）"""
        try:
            import sqlite3
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取下一期的数据
            next_issue = str(int(issue) + 1)
            query = f"SELECT {self.position} FROM lottery_data WHERE issue = ?"
            cursor.execute(query, (next_issue,))
            result = cursor.fetchone()
            conn.close()

            if result:
                return int(result[0])
            else:
                return None

        except Exception as e:
            self.logger.warning(f"获取目标值失败，期号: {issue}, 错误: {e}")
            return None
    
    def predict_next_period(self, current_issue: str) -> Dict[str, Any]:
        """
        预测下一期的概率分布
        
        Args:
            current_issue: 当前期号
            
        Returns:
            预测结果字典
        """
        if not self.is_trained:
            raise ValueError(f"{self.position}位预测器尚未训练")
        
        try:
            start_time = time.time()
            
            # 获取当前期特征（使用缓存）
            cache_key = f"{self.position}_features_{current_issue}"
            
            if self.cache_optimizer:
                cached_features = self.cache_optimizer.get_cached_features(cache_key)
                if cached_features:
                    features = cached_features
                    self.logger.debug(f"使用缓存特征: {current_issue}")
                else:
                    features = self._get_prediction_features(current_issue)
                    self.cache_optimizer.cache_features(cache_key, features)
            else:
                features = self._get_prediction_features(current_issue)
            
            # 预测概率分布
            X = np.array(features).reshape(1, -1)
            probabilities = self.predict_probability(X)[0]
            
            # 获取最高概率的数字
            predicted_digit = np.argmax(probabilities)
            confidence = probabilities[predicted_digit]
            
            # 获取Top3预测
            top3_indices = np.argsort(probabilities)[-3:][::-1]
            top3_predictions = [(int(idx), float(probabilities[idx])) for idx in top3_indices]
            
            prediction_time = time.time() - start_time
            
            result = {
                'position': self.position,
                'issue': current_issue,
                'probabilities': probabilities.tolist(),
                'predicted_digit': int(predicted_digit),
                'confidence': float(confidence),
                'top3_predictions': top3_predictions,
                'prediction_time': prediction_time,
                'feature_count': len(features),
                'model_type': self.__class__.__name__
            }
            
            self.logger.info(f"预测完成: {current_issue}, 预测数字: {predicted_digit}, 置信度: {confidence:.3f}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            raise
    
    def _get_prediction_features(self, issue: str) -> List[float]:
        """获取预测特征"""
        if self.feature_interface is None:
            raise RuntimeError("P2特征接口未初始化")

        try:
            # 调用P2系统的特征生成方法
            feature_count = len(self.feature_names) if self.feature_names else 50
            features = self.feature_interface.get_prediction_features(issue, feature_count)

            self.logger.debug(f"获取预测特征成功，期号: {issue}, 特征数: {len(features)}")
            return features

        except Exception as e:
            self.logger.error(f"获取预测特征失败: {e}")
            # 返回零特征作为fallback
            feature_count = len(self.feature_names) if self.feature_names else 50
            return [0.0] * feature_count
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'position': self.position,
            'model_type': self.__class__.__name__,
            'is_trained': self.is_trained,
            'feature_count': len(self.feature_names),
            'training_history': self.training_history
        }
    
    def save_model(self, filepath: Optional[str] = None) -> str:
        """
        保存模型
        
        Args:
            filepath: 保存路径，None则使用默认路径
            
        Returns:
            实际保存路径
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练，无法保存")
        
        if filepath is None:
            model_dir = Path(self.training_config.get('model_save_path', f'models/{self.position}/'))
            model_dir.mkdir(parents=True, exist_ok=True)
            filepath = model_dir / f"{self.__class__.__name__}_{self.position}.pkl"
        
        # 子类需要实现具体的保存逻辑
        self.logger.info(f"模型保存到: {filepath}")
        return str(filepath)

    def save_model_with_issue(self, issue: Optional[str] = None) -> str:
        """
        按期号保存模型

        Args:
            issue: 期号，None则自动获取最新期号

        Returns:
            实际保存路径
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练，无法保存")

        # 导入动态期号管理器
        try:
            from src.utils.dynamic_issue_manager import issue_manager
        except ImportError:
            self.logger.error("无法导入动态期号管理器")
            raise

        # 获取期号
        if issue is None:
            issue = issue_manager.get_latest_issue()
            self.logger.info(f"自动获取期号: {issue}")
        else:
            self.logger.info(f"使用指定期号: {issue}")

        # 生成带期号的文件路径
        model_dir = Path(self.training_config.get('model_save_path', f'models/{self.position}/'))
        model_dir.mkdir(parents=True, exist_ok=True)

        # 根据模型类型生成文件名
        class_name = self.__class__.__name__.lower()
        if 'xgb' in class_name:
            algorithm = 'xgb'
        elif 'lgb' in class_name or 'lightgbm' in class_name:
            algorithm = 'lgb'
        elif 'lstm' in class_name:
            algorithm = 'lstm'
        elif 'ensemble' in class_name:
            algorithm = 'ensemble'
        else:
            algorithm = 'unknown'

        # 生成文件路径
        if algorithm == 'lstm':
            # LSTM模型需要特殊处理，不包含扩展名
            filename_base = f"lstm_{self.position}_model_{issue}"
            filepath = model_dir / filename_base
        elif algorithm == 'ensemble':
            # 集成模型需要特殊处理，不包含扩展名
            filename_base = f"ensemble_{self.position}_model_{issue}"
            filepath = model_dir / filename_base
        else:
            # 其他模型使用标准文件名
            filename = issue_manager.generate_model_filename(algorithm, self.position, issue)
            filepath = model_dir / filename

        # 调用原有的保存方法
        result = self.save_model(str(filepath))

        self.logger.info(f"模型已按期号 {issue} 保存: {result}")
        return result

    def load_model_by_issue(self, issue: str) -> bool:
        """
        按期号加载模型

        Args:
            issue: 期号

        Returns:
            是否加载成功
        """
        try:
            # 导入动态期号管理器
            from src.utils.dynamic_issue_manager import issue_manager

            # 根据模型类型确定算法名
            class_name = self.__class__.__name__.lower()
            if 'xgb' in class_name:
                algorithm = 'xgb'
            elif 'lgb' in class_name or 'lightgbm' in class_name:
                algorithm = 'lgb'
            elif 'lstm' in class_name:
                algorithm = 'lstm'
            elif 'ensemble' in class_name:
                algorithm = 'ensemble'
            else:
                algorithm = 'unknown'

            # 获取模型文件路径
            if algorithm == 'lstm':
                # LSTM模型需要特殊处理
                model_dir = Path(f'models/{self.position}/')
                filepath = model_dir / f"lstm_{self.position}_model_{issue}"
                # 检查.h5文件是否存在
                h5_file = Path(f"{filepath}.h5")
                if not h5_file.exists():
                    self.logger.error(f"期号 {issue} 的LSTM模型文件不存在: {h5_file}")
                    return False
            elif algorithm == 'ensemble':
                # 集成模型需要特殊处理
                model_dir = Path(f'models/{self.position}/')
                filepath = model_dir / f"ensemble_{self.position}_model_{issue}"
                # 检查_ensemble.pkl文件是否存在
                ensemble_file = Path(f"{filepath}_ensemble.pkl")
                if not ensemble_file.exists():
                    self.logger.error(f"期号 {issue} 的集成模型文件不存在: {ensemble_file}")
                    return False
            else:
                filepath = issue_manager.get_model_filepath(algorithm, self.position, issue)
                if not filepath.exists():
                    self.logger.error(f"期号 {issue} 的模型文件不存在: {filepath}")
                    return False

            # 调用原有的加载方法
            result = self.load_model(str(filepath))

            if result:
                self.logger.info(f"成功加载期号 {issue} 的模型: {filepath}")

            return result

        except Exception as e:
            self.logger.error(f"按期号加载模型失败: {e}")
            return False

    def load_model(self, filepath: str) -> bool:
        """
        加载模型
        
        Args:
            filepath: 模型文件路径
            
        Returns:
            是否加载成功
        """
        try:
            # 子类需要实现具体的加载逻辑
            self.is_trained = True
            self.logger.info(f"模型加载成功: {filepath}")
            return True
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            return False

    def _register_config_reload(self):
        """注册配置重载信号"""
        try:
            from src.config.unified_config_manager import unified_config_manager
            unified_config_manager.reload_signal.connect(self._reload_config)
            self.logger.info("配置重载信号注册成功")
        except Exception as e:
            self.logger.warning(f"配置重载信号注册失败: {e}")

    def _reload_config(self):
        """热重载配置"""
        try:
            self.logger.info("开始配置热重载...")

            # 1. 重新加载YAML配置
            self._load_config()

            # 2. 🆕 应用feature_config中的优化参数
            self._apply_feature_optimizations()

            # 3. 🆕 重新初始化受影响的模型
            self._reinitialize_affected_models()

            self.logger.info("配置热重载完成")

        except Exception as e:
            self.logger.error(f"配置热重载失败: {e}")

    def _apply_feature_optimizations(self):
        """应用feature_config中的优化参数"""
        try:
            from src.config.feature_config import feature_config_manager

            # 获取算法优化参数
            algorithm_params = feature_config_manager.get_feature_value('algorithm_optimization')
            if algorithm_params:
                self.logger.info(f"应用算法优化参数: {algorithm_params}")

                # 应用学习率调整
                if 'learning_rate' in algorithm_params:
                    self._apply_learning_rate(algorithm_params['learning_rate'])

                # 应用权重调整
                if 'weight_adjustment' in algorithm_params:
                    self._apply_weight_adjustment(algorithm_params['weight_adjustment'])

            # 获取权重调整参数
            weight_params = feature_config_manager.get_feature_value('weight_adjustment')
            if weight_params:
                self.logger.info(f"应用权重调整参数: {weight_params}")
                self._apply_weight_adjustment(weight_params.get('weight_adjustment', 0))

        except Exception as e:
            self.logger.error(f"应用优化参数失败: {e}")

    def _apply_learning_rate(self, learning_rate: float):
        """应用学习率调整"""
        try:
            # 如果有模型实例，更新学习率
            if hasattr(self, 'model') and self.model:
                if hasattr(self.model, 'set_learning_rate'):
                    self.model.set_learning_rate(learning_rate)
                    self.logger.info(f"学习率已更新为: {learning_rate}")
                elif hasattr(self.model, 'learning_rate'):
                    self.model.learning_rate = learning_rate
                    self.logger.info(f"学习率已更新为: {learning_rate}")
        except Exception as e:
            self.logger.error(f"应用学习率失败: {e}")

    def _apply_weight_adjustment(self, adjustment: float):
        """应用权重调整"""
        try:
            # 这里可以根据具体的模型类型实现权重调整
            # 目前记录调整信息
            self.logger.info(f"权重调整: {adjustment}")

            # 如果有集成模型，调整权重
            if hasattr(self, 'ensemble_weights'):
                for key in self.ensemble_weights:
                    self.ensemble_weights[key] *= (1 + adjustment)
                self.logger.info(f"集成权重已调整: {self.ensemble_weights}")

        except Exception as e:
            self.logger.error(f"应用权重调整失败: {e}")

    def _reinitialize_affected_models(self):
        """重新初始化受影响的模型"""
        try:
            # 如果配置发生重大变化，可能需要重新初始化模型
            # 目前只记录信息，具体实现由子类决定
            self.logger.info("检查是否需要重新初始化模型...")

            # 标记需要重新训练（如果配置变化较大）
            if hasattr(self, 'is_trained'):
                # 可以根据配置变化程度决定是否需要重新训练
                pass

        except Exception as e:
            self.logger.error(f"重新初始化模型失败: {e}")
