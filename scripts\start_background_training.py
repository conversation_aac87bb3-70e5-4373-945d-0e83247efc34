#!/usr/bin/env python3
"""
后台训练启动脚本

功能：
- 启动后台训练进程
- 支持多位置并行训练
- 自动日志重定向
- 进程管理和监控

Author: Augment Code AI Assistant
Date: 2025-08-15
"""

import sys
import os
import subprocess
import argparse
import json
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any

class BackgroundTrainingManager:
    """后台训练管理器"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        self.process_file = self.log_dir / "training_processes.json"
        
    def start_position_training(self, position: str, model: str = "all", 
                              db_path: str = "data/fucai3d.db") -> Dict[str, Any]:
        """启动位置训练"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 确定训练脚本
        script_map = {
            "hundreds": "enhanced_train_hundreds.py",
            "tens": "train_tens_predictor.py", 
            "units": "train_units_predictor.py",
            "sum": "train_sum_predictor.py",
            "span": "train_span_predictor.py"
        }
        
        script_name = script_map.get(position)
        if not script_name:
            raise ValueError(f"不支持的位置: {position}")
        
        script_path = Path("scripts") / script_name
        if not script_path.exists():
            raise FileNotFoundError(f"训练脚本不存在: {script_path}")
        
        # 创建日志文件
        log_file = self.log_dir / f"{position}_training_{timestamp}.log"
        
        # 构建命令
        cmd = [
            sys.executable,
            str(script_path),
            "--model", model,
            "--db-path", db_path,
            "--log-dir", str(self.log_dir)
        ]
        
        if script_name == "enhanced_train_hundreds.py":
            cmd.extend(["--background", "--verify-data"])
        
        print(f"🚀 启动{position.title()}位预测器后台训练...")
        print(f"命令: {' '.join(cmd)}")
        print(f"日志文件: {log_file}")
        
        # 启动后台进程
        try:
            with open(log_file, 'w', encoding='utf-8') as f:
                process = subprocess.Popen(
                    cmd,
                    stdout=f,
                    stderr=subprocess.STDOUT,
                    cwd=Path.cwd(),
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
                )
            
            # 记录进程信息
            process_info = {
                "position": position,
                "pid": process.pid,
                "start_time": datetime.now().isoformat(),
                "command": " ".join(cmd),
                "log_file": str(log_file),
                "status": "running",
                "model": model
            }
            
            self._save_process_info(process_info)
            
            print(f"✅ 后台训练已启动")
            print(f"进程ID: {process.pid}")
            print(f"查看进度: python scripts/check_training_status.py --position {position}")
            print(f"查看日志: tail -f {log_file}")
            
            return process_info
            
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            raise
    
    def _save_process_info(self, process_info: Dict[str, Any]):
        """保存进程信息"""
        processes = {}
        if self.process_file.exists():
            try:
                with open(self.process_file, 'r', encoding='utf-8') as f:
                    processes = json.load(f)
            except:
                pass
        
        processes[process_info["position"]] = process_info
        
        with open(self.process_file, 'w', encoding='utf-8') as f:
            json.dump(processes, f, indent=2, ensure_ascii=False)
    
    def list_running_processes(self) -> Dict[str, Any]:
        """列出运行中的进程"""
        if not self.process_file.exists():
            return {}
        
        try:
            with open(self.process_file, 'r', encoding='utf-8') as f:
                processes = json.load(f)
            
            # 检查进程是否还在运行
            running_processes = {}
            for position, info in processes.items():
                pid = info.get("pid")
                if pid and self._is_process_running(pid):
                    running_processes[position] = info
                else:
                    # 更新状态为已完成
                    info["status"] = "completed"
                    info["end_time"] = datetime.now().isoformat()
            
            # 保存更新后的进程信息
            with open(self.process_file, 'w', encoding='utf-8') as f:
                json.dump(processes, f, indent=2, ensure_ascii=False)
            
            return running_processes
            
        except Exception as e:
            print(f"❌ 读取进程信息失败: {e}")
            return {}
    
    def _is_process_running(self, pid: int) -> bool:
        """检查进程是否运行"""
        try:
            if os.name == 'nt':  # Windows
                result = subprocess.run(
                    ['tasklist', '/FI', f'PID eq {pid}'],
                    capture_output=True, text=True
                )
                return str(pid) in result.stdout
            else:  # Unix/Linux
                os.kill(pid, 0)
                return True
        except:
            return False
    
    def stop_training(self, position: str) -> bool:
        """停止训练"""
        processes = self.list_running_processes()
        
        if position not in processes:
            print(f"❌ {position.title()}位预测器没有运行中的训练任务")
            return False
        
        pid = processes[position]["pid"]
        
        try:
            if os.name == 'nt':  # Windows
                subprocess.run(['taskkill', '/PID', str(pid), '/F'], check=True)
            else:  # Unix/Linux
                os.kill(pid, 15)  # SIGTERM
                time.sleep(2)
                if self._is_process_running(pid):
                    os.kill(pid, 9)  # SIGKILL
            
            print(f"✅ {position.title()}位预测器训练已停止")
            return True
            
        except Exception as e:
            print(f"❌ 停止训练失败: {e}")
            return False

def display_running_processes(manager: BackgroundTrainingManager):
    """显示运行中的进程"""
    processes = manager.list_running_processes()
    
    if not processes:
        print("📋 当前没有运行中的训练任务")
        return
    
    print("🔄 运行中的训练任务:")
    print("=" * 60)
    
    for position, info in processes.items():
        start_time = datetime.fromisoformat(info["start_time"])
        elapsed = datetime.now() - start_time
        
        print(f"📍 {position.title()}位预测器")
        print(f"   进程ID: {info['pid']}")
        print(f"   模型: {info['model']}")
        print(f"   运行时间: {elapsed}")
        print(f"   日志文件: {info['log_file']}")
        print()

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='后台训练启动脚本')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 启动训练命令
    start_parser = subparsers.add_parser('start', help='启动后台训练')
    start_parser.add_argument(
        'position',
        choices=['hundreds', 'tens', 'units', 'sum', 'span'],
        help='预测器位置'
    )
    start_parser.add_argument(
        '--model', '-m',
        default='all',
        help='训练模型 (默认: all)'
    )
    start_parser.add_argument(
        '--db-path', '-d',
        default='data/fucai3d.db',
        help='数据库路径'
    )
    
    # 列出进程命令
    subparsers.add_parser('list', help='列出运行中的训练任务')
    
    # 停止训练命令
    stop_parser = subparsers.add_parser('stop', help='停止训练任务')
    stop_parser.add_argument(
        'position',
        choices=['hundreds', 'tens', 'units', 'sum', 'span'],
        help='要停止的预测器位置'
    )
    
    # 全局参数
    parser.add_argument(
        '--log-dir', '-l',
        default='logs',
        help='日志目录 (默认: logs)'
    )
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_arguments()
    
    if not args.command:
        print("❌ 请指定命令: start, list, stop")
        print("使用 --help 查看详细帮助")
        return 1
    
    manager = BackgroundTrainingManager(args.log_dir)
    
    try:
        if args.command == 'start':
            manager.start_position_training(
                args.position, 
                args.model, 
                args.db_path
            )
            
        elif args.command == 'list':
            display_running_processes(manager)
            
        elif args.command == 'stop':
            manager.stop_training(args.position)
        
        return 0
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
