#!/usr/bin/env python3
"""
测试训练详细信息捕获功能
"""

import subprocess
import json
import sys
import os

def test_training_info_capture():
    """测试训练详细信息的捕获"""
    print("🔍 测试训练详细信息捕获功能")
    
    # 模拟训练工作器的命令执行
    cmd = [
        sys.executable, "-u", "-W", "ignore",
        "scripts/ultra_light_train.py",
        "--position", "units",
        "--limit", "500", 
        "--issue", "2025230"
    ]
    
    print(f"🚀 执行命令: {' '.join(cmd)}")
    
    try:
        # 启动进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='replace',
            cwd=os.getcwd(),
            bufsize=1,
            universal_newlines=True
        )
        
        # 监控进程输出
        output_lines = []
        training_info_found = False
        
        while process.poll() is None:
            # 读取输出
            if process.stdout:
                try:
                    line = process.stdout.readline()
                    if line:
                        line_stripped = line.strip()
                        output_lines.append(line_stripped)
                        print(f"📝 输出: {line_stripped}")
                        
                        # 检查是否包含训练信息
                        if "[TRAINING_INFO]" in line_stripped:
                            print(f"🔍 发现训练信息: {line_stripped[:100]}...")
                            training_info_found = True
                            
                            try:
                                json_str = line_stripped.replace("[TRAINING_INFO] ", "")
                                print(f"🔍 JSON字符串: {json_str[:200]}...")
                                training_info = json.loads(json_str)
                                print(f"✅ JSON解析成功，包含键: {list(training_info.keys())}")
                                
                                # 显示关键信息
                                print(f"📊 数据范围: {training_info.get('data_range', {})}")
                                print(f"⚙️ 模型参数: {training_info.get('model_params', {})}")
                                print(f"📈 训练结果: {training_info.get('training_results', {})}")
                                
                            except json.JSONDecodeError as je:
                                print(f"❌ JSON解析失败: {je}")
                                print(f"❌ JSON字符串: {json_str}")
                                
                except UnicodeDecodeError:
                    output_lines.append("[编码错误：输出包含非UTF-8字符]")
                    print("❌ 编码错误")
        
        # 等待进程完成
        process.wait()
        
        # 读取剩余输出
        remaining_stdout, remaining_stderr = process.communicate()
        if remaining_stdout:
            for line in remaining_stdout.split('\n'):
                if line.strip():
                    output_lines.append(line.strip())
                    print(f"📝 剩余输出: {line.strip()}")
                    
                    if "[TRAINING_INFO]" in line.strip():
                        print(f"🔍 发现剩余训练信息: {line.strip()[:100]}...")
                        training_info_found = True
        
        print(f"\n📊 测试结果:")
        print(f"   - 进程返回码: {process.returncode}")
        print(f"   - 输出行数: {len(output_lines)}")
        print(f"   - 训练信息捕获: {'✅ 成功' if training_info_found else '❌ 失败'}")
        
        if not training_info_found:
            print("\n🔍 所有输出行:")
            for i, line in enumerate(output_lines):
                print(f"   {i+1}: {line}")
        
        return training_info_found
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_training_info_capture()
    sys.exit(0 if success else 1)
