{"learner": {"attributes": {}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "500"}, "iteration_indptr": [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130, 140, 150, 160, 170, 180, 190, 200, 210, 220, 230, 240, 250, 260, 270, 280, 290, 300, 310, 320, 330, 340, 350, 360, 370, 380, 390, 400, 410, 420, 430, 440, 450, 460, 470, 480, 490, 500], "tree_info": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "trees": [{"base_weights": [-0.0435035, -0.08906251, 0.010316349, 0.096810915, -0.103833884, -0.02678572, 0.06996269, 0.05463576, -0.10026739], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.50190216, 0.0, 0.29646766, 0.4809438, 0.90090865, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.08906251, 6.0, 5.0, 4.0, -0.02678572, 0.06996269, 0.05463576, -0.10026739], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.48, 5.3999996, 28.079998, 16.56, 11.5199995, 6.8399997, 9.719999, 5.04, 6.4799995], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.026946127, -0.20526318, 0.18831167, -5.334545e-09, -0.09810673, 0.08035714, 0.024532706], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [1.3223786, 0.44215864, 0.073639154, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 8.0, -5.334545e-09, -0.09810673, 0.08035714, 0.024532706], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.399998, 18.0, 14.4, 7.3799996, 10.62, 6.8399997, 7.5599995], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.025568202, 0.14468083, -0.103937015, 0.09863014, 0.05232556, -0.048816577, 0.055069927], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.9933128, 0.34421924, 0.0, 0.0, 0.51739055, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 2.0, -0.103937015, 0.09863014, 5.0, -0.048816577, 0.055069927], "split_indices": [0, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.199997, 22.499998, 11.7, 6.2999997, 16.199999, 5.7599998, 10.44], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.21015286, -0.29065746, 0.02059496, -0.38425493, -0.0035046784, -0.1480076, -0.07372882], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.86453533, 0.7105365, 0.0, 0.1272862, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 7.0, 0.02059496, 4.0, -0.0035046784, -0.1480076, -0.07372882], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.64, 27.9, 7.74, 20.339998, 7.5599995, 9.54, 10.799999], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.041345116, -0.3494927, 0.23541452, -0.05753425, -0.123251766, 0.22968751, -0.014144291, -0.04372198, 0.04823151], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [3.1877437, 0.03258276, 2.6714845, 0.0, 0.0, 0.0, 0.34740472, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [5.0, 4.0, 4.0, -0.05753425, -0.123251766, 0.22968751, 8.0, -0.04372198, 0.04823151], "split_indices": [0, 2, 2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.28, 16.74, 18.539999, 6.2999997, 10.44, 5.3999996, 13.139999, 7.9199996, 5.22], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.18841393, -0.034246594, 0.46526054, -0.06436568, 0.04197761, 0.09357798, 0.19292605], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [2.251053, 0.6793856, 0.14344358, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 5.0, -0.06436568, 0.04197761, 0.09357798, 0.19292605], "split_indices": [0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.559998, 19.439999, 15.119999, 9.719999, 9.719999, 9.9, 5.22], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.09743874, -0.060160436, 0.17323367, 0.2555762, -0.010089692, 0.044709384, 0.11208791], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.8432584, 0.0, 0.5322671, 0.16272318, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.060160436, 8.0, 5.0, -0.010089692, 0.044709384, 0.11208791], "split_indices": [2, 0, 2, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.92, 6.4799995, 28.439999, 20.519999, 7.9199996, 12.419999, 8.099999], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.043962467, 0.28263795, -0.1283317, 0.02106741, 0.123569794, 0.007832893, -0.21323532, -0.008645539, -0.10182769], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [1.4547981, 0.3308704, 0.28994185, 0.0, 0.0, 0.0, 0.2698878, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [5.0, 2.0, 2.0, 0.02106741, 0.123569794, 0.007832893, 7.0, -0.008645539, -0.10182769], "split_indices": [2, 2, 1, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.12, 13.86, 19.259998, 6.12, 7.74, 6.66, 12.599999, 5.9399996, 6.66], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.2727784, 0.47821465, 0.03382185, -0.03476822, 0.2133527, 0.07372881, -0.09510087], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [1.6782749, 2.7467146, 1.389825, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 5.0, -0.03476822, 0.2133527, 0.07372881, -0.09510087], "split_indices": [0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.559998, 17.82, 16.74, 5.04, 12.78, 10.799999, 5.9399996], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.13706456, -0.038797304, -0.07597404, -0.06436568, 0.038532108], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.36079037, 0.6422488, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [6.0, 3.0, -0.07597404, -0.06436568, 0.038532108], "split_indices": [2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [34.019997, 19.619999, 14.4, 9.719999, 9.9], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.06681099, -0.0, -0.1935217, -0.028473008, 0.06949815, 0.0046281624, -0.10231602, 0.03370668, 0.0011885703], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.3230297, 0.16423377, 0.43661463, 0.0, 0.030322812, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 4.0, -0.028473008, 3.0, 0.0046281624, -0.10231602, 0.03370668, 0.0011885703], "split_indices": [1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.170242, 23.233665, 11.936576, 9.137906, 14.095759, 5.0300856, 6.90649, 7.16798, 6.9277797], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.15073666, -0.23896612, 0.018981826, -0.020902341, -0.3212045, -0.08645421, 0.08170909, -0.0572217, -0.13607538], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.56963557, 0.27718902, 1.084362, 0.0, 0.120158195, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 3.0, -0.020902341, 6.0, -0.08645421, 0.08170909, -0.0572217, -0.13607538], "split_indices": [0, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.2692, 23.324814, 11.944387, 8.627629, 14.697185, 5.15872, 6.785667, 9.011881, 5.685303], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.107969366, -0.028128164, -0.07682882, -0.16865459, 0.03973002, -0.015730312, -0.0879727], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.4044833, 0.6067833, 0.0, 0.16000453, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 5.0, -0.07682882, 2.0, 0.03973002, -0.015730312, -0.0879727], "split_indices": [0, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.213455, 24.74223, 11.471224, 13.499569, 11.242661, 8.134709, 5.364859], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.07735232, -0.20601839, 0.0898334, 0.00038230894, -0.11774185, 0.0061609256, 0.049762502], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.73949313, 0.784681, 0.05541312, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 8.0, 0.00038230894, -0.11774185, 0.0061609256, 0.049762502], "split_indices": [1, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.976759, 18.276802, 13.699956, 8.871101, 9.405702, 8.520412, 5.1795435], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.07256013, -0.3131434, 0.18506576, -0.12664087, -0.025182161, 0.118898675, -0.055430662], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [2.3364077, 0.40045977, 1.5069969, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 6.0, 5.0, -0.12664087, -0.025182161, 0.118898675, -0.055430662], "split_indices": [0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.55422, 18.47451, 17.07971, 11.654428, 6.820082, 10.996727, 6.082982], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.12904564, -0.08049579, 0.25583827, 0.47091118, 0.012636732, 0.019535746, 0.22103554, 0.10219197, -0.055473875], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [1.9318074, 0.0, 1.4498115, 1.4917672, 1.0337523, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.08049579, 6.0, 6.0, 6.0, 0.019535746, 0.22103554, 0.10219197, -0.055473875], "split_indices": [0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.00476, 8.248689, 27.756075, 14.0147705, 13.741303, 6.1893196, 7.8254504, 5.0832176, 8.658086], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.050767746, 0.2089085, -0.035986643, -0.043319304, 0.14153568, 0.05625326, -0.13115619, -0.1153816, 0.03825503], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.53139067, 1.3673027, 0.5355798, 0.0, 0.0, 0.0, 1.250714, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 2.0, 4.0, -0.043319304, 0.14153568, 0.05625326, 7.0, -0.1153816, 0.03825503], "split_indices": [0, 0, 2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.924385, 12.635792, 23.288593, 5.420692, 7.2151, 6.3515306, 16.937061, 8.504276, 8.432786], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.10509687, 0.18763307, -0.08951012, 0.29781595, 0.0069179, 0.1140945, 0.036846787, -0.0038776114, 0.011064982], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1.2764039, 0.5950959, 0.0, 0.18036413, 0.010784737, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, -0.08951012, 4.0, 3.0, 0.1140945, 0.036846787, -0.0038776114, 0.011064982], "split_indices": [1, 2, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.981377, 30.520605, 5.46077, 18.214062, 12.306542, 11.192634, 7.021429, 5.738163, 6.5683794], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.03894005, 0.18048391, -0.14870644, 0.12419087, -0.0, -0.14340758, 0.051730577, -0.004296565, 0.0030938198], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [1.0241095, 0.9371636, 1.8408166, 0.0, 0.0021909147, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 6.0, 0.12419087, 3.0, -0.14340758, 0.051730577, -0.004296565, 0.0030938198], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.42789, 21.090315, 15.337574, 8.667594, 12.422721, 7.4849296, 7.852645, 5.652463, 6.770258], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.053427927, -0.0, -0.08510817, -0.06353, 0.038374472, 0.040724155, -0.085528776], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.39964896, 0.23481008, 0.0, 0.9231686, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 7.0, -0.08510817, 3.0, 0.038374472, 0.040724155, -0.085528776], "split_indices": [1, 2, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.334538, 27.186548, 5.1479897, 18.693619, 8.49293, 9.808138, 8.88548], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.043862898, 0.030434782, -0.08008286, -0.087186925, -0.015882222, -0.031439804, 0.013039184], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.18805563, 0.0, 0.3540219, 0.0, 0.13465379, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.030434782, 4.0, -0.087186925, 5.0, -0.031439804, 0.013039184], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.592617, 5.7923307, 27.800285, 5.3081794, 22.492107, 9.370946, 13.121161], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.14316347, -0.3127182, 0.08136371, -0.12645963, -0.021624804, -0.0, 0.05034152], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [1.3160338, 0.42898726, 0.113972574, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 7.0, 8.0, -0.12645963, -0.021624804, -0.0, 0.05034152], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.293037, 18.42884, 13.864195, 11.886522, 6.54232, 7.322101, 6.542094], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.13211966, -0.015928917, -0.11397607, -0.048535634, 0.067570016, 0.09939932, -0.027714415], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.8843698, 0.30422327, 0.0, 0.0, 0.69122595, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 3.0, -0.11397607, -0.048535634, 6.0, 0.09939932, -0.027714415], "split_indices": [0, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.300718, 22.308405, 8.992314, 8.216249, 14.092155, 5.151325, 8.940831], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.09546061, -0.31498295, 0.0999805, 0.0068704668, -0.145909, 0.05311166, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [1.4317517, 0.96184576, 0.14460278, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 1.0, 6.0, 0.0068704668, -0.145909, 0.05311166, -0.0], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.136147, 14.617531, 16.518616, 5.0143585, 9.603172, 9.200782, 7.317833], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.03095649, -0.19491501, 0.19957605, -0.09257147, -0.017341005, 0.028662669, 0.094768204], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [1.3826218, 0.28122026, 0.110446095, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 9.0, -0.09257147, -0.017341005, 0.028662669, 0.094768204], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.562607, 20.54146, 14.021145, 10.148871, 10.39259, 8.816679, 5.2044654], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.1119584, -0.119174056, 0.2284503, -0.053508252, -0.00800173, 0.02153135, 0.08554434], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [1.053947, 0.04597871, 0.17227423, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 5.0, 6.0, -0.053508252, -0.00800173, 0.02153135, 0.08554434], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.726715, 12.012054, 24.71466, 6.0998425, 5.9122124, 7.6377993, 17.07686], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0071751503, -0.085995585, 0.064971425, 0.22282173, -0.095551066, -0.0, 0.115308784, 0.024304604, -0.0637314], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.6400281, 0.0, 0.8642992, 0.62342614, 0.37361008, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.085995585, 6.0, 5.0, 5.0, -0.0, 0.115308784, 0.024304604, -0.0637314], "split_indices": [0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.89706, 5.1295137, 31.767546, 16.135506, 15.632039, 7.1568313, 8.978675, 6.0079484, 9.62409], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.038287107, 0.14950763, -0.15918733, -0.05350483, -0.08590306, -0.09055763, 0.018408364], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [2.4958997, 0.0, 0.3771507, 0.5843077, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.14950763, 6.0, 2.0, -0.08590306, -0.09055763, 0.018408364], "split_indices": [0, 0, 2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.76913, 6.0344634, 30.734667, 17.989983, 12.744684, 5.449715, 12.540268], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.18926196, 0.2629545, 0.014822308, -0.0, 0.34914693, -0.034569234, 0.037860088, 0.043587692, 0.15196212], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.47195935, 0.5905827, 0.20536876, 0.0, 0.50949955, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 6.0, -0.0, 4.0, -0.034569234, 0.037860088, 0.043587692, 0.15196212], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.178425, 26.080118, 12.098309, 6.8753057, 19.204813, 5.151087, 6.9472213, 9.482118, 9.722694], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.03158764, -0.08301188, 0.039892606, -0.005739622, -0.0907052, 0.034152877, -0.05194874], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.29275936, 0.42325634, 0.0, 0.45095322, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 5.0, 0.039892606, 6.0, -0.0907052, 0.034152877, -0.05194874], "split_indices": [1, 1, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.8808, 25.892796, 6.9880004, 20.243254, 5.649542, 11.647039, 8.596214], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.052334346, 0.037262406, -0.1067373, -0.09660491, 0.13422103, 0.056753315, -0.12096676], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.17290676, 2.013529, 1.9953789, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 4.0, -0.09660491, 0.13422103, 0.056753315, -0.12096676], "split_indices": [0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.231655, 11.601675, 20.629978, 6.1688657, 5.4328094, 10.337254, 10.292726], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.051646095, -0.11738181, 0.27694452, 0.14459564, 0.02293805], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 31, "left_children": [1, -1, 3, -1, -1], "loss_changes": [3.8325095, 0.0, 0.62030935, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [6.0, -0.11738181, 4.0, 0.14459564, 0.02293805], "split_indices": [2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [32.268627, 15.923694, 16.344934, 7.139882, 9.205052], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.008613503, -0.09095368, 0.23176838, 0.12349656, 0.087179415, 0.065282755, -0.012419551], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [2.4719226, 0.0, 0.45866, 0.0, 0.247686, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [5.0, -0.09095368, 7.0, 0.12349656, 5.0, 0.065282755, -0.012419551], "split_indices": [2, 0, 2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.620087, 13.688194, 19.931894, 7.7505493, 12.181344, 6.089474, 6.0918694], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.03626939, 0.19788742, -0.19765809, -0.0, 0.10377492, -0.09852158, -0.027504195], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [1.1890619, 0.36323637, 0.1879288, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 4.0, -0.0, 0.10377492, -0.09852158, -0.027504195], "split_indices": [2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.430344, 11.662947, 17.767397, 5.483241, 6.179705, 6.5921125, 11.175285], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.12206867, -0.13940977, 0.243304, -0.020984676, -0.06440436, -0.014560441, 0.13337885, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.5850015, 0.46144554, 0.07293758, 0.56936365, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 8.0, 3.0, -0.020984676, -0.06440436, -0.014560441, 0.13337885, -0.0], "split_indices": [2, 1, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.380608, 17.346384, 15.034222, 10.7884865, 6.557899, 6.8760333, 8.158189, 5.4108467, 5.3776393], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.028333617, -0.07601195, 0.11040897, 0.032835923, 0.110407345, -0.021203961, 0.07090782], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.9258658, 0.0, 0.57778, 0.57311064, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.07601195, 8.0, 8.0, 0.110407345, -0.021203961, 0.07090782], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.452465, 7.929751, 30.522713, 24.62368, 5.899033, 16.428974, 8.194705], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.05210521, -0.055126037, 0.13628753, 0.26035184, -0.026561925, 0.14272718, 0.021548385, -0.09050138, 0.06644779], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.74829143, 0.0, 0.5941348, 0.5722338, 0.93648934, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.055126037, 6.0, 6.0, 8.0, 0.14272718, 0.021548385, -0.09050138, 0.06644779], "split_indices": [2, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.665325, 8.757686, 26.907639, 15.281219, 11.626419, 6.1734595, 9.107759, 5.6012454, 6.025174], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.08722026, 0.19380723, -0.059346344, 0.07797678, -0.140784, -0.03611091, 0.056191433], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [3.0648413, 0.0, 1.7353117, 0.52274615, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.19380723, 8.0, 4.0, -0.140784, -0.03611091, 0.056191433], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.236416, 6.7553506, 28.481066, 21.758167, 6.722899, 7.3705664, 14.387601], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.20510869, 0.1205698, 0.17567044, 0.10368956, -0.0, -0.055163484, 0.09728025], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.085779, 0.89397335, 0.0, 0.0, 1.3820472, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 4.0, 0.17567044, 0.10368956, 8.0, -0.055163484, 0.09728025], "split_indices": [1, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.148533, 31.687294, 5.4612374, 10.461036, 21.226257, 13.861323, 7.3649344], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.1932053, -0.012848302, -0.22279046, -0.10274943, -0.15705846, -0.065738834, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.108124256, 0.0, 0.103159666, 0.0, 0.1923491, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, -0.012848302, 3.0, -0.10274943, 7.0, -0.065738834, -0.0], "split_indices": [1, 0, 2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.418379, 6.1231046, 25.295275, 6.9345636, 18.36071, 12.951658, 5.4090524], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.10008476, 0.10322831, -0.23797572, -0.047632873, 0.09367241, -0.099195816, -0.16671558, -0.0, -0.096269876], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [1.0047643, 0.8502093, 0.03476906, 0.0, 0.0, 0.0, 0.34906837, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 4.0, 6.0, -0.047632873, 0.09367241, -0.099195816, 4.0, -0.0, -0.096269876], "split_indices": [0, 1, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.4827, 13.3398905, 20.142809, 5.8138967, 7.5259933, 6.5634117, 13.579398, 7.254532, 6.3248663], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.048183266, -0.24966247, 0.20518391, -0.014209931, -0.33965522, 0.012300829, 0.09404463, -0.13224004, -0.058709536], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [1.7385162, 0.2917297, 0.21718907, 0.0, 0.034561157, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 6.0, -0.014209931, 4.0, 0.012300829, 0.09404463, -0.13224004, -0.058709536], "split_indices": [2, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.965727, 18.034155, 13.931571, 6.364604, 11.669552, 6.452103, 7.4794674, 5.234839, 6.434713], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.02783276, -0.1496994, 0.27064013, 0.11063243, 0.022608466], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 42, "left_children": [1, -1, 3, -1, -1], "loss_changes": [4.875288, 0.0, 0.33126915, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [5.0, -0.1496994, 6.0, 0.11063243, 0.022608466], "split_indices": [2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [32.544773, 12.490556, 20.054218, 12.414791, 7.639428], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.06251914, -0.2036674, 0.13713278, -0.060987514, -0.1077747, 0.07733299, -0.003407652, -0.042858277, 0.006400996], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.93833864, 0.3685705, 0.2709688, 0.09408033, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 5.0, 5.0, -0.1077747, 0.07733299, -0.003407652, -0.042858277, 0.006400996], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.070227, 18.483603, 12.586625, 10.702947, 7.7806544, 6.90999, 5.676635, 5.6065044, 5.096442], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.01268317, 0.20142604, -0.15176131, -0.0068497676, 0.113049984, -0.06564314, -0.076084234, 0.0076955087, -0.051529333], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [1.0475334, 0.5813603, 0.14139637, 0.0, 0.0, 0.15094005, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 3.0, 5.0, -0.0068497676, 0.113049984, 7.0, -0.076084234, 0.0076955087, -0.051529333], "split_indices": [2, 1, 1, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.318436, 12.66047, 20.657965, 5.6880336, 6.9724364, 12.616669, 8.041296, 6.6831765, 5.9334927], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.01772623, -0.06627044, 0.05057175, -0.121931136, 0.2186588, -0.011548845, -0.04828183, 0.14009708, 0.0129690645], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.57425064, 0.0, 0.9031832, 0.02301462, 0.61682063, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.06627044, 4.0, 6.0, 6.0, -0.011548845, -0.04828183, 0.14009708, 0.0129690645], "split_indices": [2, 0, 1, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.350555, 9.454568, 28.895987, 14.10258, 14.793406, 5.942522, 8.160058, 5.200171, 9.593235], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0898081, -0.0037919327, 0.25452346, 0.042377375, -0.14017071, 0.097062156, 0.03378881, -0.061475422, -0.010777426], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.6045842, 0.5048675, 0.05703777, 0.0, 0.059663177, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 7.0, 0.042377375, 6.0, 0.097062156, 0.03378881, -0.061475422, -0.010777426], "split_indices": [1, 1, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.525608, 23.526482, 12.999128, 11.028138, 12.498343, 7.410007, 5.5891213, 6.522327, 5.9760165], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.006610932, 0.28499645, -0.23348954, 0.15752192, 0.0022552656, -0.005786035, -0.30585584, -0.1328651, -0.05439819], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [2.5893576, 1.1476079, 0.2806114, 0.0, 0.0, 0.0, 0.11267316, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [5.0, 5.0, 3.0, 0.15752192, 0.0022552656, -0.005786035, 6.0, -0.1328651, -0.05439819], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.666737, 17.08591, 19.580828, 8.488724, 8.597187, 5.6134634, 13.967363, 5.0504613, 8.916903], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.15364029, 0.24913311, -0.0, 0.12800047, 0.32324454, 0.024447551, -0.04373192, -0.0062860153, 0.083825484, 0.1393172, 0.03640817], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6164668, 0.14599133, 0.21494171, 0.30230927, 0.30963075, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 5.0, 5.0, 4.0, 0.024447551, -0.04373192, -0.0062860153, 0.083825484, 0.1393172, 0.03640817], "split_indices": [0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.97905, 24.897636, 16.081413, 10.998087, 13.8995495, 10.587789, 5.493625, 5.6455746, 5.352513, 7.1235433, 6.776006], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.030151876, 0.050837792, -0.09704048, -0.073539846, 0.19590057, 0.012670872, 0.09283693], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.8052839, 1.1473342, 0.0, 0.0, 0.26658934, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 3.0, -0.09704048, -0.073539846, 5.0, 0.012670872, 0.09283693], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.058823, 24.719185, 6.339638, 7.6768875, 17.042297, 8.199398, 8.842898], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.068310544, -0.24197312, 0.13883908, -0.037407283, -0.09117829, 0.10335636, -0.046706036], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [1.2036729, 0.05122435, 0.9857303, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 5.0, -0.037407283, -0.09117829, 0.10335636, -0.046706036], "split_indices": [2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.238838, 17.164078, 14.07476, 7.53215, 9.6319275, 8.387282, 5.687478], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.033526994, 0.083354965, -0.10915963, -0.006709276, 0.16156727, 0.020061566, 0.07398816], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.2966127, 0.21827942, 0.0, 0.0, 0.06991699, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 2.0, -0.10915963, -0.006709276, 7.0, 0.020061566, 0.07398816], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.06973, 23.278254, 7.791477, 9.658453, 13.619801, 7.846394, 5.7734065], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.032803707, -0.06526767, 0.16000186, 0.10539389, 0.021529393, 0.047307804, -0.045489874], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [1.0829555, 0.0, 0.55878115, 0.0, 0.3684517, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [5.0, -0.06526767, 4.0, 0.10539389, 8.0, 0.047307804, -0.045489874], "split_indices": [2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.094032, 10.308119, 21.785915, 8.199041, 13.586873, 7.979307, 5.607566], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.053456124, 0.053731266, -0.10873921, -0.17377721, 0.024428852, -0.01356841, -0.13248001], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.41769788, 0.0, 0.35036317, 0.6340481, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.053731266, 8.0, 6.0, 0.024428852, -0.01356841, -0.13248001], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.52132, 5.084333, 25.436989, 19.410582, 6.026406, 14.157528, 5.2530546], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0819948, -0.19042362, 0.11777694, -0.29498428, -0.073277555, -0.04606758, -0.106829226, -0.034654297, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1.8551981, 0.30080938, 0.0, 0.021538734, 0.05320344, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, 0.11777694, 4.0, 7.0, -0.04606758, -0.106829226, -0.034654297, -0.0], "split_indices": [0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.9923, 28.329334, 5.662964, 13.71556, 14.613774, 5.692106, 8.023454, 9.270362, 5.343412], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.047814194, -0.0597115, 0.10979346, 0.09001898, 0.05610693, 0.0026189606, 0.033341173], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.5947574, 0.0, 0.26793858, 0.0, 0.055096924, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.0597115, 2.0, 0.09001898, 7.0, 0.0026189606, 0.033341173], "split_indices": [0, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.028675, 6.6636634, 30.365011, 5.2624807, 25.102531, 15.088368, 10.014162], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.05383761, 0.073026314, -0.16220534, -0.21610503, -0.012585026, -0.14208302, -0.017095441], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [1.2767545, 0.0, 0.15425771, 0.7121115, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, 0.073026314, 7.0, 5.0, -0.012585026, -0.14208302, -0.017095441], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.660862, 9.482049, 28.178814, 18.333103, 9.84571, 6.0070243, 12.326079], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.04097409, 0.17491513, -0.1384485, 0.1379883, 0.057587862, 0.042128123, -0.0421364], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [2.431916, 0.88497525, 0.0, 0.0, 0.37727922, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, -0.1384485, 0.1379883, 7.0, 0.042128123, -0.0421364], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.361187, 27.801237, 6.559951, 7.006456, 20.79478, 15.223014, 5.5717673], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.057822883, 0.14714892, -0.053693697, 0.03165132, 0.20738201, -0.0445425, 0.06053551, 0.08355706, 0.0108390795], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.9062972, 0.18638217, 0.0, 0.41631532, 0.19893831, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [7.0, 2.0, -0.053693697, 5.0, 5.0, -0.0445425, 0.06053551, 0.08355706, 0.0108390795], "split_indices": [1, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.75113, 30.170973, 10.580154, 11.433264, 18.737709, 5.3298755, 6.103388, 12.383154, 6.3545547], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.080416776, -0.14340621, 0.028172765, -0.005735318, -0.21904038, -0.026102727, -0.09561422], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.36158985, 0.19548619, 0.0, 0.0, 0.11836231, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 2.0, 0.028172765, -0.005735318, 6.0, -0.026102727, -0.09561422], "split_indices": [1, 1, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.252396, 22.774324, 7.4780703, 9.590054, 13.18427, 6.908818, 6.2754526], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.10041827, 0.037125025, -0.23335093, -0.0023819895, -0.12056319], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 60, "left_children": [1, -1, 3, -1, -1], "loss_changes": [1.0053809, 0.0, 0.7522956, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, 0.037125025, 3.0, -0.0023819895, -0.12056319], "split_indices": [0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [31.401554, 11.43507, 19.966486, 9.243463, 10.723022], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.018150672, 0.10613091, -0.052798003, -0.1339433, 0.020659635, -0.006378911, -0.06088705, -0.037224047, 0.06039543], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.81865317, 0.0, 0.17690687, 0.08025357, 0.41331038, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.10613091, 6.0, 4.0, 5.0, -0.006378911, -0.06088705, -0.037224047, 0.06039543], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.331875, 5.017944, 26.31393, 12.758318, 13.555613, 5.893284, 6.8650346, 7.3925242, 6.163088], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.048922144, -0.06788751, 0.19056547, 0.13672796, 0.058138166, 0.08004816, -0.029126598], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [1.3564442, 0.0, 0.732868, 0.0, 0.5869366, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [5.0, -0.06788751, 2.0, 0.13672796, 7.0, 0.08004816, -0.029126598], "split_indices": [2, 0, 1, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.781918, 10.69597, 22.085949, 6.2910304, 15.794918, 6.7053814, 9.089537], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.043675765, 0.003021541, -0.061647255, -0.04499878, 0.095776774, 0.056028504, 0.008689036], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.25455672, 0.35676345, 0.0, 0.0, 0.07195169, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 4.0, -0.061647255, -0.04499878, 2.0, 0.056028504, 0.008689036], "split_indices": [1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.92625, 23.506096, 6.4201546, 8.231479, 15.274617, 5.073565, 10.201052], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.011995034, -0.15575816, 0.1649777, -0.0010079885, -0.10393277, 0.09116299, 0.008515427, -0.05088091, 0.0506942], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.892247, 0.5444299, 0.25140053, 0.37645996, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 4.0, 3.0, -0.10393277, 0.09116299, 0.008515427, -0.05088091, 0.0506942], "split_indices": [0, 2, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.09587, 18.639269, 14.456604, 11.133542, 7.505727, 6.1975937, 8.25901, 5.886171, 5.2473707], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.06430664, -0.005503502, 0.1007766, 0.20613867, -0.1197708, 0.00485786, 0.103674166, -0.096115544, -0.0058748135], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.76371026, 0.7722493, 0.0, 0.25020802, 0.38673472, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 2.0, 0.1007766, 1.0, 3.0, 0.00485786, 0.103674166, -0.096115544, -0.0058748135], "split_indices": [1, 1, 0, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.436813, 30.268755, 7.168057, 10.023819, 20.244936, 5.019541, 5.004278, 5.678493, 14.566443], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.013926804, -0.10816231, 0.09779716, 0.018654978, -0.08296552, 0.056334898, -0.0135361105, -0.007822002, 0.022206048], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 66, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.40764228, 0.48719212, 0.24458732, 0.03838511, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 7.0, 1.0, -0.08296552, 0.056334898, -0.0135361105, -0.007822002, 0.022206048], "split_indices": [1, 1, 2, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.615314, 20.365025, 16.25029, 11.784925, 8.5801, 10.208466, 6.0418243, 5.8122287, 5.9726963], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.016743436, 0.10693044, -0.088492416, 0.12833735, -0.033423368, -0.072671846, 0.0382422, -0.0478717, 0.010909792], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 67, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.3891607, 1.0626576, 0.6536015, 0.0, 0.16159624, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 6.0, 0.12833735, 1.0, -0.072671846, 0.0382422, -0.0478717, 0.010909792], "split_indices": [1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.812252, 21.403564, 17.408688, 6.1069946, 15.29657, 10.368621, 7.0400667, 5.5004787, 9.796091], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.097419105, 0.12923746, 0.025814578, -0.06911133, 0.1237347, -0.0, 0.07126702], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 68, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.9847716, 0.0, 0.9517465, 0.0, 0.3719863, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.12923746, 4.0, -0.06911133, 4.0, -0.0, 0.07126702], "split_indices": [0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [42.632614, 6.404071, 36.228542, 9.361756, 26.866787, 13.714934, 13.151853], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.09925337, -0.05066117, -0.08472221, -0.107064135, 0.039024584, 0.0060544577, -0.04578176], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 69, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.2422941, 0.2908962, 0.0, 0.1429376, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 6.0, -0.08472221, 3.0, 0.039024584, 0.0060544577, -0.04578176], "split_indices": [1, 1, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.574461, 26.39441, 5.1800523, 20.849117, 5.5452914, 5.0632453, 15.785872], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.034014404, -0.12450256, 0.0377929, 0.033344958, -0.23159209, -0.10564711, -0.013231155], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 70, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.45183527, 0.5463327, 0.0, 0.0, 0.27196115, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 3.0, 0.0377929, 0.033344958, 7.0, -0.10564711, -0.013231155], "split_indices": [0, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.136782, 19.151726, 9.985056, 5.6803346, 13.471392, 7.339833, 6.1315594], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.034787793, 0.12072448, -0.06763531, -0.0024285845, 0.18020336, 0.07705323, 0.007834888], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 71, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.7554793, 0.21613786, 0.0, 0.0, 0.179991, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 4.0, -0.06763531, -0.0024285845, 8.0, 0.07705323, 0.007834888], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.032215, 24.772232, 7.259981, 7.566748, 17.205484, 10.611577, 6.5939074], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.018595189, -0.10301232, 0.08834196, 0.14896278, -0.01937035, 0.06594638, -0.00048040683], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 72, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [1.1901406, 0.0, 0.2510336, 0.22241461, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [4.0, -0.10301232, 7.0, 8.0, -0.01937035, 0.06594638, -0.00048040683], "split_indices": [2, 0, 1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.659704, 7.526148, 24.133556, 17.7689, 6.364657, 12.221436, 5.5474644], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.032163955, 0.04514594, -0.13601804, -0.036549326, 0.08676072, 0.005274477, -0.07183809], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 73, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.25849363, 0.76789016, 0.24456239, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 5.0, -0.036549326, 0.08676072, 0.005274477, -0.07183809], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.298586, 16.567785, 12.730801, 9.891409, 6.6763763, 5.0682917, 7.6625085], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.008084654, 0.2107744, -0.18593192, 0.02740795, 0.09050681, -0.10936451, -0.08952582, -0.059564583, 0.001666572], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 74, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [1.2759025, 0.09043723, 0.08460903, 0.0, 0.0, 0.15388669, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 2.0, 8.0, 0.02740795, 0.09050681, 6.0, -0.08952582, -0.059564583, 0.001666572], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.836744, 13.489332, 17.34741, 7.172825, 6.316508, 12.024117, 5.3232937, 6.850335, 5.1737823], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.070474856, -0.049957242, 0.11894476, 0.16872251, 0.023669086, 0.022796903, 0.057752684, -0.04470835, 0.04659244], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 75, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.4869871, 0.0, 0.1429553, 0.0074098706, 0.33926404, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.049957242, 6.0, 6.0, 8.0, 0.022796903, 0.057752684, -0.04470835, 0.04659244], "split_indices": [0, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.08339, 6.041932, 34.04146, 21.163761, 12.877696, 6.0349374, 15.128823, 5.1844645, 7.693232], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0028721713, 0.061115842, -0.067449875, -0.13430005, 0.022365334, -0.090343684, -0.008503887, 0.03358568, -0.01484491], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 76, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.58714134, 0.0, 0.19274509, 0.27369922, 0.0948717, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, 0.061115842, 7.0, 5.0, 5.0, -0.090343684, -0.008503887, 0.03358568, -0.01484491], "split_indices": [0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.63281, 9.895443, 28.737366, 16.829294, 11.9080715, 5.4543204, 11.374973, 5.670827, 6.2372446], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.026689768, 0.20053999, -0.15692526, -0.0087205535, 0.3388187, -0.0, -0.088229835, 0.035234243, 0.13562495], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 77, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [1.2817239, 0.700168, 0.4256171, 0.0, 0.2237277, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 4.0, -0.0087205535, 2.0, -0.0, -0.088229835, 0.035234243, 0.13562495], "split_indices": [2, 2, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.029873, 19.766766, 18.26311, 7.4548206, 12.311944, 8.858036, 9.405073, 5.1555095, 7.156435], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.00032334874, -0.05426776, 0.06640965, -0.16432627, 0.063183546, -0.020915104, -0.10805092], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 78, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.55856574, 1.0693328, 0.0, 0.4139343, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, 0.06640965, 6.0, 0.063183546, -0.020915104, -0.10805092], "split_indices": [1, 2, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [42.908665, 34.65256, 8.256106, 25.01562, 9.63694, 18.125862, 6.889758], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.099320166, -0.09793918, -0.01893423, 0.10635122, -0.09646034, -0.0, 0.08946268], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 79, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.51568854, 0.0, 0.9615788, 0.3728076, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.09793918, 8.0, 7.0, -0.09646034, -0.0, 0.08946268], "split_indices": [0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.472904, 6.578646, 22.894258, 16.415733, 6.478526, 11.008459, 5.4072733], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.08642786, -0.0005746192, -0.06915573, -0.042591278, 0.041684583], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 80, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.36604816, 0.43741137, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 4.0, -0.06915573, -0.042591278, 0.041684583], "split_indices": [2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [30.468718, 20.156784, 10.3119335, 10.358453, 9.79833], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.116737515, -0.28658867, 0.020124746, -0.106337175, -0.03865756, 0.012924612, -8.264888e-05], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 81, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.756154, 0.05890453, 0.012652055, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 7.0, 7.0, -0.106337175, -0.03865756, 0.012924612, -8.264888e-05], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.133678, 13.284182, 16.849495, 7.9725137, 5.311668, 9.711162, 7.1383343], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.004483865, 0.06404504, -0.067456424, -0.06250324, 0.06992343, -0.0, 0.039543845], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 82, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.4339062, 0.0, 0.52493703, 0.0, 0.07058987, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.06404504, 5.0, -0.06250324, 6.0, -0.0, 0.039543845], "split_indices": [1, 0, 1, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.626795, 5.9732947, 24.653502, 12.275703, 12.377797, 5.5504165, 6.8273807], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.07338105, -0.09046241, 0.18842556, -0.04241498, -0.0042202766, 0.084523134, 0.0043590865], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 83, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.585205, 0.03123483, 0.2597682, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 5.0, 8.0, -0.04241498, -0.0042202766, 0.084523134, 0.0043590865], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.62251, 11.536692, 17.08582, 5.6266055, 5.9100866, 10.341149, 6.74467], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.022348952, -0.08485287, 0.091111645, 0.21494715, -0.05533898, 0.13008717, 0.016316375, -0.08508708, 0.03144006], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 84, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.7075734, 0.0, 0.5408107, 0.48587513, 0.5368915, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.08485287, 5.0, 3.0, 7.0, 0.13008717, 0.016316375, -0.08508708, 0.03144006], "split_indices": [1, 0, 1, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.402386, 5.1182566, 27.28413, 14.900067, 12.384063, 5.3245974, 9.57547, 5.0452347, 7.338828], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.064609334, -0.18983576, 0.20170371, -0.0, -0.10858148, 0.15677777, 0.0025687446, 0.054731444, -0.030159684], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 85, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [1.4828447, 0.49881524, 1.711683, 0.0, 0.0, 0.0, 0.36490893, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 2.0, 6.0, -0.0, -0.10858148, 0.15677777, 5.0, 0.054731444, -0.030159684], "split_indices": [2, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.37739, 13.747712, 26.62968, 6.8828807, 6.864832, 9.383115, 17.246567, 6.3568535, 10.889712], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.11837045, 0.10818442, 0.019190628, -0.057857253, 0.061333586, -0.046469215, 0.047683943], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 86, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.8165632, 0.0, 0.4088632, 0.43064976, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, 0.10818442, 8.0, 8.0, 0.061333586, -0.046469215, 0.047683943], "split_indices": [0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.855877, 9.066627, 25.78925, 18.318449, 7.4708, 13.17029, 5.14816], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.14934245, 0.23811066, -0.0601315, 0.3739619, 0.047026224, 0.039702535, 0.13853985, -0.022787072, 0.053093106], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 87, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1.1877646, 0.7157954, 0.0, 0.2404859, 0.24509792, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, -0.0601315, 1.0, 4.0, 0.039702535, 0.13853985, -0.022787072, 0.053093106], "split_indices": [0, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.595566, 28.873203, 6.7223606, 16.010313, 12.86289, 5.2403903, 10.769923, 6.4267244, 6.436166], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.023643708, 0.13521177, -0.04570674, 0.06976998, -0.0, 0.030894734, -0.13829063, -0.009487106, 0.044309083, -0.09118493, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 88, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.35405254, 0.24187395, 0.20796786, 0.0, 0.0, 0.12665261, 0.30949497, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 6.0, 5.0, 0.06976998, -0.0, 8.0, 7.0, -0.009487106, 0.044309083, -0.09118493, -0.0], "split_indices": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [42.803673, 16.596382, 26.20729, 9.419744, 7.176639, 14.115063, 12.092228, 9.111327, 5.003735, 5.0602045, 7.032023], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.01985023, -0.043355346, 0.089062445, -0.026668718, 0.1796494, 0.12892018, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 89, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.35759568, 0.0, 0.39119625, 0.0, 0.7069321, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.043355346, 2.0, -0.026668718, 6.0, 0.12892018, -0.0], "split_indices": [2, 0, 1, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.789528, 8.076426, 21.713102, 6.958291, 14.75481, 5.581434, 9.173377], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.01287494, 0.04234006, -0.060850736, 0.117442995, -0.021947129, -0.015088084, 0.0987878], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 90, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.34960037, 0.22488087, 0.0, 0.60267127, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 6.0, -0.060850736, 5.0, -0.021947129, -0.015088084, 0.0987878], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.98221, 23.424276, 6.5579333, 14.708762, 8.715513, 8.371226, 6.3375363], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.009378075, -0.1976131, 0.1684571, -0.0020778296, -0.09819666, 0.08203541, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 91, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [1.0495007, 0.3015957, 0.32817096, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 7.0, -0.0020778296, -0.09819666, 0.08203541, -0.0], "split_indices": [2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.950205, 12.636689, 17.313517, 5.854405, 6.7822843, 10.338829, 6.9746876], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.04343485, -0.078868, 0.046267137, 0.068674676, -0.1175856, -0.105349846, 0.051255453], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 92, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.6658279, 0.0, 0.73584634, 0.0, 0.9311475, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [5.0, -0.078868, 7.0, 0.068674676, 6.0, -0.105349846, 0.051255453], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.104355, 8.788094, 22.316261, 10.653694, 11.662567, 6.5154657, 5.1471014], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.016717652, 0.032883644, -0.080076344, -0.06756308, 0.011302119, 0.06592837, -0.055216767], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 93, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.24027972, 0.0, 0.2973094, 0.0, 0.5807005, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, 0.032883644, 4.0, -0.06756308, 7.0, 0.06592837, -0.055216767], "split_indices": [2, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.292017, 8.678923, 19.613094, 7.4346323, 12.178462, 6.0788827, 6.0995793], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.017453156, 0.16278942, -0.09441457, -0.0204039, 0.09944212, 0.03281106, -0.07116785], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 94, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.56757414, 0.64632386, 0.59745747, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 3.0, -0.0204039, 0.09944212, 0.03281106, -0.07116785], "split_indices": [2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.543625, 14.345265, 18.198359, 6.0590053, 8.28626, 7.3064175, 10.891941], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0931395, -0.043684747, 0.22560778, -0.17251317, 0.069781214, -0.011988571, 0.3321175, -0.0, -0.080979034, 0.13618968, 0.049391657], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 95, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.76961434, 0.76709086, 0.64558566, 0.2232807, 0.0, 0.0, 0.1816324, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 8.0, 2.0, 3.0, 0.069781214, -0.011988571, 8.0, -0.0, -0.080979034, 0.13618968, 0.049391657], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.914295, 19.592678, 20.321619, 13.865154, 5.727524, 5.7272663, 14.594353, 5.729019, 8.136135, 7.11222, 7.482133], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.044410195, 0.029959574, 0.07278024, -0.018235056, -0.033057626, 0.009103241], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 96, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.16924483, 0.0, 0.36093238, 0.0, 0.13248676, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.044410195, 3.0, 0.07278024, 5.0, -0.033057626, 0.009103241], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.986153, 5.387929, 31.598225, 5.476466, 26.12176, 9.302663, 16.819096], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.22307889, -0.2929893, -0.1331123, -0.04967198, -0.12249487, 0.016312726, -0.08722389], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 97, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.14266932, 0.14844012, 0.57293004, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 7.0, -0.04967198, -0.12249487, 0.016312726, -0.08722389], "split_indices": [2, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.658855, 17.608013, 17.050844, 9.934698, 7.673315, 7.797389, 9.253455], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.013563381, 0.08751355, -0.08212949, -0.00019926814, 0.11247398, 0.03235283, -0.059386306], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 98, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [1.1853421, 0.8385485, 0.0, 0.56616884, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 8.0, -0.08212949, 4.0, 0.11247398, 0.03235283, -0.059386306], "split_indices": [0, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [42.450275, 30.77492, 11.675357, 24.077848, 6.6970696, 15.537848, 8.540001], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.008703753, 0.048857983, -0.061391555, -0.0933016, 0.046230815, -0.0057734847, 0.05011635], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 99, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.35932738, 0.0, 0.6280546, 0.0, 0.14349456, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [4.0, 0.048857983, 4.0, -0.0933016, 8.0, -0.0057734847, 0.05011635], "split_indices": [0, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.389421, 9.531224, 20.858198, 5.9944263, 14.863771, 9.648307, 5.215464], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.017776014, -0.030296218, 0.079424426, 0.20953621, -0.08058025, 0.031756178, 0.08097095], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 100, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.23054278, 0.0, 1.0155694, 0.035785854, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [4.0, -0.030296218, 6.0, 2.0, -0.08058025, 0.031756178, 0.08097095], "split_indices": [2, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.687136, 9.372034, 20.3151, 15.285696, 5.029405, 7.2476516, 8.038044], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.055324644, 0.15294042, -0.057955436, 0.08508807, -0.012461905, -0.059685726, 0.02889635], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 101, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.33420163, 0.44495407, 0.32246423, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 7.0, 0.08508807, -0.012461905, -0.059685726, 0.02889635], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.562273, 15.090842, 12.471431, 9.059116, 6.0317264, 6.7035265, 5.7679043], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.03168543, 0.08491082, -0.106866434, 0.022914775, -0.20363826, -0.035114206, -0.08046488], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 102, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.7945487, 0.0, 0.51873016, 0.0, 0.03480941, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.08491082, 2.0, 0.022914775, 6.0, -0.035114206, -0.08046488], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.04662, 5.389661, 26.65696, 8.92802, 17.72894, 9.436628, 8.292311], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0044299243, 0.07641046, -0.07310104, 0.03738818, -0.13702501, -0.06550953, 0.0038318927], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 103, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.5756915, 0.0, 0.35498402, 0.0, 0.2790667, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.07641046, 3.0, 0.03738818, 8.0, -0.06550953, 0.0038318927], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.325802, 5.68985, 25.635952, 5.6489043, 19.987047, 13.089374, 6.8976736], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.028329624, -0.12126279, 0.13071072, -0.0, -0.06396654, 0.081155054, 0.041265585, 0.052976515, -0.02573671], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 104, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.51778466, 0.16754316, 0.21148887, 0.0, 0.0, 0.0, 0.26484892, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [5.0, 3.0, 3.0, -0.0, -0.06396654, 0.081155054, 8.0, 0.052976515, -0.02573671], "split_indices": [0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.711409, 12.413079, 19.29833, 5.3895493, 7.02353, 6.3034334, 12.994897, 6.4850397, 6.509857], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0051209135, -0.06607793, 0.1326807, -0.19873953, 0.05013315, 0.1015362, -0.020706112, -0.10517929, -0.024061475], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 105, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.3614969, 0.9902207, 0.5878264, 0.29570597, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [8.0, 7.0, 6.0, 4.0, 0.05013315, 0.1015362, -0.020706112, -0.10517929, -0.024061475], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [41.784637, 29.833742, 11.950893, 19.383167, 10.450575, 5.8255324, 6.125361, 7.2691064, 12.114061], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.020302374, 0.17787443, -0.0649731, -0.011456658, 0.1396577, 0.12718855, -0.26925614, -0.0, 0.07315584, -0.13366264, -0.020388495], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 106, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.5347992, 0.934235, 1.031485, 0.0, 0.0, 0.18505184, 0.36943257, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [4.0, 5.0, 5.0, -0.011456658, 0.1396577, 3.0, 8.0, -0.0, 0.07315584, -0.13366264, -0.020388495], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.10008, 13.058103, 24.041977, 7.753681, 5.3044214, 12.330755, 11.711222, 6.592203, 5.738552, 5.3317294, 6.3794923], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0023039584, -0.145853, 0.08990743, -0.0040371395, -0.07298596, 0.016379155, 0.06920561, 0.035748433, -0.017733233], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 107, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.49444023, 0.16277453, 0.20004286, 0.0, 0.0, 0.13754848, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 3.0, 6.0, -0.0040371395, -0.07298596, 6.0, 0.06920561, 0.035748433, -0.017733233], "split_indices": [2, 1, 1, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.93998, 13.862472, 21.077507, 6.794432, 7.06804, 15.045785, 6.031723, 6.687154, 8.358631], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.050231446, -0.10673361, 0.10846001, -0.09726446, 0.036545698, 0.25186747, -0.0, 0.022718411, 0.116173856, 0.03253633, -0.06143708], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 108, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.42240673, 0.66599333, 0.5440578, 0.0, 0.0, 0.2780481, 0.47164536, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 5.0, 5.0, -0.09726446, 0.036545698, 6.0, 5.0, 0.022718411, 0.116173856, 0.03253633, -0.06143708], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [43.930466, 11.196907, 32.73356, 5.7471423, 5.4497647, 13.831752, 18.901808, 7.0096445, 6.8221073, 12.419519, 6.4822884], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.021704555, -0.08313973, 0.040081583, -0.054565255, 0.13148744, 0.0046620155, 0.080623075], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 109, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.5045645, 0.0, 0.51648337, 0.0, 0.25817952, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.08313973, 2.0, -0.054565255, 7.0, 0.0046620155, 0.080623075], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.765203, 5.2391896, 23.526012, 6.2543545, 17.271658, 10.360983, 6.910676], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.12292982, 0.15106031, 0.0020303559, 0.044593744, 0.08806749, -0.041857634, 0.07820919], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 110, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.08694634, 0.32965678, 0.0, 0.6766478, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.0020303559, 5.0, 0.08806749, -0.041857634, 0.07820919], "split_indices": [2, 1, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.750675, 23.820791, 6.929884, 14.787271, 9.033521, 7.9259863, 6.8612843], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.00042520493, -0.14750868, 0.16690633, -0.06583633, -0.017404072, 0.10134068, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 111, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.75941664, 0.06756461, 0.4094948, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 4.0, -0.06583633, -0.017404072, 0.10134068, -0.0], "split_indices": [2, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.93439, 15.779968, 13.154422, 7.3232417, 8.456726, 6.0441, 7.110322], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.04020527, 0.024857916, -0.10781157, -0.0019596287, -0.053364687], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 112, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.2764438, 0.0, 0.13310304, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [2.0, 0.024857916, 6.0, -0.0019596287, -0.053364687], "split_indices": [1, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [30.852934, 10.335061, 20.517872, 9.421085, 11.096787], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.028680932, -0.08346089, 0.023146585, 0.026501294, -0.059011806], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 113, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.18092963, 0.43411052, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 2.0, 0.023146585, 0.026501294, -0.059011806], "split_indices": [1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.987673, 19.86301, 9.124661, 7.6279426, 12.235068], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.23892282, 0.13421275, -0.09277825, -0.02987233, 0.20594706, -0.010176348, 0.02468221, 0.10042411], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 114, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [1.0813625, 0.046981335, 0.28551263, 0.0, 0.0, 0.1815697, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [5.0, 5.0, 5.0, -0.09277825, -0.02987233, 3.0, -0.010176348, 0.02468221, 0.10042411], "split_indices": [0, 1, 1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.77318, 11.1062565, 20.666924, 6.0935125, 5.012744, 14.745586, 5.921337, 8.75462, 5.990966], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.014327235, 0.07964439, -0.10116858, 0.10057593, 0.023231626, 0.028270533, -0.008487058], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 115, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.94214135, 0.46983248, 0.0, 0.0, 0.12107797, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 2.0, -0.10116858, 0.10057593, 6.0, 0.028270533, -0.008487058], "split_indices": [0, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.981434, 34.609146, 5.372289, 5.043106, 29.56604, 12.900053, 16.665987], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.00083472364, 0.044761162, -0.04852327, 0.16210896, -0.09329271, 0.06973749, 0.02440356, -0.036298413, -0.009936994], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 116, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.2981527, 0.5072507, 0.0, 0.045999974, 0.003924869, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, -0.04852327, 3.0, 8.0, 0.06973749, 0.02440356, -0.036298413, -0.009936994], "split_indices": [0, 2, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.056656, 28.952835, 8.103821, 15.9474125, 13.005422, 6.8576365, 9.089776, 7.0751476, 5.930274], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.06939415, 0.082154974, -0.0020957226, -0.042236555, 0.056623694, 0.07343255, -0.011474229], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 117, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.5351534, 0.0, 0.23098911, 0.0, 0.3641614, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.082154974, 4.0, -0.042236555, 6.0, 0.07343255, -0.011474229], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.71443, 8.363533, 25.350897, 7.631346, 17.719551, 5.7633905, 11.9561615], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.07107205, 0.14383565, -0.12712854, -0.018566793, 0.2175386, 0.018711885, -0.0986146, 0.032958914, 0.12143877], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 118, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.6672965, 0.53956467, 0.5137756, 0.0, 0.41558027, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [8.0, 2.0, 5.0, -0.018566793, 6.0, 0.018711885, -0.0986146, 0.032958914, 0.12143877], "split_indices": [2, 1, 1, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [43.886265, 32.670918, 11.215347, 8.301807, 24.36911, 5.881229, 5.3341184, 16.81879, 7.5503187], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.118748486, -0.09313854, -0.04999159, 0.040713277, -0.08855114, -0.03340958, 0.074412756], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 119, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.3381499, 0.0, 0.5564436, 0.5967182, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.09313854, 8.0, 6.0, -0.08855114, -0.03340958, 0.074412756], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.565107, 6.228517, 22.33659, 16.615646, 5.720943, 9.57373, 7.041916], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.090127565, -0.16687612, -0.0, -0.09062793, 0.0105086975, 0.025338769, -0.02571355], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 120, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.22625902, 0.5106512, 0.11757183, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 8.0, -0.09062793, 0.0105086975, 0.025338769, -0.02571355], "split_indices": [2, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.587147, 16.33866, 14.248488, 9.784796, 6.5538635, 7.4573426, 6.7911453], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.007287918, -0.096479215, 0.05343428], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 121, "left_children": [1, -1, -1], "loss_changes": [1.9348233, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.096479215, 0.05343428], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [30.973177, 11.419184, 19.553993], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.018005623, 0.078813575, -0.03991619, -0.053950276, 0.004381342, 0.02365385, -0.022023045], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 122, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.48245886, 0.0, 0.18067324, 0.0, 0.12408207, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.078813575, 4.0, -0.053950276, 7.0, 0.02365385, -0.022023045], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.726976, 5.534754, 25.192223, 5.8649573, 19.327265, 10.524634, 8.80263], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.055818304, -0.2398584, 0.12865572, -0.043612223, -0.0936367, 0.083978534, 0.0016090503], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 123, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [1.0489688, 0.014489412, 0.25331655, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 4.0, -0.043612223, -0.0936367, 0.083978534, 0.0016090503], "split_indices": [1, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.660416, 14.468151, 14.192265, 8.174978, 6.293173, 5.4382744, 8.75399], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.013743916, -0.09210122, 0.07979186, -0.058431562, -0.0, 0.0020835213, -0.0016985345], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 124, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.7412232, 0.25896746, 0.0, 0.0, 0.00065264583, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 4.0, 0.07979186, -0.058431562, 6.0, 0.0020835213, -0.0016985345], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.49389, 26.186483, 6.307404, 11.905404, 14.28108, 7.0814295, 7.199651], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.011770736, -0.07123588, 0.12393059, -0.12715746, 0.028615562, -0.016452806, 0.08783253, -0.0018817277, -0.108084425], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 125, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.3519237, 0.30568665, 0.4324414, 0.6433848, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [8.0, 8.0, 4.0, 5.0, 0.028615562, -0.016452806, 0.08783253, -0.0018817277, -0.108084425], "split_indices": [0, 1, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [42.170574, 30.164343, 12.006231, 23.211184, 6.9531584, 5.8719316, 6.1342993, 16.24388, 6.9673057], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.07086435, -0.0035685177, 0.16750775, 0.06563692, -0.049066838, -0.018068058, 0.09547234], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 126, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.29144797, 0.8110568, 0.6355853, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 3.0, 0.06563692, -0.049066838, -0.018068058, 0.09547234], "split_indices": [0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.153664, 20.94815, 16.205513, 8.3501425, 12.598008, 6.4291253, 9.776388], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.075544514, -0.045715712, 0.18019475, 0.0281497, -0.09095945, 0.015368291, 0.11376405, 0.026829703, -0.012556295], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 127, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.49618924, 0.6845223, 0.6342638, 0.0, 0.0, 0.062106285, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 6.0, 6.0, 0.0281497, -0.09095945, 3.0, 0.11376405, 0.026829703, -0.012556295], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.392193, 16.676458, 19.715736, 10.975342, 5.7011156, 11.648328, 8.067409, 5.593394, 6.054934], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.007979328, 0.057672758, -0.036520667, -0.016576739, 0.07037938, 0.031836916, -0.05157421], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 128, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.2873531, 0.46442783, 0.0, 0.4834251, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 6.0, -0.036520667, 4.0, 0.07037938, 0.031836916, -0.05157421], "split_indices": [2, 2, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [43.483334, 32.39804, 11.085294, 23.061152, 9.336888, 12.70144, 10.359713], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0022987768, -0.10052536, 0.08092623, 0.055044148, -0.08291256, -0.037420414, 0.05946594], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 129, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.25986663, 0.80967486, 0.4311262, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 5.0, 0.055044148, -0.08291256, -0.037420414, 0.05946594], "split_indices": [0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.553442, 14.010818, 15.542624, 5.1237144, 8.887103, 5.31223, 10.230394], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.049136538, -0.16013484, 0.07049611, -0.13643172, 0.016139137, 0.10255634, -0.08899692], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 130, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.43328363, 1.1281401, 1.6233473, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 5.0, 5.0, -0.13643172, 0.016139137, 0.10255634, -0.08899692], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.104015, 15.873503, 14.230512, 6.400323, 9.47318, 8.378974, 5.8515377], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.057517618, 0.033810146, 0.06779301, -0.09907958, 0.056326754, -0.036999773], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 131, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.21183068, 0.7642502, 0.0, 0.37179634, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [5.0, 3.0, 0.033810146, 7.0, -0.09907958, 0.056326754, -0.036999773], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.161913, 19.937725, 10.224189, 13.924901, 6.012824, 8.8874235, 5.0374775], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.01278608, -0.09204449, 0.10167193, -0.0, 0.20475113, 0.01932907, -0.014311002, 0.08720843, 0.021484666], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 132, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.9705688, 0.0, 0.28526887, 0.048940934, 0.098207355, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [4.0, -0.09204449, 4.0, 1.0, 7.0, 0.01932907, -0.014311002, 0.08720843, 0.021484666], "split_indices": [2, 0, 1, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.789368, 6.356586, 26.432783, 13.863952, 12.568831, 5.8460646, 8.017887, 6.4625015, 6.1063304], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.06465594, 0.06011724, -0.17729206, 0.031825088, -0.0, -0.073703624, -0.016255856], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 133, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.41714534, 0.04470017, 0.0866639, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 6.0, 6.0, 0.031825088, -0.0, -0.073703624, -0.016255856], "split_indices": [2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.08954, 12.652327, 14.437215, 7.3564086, 5.2959175, 8.119027, 6.318187], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.047163006, -0.1074432, 0.05104068, -0.07666314, 0.018342568, 0.024861617, -0.013407936], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 134, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.42950365, 0.511348, 0.0, 0.0, 0.06685301, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 4.0, 0.05104068, -0.07666314, 7.0, 0.024861617, -0.013407936], "split_indices": [0, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.872612, 24.924204, 5.9484067, 11.338104, 13.5861, 7.341916, 6.244184], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.02860195, -0.043677773, 0.19955088, 0.031210192, -0.13035901, 0.033503428, 0.07857428, 0.04494128, -0.04710347, -0.054314297, -0.0064539765], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 135, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5366741, 0.20582683, 0.0041562915, 0.38091782, 0.05590579, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, 5.0, 7.0, 5.0, 0.033503428, 0.07857428, 0.04494128, -0.04710347, -0.054314297, -0.0064539765], "split_indices": [0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.466896, 28.553558, 11.913336, 15.043269, 13.51029, 6.769324, 5.144012, 9.679709, 5.36356, 8.138282, 5.372008], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.013591136, -0.058376692, 0.10148211, -0.13966922, 0.024797741, 0.16094744, -0.0, -0.0, -0.07565459, 0.07668449, 0.0125215985], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 136, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.27463752, 0.28084654, 0.12700512, 0.25862312, 0.0, 0.10380253, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 8.0, 3.0, 0.024797741, 4.0, -0.0, -0.0, -0.07565459, 0.07668449, 0.0125215985], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.800686, 21.991446, 18.80924, 14.406387, 7.5850587, 11.778409, 7.030831, 6.6139255, 7.792462, 5.4596214, 6.318787], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.084296755, 0.18293384, -0.039330628, 0.3285702, -0.026974162, -0.06839659, 0.07711613, 0.13390142, 0.055300675], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 137, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.4683465, 0.8881309, 0.98574394, 0.095008016, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 6.0, 2.0, -0.026974162, -0.06839659, 0.07711613, 0.13390142, 0.055300675], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.639523, 20.006413, 15.633112, 13.149799, 6.856613, 9.899766, 5.733346, 5.704345, 7.445454], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.08559733, 0.07466479, 0.041866202, -0.049182616, 0.14321443, 0.017867906, -0.09055348, 0.013289132, 0.09581956], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 138, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.28364477, 0.0, 0.36136875, 0.58936477, 0.27127522, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.07466479, 6.0, 4.0, 8.0, 0.017867906, -0.09055348, 0.013289132, 0.09581956], "split_indices": [0, 0, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.253498, 7.8742747, 36.379227, 18.914717, 17.464508, 13.481119, 5.4335976, 12.349808, 5.1147], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.06045886, 0.068261065, -0.0, 0.06634384, 0.005568981, -0.010002038], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 139, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.41789556, 0.0, 0.24082324, 0.011287287, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.06045886, 7.0, 7.0, 0.06634384, 0.005568981, -0.010002038], "split_indices": [2, 0, 1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.583578, 6.7889576, 20.79462, 14.793851, 6.000769, 8.622494, 6.1713576], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0074858414, 0.11654253, -0.08335177, -0.048775397, 0.07618287], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 140, "left_children": [1, 3, -1, -1, -1], "loss_changes": [1.0103254, 0.82184345, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [6.0, 4.0, -0.08335177, -0.048775397, 0.07618287], "split_indices": [1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.683956, 19.114124, 8.569832, 6.004653, 13.109471], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.021950712, -0.13921714, 0.059317548, 0.0018254644, -0.19570366, -0.086851165, -0.03345092], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 141, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.81916106, 0.193062, 0.0, 0.0, 0.0515818, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 3.0, 0.059317548, 0.0018254644, 4.0, -0.086851165, -0.03345092], "split_indices": [2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.957548, 20.066965, 9.890584, 5.3715363, 14.695428, 5.2362714, 9.459157], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.088072866, -0.009160695, -0.080444835, -0.08544528, 0.022021007, 0.015048385, -0.064319424], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 142, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.43577474, 0.16002893, 0.0, 0.2664135, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 4.0, -0.080444835, 2.0, 0.022021007, 0.015048385, -0.064319424], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.790611, 23.221016, 8.569596, 12.680877, 10.540139, 6.1008086, 6.580068], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.13738833, -0.025460431, -0.08058913, -0.032199588, 0.012013114], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 143, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.37917513, 0.101011515, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [6.0, 2.0, -0.08058913, -0.032199588, 0.012013114], "split_indices": [1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.28002, 15.779041, 11.500978, 7.4080396, 8.371002], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.069209285, 0.07161825, -0.09910031, 0.08257692, -0.06737063, -0.05327243, 0.0075685293], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 144, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.255923, 0.65875787, 0.0, 0.0, 0.15767126, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [5.0, 3.0, -0.09910031, 0.08257692, 6.0, -0.05327243, 0.0075685293], "split_indices": [1, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.805967, 20.885155, 10.920813, 8.366347, 12.518806, 5.7901163, 6.72869], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.009381368, 0.10391804, -0.10648794, -0.091773406, 0.10357327, -0.07760775, 0.04183375, -0.0, -0.05674663], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 145, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.4628981, 1.1767287, 0.7428294, 0.13275275, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 7.0, 2.0, 0.10357327, -0.07760775, 0.04183375, -0.0, -0.05674663], "split_indices": [2, 2, 1, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.271976, 22.709608, 17.562368, 12.653896, 10.055712, 11.067562, 6.494807, 6.7295723, 5.9243245], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.076744296, -0.0, -0.1904728, -0.022892106, 0.07022719, -0.13141128, -0.0057702195, 0.04762776, -0.0067294994], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 146, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.3370819, 0.13036472, 0.59984535, 0.0, 0.12341068, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 8.0, -0.022892106, 8.0, -0.13141128, -0.0057702195, 0.04762776, -0.0067294994], "split_indices": [0, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.79705, 22.369915, 14.427135, 10.421831, 11.948085, 5.023084, 9.404051, 6.297202, 5.6508822], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.08391084, 0.08593604, 0.0028155113, 0.043199293, -0.036503285, -0.029350264, 0.03598028], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 147, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.5652275, 0.0, 0.13429956, 0.250991, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.08593604, 8.0, 4.0, -0.036503285, -0.029350264, 0.03598028], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.222317, 8.993884, 26.228432, 20.903795, 5.3246355, 6.8097687, 14.0940275], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0957134, -0.094298854, 0.1607684, -0.08780893, 0.02482063, 0.26554132, -0.006356668, 0.12118919, 0.03909255], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 148, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.58867913, 0.4643915, 0.69586426, 0.0, 0.0, 0.31671464, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [2.0, 5.0, 7.0, -0.08780893, 0.02482063, 4.0, -0.006356668, 0.12118919, 0.03909255], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.858784, 10.9345455, 33.924236, 5.11445, 5.820096, 21.585373, 12.338863, 9.319455, 12.265919], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.07469595, -0.042759977, 0.13733055, 0.22021571, -0.034652308, 0.081783995, 0.032056365], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 149, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.42170376, 0.0, 0.53544647, 0.043367922, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.042759977, 8.0, 6.0, -0.034652308, 0.081783995, 0.032056365], "split_indices": [0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.593412, 5.7565074, 22.836905, 17.593845, 5.24306, 10.468265, 7.1255803], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.034930985, 0.087424874, -0.030414747, -0.037276644, 0.16426551, -0.0113854725, 0.08610602], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 150, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.21943772, 0.38963205, 0.0, 0.0, 0.46401688, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [6.0, 1.0, -0.030414747, -0.037276644, 5.0, -0.0113854725, 0.08610602], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.622868, 21.453459, 7.1694083, 5.202681, 16.250778, 6.091793, 10.158985], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.08936616, -0.028943645, 0.21204473, -0.049078695, 0.017731551, 0.008747677, 0.09946423], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 151, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.47933233, 0.21478704, 0.30223048, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 6.0, 7.0, -0.049078695, 0.017731551, 0.008747677, 0.09946423], "split_indices": [0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.340939, 15.419491, 14.921449, 6.206178, 9.213312, 6.7192197, 8.2022295], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.09511735, 0.19479084, -0.025435371, 0.02207452, 0.079540856, -0.049419384, 0.050539486], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 152, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.39438793, 0.0961678, 0.41796467, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 8.0, 0.02207452, 0.079540856, -0.049419384, 0.050539486], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.000378, 16.539309, 13.46107, 7.3472075, 9.192101, 8.221206, 5.2398643], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.049284346, -0.012034251, 0.11136238, 0.05298383, -0.0012085898], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 153, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.164478, 0.0, 0.14735074, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, -0.012034251, 8.0, 0.05298383, -0.0012085898], "split_indices": [1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [26.548588, 10.364958, 16.18363, 10.552529, 5.6311007], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.01633859, 0.06463285, -0.16388647, -0.049576845, 0.07053463, -0.0059774625, -0.082008466, -0.047181744, 0.009199098], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 154, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.39870346, 0.4336078, 0.15334982, 0.12879749, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 4.0, 4.0, 0.07053463, -0.0059774625, -0.082008466, -0.047181744, 0.009199098], "split_indices": [1, 1, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.654844, 19.718124, 10.93672, 11.881258, 7.836866, 5.6294065, 5.3073134, 5.1949887, 6.6862693], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.033218615, 0.07424775, -0.043049064, 0.046197645, 0.036189806, 0.032398846, -0.058278453], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 155, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.31209064, 0.08355546, 0.0, 0.0, 0.44719946, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 2.0, -0.043049064, 0.046197645, 8.0, 0.032398846, -0.058278453], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [41.529747, 34.634228, 6.8955173, 9.356124, 25.278105, 20.008848, 5.269256], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.017314687, 0.044558328, -0.03542319, -0.050890114, 0.030024951, -0.01864473, 0.061570864], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 156, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.27931195, 0.0, 0.2592665, 0.0, 0.32957628, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.044558328, 5.0, -0.050890114, 6.0, -0.01864473, 0.061570864], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.968784, 10.621665, 26.34712, 8.615627, 17.731493, 11.667879, 6.0636144], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.024848772, 0.016780639, -0.07991873, -0.06862642, 0.11037202, -0.056345563, 0.023996096, 0.09864433, -0.022315206], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 157, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.4155485, 0.27825877, 0.0, 0.3356314, 0.7299638, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, -0.07991873, 5.0, 5.0, -0.056345563, 0.023996096, 0.09864433, -0.022315206], "split_indices": [0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.398716, 32.341373, 5.0573435, 16.490862, 15.85051, 9.378291, 7.1125717, 7.1538453, 8.696665], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.064013444, -0.050342552, 0.1408403, 0.21084493, -0.012831519, 0.031458937, 0.12640995], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 158, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.84421, 0.0, 0.48324466, 0.490579, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.050342552, 8.0, 8.0, -0.012831519, 0.031458937, 0.12640995], "split_indices": [1, 0, 2, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.224915, 10.638325, 34.58659, 25.32673, 9.259858, 18.158438, 7.1682916], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.06235027, 0.0073799216, -0.042734552, 0.03108448, -0.01589647], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 159, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.17096788, 0.09917302, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [6.0, 3.0, -0.042734552, 0.03108448, -0.01589647], "split_indices": [0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.003996, 14.254243, 12.749754, 5.857197, 8.397046], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.1126572, 0.067789815, 0.06342552, -0.013066893, 0.12843141, 0.098795354, -0.027577069], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 160, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.12714785, 0.0, 0.17613484, 0.0, 0.7325852, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, 0.067789815, 2.0, -0.013066893, 7.0, 0.098795354, -0.027577069], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.549883, 7.2477674, 22.302114, 7.9727683, 14.329347, 7.474526, 6.8548207], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.042118996, 0.080725156, 0.060518507, -0.002399064, -0.04021714, 0.025722386], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 161, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.3756318, 0.0, 0.23897652, 0.0, 0.1715821, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [6.0, -0.042118996, 3.0, 0.060518507, 8.0, -0.04021714, 0.025722386], "split_indices": [2, 0, 1, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.602314, 10.873023, 20.72929, 8.350186, 12.379104, 5.210146, 7.168958], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.019964037, 0.044633027, -0.071854234, -0.0085471785, -0.050121807, -0.027196428, 0.037637815], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 162, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.28681082, 0.0, 0.13724236, 0.19771883, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, 0.044633027, 6.0, 7.0, -0.050121807, -0.027196428, 0.037637815], "split_indices": [1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.413551, 6.495773, 24.917778, 16.25226, 8.665518, 10.740904, 5.5113564], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.05807626, -0.06757332, 0.05353513, 0.07121681, -0.026197894], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 163, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.5493516, 0.0, 0.48028573, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, -0.06757332, 5.0, 0.07121681, -0.026197894], "split_indices": [1, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [26.876833, 10.7114315, 16.165401, 7.023233, 9.142169], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.018431166, -0.06412842, 0.04387716, 0.09928123, -0.103316635, -0.0132465605, -0.043254584], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 164, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.41350088, 0.0, 1.0945914, 0.0, 0.015332565, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [4.0, -0.06412842, 3.0, 0.09928123, 8.0, -0.0132465605, -0.043254584], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.808622, 7.2511683, 23.557453, 7.7921376, 15.765316, 8.290532, 7.4747834], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.020464571, -0.08547004, 0.08462592, -0.00067034643, -0.050722297, 0.030263228, -0.050525192], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 165, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [1.1142049, 0.18132827, 0.0, 0.28763732, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.08462592, 7.0, -0.050722297, 0.030263228, -0.050525192], "split_indices": [0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.623154, 26.98685, 10.636305, 14.491883, 12.4949665, 8.904143, 5.5877404], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015453274, -0.034571536, 0.033418804, -0.112926856, 0.048201706, 0.002891953, -0.09180736, 0.09695056, -0.03395302], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 166, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.13737662, 0.21128629, 0.0, 0.42809695, 0.724456, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.033418804, 2.0, 7.0, 0.002891953, -0.09180736, 0.09695056, -0.03395302], "split_indices": [2, 1, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.857105, 29.739902, 7.117203, 15.711147, 14.028755, 9.875185, 5.8359632, 5.0468025, 8.981953], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.044982597, 0.011262922, -0.061667565, -0.14246325, 0.15280265, 0.035628013, -0.09596585, 0.074542694, 0.020353455], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 167, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.36363822, 0.64339125, 0.0, 0.7000622, 0.078884065, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, -0.061667565, 1.0, 5.0, 0.035628013, -0.09596585, 0.074542694, 0.020353455], "split_indices": [2, 1, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.854923, 27.521845, 9.333076, 12.851166, 14.67068, 5.1037807, 7.747385, 5.4413257, 9.229354], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.015195975, 0.09172079, -0.085316, -0.25912455, 0.09305071, -0.033138636, -0.11969556, -0.0044418764, 0.056074], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 168, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [1.0395907, 0.0, 1.2436745, 0.32499695, 0.21842562, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.09172079, 6.0, 3.0, 6.0, -0.033138636, -0.11969556, -0.0044418764, 0.056074], "split_indices": [0, 0, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.0167, 7.2137637, 37.802937, 19.200748, 18.60219, 10.521695, 8.679052, 8.552004, 10.050186], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010538215, -0.069195405, 0.058228374, -0.18690959, 0.050945424, 0.05924326, -0.14366563], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 169, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.38037333, 0.65990764, 0.0, 1.8819325, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 7.0, 0.058228374, 4.0, 0.050945424, 0.05924326, -0.14366563], "split_indices": [2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.53367, 21.265923, 6.2677464, 14.67491, 6.591014, 6.326806, 8.348104], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.07706063, 0.087165, -0.18298833, 0.010546818, 0.03552371, -0.080368385, -0.100403875, -0.09622861, 0.045571756], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 170, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.59200555, 0.0017295256, 0.25052488, 0.0, 0.0, 0.84341824, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 6.0, 7.0, 0.010546818, 0.03552371, 5.0, -0.100403875, -0.09622861, 0.045571756], "split_indices": [0, 2, 2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.563334, 12.075341, 19.487991, 6.5307484, 5.5445933, 12.929896, 6.558095, 6.3782506, 6.5516458], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.082103014, -0.18105547, 0.006112978, -0.06896361, -0.02808981, 0.032504562, -0.012352531], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 171, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.2992059, 0.013212204, 0.095433846, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 8.0, -0.06896361, -0.02808981, 0.032504562, -0.012352531], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.145582, 14.717752, 16.42783, 7.664159, 7.053593, 5.4558606, 10.97197], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.10864215, -0.12975064, -0.010500525, 0.05534451, -0.056227736, -0.026088746, 0.04449556], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 172, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.9008572, 0.0, 0.3054166, 0.25707167, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [4.0, -0.12975064, 8.0, 2.0, -0.056227736, -0.026088746, 0.04449556], "split_indices": [2, 0, 2, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.879646, 5.687029, 23.192617, 16.991125, 6.201492, 6.314767, 10.676358], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.12298245, 0.089092635, -0.0, 0.04259747, -0.05617619], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 173, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.5798816, 0.0, 0.45497632, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, 0.089092635, 6.0, 0.04259747, -0.05617619], "split_indices": [2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [25.015213, 10.036453, 14.97876, 8.419108, 6.559652], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.029233195, -0.095663674, 0.062028136, 0.0698072, -0.044494145, -0.03531754, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 174, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.8416154, 0.0, 0.45597857, 0.0, 0.057301607, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [4.0, -0.095663674, 3.0, 0.0698072, 7.0, -0.03531754, -0.0], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.119064, 6.641587, 22.477478, 8.5437, 13.933779, 5.267779, 8.665999], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0039749336, -0.050940283, 0.04204912, 0.16917738, -0.014146885, -0.0, 0.08716034, -0.070704214, 0.023035452], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 175, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.26695904, 0.0, 0.26843238, 0.20486224, 0.53147954, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.050940283, 5.0, 6.0, 7.0, -0.0, 0.08716034, -0.070704214, 0.023035452], "split_indices": [0, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.19994, 6.1310024, 34.068935, 10.372641, 23.696295, 5.0895386, 5.283102, 6.7527313, 16.943563], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.006193196, 0.096149504, -0.06387076, -0.014479925, 0.090094455, 0.06874765, -0.07962735, 0.08547761, -0.03118014], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 176, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.24243923, 0.54728854, 0.5929353, 0.0, 0.0, 0.5337023, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 6.0, 7.0, -0.014479925, 0.090094455, 7.0, -0.07962735, 0.08547761, -0.03118014], "split_indices": [1, 2, 2, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.979454, 16.215305, 19.76415, 9.657725, 6.557579, 12.018789, 7.7453613, 5.305721, 6.713069], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.06701259, 0.15203732, -0.10368825, 0.20503631, 0.013854971, -0.042514484, -0.012903497, 0.03613779, 0.084007844], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 177, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.5582439, 0.10860121, 0.0043195635, 0.02825576, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 3.0, 4.0, 0.013854971, -0.042514484, -0.012903497, 0.03613779, 0.084007844], "split_indices": [2, 1, 1, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.043694, 24.52376, 11.519935, 15.087892, 9.435868, 5.247566, 6.2723684, 8.942255, 6.1456366], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.053036917, 0.19806278, -0.0021419243, 0.019139089, 0.08453897, 0.074996464, -0.052896913, -0.01901254, 0.045409225], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 178, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.38282734, 0.09368271, 0.47120824, 0.0, 0.0, 0.26571542, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 5.0, 7.0, 0.019139089, 0.08453897, 5.0, -0.052896913, -0.01901254, 0.045409225], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.338005, 12.005891, 32.332115, 5.7469335, 6.258958, 22.40506, 9.927054, 7.562724, 14.842336], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.03299222, -0.03840382, -0.050484683, 0.043244317, 0.021769784, -0.053421687], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 179, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.12799306, 0.2311041, 0.0, 0.23426642, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 4.0, -0.03840382, 5.0, 0.043244317, 0.021769784, -0.053421687], "split_indices": [1, 1, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.387989, 22.112673, 5.275316, 12.426584, 9.686089, 6.146308, 6.2802763], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.009066837, -0.06293053, 0.026327461, -0.09535534, 0.071912974, -0.010143479, 0.051547714], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 180, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.15158498, 0.7193771, 0.0, 0.0, 0.16056259, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 3.0, 0.026327461, -0.09535534, 3.0, -0.010143479, 0.051547714], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.482262, 18.547276, 8.934985, 6.2036805, 12.3435955, 5.832519, 6.5110765], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.035291534, 0.08352261, -0.0401238, -0.006267816, 0.12717578, 0.051834725, 0.015067877], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 181, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.24216846, 0.12084994, 0.0, 0.0, 0.030287802, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 5.0, -0.0401238, -0.006267816, 3.0, 0.051834725, 0.015067877], "split_indices": [1, 2, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.949339, 22.598764, 5.3505745, 6.140861, 16.457905, 8.735547, 7.7223573], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.011680526, 0.03375476, -0.055620167, -0.020993382, 0.0817463, -0.0068078027, 0.057820734], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 182, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.27155745, 0.13389456, 0.0, 0.0, 0.2365353, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 5.0, -0.055620167, -0.020993382, 4.0, -0.0068078027, 0.057820734], "split_indices": [2, 2, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.690628, 24.510206, 6.1804214, 6.948766, 17.56144, 8.993388, 8.568051], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.014279872, -0.052155126, 0.04952968, 0.032907564, -0.16665486, -0.08836004, -0.004284025], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 183, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.2937033, 0.3810699, 0.0, 0.0, 0.19960243, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 3.0, 0.04952968, 0.032907564, 5.0, -0.08836004, -0.004284025], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [26.326084, 18.269573, 8.05651, 7.225231, 11.044342, 5.140778, 5.903564], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.070068546, -0.019281004, 0.04776502, -0.08221708, 0.040392205], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 184, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.25218672, 0.64198506, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [4.0, 6.0, 0.04776502, -0.08221708, 0.040392205], "split_indices": [1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.518265, 14.074233, 14.444032, 5.251302, 8.822932], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.023306824, -0.12888932, 0.15710261, -0.0, -0.10083229, 0.1153377, -0.014957692, -0.040556327, 0.034602255], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 185, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.81373054, 0.72426903, 0.78359175, 0.29414475, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 5.0, 6.0, -0.10083229, 0.1153377, -0.014957692, -0.040556327, 0.034602255], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.804893, 26.27035, 14.534545, 16.848875, 9.421475, 6.7475796, 7.786965, 7.7262096, 9.122665], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.050308622, 0.097452655, -0.025772918, -0.0, 0.15065964, 0.054685388, -0.06916876, 0.062309206, 0.002220087], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 186, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.14456037, 0.1276271, 0.658976, 0.0, 0.10576546, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 4.0, -0.0, 8.0, 0.054685388, -0.06916876, 0.062309206, 0.002220087], "split_indices": [0, 1, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.604855, 23.23854, 13.366312, 8.474338, 14.764203, 6.442547, 6.9237657, 9.738895, 5.0253077], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.05696695, 0.022157714, -0.107686184, 0.041130394, -0.027060965, -0.043436415, 0.0025943448], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 187, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.99618137, 0.19763874, 0.0, 0.0, 0.11099061, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 2.0, -0.107686184, 0.041130394, 1.0, -0.043436415, 0.0025943448], "split_indices": [1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.94825, 31.333847, 7.614404, 9.468241, 21.865606, 5.02956, 16.836046], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0422163, 0.20912147, -0.04654814, 0.021898814, 0.08760409, 0.037404615, -0.058577072, -0.038329825, 0.12829013], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 188, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.7081367, 0.12902766, 0.4036754, 0.0, 0.0, 1.3668889, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 3.0, 6.0, 0.021898814, 0.08760409, 4.0, -0.058577072, -0.038329825, 0.12829013], "split_indices": [2, 1, 1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.043465, 15.558756, 29.48471, 7.0569086, 8.501848, 18.894138, 10.590571, 13.567714, 5.326424], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.04069368, 0.03508737, -0.13973244, -0.076254904, -0.004749895], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 189, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.46659842, 0.0, 0.23083815, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [3.0, 0.03508737, 7.0, -0.076254904, -0.004749895], "split_indices": [1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.691507, 10.224824, 17.466684, 8.113401, 9.353283], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.08327547, 0.058466386, -0.0, 0.03848325, -0.057435777], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 190, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.26463455, 0.0, 0.48216236, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, 0.058466386, 4.0, 0.03848325, -0.057435777], "split_indices": [0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [29.381832, 11.514749, 17.867083, 11.181187, 6.685896], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.012060362, -0.15146229, 0.16550958, 0.0022949802, -0.077500194, -0.015237392, 0.094709165], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 191, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.7377524, 0.263909, 0.5433556, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 3.0, 0.0022949802, -0.077500194, -0.015237392, 0.094709165], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.346375, 12.929433, 14.416941, 5.192089, 7.7373443, 5.9064393, 8.510501], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.13535537, 0.2121643, 0.02896543, 0.08856033, -0.0, -0.018345462, 0.05326439], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 192, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.21840656, 0.27125955, 0.21614331, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 8.0, 0.08856033, -0.0, -0.018345462, 0.05326439], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.430023, 15.967805, 13.462218, 10.87743, 5.0903745, 8.327128, 5.1350894], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.03001562, -0.095016256, 0.14247394, 0.010321903, 0.077830225], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 193, "left_children": [1, -1, 3, -1, -1], "loss_changes": [1.4519302, 0.0, 0.18937013, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, -0.095016256, 6.0, 0.010321903, 0.077830225], "split_indices": [0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.094604, 10.081354, 17.013252, 9.959833, 7.0534186], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.027496384, 0.02529819, -0.03875293, -0.027033094, 0.041974552], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 194, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.17382298, 0.27987167, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [5.0, 4.0, -0.03875293, -0.027033094, 0.041974552], "split_indices": [1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.836554, 18.828232, 10.008323, 9.053527, 9.774705], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.052588485, 0.09522928, -0.061249536, -0.005343124, 0.24717146, -0.036574386, 0.02299956, 0.022659868, 0.09980029], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 195, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.4922564, 0.62001824, 0.0, 0.24323678, 0.16755795, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, -0.061249536, 6.0, 5.0, -0.036574386, 0.02299956, 0.022659868, 0.09980029], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [43.181553, 37.905, 5.276557, 22.992601, 14.912396, 9.797967, 13.1946335, 5.9734507, 8.938946], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.040793695, -0.10430438, 0.084323116, 0.027602142, -0.22699893, 0.063542716, -0.011458196, -0.015644033, -0.11148733], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 196, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.3178067, 0.6751932, 0.22899455, 0.0, 0.36748326, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 5.0, 0.027602142, 4.0, 0.063542716, -0.011458196, -0.015644033, -0.11148733], "split_indices": [2, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.63623, 25.604631, 12.031598, 9.6306505, 15.973981, 5.9390664, 6.092532, 8.160249, 7.8137317], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.102705896, -0.055900045, -0.090239495, -0.15302461, 0.101633176, -0.08090924, -0.01925156, 0.05655149, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 197, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.30071104, 0.51717633, 0.0, 0.1656088, 0.12068026, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, -0.090239495, 2.0, 4.0, -0.08090924, -0.01925156, 0.05655149, -0.0], "split_indices": [2, 2, 0, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.093174, 31.449257, 5.643916, 19.865135, 11.584122, 7.2216167, 12.643518, 6.054219, 5.5299034], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.031554833, -0.11489486, 0.017125275, 0.013988926, -0.20987667, 0.04954443, -0.060154945, -0.003772869, -0.10112892, 0.008170878, -0.0642577], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 198, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.20302838, 0.3064471, 0.3621981, 0.0, 0.2639587, 0.0, 0.29265377, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 5.0, 0.013988926, 4.0, 0.04954443, 5.0, -0.003772869, -0.10112892, 0.008170878, -0.0642577], "split_indices": [1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.149067, 17.272568, 28.8765, 6.1945105, 11.078056, 9.958651, 18.917849, 5.0817895, 5.996267, 12.181064, 6.7367845], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.17514092, 0.13158265, -0.0, -0.092128694, 0.0014597238, 0.054328315], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 199, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.6728635, 0.2756923, 0.08529791, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 3.0, -0.0, -0.092128694, 0.0014597238, 0.054328315], "split_indices": [2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.209455, 11.509498, 15.699958, 5.5173573, 5.9921403, 5.2869363, 10.413021], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.012549283, 0.039643183, -0.045316167, -0.040208444, 0.14890024, -0.0, 0.07248093], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 200, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.23726211, 0.4461965, 0.0, 0.0, 0.21499428, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [6.0, 5.0, -0.045316167, -0.040208444, 2.0, -0.0, 0.07248093], "split_indices": [1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.451782, 21.385063, 8.066719, 7.8171654, 13.567897, 5.3514447, 8.216453], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.034736972, 0.06925303, -0.16873132, 0.048931744, -0.022425232, -0.10025889, 0.0010608345], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 201, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.45225894, 0.26010075, 0.43944338, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 7.0, 0.048931744, -0.022425232, -0.10025889, 0.0010608345], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.90453, 16.689247, 13.215283, 10.444163, 6.245083, 6.557364, 6.65792], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.08867304, -0.048436496, -0.018516444, 0.061751213], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 202, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.46140203, 0.38621163, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [6.0, 6.0, -0.048436496, -0.018516444, 0.061751213], "split_indices": [1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [29.932123, 19.37898, 10.553144, 8.319546, 11.059434], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.04161308, -0.1484857, -0.090373136, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 203, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.603793, 0.0, 0.3321939, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [5.0, 0.04161308, 6.0, -0.090373136, -0.0], "split_indices": [2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.285452, 13.921878, 13.363575, 6.1503882, 7.2131867], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.030485323, -0.039371155, 0.100551456, 0.0813151, -0.0, 0.034048118, -0.032972988], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 204, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.35187888, 0.0, 0.36144495, 0.0, 0.20089746, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [5.0, -0.039371155, 3.0, 0.0813151, 8.0, 0.034048118, -0.032972988], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.160019, 8.119288, 21.040731, 6.9529533, 14.087777, 7.1860056, 6.901772], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.024352763, -0.08186393, 0.07309012, -0.0, -0.049188923, -0.03907889, 0.14744252, 0.00838294, -0.03832878, 0.07460803, 0.02102375], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 205, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.22564815, 0.08302602, 0.26994014, 0.0, 0.0, 0.0870364, 0.09962797, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 4.0, -0.0, -0.049188923, 7.0, 6.0, 0.00838294, -0.03832878, 0.07460803, 0.02102375], "split_indices": [2, 2, 1, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [41.602657, 12.226056, 29.376598, 6.8407025, 5.385354, 11.363275, 18.013325, 6.219925, 5.1433496, 6.283043, 11.730282], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.011207615, -0.046781912, 0.0644867, 0.016640335, -0.038705535, 0.15198554, -0.015545647, -0.0044858246, 0.08557217], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 206, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.12283446, 0.16566442, 0.23395875, 0.0, 0.0, 0.3173973, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [5.0, 3.0, 5.0, 0.016640335, -0.038705535, 3.0, -0.015545647, -0.0044858246, 0.08557217], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.207558, 16.975872, 20.231686, 7.170213, 9.805659, 11.86126, 8.370425, 5.3261504, 6.53511], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.055391274, -0.0, -0.07192861, -0.112413526, 0.11666278, -0.0, -0.059947733, -0.0, 0.056895837], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 207, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.385149, 0.38712865, 0.0, 0.15068588, 0.14214602, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, -0.07192861, 1.0, 3.0, -0.0, -0.059947733, -0.0, 0.056895837], "split_indices": [2, 1, 0, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.125282, 27.49727, 7.6280107, 13.726924, 13.770346, 6.292558, 7.434366, 5.229317, 8.541028], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.06651962, 0.06494712, 0.028275508, -0.0045580897, 0.045342416, 0.0075605763, -0.025545457], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 208, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.23902337, 0.0, 0.17323813, 0.08300588, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.06494712, 8.0, 6.0, 0.045342416, 0.0075605763, -0.025545457], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.009228, 7.870448, 38.13878, 30.213732, 7.9250464, 21.66149, 8.552242], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.083110966, -0.05445675, 0.1749224, -0.0, 0.2333691, 0.10775267, 0.039562933], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 209, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.6982251, 0.0, 0.20867264, 0.0, 0.104356825, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.05445675, 2.0, -0.0, 5.0, 0.10775267, 0.039562933], "split_indices": [2, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [26.458109, 6.2827272, 20.17538, 5.4468474, 14.728534, 5.0029016, 9.725632], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.010601984, 0.09559788, -0.041427657, 0.006473617, 0.041499708], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 210, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.39488477, 0.04570709, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 4.0, -0.041427657, 0.006473617, 0.041499708], "split_indices": [2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [29.536541, 19.44887, 10.08767, 8.526076, 10.922794], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.070581846, -0.2583436, 0.056043275, -0.040702477, -0.095884964, -0.025469322, 0.044725284], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 211, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.72919166, 0.0032660365, 0.25444493, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 3.0, -0.040702477, -0.095884964, -0.025469322, 0.044725284], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.21021, 11.261293, 16.948917, 5.365618, 5.8956757, 6.347015, 10.601902], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0060845483, 0.05150205, -0.06499523, -0.119513266, 0.017919246, -0.009728416, -0.06337226], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 212, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.30864155, 0.0, 0.17121121, 0.10513514, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.05150205, 7.0, 3.0, 0.017919246, -0.009728416, -0.06337226], "split_indices": [0, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.426651, 6.176864, 22.249786, 16.069376, 6.18041, 9.496863, 6.572513], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.010851827, 0.038893417, -0.06632811, 0.017305493, -0.1280182, -0.05250525, -0.009419268], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 213, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.22797804, 0.0, 0.18221256, 0.0, 0.04423204, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, 0.038893417, 3.0, 0.017305493, 7.0, -0.05250525, -0.009419268], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.958694, 7.0258512, 20.932842, 6.436907, 14.495936, 8.510028, 5.9859076], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0033545198, -0.03290704, 0.036467038, -0.038715877, 0.037669465, 0.035590902, -0.012077924], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 214, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.14554483, 0.17568609, 0.0, 0.0, 0.10041503, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [6.0, 7.0, 0.036467038, -0.038715877, 9.0, 0.035590902, -0.012077924], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.955118, 22.827728, 7.1273904, 9.912435, 12.915293, 6.707349, 6.207944], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-4.0350784e-05, -0.14911798, 0.0608818, 0.0015161344, -0.095658936, 0.06243426, -0.03198789, 0.022350822, -0.03356075], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 215, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.4075811, 0.37878627, 0.44394404, 0.0, 0.0, 0.0, 0.17600435, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 2.0, 6.0, 0.0015161344, -0.095658936, 0.06243426, 5.0, 0.022350822, -0.03356075], "split_indices": [2, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [41.90611, 12.213415, 29.692696, 6.6000233, 5.613392, 11.421421, 18.271275, 7.3235884, 10.947685], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.01160617, 0.08798778, -0.11252596, -0.01621042, 0.20959064, -0.0060551716, -0.04701334, 0.030997153, 0.075061105], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 216, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.38517675, 0.4711264, 0.0408227, 0.0, 0.001124382, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 5.0, -0.01621042, 3.0, -0.0060551716, -0.04701334, 0.030997153, 0.075061105], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.838966, 24.723034, 14.115932, 11.281008, 13.442026, 5.772357, 8.3435755, 5.3775816, 8.064445], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.014532984, -0.052335266, 0.059479274, -0.03655801, 0.09302785, 0.06324782, -0.049237598], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 217, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.29758632, 0.0, 0.75069743, 0.7239957, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.052335266, 6.0, 4.0, 0.09302785, 0.06324782, -0.049237598], "split_indices": [2, 0, 1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.166348, 5.583487, 28.58286, 20.993193, 7.5896673, 6.6536264, 14.339566], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.09534515, 0.09449209, 0.058733437, 0.18121581, -0.0, 0.02393143, 0.085494325, -0.015699646, 0.051026247], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 218, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.32658005, 0.0, 0.31197685, 0.08766267, 0.26630586, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.09449209, 3.0, 6.0, 8.0, 0.02393143, 0.085494325, -0.015699646, 0.051026247], "split_indices": [1, 0, 2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.562668, 5.1763034, 41.386364, 12.952655, 28.43371, 7.9312673, 5.0213876, 22.448189, 5.9855204], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.05577151, -0.07284141, 0.017801907, -0.023389721, 0.098266415, -0.0, 0.050741095], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 219, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.43190405, 0.0, 0.17671582, 0.0, 0.087006845, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.07284141, 5.0, -0.023389721, 5.0, -0.0, 0.050741095], "split_indices": [1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.437616, 7.7627397, 20.674877, 8.886532, 11.788346, 5.430271, 6.3580747], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.014523578, 0.082783654, -0.093829185, -0.058724876, -0.04290558, 0.031977676, -0.038089972], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 220, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.70948535, 0.0, 0.09722227, 0.0, 0.24895525, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.082783654, 3.0, -0.058724876, 6.0, 0.031977676, -0.038089972], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.465813, 5.5173173, 23.948496, 6.377207, 17.57129, 5.7995186, 11.7717705], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.11788831, -0.16230357, -0.0071509294, -0.012352884, -0.2367156, -0.033537455, -0.09297501], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 221, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.10368323, 0.1393078, 0.0, 0.0, 0.027722597, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 5.0, -0.0071509294, -0.012352884, 6.0, -0.033537455, -0.09297501], "split_indices": [2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.202484, 18.620281, 10.582203, 8.1884365, 10.431845, 5.2926903, 5.139154], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.016196053, 0.06314402, -0.03240755, -0.045275737, 0.06336034, -0.0, -0.026339792], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 222, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.18553068, 0.41160804, 0.0, 0.029365595, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 4.0, -0.03240755, 2.0, 0.06336034, -0.0, -0.026339792], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.41285, 23.01231, 7.4005394, 13.295223, 9.717086, 6.6844544, 6.610769], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.08729029, -0.014329659, 0.17328615, 0.015397175, 0.22150785, 0.0958355, 0.026260085], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 223, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.35595366, 0.0, 0.06896144, 0.0, 0.09174007, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [4.0, -0.014329659, 3.0, 0.015397175, 7.0, 0.0958355, 0.026260085], "split_indices": [1, 0, 2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.854622, 10.556865, 17.297758, 6.073856, 11.223902, 5.2528734, 5.971028], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.018178543, -0.085709415, 0.052459907, -0.06985062, 0.039871406], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 224, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.38427615, 0.7661514, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 5.0, 0.052459907, -0.06985062, 0.039871406], "split_indices": [1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.011196, 21.547491, 6.463705, 13.114132, 8.433359], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.023388103, 0.024872994, -0.087498166, -0.039669402, 0.14910035, 0.015407445, -0.056841522, 0.07776385, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 225, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.14133504, 0.49749333, 0.3004842, 0.0, 0.24260369, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 6.0, 5.0, -0.039669402, 9.0, 0.015407445, -0.056841522, 0.07776385, -0.0], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [41.952564, 23.394068, 18.558496, 9.946193, 13.447875, 7.653085, 10.905412, 7.3908677, 6.057008], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.07011032, 0.015721954, 0.055601336, 0.12517963, -0.04338505, -0.01149792, 0.065016024], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 226, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.2137004, 0.49603108, 0.0, 0.28293046, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 4.0, 0.055601336, 3.0, -0.04338505, -0.01149792, 0.065016024], "split_indices": [0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.772297, 26.342634, 10.429662, 16.156271, 10.186362, 5.6153145, 10.540957], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.031904515, -0.028556228, -0.13415985, 0.03649447, 0.0052725635, -0.07381143], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 227, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.110715985, 0.0, 0.48705176, 0.33690235, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, 0.031904515, 6.0, 3.0, 0.03649447, 0.0052725635, -0.07381143], "split_indices": [1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.252773, 6.655226, 28.597548, 17.245443, 11.352105, 7.297404, 9.948039], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.04494208, 0.075515024, -0.09038525, -0.033219196, -0.21075822, -0.019672055, 0.022929965, -0.091417044, -0.024693487], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 228, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.64520675, 0.0, 0.25833544, 0.111414626, 0.09078926, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.075515024, 7.0, 8.0, 7.0, -0.019672055, 0.022929965, -0.091417044, -0.024693487], "split_indices": [0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.369102, 5.264177, 41.104923, 29.297863, 11.807061, 23.637333, 5.6605306, 5.576592, 6.230469], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.029162189, 0.0753735, -0.063731216, -0.021135528, -0.03606181, -0.039531305, 0.014338639], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 229, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.65350795, 0.0, 0.035351202, 0.1256663, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [4.0, 0.0753735, 6.0, 2.0, -0.03606181, -0.039531305, 0.014338639], "split_indices": [0, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.992418, 8.365244, 20.627174, 13.551219, 7.075955, 5.4089103, 8.142309], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.08438966, -0.07716185, -0.0015776983, 0.07093503, -0.052237973, 0.045436796, 0.00053551415], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 230, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.3849482, 0.0, 0.2832135, 0.06772737, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.07716185, 7.0, 5.0, -0.052237973, 0.045436796, 0.00053551415], "split_indices": [1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.734858, 7.9114194, 19.823439, 13.96435, 5.8590894, 5.233672, 8.730678], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.007137615, -0.04804926, 0.076318234, -0.0, 0.049998436], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 231, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.39848962, 0.0, 0.123914495, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [2.0, -0.04804926, 8.0, -0.0, 0.049998436], "split_indices": [1, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.589123, 10.187738, 18.401384, 10.871825, 7.5295587], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.11467675, -0.016520485, 0.16727151, 0.09852968, 0.041689165, -0.05097228, 0.053823568], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 232, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.2990361, 0.0, 0.4455989, 0.0, 0.47661832, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [5.0, -0.016520485, 4.0, 0.09852968, 6.0, -0.05097228, 0.053823568], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.206743, 6.6845565, 23.522186, 9.245633, 14.276553, 5.250268, 9.026285], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.003648935, 0.06439152, -0.06726626, 0.049487393, -0.0, -0.0483607, 0.0008475825, -0.042149987, 0.04565675], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 233, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.13130488, 0.097935446, 0.104256764, 0.0, 0.278195, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 4.0, 0.049487393, 6.0, -0.0483607, 0.0008475825, -0.042149987, 0.04565675], "split_indices": [1, 2, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.543543, 16.183582, 12.3599615, 5.217426, 10.966157, 5.3357964, 7.0241647, 5.4657187, 5.500438], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.020641742, -0.026196087, 0.09846707, 0.0643369, -0.0, 0.06084683, -0.0527592], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 234, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.27779543, 0.0, 0.21586832, 0.0, 0.45515123, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.026196087, 5.0, 0.0643369, 6.0, 0.06084683, -0.0527592], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.906311, 12.357132, 18.54918, 7.814942, 10.734238, 5.0050273, 5.7292104], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.05667346, -0.07155369, 0.0054155136, 0.044141203, -0.030469723, -0.027279316, 0.011347619], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 235, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.51780516, 0.0, 0.18465422, 0.0, 0.11901565, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [4.0, -0.07155369, 5.0, 0.044141203, 5.0, -0.027279316, 0.011347619], "split_indices": [0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [42.684288, 10.551483, 32.132805, 6.4290304, 25.703773, 14.234676, 11.469097], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.041020405, -0.06356799, 0.15350924, 0.016107973, -0.06953805, 0.0909463, 0.068872325, 0.053883404, -0.00052415347], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 236, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.4454055, 0.40581894, 0.033803195, 0.0, 0.0, 0.11983021, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [5.0, 2.0, 8.0, 0.016107973, -0.06953805, 4.0, 0.068872325, 0.053883404, -0.00052415347], "split_indices": [1, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.264523, 18.038677, 17.225845, 10.680348, 7.3583293, 11.377912, 5.8479347, 5.887457, 5.4904547], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.074258335, 0.1453894, -0.019631606, -0.0024753753, 0.083078265, -0.050554425, 0.021046896], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 237, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.25992125, 0.46474585, 0.2362797, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 6.0, -0.0024753753, 0.083078265, -0.050554425, 0.021046896], "split_indices": [1, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.608757, 20.553776, 15.054981, 9.562045, 10.991732, 5.752807, 9.302174], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0106719695, 0.050518356, -0.12473104, -0.02510701, 0.08573953, -0.10920905, 0.009622612, 0.039927, 0.01083849, 0.025798688, -0.015081484], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 238, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.3587784, 0.16014908, 0.6215582, 0.0, 0.040954188, 0.0, 0.06421486, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [7.0, 1.0, 4.0, -0.02510701, 4.0, -0.10920905, 7.0, 0.039927, 0.01083849, 0.025798688, -0.015081484], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.439816, 31.31846, 17.121357, 5.634224, 25.684237, 5.809245, 11.312112, 11.249266, 14.43497, 5.514376, 5.797736], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.05845148, -0.08940248, 0.01280528, -0.077591896, 0.04962068, -0.062161572, 0.017010799], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 239, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.5050265, 0.0, 0.32481197, 0.2717448, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.08940248, 7.0, 7.0, 0.04962068, -0.062161572, 0.017010799], "split_indices": [2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [26.691212, 5.7113914, 20.97982, 13.072176, 7.907645, 6.7546725, 6.3175035], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0091298735, 0.06570926, -0.09584992, -0.053653564, -0.0, 0.0025017145, -0.0026353833], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 240, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.6070978, 0.0, 0.181345, 0.0, 0.00093097996, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, 0.06570926, 6.0, -0.053653564, 7.0, 0.0025017145, -0.0026353833], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.244543, 7.316347, 21.928196, 11.255231, 10.672965, 5.1581974, 5.5147676], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.047160394, -0.08453007, -0.076559305, 0.036668412], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 241, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.3993375, 0.0, 0.73343384, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [5.0, 0.047160394, 8.0, -0.076559305, 0.036668412], "split_indices": [0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.038359, 9.5142765, 18.524084, 10.284315, 8.239768], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.0735525, -0.06776334, -0.015273371, 0.158974, 0.094016045, -0.016167706], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 242, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.5585604, 0.28463066, 0.0, 0.0, 0.553645, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 3.0, -0.06776334, -0.015273371, 7.0, 0.094016045, -0.016167706], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.291271, 23.993774, 7.297498, 9.453236, 14.540538, 8.425937, 6.114601], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.008910012, 0.10404835, -0.112565584, -0.008202836, 0.08633092, -0.0025168774, -0.053222083], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 243, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.34936568, 0.45410472, 0.06695616, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 5.0, -0.008202836, 0.08633092, -0.0025168774, -0.053222083], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.290087, 16.406988, 11.883098, 9.744752, 6.662237, 5.5837293, 6.2993684], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0036275983, 0.06570942, -0.076204166, -0.011513874, 0.046487454, -0.056783643, 0.019829312], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 244, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.15686914, 0.1584293, 0.27922705, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 9.0, -0.011513874, 0.046487454, -0.056783643, 0.019829312], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.969212, 14.158991, 14.810222, 6.2819676, 7.8770227, 8.460236, 6.349986], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.015991801, -0.111407615, 0.06054988, 0.006187879, -0.1673051, 0.12782347, -0.029703734, -0.08420487, -0.018561928, 0.092575185, -0.008790787], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 245, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.30895498, 0.15866074, 0.25857133, 0.0, 0.11597493, 0.5142263, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 9.0, 0.006187879, 6.0, 4.0, -0.029703734, -0.08420487, -0.018561928, 0.092575185, -0.008790787], "split_indices": [0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.68353, 18.042702, 21.640825, 5.0221143, 13.020589, 15.80349, 5.8373346, 5.015935, 8.004654, 7.1967435, 8.606747], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.037711218, 0.1134611, -0.091496386, -0.062435377, 0.10622373, -0.07368054, 0.03507352], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 246, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [1.5164915, 1.0506742, 0.0, 0.50974095, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 5.0, -0.091496386, 4.0, 0.10622373, -0.07368054, 0.03507352], "split_indices": [2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.201965, 22.613224, 12.588742, 13.251235, 9.361988, 6.6488996, 6.6023355], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.037463542, 0.0623597, -0.12945224, -0.07426077, 0.020007255], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 247, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.81047755, 0.0, 0.64646393, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [2.0, 0.0623597, 5.0, -0.07426077, 0.020007255], "split_indices": [0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [34.068558, 8.650727, 25.417831, 16.020721, 9.397109], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.05608802, -0.082987726, 0.021147033, 0.080374196, -0.05839517, 0.00055173395, 0.063695915, 0.008248503, -0.052157346], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 248, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.8241122, 0.0, 0.17206702, 0.19827384, 0.16641596, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.082987726, 5.0, 8.0, 5.0, 0.00055173395, 0.063695915, 0.008248503, -0.052157346], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.539257, 11.478001, 34.061256, 20.238441, 13.822817, 13.797738, 6.4407024, 7.8667216, 5.9560957], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.10424592, -0.0, -0.21607588, 0.050980423, -0.042558786, -0.10126239, -0.005649252], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 249, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.33163795, 0.3999514, 0.28577644, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 7.0, 0.050980423, -0.042558786, -0.10126239, -0.005649252], "split_indices": [2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.25934, 14.578576, 12.680763, 6.5755687, 8.003008, 7.0939674, 5.586796], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0871255, 0.09305803, 0.023622198, 0.116188265, -0.058081288, -0.00022011685, 0.061310306], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 250, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.38479382, 0.0, 0.5136869, 0.20966871, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.09305803, 6.0, 2.0, -0.058081288, -0.00022011685, 0.061310306], "split_indices": [2, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.037867, 5.224915, 23.812952, 17.346582, 6.4663696, 7.3966203, 9.949962], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.025745774, 0.0913641, -0.11534383, -0.035283323, -0.091891676, 0.01599757, -0.028536845], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 251, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.8669016, 0.0, 0.31546658, 0.105278626, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, 0.0913641, 7.0, 5.0, -0.091891676, 0.01599757, -0.028536845], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.745895, 5.1704617, 22.575434, 17.110365, 5.4650693, 6.2674437, 10.842921], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.045027133, -0.0567628, -0.0026500819, 0.08457732, -0.08605809, -0.004703719, 0.044678822], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 252, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.17204285, 0.0, 0.68414485, 0.14627995, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, -0.0567628, 7.0, 3.0, -0.08605809, -0.004703719, 0.044678822], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.379658, 5.5776815, 24.801975, 19.245783, 5.556193, 7.2121396, 12.033644], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.02745232, -0.017288389, 0.08314814, -0.0047899615, 0.13124955, 0.062997214, 0.008367259], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 253, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.14989959, 0.0, 0.105644286, 0.0, 0.082525626, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [4.0, -0.017288389, 3.0, -0.0047899615, 7.0, 0.062997214, 0.008367259], "split_indices": [1, 0, 2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.083874, 10.782498, 18.301374, 5.518805, 12.78257, 6.120586, 6.661984], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.038895458, -0.08891384, 0.039139874, 0.010223136, -0.061564576, -0.03377601, 0.036557805], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 254, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.25075147, 0.28936046, 0.0, 0.19322474, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 7.0, 0.039139874, 4.0, -0.061564576, -0.03377601, 0.036557805], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.721607, 22.22365, 5.4979568, 12.02534, 10.19831, 5.2955165, 6.7298236], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.017980391, -0.08607745, 0.083767325, -0.042593513, -0.008924119, 0.17764, -0.012133328, 0.0017512682, 0.115672424], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 255, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.27953616, 0.026864491, 0.318103, 0.0, 0.0, 0.5002223, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 6.0, 7.0, -0.042593513, -0.008924119, 8.0, -0.012133328, 0.0017512682, 0.115672424], "split_indices": [1, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.8146, 14.32604, 24.488562, 5.536276, 8.789764, 14.161208, 10.3273535, 8.580617, 5.5805907], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.09150297, 0.073570035, -0.19000861, -0.001692291, 0.04875205, -0.09296149, -0.10687676, -0.07293294, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 256, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.64079607, 0.1186133, 0.16785938, 0.0, 0.0, 0.0, 0.26057658, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 5.0, 6.0, -0.001692291, 0.04875205, -0.09296149, 4.0, -0.07293294, -0.0], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.832085, 13.480242, 23.351843, 7.0306325, 6.449609, 7.9536114, 15.3982315, 6.5781684, 8.820063], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.11737035, -0.0078623425, 0.14817794, -0.009146175, 0.19165862, 0.08819387, 0.0271483], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 257, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.17307794, 0.0, 0.25460058, 0.0, 0.19034082, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.0078623425, 1.0, -0.009146175, 5.0, 0.08819387, 0.0271483], "split_indices": [2, 0, 1, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.49333, 5.546645, 28.946682, 5.274079, 23.672604, 10.330021, 13.342582], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.074306615, 0.015838189, -0.09510488, -0.049863465, -0.066339485, 0.029153919, -0.035374455], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 258, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.13069385, 0.0, 0.048584253, 0.0, 0.27927303, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.015838189, 2.0, -0.049863465, 4.0, 0.029153919, -0.035374455], "split_indices": [0, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.94027, 5.4272494, 39.51302, 9.003133, 30.509888, 6.638726, 23.871162], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.025001697, 0.06729189, -0.057465684, -0.12428674, 0.018540293, -0.090517536, 0.011435826], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 259, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.46952972, 0.0, 0.16833575, 0.4207539, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [4.0, 0.06729189, 8.0, 7.0, 0.018540293, -0.090517536, 0.011435826], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [25.7676, 7.393026, 18.374575, 12.286969, 6.087605, 5.756045, 6.5309243], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.061736934, 0.00583748, 0.039779518, -0.032604855, 0.045315146], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 260, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.10283989, 0.32682943, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [5.0, 7.0, 0.039779518, -0.032604855, 0.045315146], "split_indices": [1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.652628, 17.41125, 11.241378, 9.490003, 7.921247], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.08893928, 0.005472863, -0.22189727, 0.04782029, -0.045790505, -0.09918754, -0.023002405], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 261, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.4045132, 0.4707501, 0.14058632, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 4.0, 0.04782029, -0.045790505, -0.09918754, -0.023002405], "split_indices": [0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.403961, 17.315277, 12.088684, 9.089941, 8.225336, 5.7891126, 6.2995715], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.089364946, -0.11137742, 0.0010983741, -0.052737094, -0.055165283, -0.0, -0.029548043], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 262, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.07720977, 0.06488305, 0.0, 0.0, 0.04427425, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 3.0, 0.0010983741, -0.052737094, 4.0, -0.0, -0.029548043], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.245537, 25.976503, 5.269034, 10.284901, 15.691602, 6.8915167, 8.800086], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.02200294, -0.13267492, 0.049449254, 0.018018985, -0.0741549], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 263, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.5890552, 0.43554652, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 3.0, 0.049449254, 0.018018985, -0.0741549], "split_indices": [0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [26.585941, 17.214157, 9.371784, 6.267301, 10.9468565], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.099402316, -0.22881843, 0.019492297, -0.021800805, -0.11330152, 0.03731851, -0.016071813], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 264, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.51844656, 0.29532802, 0.14572944, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 6.0, -0.021800805, -0.11330152, 0.03731851, -0.016071813], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.04378, 14.824224, 16.219553, 8.288975, 6.5352497, 6.9324465, 9.287107], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.10534962, 0.013935957, -0.15650536, 0.032195, -0.016776478, -0.25360096, 0.040127933, -0.0101547595, -0.096723735], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 265, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.26055568, 0.091629654, 0.8538319, 0.0, 0.0, 0.28741097, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [2.0, 5.0, 8.0, 0.032195, -0.016776478, 4.0, 0.040127933, -0.0101547595, -0.096723735], "split_indices": [1, 2, 2, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.00525, 11.338578, 27.666672, 5.2354527, 6.1031256, 21.08457, 6.5821013, 5.7301874, 15.354383], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.06939049, -0.11015386, -0.0, -0.09957981, -0.017925298, 0.00576552, -0.0040514614, -0.046389226, 0.011011371], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 266, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.11907002, 0.47439188, 0.00452935, 0.0, 0.16063173, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 7.0, -0.09957981, 1.0, 0.00576552, -0.0040514614, -0.046389226, 0.011011371], "split_indices": [1, 2, 2, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.76008, 24.194048, 13.566032, 6.024311, 18.169737, 8.041209, 5.5248227, 5.1878004, 12.981936], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.036265396, 0.11285859, -0.053890422, 0.07159444, 0.041264854, -0.0, -0.035768654, -0.025438914, 0.06577471], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 267, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.26630586, 0.14980456, 0.07075426, 0.0, 0.36845958, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 6.0, 0.07159444, 6.0, -0.0, -0.035768654, -0.025438914, 0.06577471], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.83059, 19.835999, 15.994591, 5.841827, 13.994171, 8.545403, 7.449188, 8.169598, 5.824573], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.034225978, 0.043740116, -0.14048141, -0.16033064, 0.18896827, -0.10049621, 0.015503409, -0.0907041, -0.0, 0.10085413, 0.02313917], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 268, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.4030736, 0.8387389, 0.8142401, 0.2915113, 0.20646107, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 8.0, 5.0, 4.0, -0.10049621, 0.015503409, -0.0907041, -0.0, 0.10085413, 0.02313917], "split_indices": [1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.745506, 26.181652, 19.563852, 10.556031, 15.62562, 9.611802, 9.95205, 5.438528, 5.1175036, 5.4938874, 10.131732], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.032029007, 0.09092185, -0.045823056, 0.0012113426, 0.14017363, -0.0043311105, 0.08265114], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 269, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.31491625, 0.08106129, 0.0, 0.0, 0.31382382, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 5.0, -0.045823056, 0.0012113426, 5.0, -0.0043311105, 0.08265114], "split_indices": [0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.231659, 21.445448, 5.786211, 8.884627, 12.560821, 5.934835, 6.625985], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.07441732, -0.0, 0.10436871, 0.16954923, -0.012483067, 0.01574208, 0.06901417], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 270, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.06529075, 0.0, 0.2239588, 0.06946364, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.0, 7.0, 3.0, -0.012483067, 0.01574208, 0.06901417], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.404078, 8.952654, 20.451424, 14.500448, 5.9509754, 6.1395354, 8.360913], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.07596715, 0.17225245, -0.008110693, 0.078847244, 0.011652872, -0.03918901, 0.035131864], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 271, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.2487462, 0.1275469, 0.25642523, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [3.0, 5.0, 7.0, 0.078847244, 0.011652872, -0.03918901, 0.035131864], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.526468, 12.897265, 14.629202, 6.6577168, 6.239549, 7.7760534, 6.8531485], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.027486503, 0.038210634, -0.054772235, -0.040790375, 0.072531514, 0.00039531032, -0.038559377], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 272, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.33129627, 0.37954092, 0.0, 0.07260309, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [6.0, 4.0, -0.054772235, 7.0, 0.072531514, 0.00039531032, -0.038559377], "split_indices": [1, 1, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.414108, 20.738031, 8.676078, 15.162072, 5.5759587, 10.140679, 5.0213923], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.083337314, 0.014407892, -0.14285302, -0.07342144, -0.0006897925], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 273, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.24239524, 0.0, 0.27079016, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [3.0, 0.014407892, 7.0, -0.07342144, -0.0006897925], "split_indices": [1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.913544, 8.2396755, 19.673868, 10.5637, 9.110168], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.047716264, -0.14447317, 0.03826886, -0.012637385, -0.069906466, 0.073144265, -0.06459953, -0.0, -0.039853804], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 274, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.27484402, 0.10212237, 0.38692665, 0.0, 0.0, 0.0, 0.06028132, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 2.0, 5.0, -0.012637385, -0.069906466, 0.073144265, 6.0, -0.0, -0.039853804], "split_indices": [1, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.094412, 14.387405, 15.7070055, 7.8820252, 6.5053806, 5.106156, 10.60085, 5.4419403, 5.15891], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0087381955, -0.056145158, 0.04037986, -0.052040394, 0.15955396, 0.08942028, 0.006351417], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 275, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.37621564, 0.0, 0.8432579, 0.0, 0.3720777, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.056145158, 3.0, -0.052040394, 6.0, 0.08942028, 0.006351417], "split_indices": [2, 0, 1, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.393776, 8.3369665, 31.05681, 10.65057, 20.40624, 9.2604475, 11.145793], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.045087572, 0.08020898, -0.017993087, -0.009032934, 0.13881633, 0.05248572, 0.012082799], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 276, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.14583771, 0.20496608, 0.0, 0.0, 0.042480648, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 3.0, -0.017993087, -0.009032934, 6.0, 0.05248572, 0.012082799], "split_indices": [2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.67027, 28.373747, 8.296523, 9.420733, 18.953012, 12.557689, 6.3953247], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.068291515, -0.13214722, 0.025936019, -0.17912017, -0.0, -0.025170725, -0.10671743], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 277, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.39616948, 0.18850252, 0.0, 0.28207153, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [6.0, 7.0, 0.025936019, 3.0, -0.0, -0.025170725, -0.10671743], "split_indices": [1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.474064, 27.088297, 10.385768, 20.16334, 6.924958, 14.426499, 5.7368393], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.020102447, -0.103465006, 0.065101326, -0.08608772, 0.016983831, 0.065533176, 0.019591194, 0.02100221, -0.013384822], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 278, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.26213887, 0.3987763, 0.21884649, 0.0, 0.0, 0.0, 0.09917225, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [2.0, 5.0, 3.0, -0.08608772, 0.016983831, 0.065533176, 5.0, 0.02100221, -0.013384822], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.622482, 11.238867, 34.383614, 5.158198, 6.0806684, 6.496613, 27.887001, 16.442955, 11.444047], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0043812348, 0.055839848, -0.109620064, -0.20007981, 0.021072948, -0.011384817, -0.09581721], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 279, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.5817919, 0.0, 0.34024346, 0.21209025, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [4.0, 0.055839848, 7.0, 7.0, 0.021072948, -0.011384817, -0.09581721], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.381752, 9.13171, 18.250042, 12.438066, 5.811977, 6.186927, 6.251138], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.027040895, 0.018633638, -0.060853574, 0.06429681, -0.042844255, -0.0827225, 0.041366525], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 280, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.28413948, 0.3434315, 0.0, 0.0, 0.9147572, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, -0.060853574, 0.06429681, 6.0, -0.0827225, 0.041366525], "split_indices": [0, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.620886, 25.357994, 6.2628927, 5.8565507, 19.501442, 8.53308, 10.968363], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.09494638, 0.0065445374, -0.13740447, -0.099516764, -0.026732212, 0.0009892319, -0.020217892], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 281, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.14619987, 0.0, 0.39052808, 0.0, 0.024104752, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.0065445374, 3.0, -0.099516764, 6.0, 0.0009892319, -0.020217892], "split_indices": [1, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [25.554552, 6.3160596, 19.238491, 5.9284406, 13.310051, 6.7698016, 6.54025], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.03642691, -0.05692451, 0.010525809, 0.06300012, -0.03522544, -0.0072321533, 0.04217009, -0.042792756, 0.013182964], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 282, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.24191776, 0.0, 0.0627504, 0.10004395, 0.12222442, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [5.0, -0.05692451, 7.0, 2.0, 8.0, -0.0072321533, 0.04217009, -0.042792756, 0.013182964], "split_indices": [2, 0, 2, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.837975, 6.7815957, 23.05638, 11.581601, 11.474778, 5.172687, 6.408914, 5.0645294, 6.4102488], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.08762888, -0.06434671, -0.003554364, 0.015053292, -0.029457012], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 283, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.290676, 0.0, 0.10712611, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, -0.06434671, 6.0, 0.015053292, -0.029457012], "split_indices": [1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.369154, 10.215052, 18.154102, 11.205161, 6.9489408], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.07183495, 0.057941575, -0.034795243, -0.036927924, 0.013487959], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 284, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.42369252, 0.0, 0.1324538, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, 0.057941575, 7.0, -0.036927924, 0.013487959], "split_indices": [2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [30.012705, 14.099497, 15.913208, 7.88931, 8.023898], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.012359523, -0.05070917, 0.028600993, 0.047973022, -0.011422128, -0.03880975, 0.016244229], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 285, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.27730757, 0.0, 0.18628593, 0.0, 0.20851552, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.05070917, 5.0, 0.047973022, 7.0, -0.03880975, 0.016244229], "split_indices": [2, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.27069, 7.997092, 31.273598, 7.189425, 24.084173, 8.802592, 15.281581], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.054367956, -0.17670414, 0.06757594, -0.06400656, -0.02422219, -0.009198307, 0.034596633], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 286, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.5808563, 0.021449566, 0.1013369, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 3.0, -0.06400656, -0.02422219, -0.009198307, 0.034596633], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.40427, 18.3581, 18.04617, 11.645146, 6.7129536, 5.3343186, 12.711849], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.07920928, -0.007888851, 0.15676053, 0.024685625, -0.02498164, 0.22536926, 0.0036262777, 0.083644524, 0.034250263], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 287, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.25697488, 0.12346026, 0.16726029, 0.0, 0.0, 0.004431188, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 1.0, 7.0, 0.024685625, -0.02498164, 5.0, 0.0036262777, 0.083644524, 0.034250263], "split_indices": [1, 1, 1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.6575, 16.13972, 18.517782, 6.703185, 9.436535, 11.713738, 6.8040442, 6.286002, 5.4277363], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.079708345, 0.028490087, 0.06838142, 0.11520706, -0.043585394, 0.010871701, 0.079733014], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 288, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.2908228, 0.5149212, 0.0, 0.23517483, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 6.0, 0.06838142, 4.0, -0.043585394, 0.010871701, 0.079733014], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [41.53907, 32.259262, 9.279808, 22.128967, 10.130296, 15.765425, 6.3635426], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.0899, 0.08904782, 0.05242819, 0.008191709, 0.019877784, -0.01228833], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 289, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.7855936, 0.0, 0.13453604, 0.0, 0.04264357, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.0899, 6.0, 0.05242819, 6.0, 0.019877784, -0.01228833], "split_indices": [2, 0, 2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.041607, 5.7827497, 21.258858, 9.155394, 12.103464, 6.3216786, 5.7817855], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.03306434, -0.027843203, 0.049383037, -0.16772485, -0.13314393, 0.009541974], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 290, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.09805081, 0.0, 0.6660433, 0.0, 0.85536146, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.03306434, 3.0, 0.049383037, 6.0, -0.13314393, 0.009541974], "split_indices": [2, 0, 1, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.262878, 5.559076, 22.703802, 9.180847, 13.522955, 5.3697796, 8.153175], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.033883568, -0.03831385, 0.054379117, -0.018095711, -0.0020119592], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 291, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.32675385, 0.006554313, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [5.0, 7.0, 0.054379117, -0.018095711, -0.0020119592], "split_indices": [1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.712461, 18.641855, 9.070607, 8.589551, 10.052303], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.071277656, -0.0, -0.053293675, -0.04030626, 0.015856264, -0.0, -0.022597708], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 292, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.22735317, 0.04384365, 0.0, 0.021396343, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [6.0, 4.0, -0.053293675, 3.0, 0.015856264, -0.0, -0.022597708], "split_indices": [0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.051056, 18.84785, 11.203205, 11.315285, 7.532566, 5.018143, 6.2971416], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0976702, 0.18903677, -0.0, 0.02410901, 0.07511237, 0.034708004, -0.044236355], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 293, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.24015376, 0.03938344, 0.255356, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 5.0, 7.0, 0.02410901, 0.07511237, 0.034708004, -0.044236355], "split_indices": [2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [25.851847, 12.888554, 12.963293, 6.071698, 6.816856, 7.373935, 5.5893583], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.08980816, -0.044209257, 0.057892155, -0.009522112], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 294, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.40570295, 0.26120123, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [6.0, 7.0, -0.044209257, 0.057892155, -0.009522112], "split_indices": [2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.660421, 18.0558, 10.604623, 9.893591, 8.162208], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.015394097, -0.042731497, 0.020173464, 0.09336738, -0.056130078, 0.03767498, 0.009897811, 0.00044633643, -0.044797238], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 295, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.18436573, 0.0, 0.17387122, 0.011342987, 0.09239322, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [4.0, -0.042731497, 6.0, 4.0, 8.0, 0.03767498, 0.009897811, 0.00044633643, -0.044797238], "split_indices": [0, 0, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.459347, 7.932385, 28.52696, 15.114934, 13.412026, 8.071566, 7.0433683, 8.232952, 5.1790743], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.064564146, -0.06610393, -0.022912312, -0.11001638, 0.08055326, -0.1093218, 0.02282526, 0.09436688, -0.038295463], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 296, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.21708526, 0.0, 0.29653105, 0.91345036, 0.7649377, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.06610393, 6.0, 5.0, 6.0, -0.1093218, 0.02282526, 0.09436688, -0.038295463], "split_indices": [1, 0, 2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.98341, 6.39065, 30.59276, 17.10003, 13.492729, 7.046741, 10.053289, 6.3384223, 7.1543074], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.03234644, -0.06808576, 0.010213685, -0.13356178, 0.11330548, -0.12872323, 0.04287644, 0.14089495, -0.011395406], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 297, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.3374133, 0.0, 0.48127905, 1.1720635, 1.1037519, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.06808576, 4.0, 5.0, 4.0, -0.12872323, 0.04287644, 0.14089495, -0.011395406], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.841587, 6.255487, 30.586102, 12.244256, 18.341845, 5.8264756, 6.417781, 5.0362635, 13.305581], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.03956884, -0.0995, 0.05792211, -0.0032668696, -0.09501203, 0.021920823, 0.016293235, -0.06118277], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 298, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.1954948, 0.25826794, 0.5725154, 0.0, 0.35938996, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 2.0, 3.0, 0.05792211, 5.0, -0.09501203, 0.021920823, 0.016293235, -0.06118277], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.301395, 35.294865, 13.006531, 7.444425, 27.85044, 5.6452713, 7.36126, 21.82071, 6.0297303], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.088423945, -0.0030637207, -0.16789417, -0.03604005, 0.031834513, -0.012528187, -0.092329875], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 299, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.17983998, 0.21429229, 0.20709941, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 7.0, -0.03604005, 0.031834513, -0.012528187, -0.092329875], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.107, 14.658722, 13.448279, 7.467303, 7.1914186, 8.130171, 5.3181086], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.032474212, 0.065329, -0.08635432, -0.0, 0.065929994, -0.03817576, 0.019776704], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 300, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.8600498, 0.23243006, 0.0, 0.15840593, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [6.0, 4.0, -0.08635432, 5.0, 0.065929994, -0.03817576, 0.019776704], "split_indices": [1, 1, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.790745, 23.242071, 8.548673, 17.287403, 5.9546685, 5.295667, 11.991735], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.05474306, -0.21837346, 0.084787205, -0.006056108, -0.12979612, -0.041065063, 0.063627966], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 301, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.68015784, 0.5268565, 0.48006707, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 3.0, -0.006056108, -0.12979612, -0.041065063, 0.063627966], "split_indices": [0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.394411, 12.678116, 14.716295, 7.397293, 5.2808228, 5.0544777, 9.661818], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.0539343, -0.05256807, -0.0092909895, 0.06869123, 0.014510763, -0.04470631], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 302, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.29245293, 0.29006654, 0.0, 0.16388541, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 7.0, -0.05256807, 5.0, 0.06869123, 0.014510763, -0.04470631], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.281275, 23.033405, 6.2478685, 17.197186, 5.836221, 12.129902, 5.067283], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.08928044, 0.14259797, 0.0042300154, 0.014722514, 0.070692055, -0.03340242, 0.03024949], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 303, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.12184018, 0.11514804, 0.15983984, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 8.0, 0.014722514, 0.070692055, -0.03340242, 0.03024949], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.239449, 16.833956, 12.405493, 9.693051, 7.1409035, 5.107392, 7.298101], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.016540827, -0.016456774, 0.043958418, -0.03162315, 0.014794906, 0.0491924, -0.021080783], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 304, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.14705448, 0.079457566, 0.0, 0.0, 0.25399348, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 2.0, 0.043958418, -0.03162315, 5.0, 0.0491924, -0.021080783], "split_indices": [0, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.689623, 23.761734, 5.9278884, 6.3926854, 17.36905, 6.397491, 10.971559], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.00015266454, 0.08885982, -0.10179079, -0.019421969, 0.18986903, -0.06015687, -0.006183389, 0.081927344, 0.014754433], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 305, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.36116058, 0.36544043, 0.11902705, 0.0, 0.116995275, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 4.0, -0.019421969, 5.0, -0.06015687, -0.006183389, 0.081927344, 0.014754433], "split_indices": [2, 2, 1, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.080406, 20.955254, 17.125153, 8.063122, 12.892131, 6.508018, 10.617134, 7.062486, 5.8296447], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.03514631, 0.1347159, -0.043053437, -0.006733253, 0.065736435, -0.06576686, 0.032569475, 0.042387977, -0.02671485], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 306, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.31384453, 0.259248, 0.31483546, 0.0, 0.0, 0.0, 0.22477935, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 1.0, 3.0, -0.006733253, 0.065736435, -0.06576686, 7.0, 0.042387977, -0.02671485], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.385067, 16.690092, 20.694977, 5.674225, 11.015868, 6.042338, 14.6526375, 8.085188, 6.5674496], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.014326981, 0.00252634, -0.035490952, 0.083056755, -0.031141344, -0.015721874, 0.068989746], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 307, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.07931287, 0.2914101, 0.0, 0.42823958, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 4.0, -0.035490952, 4.0, -0.031141344, -0.015721874, 0.068989746], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.483482, 32.26728, 5.216204, 19.020962, 13.246318, 9.876523, 9.144438], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [3.993349e-05, 0.07662384, -0.058351252, -0.020573119, 0.13734044, 0.08009207, 0.024742076], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 308, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.69270927, 0.31866214, 0.0, 0.0, 0.12087244, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 3.0, -0.058351252, -0.020573119, 1.0, 0.08009207, 0.024742076], "split_indices": [1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.284718, 33.29717, 11.987549, 9.29416, 24.003008, 5.4464116, 18.556597], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.071640044, -0.13566774, 0.025324965, -0.05904386, -0.017573839], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 309, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.29899862, 0.061350435, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.025324965, -0.05904386, -0.017573839], "split_indices": [2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.264864, 19.879887, 7.384978, 9.496849, 10.383038], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.0047391793, -0.055883124, 0.090415485, -0.081889845, 0.036923543, 0.07081051, -0.006223637, -0.02422485, 0.05113396], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 310, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.17520832, 0.4110449, 0.25895777, 0.0, 0.23766297, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 5.0, -0.081889845, 2.0, 0.07081051, -0.006223637, -0.02422485, 0.05113396], "split_indices": [1, 2, 2, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.095833, 17.73065, 13.365184, 5.0513554, 12.679294, 5.6889544, 7.6762295, 6.5516586, 6.127635], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.03573743, -0.06558463, 0.115716636, 0.035143003, -0.0674165, 0.053111468, 0.013083937], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 311, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.25785658, 0.427832, 0.049035788, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 4.0, 0.035143003, -0.0674165, 0.053111468, 0.013083937], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.26462, 12.437617, 16.827002, 5.6199937, 6.8176236, 7.557433, 9.269569], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.002826134, 0.035796273, -0.07405836, -0.06418206, 0.01870688], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 312, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.25651836, 0.0, 0.39951777, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [2.0, 0.035796273, 6.0, -0.06418206, 0.01870688], "split_indices": [1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.986729, 9.543259, 18.44347, 9.187355, 9.256115], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.04479804, 0.05349223, 0.0040738853, -0.09704783, -0.057764918, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 313, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.24492899, 0.08477488, 0.0, 0.0, 0.11868648, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 5.0, 0.05349223, 0.0040738853, 3.0, -0.057764918, -0.0], "split_indices": [2, 1, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.773909, 23.624517, 5.149391, 10.603574, 13.020945, 5.762966, 7.257978], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.008907085, 0.04347655, -0.07494383, -0.06130587, -0.01077868, 0.015381757, -0.028018627], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 314, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.31292742, 0.0, 0.16228811, 0.0, 0.09383155, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.04347655, 4.0, -0.06130587, 4.0, 0.015381757, -0.028018627], "split_indices": [1, 0, 1, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.424616, 8.024085, 21.40053, 5.852952, 15.547579, 8.404311, 7.143267], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.064806156, -0.04902939, 0.11651911, 0.04161609, -0.06470274, -0.0, 0.073786944, 0.024795452, -0.018017296], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 315, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.24674419, 0.4297021, 0.41063887, 0.0, 0.0, 0.086493604, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 3.0, 6.0, 0.04161609, -0.06470274, 6.0, 0.073786944, 0.024795452, -0.018017296], "split_indices": [2, 1, 1, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.7981, 11.531653, 27.266447, 5.219117, 6.3125362, 15.134627, 12.13182, 6.6574664, 8.477161], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.11078505, 0.09462214, -0.006429735, -0.14096096, 0.00222216, 0.14124791, -0.06341528, -0.00993893, 0.07251241, 0.0066047567], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 316, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.4186982, 0.031121925, 0.070093036, 0.0, 0.066904545, 0.0, 0.12377399, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 5.0, -0.006429735, 7.0, 0.00222216, 7.0, -0.06341528, -0.00993893, 0.07251241, 0.0066047567], "split_indices": [1, 2, 2, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.835564, 17.685797, 20.149767, 5.733305, 11.952493, 8.170395, 11.979372, 6.088477, 5.864015, 5.488813, 6.4905586], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.017332733, 0.037495084, -0.066525824, -0.15094426, 0.031753745, -0.08578496, -0.010548383], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 317, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.262363, 0.0, 0.43900725, 0.27214986, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.037495084, 6.0, 5.0, 0.031753745, -0.08578496, -0.010548383], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.12253, 8.360885, 27.761644, 19.108011, 8.653634, 7.744434, 11.363578], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.06534457, -0.1292608, -0.0, -0.020421056, -0.11793517, -0.03713004, 0.11301524, 0.021191388, -0.029586492, 0.088596925, -0.023690255], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 318, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.19933811, 0.63154876, 0.34054017, 0.13872099, 0.0, 0.0, 0.49888346, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 6.0, 1.0, -0.11793517, -0.03713004, 7.0, 0.021191388, -0.029586492, 0.088596925, -0.023690255], "split_indices": [1, 1, 2, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.097343, 22.710878, 22.386465, 17.109617, 5.6012616, 10.4171095, 11.969357, 7.3587832, 9.7508335, 6.1304502, 5.8389063], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.06761159, 0.06302817, -0.0, -0.046031192, 0.03746109], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 319, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.24944289, 0.0, 0.40885583, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, 0.06302817, 4.0, -0.046031192, 0.03746109], "split_indices": [2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.04154, 7.5553846, 19.486155, 8.286428, 11.199727], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.0303048, 0.08142395, -0.026165515, -0.0, 0.10314068, 0.044250056, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 320, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.20051655, 0.03788665, 0.0, 0.0, 0.08751461, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [6.0, 4.0, -0.026165515, -0.0, 4.0, 0.044250056, -0.0], "split_indices": [1, 2, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.291027, 22.613066, 8.67796, 5.3353863, 17.27768, 11.959579, 5.3181005], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.09299054, -0.0007283896, 0.13043618, 0.0043895924, 0.16097747, 0.066344835, 0.024779137], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 321, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.11714828, 0.0, 0.056764632, 0.0, 0.02857861, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [4.0, -0.0007283896, 6.0, 0.0043895924, 7.0, 0.066344835, 0.024779137], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.115736, 7.434885, 20.680851, 5.3079386, 15.372913, 6.944417, 8.428496], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.023321591, -0.08806546, 0.11591796, 0.107592724, -0.035510704, -0.073750906, 0.03674475], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 322, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.91721493, 0.0, 0.9537627, 0.0, 0.5701749, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [5.0, -0.08806546, 4.0, 0.107592724, 6.0, -0.073750906, 0.03674475], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.705877, 5.9826217, 23.723255, 8.842166, 14.881088, 6.416364, 8.464725], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.021839144, -0.13673848, 0.051020578, -0.085734844, 0.008150108, 0.055202443, -0.032541033], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 323, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.2463247, 0.31460035, 0.3894708, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 6.0, -0.085734844, 0.008150108, 0.055202443, -0.032541033], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [26.566805, 10.501603, 16.0652, 5.4302416, 5.0713615, 9.018559, 7.046641], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.06291244, -0.004874532, 0.10059125, 0.19884975, -0.0, 0.099514276, 0.003731489, -0.042899463, 0.043891698], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 324, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.10253387, 0.0, 0.22194698, 0.23990542, 0.26679766, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.004874532, 5.0, 3.0, 8.0, 0.099514276, 0.003731489, -0.042899463, 0.043891698], "split_indices": [1, 0, 1, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.00061, 9.002228, 20.99838, 10.242438, 10.755943, 5.211808, 5.03063, 5.529391, 5.226552], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.008095578, -0.15312186, 0.08984944, -0.02583895, -0.060292356, 0.17738721, -0.058199, 0.0085315425, 0.103015214], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 325, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.60314435, 0.009529024, 0.6469547, 0.0, 0.0, 0.4397472, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 7.0, 9.0, -0.02583895, -0.060292356, 7.0, -0.058199, 0.0085315425, 0.103015214], "split_indices": [1, 0, 1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.047207, 16.32805, 23.719158, 8.840835, 7.487216, 18.684305, 5.034853, 10.782722, 7.9015822], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.001999241, 0.122616746, -0.1005596, 0.009371056, 0.062539026, -0.09756053, 0.0049104583, -0.02037314, 0.022618659], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 326, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.47135434, 0.10969275, 0.5088981, 0.0, 0.0, 0.0, 0.07943582, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 5.0, 6.0, 0.009371056, 0.062539026, -0.09756053, 4.0, -0.02037314, 0.022618659], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.928364, 16.868784, 19.05958, 9.395953, 7.472831, 5.72679, 13.33279, 5.8778925, 7.4548974], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.09607624, 0.13574527, 0.023160098, -0.0026440725, 0.0776868, -0.03343531, 0.053896226], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 327, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.08187148, 0.41023004, 0.32988808, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 7.0, -0.0026440725, 0.0776868, -0.03343531, 0.053896226], "split_indices": [1, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.885212, 20.49959, 13.385624, 9.486389, 11.013201, 7.007134, 6.37849], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.06374493, -0.17335555, -0.013721009, -0.08374066, -0.009057634, 0.010707485, -0.02345096, 0.015676606, -0.024628503], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 328, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.24299178, 0.17840505, 0.062343433, 0.0, 0.0, 0.09760798, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 5.0, 7.0, -0.08374066, -0.009057634, 8.0, -0.02345096, 0.015676606, -0.024628503], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.161705, 13.379822, 33.781883, 6.7466307, 6.633191, 23.976244, 9.805638, 17.60771, 6.368533], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.057639763, 0.027730832, -0.1416897, -0.07995004, -0.06330043, -0.043332774, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 329, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.36462438, 0.0, 0.032925457, 0.061305627, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [4.0, 0.027730832, 8.0, 7.0, -0.06330043, -0.043332774, -0.0], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [26.477837, 9.042464, 17.435371, 11.123404, 6.3119683, 5.6526465, 5.470757], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.016399307, -0.08999371, 0.042738866, -0.0927625, 0.019070804, 0.042375065, -0.042151727], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 330, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.15438259, 0.58290696, 0.35388196, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 4.0, -0.0927625, 0.019070804, 0.042375065, -0.042151727], "split_indices": [0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [32.502502, 15.002218, 17.500282, 6.0307827, 8.971436, 11.885559, 5.614724], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.010485483, 0.12623675, -0.10791994, 0.0518081, 0.015634993, -0.0578499, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 331, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.4023579, 0.010643527, 0.16168502, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 8.0, 0.0518081, 0.015634993, -0.0578499, -0.0], "split_indices": [0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.318577, 11.233516, 17.085062, 5.258514, 5.9750023, 9.012664, 8.072397], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.048489653, -0.03261173, 0.017434891, 0.024640175, -0.0, 0.008156678], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 332, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.17011976, 0.010751314, 0.0, 0.0011588102, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [6.0, 7.0, -0.03261173, 2.0, 0.024640175, -0.0, 0.008156678], "split_indices": [1, 2, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.370472, 20.19055, 9.179922, 12.834433, 7.3561172, 6.5781217, 6.256311], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.04840707, -0.17577706, 0.072575346, -0.13398017, 0.0135894, 0.042272124, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 333, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.4674712, 0.9430859, 0.07733745, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 6.0, 8.0, -0.13398017, 0.0135894, 0.042272124, -0.0], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.838905, 13.743468, 14.095437, 5.9215717, 7.821897, 6.8997426, 7.1956944], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.06852315, 0.035676483, -0.15621823, -0.08977148, 0.009593053], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 334, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.48602802, 0.0, 0.5749997, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [2.0, 0.035676483, 6.0, -0.08977148, 0.009593053], "split_indices": [1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.210318, 8.195775, 19.014544, 10.770644, 8.243898], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.006864446, -0.0693101, 0.06257488, -0.0037474094, -0.07445877, -0.040473625, 0.012289048], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 335, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.6177414, 0.30498868, 0.0, 0.14443572, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 7.0, 0.06257488, 4.0, -0.07445877, -0.040473625, 0.012289048], "split_indices": [0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.3551, 27.23214, 10.122958, 21.084509, 6.1476316, 5.4157963, 15.668712], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.010534, -0.0464135, 0.04751728, 0.09606312, -0.028466543, -0.04626424, 0.008698909], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 336, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.21995124, 0.0, 0.6830766, 0.0, 0.19741374, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.0464135, 4.0, 0.09606312, 2.0, -0.04626424, 0.008698909], "split_indices": [2, 0, 2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.54185, 5.4231896, 30.118662, 6.149832, 23.96883, 7.5354166, 16.433414], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.104812376, 0.03648913, -0.08737874, 0.16463293, -0.061718363, 0.09216517, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 337, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [1.0059571, 0.6978771, 0.0, 0.36947346, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [5.0, 4.0, -0.08737874, 2.0, -0.061718363, 0.09216517, -0.0], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.812565, 20.52535, 15.287215, 13.903126, 6.6222253, 7.1944046, 6.7087216], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.028103055, -0.09146818, 0.08066705, -0.033704907, -0.008939757, 0.18619868, -0.043979883, 0.007138685, 0.094988756], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 338, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.29502544, 0.0003567934, 0.8232623, 0.0, 0.0, 0.45180005, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 2.0, 7.0, -0.033704907, -0.008939757, 4.0, -0.043979883, 0.007138685, 0.094988756], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.938038, 12.915535, 32.022503, 7.823179, 5.092356, 22.291927, 9.7305765, 10.797169, 11.494759], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.050070535, -0.0, 0.035054836, 0.022397421, -0.018278897], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 339, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.081748135, 0.08408445, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 4.0, 0.035054836, 0.022397421, -0.018278897], "split_indices": [2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [26.23166, 16.244276, 9.987385, 7.785835, 8.458441], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.019461157, 0.0331872, -0.07476317, 0.016984608, -0.059417117], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 340, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.23315528, 0.0, 0.40473452, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [3.0, 0.0331872, 3.0, 0.016984608, -0.059417117], "split_indices": [2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [30.758644, 8.337649, 22.420996, 10.723777, 11.697219], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.0124898385, 0.09462352, -0.04147837, -0.011081273, 0.065497525, -0.033614624, 0.022407252], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 341, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.14051849, 0.23424333, 0.15830436, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 5.0, -0.011081273, 0.065497525, -0.033614624, 0.022407252], "split_indices": [0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.616686, 11.783348, 16.833338, 5.6584167, 6.124932, 11.029456, 5.8038807], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.011867217, -0.084920704, 0.093135715, -0.0, 0.17969944, 0.0068997494, -0.010184623, 0.0804519, 0.0146454675], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 342, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.75698644, 0.0, 0.21790671, 0.011608386, 0.112779826, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [5.0, -0.084920704, 4.0, 7.0, 7.0, 0.0068997494, -0.010184623, 0.0804519, 0.0146454675], "split_indices": [2, 0, 1, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.27436, 5.737704, 24.536657, 11.928957, 12.6077, 6.128432, 5.800525, 6.440797, 6.166903], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.03914707, 0.04626426, -0.1377431, -0.03342198, 0.05995283, 0.0027012317, -0.09046212], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 343, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.25427726, 0.40393788, 0.36740324, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 6.0, -0.03342198, 0.05995283, 0.0027012317, -0.09046212], "split_indices": [2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.351814, 14.396771, 12.955043, 6.917947, 7.478825, 7.009605, 5.9454384], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.13428757, 0.0740637, 0.093870856, -0.008385534, 0.13218124, 0.082604006, 0.00084312784], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 344, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.25430107, 0.15469113, 0.0, 0.0, 0.2659725, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 3.0, 0.093870856, -0.008385534, 5.0, 0.082604006, 0.00084312784], "split_indices": [0, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.39253, 22.709925, 5.682607, 7.806076, 14.903849, 6.172196, 8.731652], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.015673121, 0.08006063, -0.07040388, 0.012721891, 0.061212152, -0.029195627, -0.006800568, 0.026551515, -0.026404902], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 345, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.23044655, 0.17808038, 0.007830247, 0.1398804, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 6.0, 2.0, 0.061212152, -0.029195627, -0.006800568, 0.026551515, -0.026404902], "split_indices": [1, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.388454, 23.237747, 16.150705, 16.295929, 6.941818, 8.3238325, 7.826874, 9.935658, 6.360271], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.060708623, -0.11824319, -0.0068098716, -0.0, -0.049377874, -0.088474326, 0.038215548, -0.0, -0.053155556], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 346, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.09793198, 0.07683377, 0.2318193, 0.0, 0.0, 0.104320645, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [5.0, 2.0, 7.0, -0.0, -0.049377874, 3.0, 0.038215548, -0.0, -0.053155556], "split_indices": [2, 1, 1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.87263, 15.392585, 19.480045, 5.0962925, 10.296292, 12.785382, 6.694663, 7.0253444, 5.7600374], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.03951445, 0.11323959, -0.0421727, 0.08789536, 0.018023426, -0.05908861, 0.04443338], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 347, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.5099053, 0.4341438, 0.0, 0.0, 0.56688684, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 3.0, -0.0421727, 0.08789536, 5.0, -0.05908861, 0.04443338], "split_indices": [0, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.411606, 26.49258, 9.919025, 8.091155, 18.401424, 6.4534907, 11.947933], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.03144289, 0.15555455, -0.0510145, 0.003338335, 0.06513257, -0.11963006, 0.037987463, -0.062948026, 0.022287864], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 348, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.47016168, 0.13791516, 0.3410123, 0.0, 0.0, 0.38328826, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 3.0, 6.0, 0.003338335, 0.06513257, 8.0, 0.037987463, -0.062948026, 0.022287864], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [43.144566, 17.352509, 25.792055, 6.023856, 11.328652, 19.247103, 6.5449533, 13.432901, 5.814201], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.109034576, 0.22642434, -0.016495949, 0.099911, 0.0034381582, -0.06421588, 0.056201827], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 349, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.4331828, 0.30098778, 0.60311943, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 6.0, 7.0, 0.099911, 0.0034381582, -0.06421588, 0.056201827], "split_indices": [1, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [26.718172, 13.803098, 12.915075, 8.548812, 5.254286, 6.7822204, 6.1328545], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.02787259, 0.06937824, -0.019484783, 0.016613724, 0.03976604, 0.010894462, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 350, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.122698516, 0.05737488, 0.0, 0.0059248502, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [6.0, 3.0, -0.019484783, 6.0, 0.03976604, 0.010894462, -0.0], "split_indices": [1, 1, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.563402, 21.347628, 8.215776, 13.182968, 8.164659, 6.4611754, 6.721792], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.069113545, 0.14037032, -0.024862787, 0.017506335, 0.05826189, -0.021062803, 0.002759617], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 351, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.2164776, 0.042289376, 0.027161218, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, 7.0, 0.017506335, 0.05826189, -0.021062803, 0.002759617], "split_indices": [2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.08114, 16.864326, 12.216814, 8.244996, 8.61933, 6.0444155, 6.172399], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.05093354, 0.05180469, -0.12007211, -0.032739587, -0.05675986, 0.01783248, -0.04227652], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 352, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.47206327, 0.0, 0.1134198, 0.13845249, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.05180469, 4.0, 5.0, -0.05675986, 0.01783248, -0.04227652], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.532719, 6.0431795, 22.489538, 11.2265835, 11.262955, 5.8057356, 5.4208484], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.04016268, 0.044704717, -0.049929984, -0.03901813, 0.09303823], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 353, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.32402506, 0.86097544, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [6.0, 4.0, -0.049929984, -0.03901813, 0.09303823], "split_indices": [0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.40655, 16.303116, 11.103433, 9.910723, 6.3923936], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.0026188411, 0.05065353, -0.024615709, 0.037591234, -0.01304388], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 354, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.11955895, 0.15394947, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [8.0, 5.0, -0.024615709, 0.037591234, -0.01304388], "split_indices": [0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.146322, 18.968266, 9.178057, 10.986671, 7.9815936], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.028512316, 0.06790086, -0.042736072, 0.017061042, 0.11524698, 0.036726277, -0.02453666, 0.00941521, 0.06335817], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 355, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.26228544, 0.06313068, 0.0, 0.20033081, 0.0950969, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 6.0, -0.042736072, 4.0, 5.0, 0.036726277, -0.02453666, 0.00941521, 0.06335817], "split_indices": [0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.61957, 31.540712, 6.0788608, 16.883654, 14.657057, 8.546978, 8.336677, 9.08532, 5.5717363], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.022907652, -0.09383419, 0.11338828, -0.040868483, -0.008060381, 0.05756115, 0.03490938, 0.08409632, -0.05446452], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 356, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.3898067, 0.024480924, 0.10029817, 0.0, 0.0, 0.0, 0.71876144, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [5.0, 3.0, 7.0, -0.040868483, -0.008060381, 0.05756115, 4.0, 0.08409632, -0.05446452], "split_indices": [2, 2, 2, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.711075, 14.65732, 20.053753, 7.4275904, 7.22973, 8.667146, 11.386608, 5.414639, 5.9719687], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.055077303, 0.09667875, -0.015415391, -0.014174295, 0.14173669, 0.08510236, 0.015952248], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 357, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.1845252, 0.20704985, 0.0, 0.0, 0.2428416, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 1.0, -0.015415391, -0.014174295, 3.0, 0.08510236, 0.015952248], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.51889, 28.43768, 10.081212, 6.2394257, 22.198254, 7.248673, 14.949581], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0493406, 0.104806796, -0.029788844, 0.0060462067, 0.13991109, -0.048079453, 0.023442045, 0.077936575, 0.014610981, 0.028933585, -0.014332942], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 358, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.20659891, 0.06204793, 0.14548582, 0.0, 0.1558665, 0.0, 0.079259396, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 4.0, 0.0060462067, 5.0, -0.048079453, 7.0, 0.077936575, 0.014610981, 0.028933585, -0.014332942], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [43.65131, 26.215014, 17.436296, 8.980829, 17.234184, 5.014051, 12.422246, 6.1743364, 11.059848, 6.6527214, 5.769524], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0014065102, -0.038223185, 0.042498726, -0.008363332, 0.09016228, 0.057213493, -0.011365835], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 359, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.14372627, 0.0, 0.083761126, 0.0, 0.20813224, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.038223185, 5.0, -0.008363332, 8.0, 0.057213493, -0.011365835], "split_indices": [2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [26.733253, 5.3688064, 21.364449, 7.947228, 13.41722, 7.6789474, 5.7382727], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.087931134, -0.035951246, -0.050329525, -0.04680832, 0.02300968], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 360, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.10630876, 0.30937883, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 4.0, -0.050329525, -0.04680832, 0.02300968], "split_indices": [2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [30.980585, 20.400648, 10.579937, 10.118132, 10.2825165], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.023131743, 0.08211015, -0.031339668, 0.046960283, 0.003725007, 0.016840212, -0.011112327], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 361, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.2365656, 0.11183722, 0.0, 0.0, 0.030486267, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 7.0, -0.031339668, 0.046960283, 5.0, 0.016840212, -0.011112327], "split_indices": [0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.592508, 21.017801, 8.574706, 9.6154785, 11.402324, 5.8970704, 5.5052533], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.060171384, 0.14403272, -0.01819701, 0.081898056, -0.018258426], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 362, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.29574218, 0.4875275, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [5.0, 7.0, -0.01819701, 0.081898056, -0.018258426], "split_indices": [0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [26.50157, 16.019045, 10.482524, 9.926794, 6.092252], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.061528403, 0.016820533, -0.12647216, 0.01756222, -0.0045347493, -0.061379585, 0.0038918536], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 363, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.15390775, 0.02218537, 0.1955486, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 8.0, 0.01756222, -0.0045347493, -0.061379585, 0.0038918536], "split_indices": [2, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [26.67446, 11.727485, 14.946975, 6.081317, 5.646168, 9.739915, 5.20706], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.013743965, 0.11434101, -0.08144425, -0.004318996, 0.054779768, -0.0681817, 0.014712804], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 364, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.3178796, 0.16379687, 0.34206498, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 7.0, -0.004318996, 0.054779768, -0.0681817, 0.014712804], "split_indices": [2, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.880592, 15.426676, 15.453917, 5.1110654, 10.31561, 7.309588, 8.144329], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.008023754, -0.040523134, 0.01950577, 0.10095653, -0.044786703, -0.0, 0.075798854, 0.022498429, -0.038786698], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 365, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.16239509, 0.0, 0.1936965, 0.2530768, 0.21347363, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.040523134, 6.0, 8.0, 5.0, -0.0, 0.075798854, 0.022498429, -0.038786698], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [41.37139, 7.3189683, 34.05242, 15.456296, 18.596128, 9.864616, 5.5916796, 7.249342, 11.346786], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0450134, -0.010178612, 0.11353966, 0.033904117, -0.08837126, -0.048137236, 0.082750365, -0.055762846, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 366, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.14771903, 0.20075503, 0.8099348, 0.0, 0.118827455, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 3.0, 0.033904117, 3.0, -0.048137236, 0.082750365, -0.055762846, -0.0], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.01892, 19.059944, 15.958979, 6.7310205, 12.328923, 5.711537, 10.247441, 5.4520926, 6.876831], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.043124385, -0.062548496, 0.102655135, 0.018550813, 0.08884739, 0.038928628, -0.010605962], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 367, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.5442754, 0.0, 0.4478688, 0.14706723, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.062548496, 6.0, 4.0, 0.08884739, 0.038928628, -0.010605962], "split_indices": [2, 0, 1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.613403, 5.822263, 28.79114, 21.192612, 7.5985284, 7.0648146, 14.127797], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.009001871, 0.06340142, -0.077804126, 0.05100184, -0.010632433, -0.07293736, 0.018690135, 0.059639424, -0.0698033], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 368, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.24180117, 0.2604695, 0.43202057, 0.0, 0.0, 0.0, 0.7934515, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 5.0, 5.0, 0.05100184, -0.010632433, -0.07293736, 8.0, 0.059639424, -0.0698033], "split_indices": [1, 2, 2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.26037, 21.870377, 24.389992, 10.662416, 11.20796, 8.811057, 15.578935, 9.429527, 6.149408], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.056017824, -0.081095055, 0.0015593491, 0.065838, -0.11463215, -0.013901966, -0.05259272], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 369, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.3630233, 0.0, 0.59449625, 0.0, 0.03084901, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.081095055, 5.0, 0.065838, 7.0, -0.013901966, -0.05259272], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [26.209118, 5.1358204, 21.073298, 7.319147, 13.75415, 8.076163, 5.677987], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.031606417, 0.043430608, -0.12144111, 0.066807054, -0.043508224, 0.012599743, -0.071537174, -0.04865999, 0.019024633], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 370, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.24335566, 0.31972283, 0.34365118, 0.0, 0.18620168, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 6.0, 0.066807054, 7.0, 0.012599743, -0.071537174, -0.04865999, 0.019024633], "split_indices": [0, 2, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.161522, 17.743853, 15.417671, 5.660734, 12.0831175, 6.343707, 9.073964, 5.9246616, 6.1584563], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.018816693, 0.11262723, -0.09515456, -0.0011611327, 0.17819124, -0.008519512, -0.04631479, 0.06994067, 0.02368787], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 371, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.34273422, 0.15471306, 0.032720342, 0.0, 0.015882432, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 7.0, -0.0011611327, 8.0, -0.008519512, -0.04631479, 0.06994067, 0.02368787], "split_indices": [1, 2, 1, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.890303, 16.882996, 13.007305, 5.869498, 11.013498, 7.6043153, 5.40299, 5.5714517, 5.4420466], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.018451057, 0.044892278, -0.08296761, 0.039450847, -0.027416471], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 372, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.5171822, 0.30168664, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 7.0, -0.08296761, 0.039450847, -0.027416471], "split_indices": [0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.456753, 23.201426, 5.255328, 14.655949, 8.545476], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.06854459, -0.08089656, 0.02525338, -0.046251703, 0.047100615, 0.0017903206, -0.037508], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 373, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.56116784, 0.0, 0.20422868, 0.0664202, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [4.0, -0.08089656, 8.0, 6.0, 0.047100615, 0.0017903206, -0.037508], "split_indices": [0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.09185, 8.359551, 18.732298, 12.070883, 6.6614146, 7.051374, 5.019509], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.061643533, -0.009207003, -0.066831015, -0.10923799, 0.043946754, -0.05674568, 0.0054045883], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 374, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.20871887, 0.36120862, 0.0, 0.1690379, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 7.0, -0.066831015, 6.0, 0.043946754, -0.05674568, 0.0054045883], "split_indices": [0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [26.69203, 21.404112, 5.2879186, 13.604329, 7.7997828, 8.540415, 5.0639153], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.009925276, 0.048846632, -0.062184975, 0.005316621, 0.07386426, 0.012718754, -0.009933833], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 375, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.3530786, 0.2930636, 0.0, 0.047798943, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, -0.062184975, 4.0, 0.07386426, 0.012718754, -0.009933833], "split_indices": [1, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [41.44087, 36.191296, 5.2495747, 30.90804, 5.283255, 17.002176, 13.905864], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.008286875, -0.11185211, 0.13344929, 0.1956904, -0.004489343, 0.09722712, 0.033053074], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 376, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [1.6960982, 0.0, 0.273111, 0.13798392, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.11185211, 8.0, 3.0, -0.004489343, 0.09722712, 0.033053074], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.014343, 7.7221394, 26.292204, 18.774065, 7.5181384, 5.9222946, 12.85177], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.07471127, -0.11731626, 0.008357788, -0.03761305, -0.07878587, 0.006369278, -0.05422705], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 377, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.1717633, 0.2690377, 0.0, 0.17846784, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [6.0, 6.0, 0.008357788, 3.0, -0.07878587, 0.006369278, -0.05422705], "split_indices": [1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.509975, 25.676033, 9.8339405, 17.838278, 7.837755, 12.750037, 5.088241], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.009105167, 0.022150794, -0.09348444, -0.09126004, 0.08875178, -0.07196912, 0.016005987, -0.08871208, 0.03136551, 0.09957679, -0.02545753], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 378, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.12489895, 0.24920624, 0.30908406, 0.5208177, 0.94762415, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 4.0, 5.0, 4.0, -0.07196912, 0.016005987, -0.08871208, 0.03136551, 0.09957679, -0.02545753], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [42.86774, 30.954037, 11.913704, 10.759906, 20.19413, 5.9891553, 5.9245486, 5.264148, 5.4957576, 8.271956, 11.922174], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.06176516, 0.11129173, -0.034576636, -0.0, 0.1757308, -0.0064756847, 0.10100918], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 379, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.25961077, 0.14999291, 0.0, 0.0, 0.48578736, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 5.0, -0.034576636, -0.0, 5.0, -0.0064756847, 0.10100918], "split_indices": [0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.193668, 21.980272, 5.213396, 8.906289, 13.073984, 5.9836903, 7.0902934], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.026678933, 0.09558164, -0.015124483, 0.055761326, -0.0021854902, 0.027672006, -0.036085013, -0.028676378, 0.052265245], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 380, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.097553715, 0.13609555, 0.098046936, 0.0, 0.0, 0.27077606, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 6.0, 4.0, 0.055761326, -0.0021854902, 2.0, -0.036085013, -0.028676378, 0.052265245], "split_indices": [0, 1, 1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.573298, 11.584603, 17.988693, 6.2574506, 5.327153, 12.633254, 5.355439, 6.6800137, 5.9532404], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.018608635, -0.029179396, 0.06649789, 0.024198873, 0.037956133, -0.007927747, 0.026246084], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 381, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.16542928, 0.0, 0.037382394, 0.056607448, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [5.0, -0.029179396, 5.0, 8.0, 0.037956133, -0.007927747, 0.026246084], "split_indices": [2, 0, 1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.200861, 7.3346615, 20.8662, 14.040676, 6.8255234, 7.2465057, 6.79417], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.059607085, 0.13431019, -0.038145408, -0.008936115, 0.067202285], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 382, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.41928035, 0.34143832, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 3.0, -0.038145408, -0.008936115, 0.067202285], "split_indices": [1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.77745, 20.424118, 7.3533316, 7.0604367, 13.363682], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.028315963, -0.017856283, 0.04733218, -0.10686982, 0.034416884, 0.004458387, -0.061702948], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 383, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.1757409, 0.24665016, 0.0, 0.17719913, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 7.0, 0.04733218, 3.0, 0.034416884, 0.004458387, -0.061702948], "split_indices": [0, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [25.588959, 18.927814, 6.6611433, 11.891834, 7.03598, 5.255054, 6.6367807], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.04559462, -0.0, -0.03769858, 0.027254757, -0.05387775], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 384, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.10865444, 0.34001854, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [5.0, 6.0, -0.03769858, 0.027254757, -0.05387775], "split_indices": [1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.642103, 18.858915, 9.783189, 12.864802, 5.9941125], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.0013585839, 0.096743725, -0.08584228, 0.01331839, 0.06475303, -0.09779709, 0.07150229, 0.03934672, -0.023236591, -0.034576308, 0.05884441], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 385, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.34231222, 0.15931895, 0.9103197, 0.14940168, 0.0, 0.0, 0.36460158, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 7.0, 3.0, 0.06475303, -0.09779709, 5.0, 0.03934672, -0.023236591, -0.034576308, 0.05884441], "split_indices": [2, 2, 2, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.43144, 17.630342, 21.801098, 11.515547, 6.114795, 8.45711, 13.343987, 5.2965884, 6.218959, 5.050258, 8.293729], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.021745045, 0.13310452, -0.08325226, 0.07129246, 0.06550294, -0.07915175, 0.012922015, -0.017723737, 0.05809645], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 386, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.4504246, 0.08750713, 0.47312286, 0.0, 0.23921841, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 7.0, 0.07129246, 7.0, -0.07915175, 0.012922015, -0.017723737, 0.05809645], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.148613, 17.875103, 18.27351, 5.451103, 12.424001, 7.3991747, 10.874335, 6.1773725, 6.246628], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.084263615, -0.12153771, -0.01680857, 0.08661766, -0.08099318, -0.014400626, 0.04224822], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 387, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.7551714, 0.0, 0.8796591, 0.18532409, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, -0.12153771, 6.0, 2.0, -0.08099318, -0.014400626, 0.04224822], "split_indices": [0, 0, 2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.155014, 5.153436, 31.001577, 22.196558, 8.805019, 5.8755503, 16.321007], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0063684704, 0.1584336, -0.07173883, -0.0, 0.0855304, 0.07888268, -0.14557184, -0.019601142, -0.07985321], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 388, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.5075765, 0.24585852, 0.8834398, 0.0, 0.0, 0.0, 0.23297173, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 5.0, 1.0, -0.0, 0.0855304, 0.07888268, 7.0, -0.019601142, -0.07985321], "split_indices": [0, 1, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.864582, 12.187042, 33.67754, 6.0899744, 6.097068, 5.3790183, 28.298523, 18.417131, 9.881392], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.08970055, -0.07293821, -0.018064326, -0.03700752, 0.042243913, -0.005567086, 0.030673867], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 389, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.28887925, 0.0, 0.14879946, 0.0, 0.06026887, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.07293821, 5.0, -0.03700752, 5.0, -0.005567086, 0.030673867], "split_indices": [1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.059479, 7.7248755, 20.334602, 7.62848, 12.706123, 5.826354, 6.8797693], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.033546686, 0.07346985, -0.026694175, -0.008165494, 0.06875492, -0.023281194, 0.017786667], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 390, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.16070214, 0.34396514, 0.0, 0.08633625, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 6.0, -0.026694175, 4.0, 0.06875492, -0.023281194, 0.017786667], "split_indices": [2, 2, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.832817, 24.173025, 6.6597924, 16.059113, 8.113913, 8.569532, 7.4895797], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.013775541, -0.06692827, 0.028795315, 0.05147899, -0.08161953, -0.01591132, 0.050402913], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 391, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.17564406, 0.54810697, 0.0, 0.18967254, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, 0.028795315, 6.0, -0.08161953, -0.01591132, 0.050402913], "split_indices": [0, 2, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.503681, 20.08066, 8.423021, 12.897595, 7.1830635, 6.634137, 6.2634583], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.030231675, -0.09157354, 0.043334987, -0.13982011, 0.0030456507, -0.004754233, -0.08136425], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 392, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.32883143, 0.1282479, 0.0, 0.23529118, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 6.0, 0.043334987, 6.0, 0.0030456507, -0.004754233, -0.08136425], "split_indices": [2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.947033, 22.199583, 6.747451, 15.470684, 6.7288985, 8.928326, 6.5423584], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.09860873, -0.0, 0.044461645], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 393, "left_children": [1, -1, -1], "loss_changes": [0.123783976, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, -0.0, 0.044461645], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [25.647314, 9.215198, 16.432117], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.06079851, -0.03410021, -0.00047086654, 0.03560223], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 394, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.20609716, 0.08302112, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [5.0, 4.0, -0.03410021, -0.00047086654, 0.03560223], "split_indices": [1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [28.018398, 18.70429, 9.314108, 8.639357, 10.064933], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.0799628, 0.118480556, 0.020396767, 0.052596066, 0.04473163, -0.043353047, 0.044489164, -0.0, 0.028731614], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 395, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.071620226, 0.062564105, 0.3862039, 0.0, 0.035062514, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 8.0, 0.052596066, 8.0, -0.043353047, 0.044489164, -0.0, 0.028731614], "split_indices": [0, 2, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.3222, 21.045483, 16.276716, 10.34172, 10.703763, 6.7115436, 9.565172, 5.327947, 5.3758163], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.021901261, -0.03432763, 0.084922016, 0.032845374, -0.052019335, 0.09755054, -0.050800707, 0.06625703, -0.054234684, -0.1042001, 0.07790158], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 396, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.13993472, 0.20241562, 0.6532327, 0.5877974, 0.0, 0.0, 1.2533723, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 7.0, 6.0, -0.052019335, 0.09755054, 6.0, 0.06625703, -0.054234684, -0.1042001, 0.07790158], "split_indices": [1, 1, 1, 2, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.239132, 18.553602, 17.68553, 12.540911, 6.0126915, 6.146468, 11.539062, 6.9013615, 5.6395493, 6.006043, 5.533018], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.083282776, 0.18977846, -0.06173234, 0.11853578, 0.062114894, 0.00074758154, -0.031276543, -0.00874946, 0.049761053], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 397, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.5716225, 0.482045, 0.052376598, 0.0, 0.15563095, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 4.0, 0.11853578, 5.0, 0.00074758154, -0.031276543, -0.00874946, 0.049761053], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.426098, 20.068884, 14.357213, 6.590333, 13.478551, 5.1574216, 9.199791, 7.040075, 6.4384766], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.034707543, 0.017352868, -0.19787736, -0.021268658, 0.031396143, -0.10193605, -0.0037709733, 0.018271621, -0.03528945], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 398, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.40881485, 0.12864897, 0.26485997, 0.20539674, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, 6.0, 4.0, 0.031396143, -0.10193605, -0.0037709733, 0.018271621, -0.03528945], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.596573, 34.02232, 10.574253, 23.317625, 10.704695, 5.210637, 5.363616, 12.223859, 11.093765], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.16654459, 0.012020086, -0.26396447, -0.11618235, -0.03240929], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 399, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.58049035, 0.0, 0.27293742, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, 0.012020086, 7.0, -0.11618235, -0.03240929], "split_indices": [0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [26.202028, 8.255567, 17.946463, 8.8349905, 9.111472], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.034469195, 0.0010836668, -0.05325903, -0.045210578, 0.05840016, 0.043045536, -0.014449136], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 400, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.17251748, 0.21525943, 0.0, 0.0, 0.19048822, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, -0.05325903, -0.045210578, 6.0, 0.043045536, -0.014449136], "split_indices": [0, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.411646, 23.792309, 5.619337, 5.596818, 18.195492, 10.44651, 7.7489815], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.013413418, -0.05676036, 0.03864382, -0.012674883, 0.07183585, 0.046348654, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 401, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.30696985, 0.0, 0.07147148, 0.0, 0.113746926, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [5.0, -0.05676036, 4.0, -0.012674883, 8.0, 0.046348654, -0.0], "split_indices": [2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.10063, 6.7142644, 23.386366, 5.8481026, 17.538263, 7.7734213, 9.764842], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.044528864, 0.10134086, -0.0211249, 0.054578487, -0.0, -0.020876287, 0.0039882287], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 402, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.11972362, 0.14361294, 0.029302144, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 5.0, 4.0, 0.054578487, -0.0, -0.020876287, 0.0039882287], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.373596, 15.736204, 12.637391, 8.5321245, 7.2040796, 6.0302525, 6.6071386], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.040237885, 0.039579324, -0.06867374, -0.0152846025, 0.04934343], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 403, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.42501855, 0.23463637, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 6.0, -0.06867374, -0.0152846025, 0.04934343], "split_indices": [2, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [25.363804, 18.00651, 7.3572936, 10.318778, 7.6877317], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.03020478, -0.032265525, 0.09941108, 0.017629454, -0.055182252, 0.007190126, 0.037755523], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 404, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.13658002, 0.23585925, 0.011730745, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 5.0, 0.017629454, -0.055182252, 0.007190126, 0.037755523], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.296322, 14.393024, 13.903298, 8.965496, 5.427528, 5.0843773, 8.818921], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.058100186, 0.020190066, -0.0974648, -0.12755723, 0.0086250445, -0.0067913905, -0.045409523], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 405, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.20872879, 0.0, 0.13402739, 0.045211703, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.020190066, 9.0, 4.0, 0.0086250445, -0.0067913905, -0.045409523], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.34426, 8.623408, 30.720854, 25.437626, 5.2832265, 5.842289, 19.595337], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.04338671, -0.0, 0.124826245, -0.0829188, 0.07392137, 0.086143695, -0.001792026, -0.0345558, -0.0063885865, 0.0412966, -0.002857788], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 406, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.13918304, 0.16668725, 0.31897068, 0.009577736, 0.0899393, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 4.0, 3.0, 8.0, 0.086143695, -0.001792026, -0.0345558, -0.0063885865, 0.0412966, -0.002857788], "split_indices": [0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.787815, 25.16934, 12.618473, 11.91418, 13.255161, 5.438562, 7.1799116, 6.1841173, 5.7300625, 7.8264008, 5.42876], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.069353715, 0.045455858, 0.027584516, -0.02269706, 0.032400522], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 407, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.09942995, 0.0, 0.22314963, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [2.0, 0.045455858, 5.0, -0.02269706, 0.032400522], "split_indices": [0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [34.40791, 9.903904, 24.504005, 10.210817, 14.293188], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.067697376, 0.043782998, 0.05522819, 0.09549145, -0.044265047, 0.0009016378, 0.040395964], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 408, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.09185569, 0.39070725, 0.0, 0.09956059, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 7.0, 0.05522819, 2.0, -0.044265047, 0.0009016378, 0.040395964], "split_indices": [1, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [43.081665, 37.5932, 5.488465, 30.400833, 7.192367, 10.12336, 20.277473], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.022817818, 0.061811056, -0.07399908, -0.06146022, 0.005222833], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 409, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.50101346, 0.0, 0.23534688, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, 0.061811056, 4.0, -0.06146022, 0.005222833], "split_indices": [0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [25.640238, 8.831682, 16.808556, 6.851754, 9.956801], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.045281675, -0.02299511, 0.09865726, 0.04457497, 0.03623631, -0.003946086, 0.027244976], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 410, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.21315299, 0.0, 0.047013924, 0.0, 0.042171735, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [4.0, -0.02299511, 3.0, 0.04457497, 5.0, -0.003946086, 0.027244976], "split_indices": [2, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [30.251404, 8.507397, 21.744007, 10.424134, 11.319874, 5.3791227, 5.940751], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.036454648, -0.080964334, 0.092638485, 0.123697095, -0.0, 0.07079282, 0.0027062225], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 411, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.9634369, 0.0, 0.055046424, 0.16515465, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.080964334, 7.0, 7.0, -0.0, 0.07079282, 0.0027062225], "split_indices": [1, 0, 1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.554153, 10.411714, 19.142439, 13.894912, 5.2475276, 6.045764, 7.849148], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.04161317, -0.060844805, 0.022205425, -0.035554003, 0.045552656, -0.03615349, 0.0079248985], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 412, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.3373428, 0.0, 0.18457735, 0.09554748, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.060844805, 6.0, 6.0, 0.045552656, -0.03615349, 0.0079248985], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.597408, 8.22085, 21.376558, 14.756279, 6.6202793, 6.5059786, 8.2503], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.051625915, 0.11901068, -0.007950508, -0.005080208, 0.08078288], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 413, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.1547288, 0.34406233, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [6.0, 4.0, -0.007950508, -0.005080208, 0.08078288], "split_indices": [0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [26.034084, 14.384328, 11.649757, 7.668491, 6.715837], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.062631615, 0.0089911185, 0.06281792, -0.028843503, 0.026488023, -0.026978916, 0.013820097], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 414, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.20635822, 0.07734202, 0.0, 0.07916178, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 6.0, 0.06281792, 7.0, 0.026488023, -0.026978916, 0.013820097], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.226229, 21.947266, 6.2789626, 14.435477, 7.5117893, 8.576306, 5.8591704], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0063577383, 0.06513501, -0.033022482, -0.069218755, 0.053736772, -0.0076752533, 0.07244973], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 415, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.35329053, 0.0, 0.6087777, 0.0, 0.38382602, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.06513501, 3.0, -0.069218755, 8.0, -0.0076752533, 0.07244973], "split_indices": [2, 0, 1, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.484562, 5.7479763, 32.736588, 9.82976, 22.906828, 16.336544, 6.570285], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.037529506, 0.20775613, -0.0540517, -0.0, 0.12039371, 0.023970198, -0.05423553, -0.0151144, 0.03364071], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 416, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.5901412, 0.49256605, 0.25648904, 0.0, 0.0, 0.111444525, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 5.0, 6.0, -0.0, 0.12039371, 7.0, -0.05423553, -0.0151144, 0.03364071], "split_indices": [2, 0, 0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.148624, 12.233318, 22.915306, 6.6282887, 5.60503, 14.196936, 8.71837, 7.3190117, 6.877924], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.007246172, -0.009123455, 0.020513456, 0.035499025, -0.056687824, -0.07768007, 0.010952652], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 417, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.041237358, 0.16382405, 0.0, 0.0, 0.4249091, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 1.0, 0.020513456, 0.035499025, 3.0, -0.07768007, 0.010952652], "split_indices": [2, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.66349, 25.933973, 7.7295156, 6.072335, 19.861637, 6.042807, 13.8188305], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.016488258, -0.054775063, 0.0691679, 0.009768785, -0.10921247, -0.018703975, 0.01581948], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 418, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.4396966, 0.87456226, 0.0, 0.1118151, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 7.0, 0.0691679, 3.0, -0.10921247, -0.018703975, 0.01581948], "split_indices": [1, 1, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.009354, 40.86004, 5.149315, 34.33864, 6.5213985, 11.782433, 22.556208], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.014314675, 0.04706883, -0.052580647], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 419, "left_children": [1, -1, -1], "loss_changes": [0.7578527, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.04706883, -0.052580647], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [26.087002, 15.288647, 10.798354], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.060976744, 0.09228575, -0.0130364, 0.00756547, 0.16103871, -0.008748699, 0.018323027, -0.002198483, 0.0884316], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 420, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.10780502, 0.1236421, 0.0, 0.029737106, 0.31754035, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, -0.0130364, 6.0, 3.0, -0.008748699, 0.018323027, -0.002198483, 0.0884316], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.251509, 23.33072, 5.9207864, 11.600554, 11.730167, 6.040725, 5.5598288, 5.2800965, 6.450071], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.07017995, 0.050666124, -0.0035803458, -0.030487498, -0.0, 0.023740988], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 421, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.11248284, 0.012087554, 0.02139125, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 7.0, 7.0, -0.0035803458, -0.030487498, -0.0, 0.023740988], "split_indices": [0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.451122, 12.462155, 16.988966, 5.967772, 6.494384, 7.199538, 9.789428], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.038123306, -0.019179089, 0.025776044], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 422, "left_children": [1, -1, -1], "loss_changes": [0.14498234, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, -0.019179089, 0.025776044], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [27.145756, 7.8835125, 19.262243], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.01026806, 0.14803208, -0.071085714, 0.071595326, 0.019905746], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 423, "left_children": [1, 3, -1, -1, -1], "loss_changes": [1.0042197, 0.072922975, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [6.0, 3.0, -0.071085714, 0.071595326, 0.019905746], "split_indices": [0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [25.759748, 15.112254, 10.647494, 5.6726604, 9.439593], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.028704895, -0.03760884, 0.079408854, 0.013367106, -0.09349337, -0.010836316, -0.044153064], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 424, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.48909605, 0.11610781, 0.0, 0.0, 0.02231516, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 5.0, 0.079408854, 0.013367106, 6.0, -0.010836316, -0.044153064], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.166008, 22.312943, 5.8530645, 8.39024, 13.922704, 8.439217, 5.483487], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.07508405, -0.03340277, 0.11973034, 0.0375731, -0.05788972, 0.19913253, -0.006036863, 0.09983963, 0.03359336], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 425, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.21088019, 0.3362839, 0.35358465, 0.0, 0.0, 0.14658326, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 4.0, 8.0, 0.0375731, -0.05788972, 2.0, -0.006036863, 0.09983963, 0.03359336], "split_indices": [2, 1, 2, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.93047, 11.062211, 28.86826, 5.340629, 5.721582, 18.599495, 10.268765, 5.77567, 12.823825], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.023624558, 0.061111245, -0.034771565, -0.018815454, 0.096286505, 0.05393185, 0.0027583744], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 426, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.20199381, 0.146245, 0.0, 0.0, 0.16479622, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 1.0, -0.034771565, -0.018815454, 4.0, 0.05393185, 0.0027583744], "split_indices": [1, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.31177, 30.418198, 6.893575, 5.9270267, 24.49117, 11.374852, 13.116319], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.010479737, -0.07619089, 0.06737608, 0.10463539, -0.014578987, 0.0048176143, 0.08576432], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 427, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.5328421, 0.0, 0.14174527, 0.34930372, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, -0.07619089, 7.0, 5.0, -0.014578987, 0.0048176143, 0.08576432], "split_indices": [0, 0, 2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.516605, 5.1869226, 29.329685, 22.924583, 6.4051013, 16.500805, 6.4237785], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.03474155, 0.019739525, -0.21026285, -0.025364459, 0.077048235, -0.09040626, -0.021121195, 0.034858085, -0.008438756], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 428, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.45583332, 0.21497396, 0.084768206, 0.0, 0.109787434, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 9.0, -0.025364459, 8.0, -0.09040626, -0.021121195, 0.034858085, -0.008438756], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.24198, 34.004833, 10.237151, 11.272116, 22.732718, 5.0671573, 5.1699934, 17.183372, 5.549345], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04070749, 0.105810985, -0.03227149, -0.034286007, 0.20434721, 0.0013150602, 0.09527342], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 429, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.260819, 0.44314325, 0.0, 0.0, 0.27787673, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 3.0, -0.03227149, -0.034286007, 5.0, 0.0013150602, 0.09527342], "split_indices": [2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [24.857265, 17.940926, 6.9163404, 5.1848855, 12.756039, 5.306579, 7.44946], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.074200176, -0.05745198, -0.010154152, 0.056051824, -0.03951013, 0.013501032], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 430, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.12703717, 0.17851865, 0.14379835, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [3.0, 7.0, 7.0, -0.010154152, 0.056051824, -0.03951013, 0.013501032], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.875822, 11.891593, 15.984229, 5.9734125, 5.918181, 9.668299, 6.31593], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.022299789, 0.053348523, -0.07508935, 0.028457789, -0.0, -0.0644716, 0.019461354], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 431, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.1259845, 0.02815247, 0.3980843, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 8.0, 0.028457789, -0.0, -0.0644716, 0.019461354], "split_indices": [0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.00032, 11.136554, 17.863766, 5.9673142, 5.1692395, 9.026052, 8.837713], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.016339969, -0.07382418, 0.042172562, -0.06137264, -0.012927669, 0.016461728, -0.04485884], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 432, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.26912907, 0.15601815, 0.0, 0.0, 0.17556189, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 1.0, 0.042172562, -0.06137264, 6.0, 0.016461728, -0.04485884], "split_indices": [2, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.42595, 21.658428, 6.767521, 5.61052, 16.047909, 10.635432, 5.4124765], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0339505, 0.16798647, -0.10048683, 0.07036102, 0.024749598, 0.021504583, -0.07368482], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 433, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.5221777, 0.032016218, 0.38646656, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 4.0, 0.07036102, 0.024749598, 0.021504583, -0.07368482], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [26.667192, 13.601044, 13.066149, 6.0095134, 7.5915303, 5.8926544, 7.173495], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.05004355, 0.02076906, -0.10257383, -0.0, -0.098529644, -0.03373181, 0.039699495], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 434, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.19428682, 0.0, 0.48349872, 0.24915686, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [5.0, 0.02076906, 5.0, 3.0, -0.098529644, -0.03373181, 0.039699495], "split_indices": [0, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.343508, 7.9869633, 20.356544, 14.7911005, 5.5654445, 8.218058, 6.5730424], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0005467345, -0.03621563, 0.036045536, 0.041241236, 0.001417373, 0.009507939, -0.011177642], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 435, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.19491436, 0.0, 0.09787122, 0.0, 0.030493569, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [4.0, -0.03621563, 5.0, 0.041241236, 6.0, 0.009507939, -0.011177642], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.410152, 9.533885, 30.876268, 6.367689, 24.50858, 15.298791, 9.209788], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.034780215, 0.056571413, -0.029398542, -0.15415746, 0.08396998, -0.09267497, -0.0, 0.07240515, -0.009769826], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 436, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.39429572, 0.0, 0.40266836, 0.30135846, 0.2929095, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, 0.056571413, 6.0, 6.0, 5.0, -0.09267497, -0.0, 0.07240515, -0.009769826], "split_indices": [0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.77407, 10.715798, 26.058271, 12.654563, 13.4037075, 5.5582757, 7.0962877, 5.6119313, 7.7917767], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.08166208, 0.12417976, -0.007835179, 0.039539844, 0.1990577, -0.03223965, 0.061973877, 0.110711925, 0.005080045], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 437, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.17825869, 0.13743651, 0.0, 0.38581678, 0.36652583, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, -0.007835179, 4.0, 4.0, -0.03223965, 0.061973877, 0.110711925, 0.005080045], "split_indices": [0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.051987, 25.692324, 9.359662, 13.404181, 12.288143, 7.0009217, 6.4032583, 5.541091, 6.747052], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.015539176, -0.024913546, 0.117262624, 0.032404985, -0.061915003, -0.012140692, 0.08563781, -0.045406286, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 438, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.20832643, 0.1704396, 0.41920972, 0.0, 0.15032658, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 4.0, 0.032404985, 5.0, -0.012140692, 0.08563781, -0.045406286, -0.0], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.672066, 33.229572, 13.442493, 6.2291374, 27.000435, 7.0221295, 6.4203634, 10.247774, 16.75266], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.10789468, 0.17470002, -0.001999868, 0.021872373, 0.082046404], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 439, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.21900389, 0.11818433, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [7.0, 6.0, -0.001999868, 0.021872373, 0.082046404], "split_indices": [0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [25.26794, 16.180403, 9.087537, 9.323584, 6.8568196], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.014689301, 0.04735151, -0.09042359, 0.022843633, -0.15871501, -0.09094939, -0.014656962], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 440, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.3933939, 0.0, 0.26560485, 0.0, 0.20455179, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.04735151, 2.0, 0.022843633, 6.0, -0.09094939, -0.014656962], "split_indices": [1, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.531258, 7.9701724, 20.561087, 5.5194125, 15.041673, 5.3444986, 9.697174], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0039019978, 0.1377342, -0.14255983, 0.06601345, 0.0052546747, -0.08619224, 0.02516992], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 441, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.59109056, 0.11620855, 0.55381775, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [3.0, 5.0, 8.0, 0.06601345, 0.0052546747, -0.08619224, 0.02516992], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.07345, 13.5341, 14.5393505, 7.047006, 6.487094, 8.975384, 5.5639668], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.053759024, -0.09523869, 0.022430014, -0.20822825, 0.04861078, -0.03226421, -0.08466602], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 442, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.16784447, 0.72235215, 0.0, 0.05773896, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 6.0, 0.022430014, 4.0, 0.04861078, -0.03226421, -0.08466602], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.763739, 22.529682, 6.234057, 16.057081, 6.472602, 8.413631, 7.6434493], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.008220939, 0.08305281, -0.06324948, -0.003251032, 0.16205615, 0.07716228, 0.00031357026], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 443, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.4786601, 0.1845732, 0.0, 0.0, 0.17181855, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 4.0, -0.06324948, -0.003251032, 6.0, 0.07716228, 0.00031357026], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.911947, 21.607758, 6.3041906, 9.689533, 11.918223, 6.725295, 5.192929], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.009593755, -0.05539219, 0.032705713, 0.04734566, -0.06082782, -0.007385003, 0.039004903], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 444, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.16707598, 0.37897083, 0.0, 0.098106384, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 3.0, 0.032705713, 7.0, -0.06082782, -0.007385003, 0.039004903], "split_indices": [2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.631678, 22.370228, 7.2614503, 13.181099, 9.189129, 6.775684, 6.405415], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.09342275, -0.020801442, 0.13976455, 0.08103174, 0.09023166, -0.0010462127, 0.071747705], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 445, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.31625718, 0.0, 0.14438277, 0.0, 0.37392792, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.020801442, 4.0, 0.08103174, 8.0, -0.0010462127, 0.071747705], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.71074, 7.9909496, 30.71979, 6.677265, 24.042524, 14.943679, 9.0988455], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.04177911, 0.10009499, -0.12098744, -0.0, 0.047653977, -0.28705305, 0.02602885, -0.106182165, -0.0425983, 0.06412873, -0.036481313], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 446, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.4409592, 0.07647681, 0.6506524, 0.0, 0.0, 0.019211888, 0.423341, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 7.0, -0.0, 0.047653977, 5.0, 4.0, -0.106182165, -0.0425983, 0.06412873, -0.036481313], "split_indices": [2, 2, 2, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.976017, 12.700791, 24.275225, 5.2296767, 7.471114, 11.271225, 13.004, 6.2568126, 5.014413, 5.825542, 7.178458], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.031503614, -0.15728374, 0.058633905, -0.0030311237, -0.07506102, -0.035917636, 0.059141237, 0.017568024, -0.051331017], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 447, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.4425929, 0.19475189, 0.31164438, 0.0, 0.0, 0.19378015, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 1.0, 6.0, -0.0030311237, -0.07506102, 4.0, 0.059141237, 0.017568024, -0.051331017], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.354393, 15.328754, 21.025639, 6.777877, 8.550878, 12.506534, 8.519104, 7.259887, 5.2466464], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.040774297, -0.015007527, 0.0751453, -0.08318158, 0.04760041, 0.03168581, -0.07576127], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 448, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.5325756, 0.41631892, 0.0, 0.87196493, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, 0.0751453, 4.0, 0.04760041, 0.03168581, -0.07576127], "split_indices": [1, 2, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [42.19272, 33.684418, 8.508301, 24.976902, 8.707517, 11.709182, 13.26772], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.03157316, 0.100366615, -0.06983311, 0.011676929, 0.04184368, -0.03720438, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 449, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.2041958, 0.018974781, 0.039775655, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 7.0, 0.011676929, 0.04184368, -0.03720438, -0.0], "split_indices": [1, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [26.828423, 16.578228, 10.250194, 8.218799, 8.359429, 5.153537, 5.0966573], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.04747486, 0.023791894, -0.099799804, -0.06724905, -0.031189207, 0.016582688, -0.057637546], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 450, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.22821411, 0.0, 0.17472552, 0.0, 0.26550183, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, 0.023791894, 4.0, -0.06724905, 8.0, 0.016582688, -0.057637546], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [31.899609, 8.610336, 23.289272, 6.9560456, 16.333227, 10.647758, 5.685469], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.08133597, -0.01811872, 0.14898589, 0.20713751, -0.0, 0.10007944, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 451, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.28532112, 0.0, 0.18354651, 0.40736294, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [4.0, -0.01811872, 6.0, 8.0, -0.0, 0.10007944, -0.0], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [26.8953, 8.246756, 18.648544, 13.442782, 5.205762, 8.228731, 5.214052], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.06087885, 0.083716944, -0.0, 0.10397133, -0.040742844, 0.0057297037, 0.046294402], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 452, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.3809837, 0.0, 0.365835, 0.044354796, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, 0.083716944, 7.0, 5.0, -0.040742844, 0.0057297037, 0.046294402], "split_indices": [1, 0, 2, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.4378, 5.3410487, 24.09675, 14.129209, 9.967543, 6.513697, 7.615511], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.1063457, 0.033557348, 0.064693235, 0.02855181, -0.0, -0.04536715, 0.040490225], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 453, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.17652765, 0.04171408, 0.0, 0.0, 0.26017144, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 3.0, 0.064693235, 0.02855181, 5.0, -0.04536715, 0.040490225], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [25.230944, 16.478725, 8.752217, 5.7783036, 10.700422, 5.1941543, 5.5062675], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.09727234, -0.175881, -0.0, -0.0796907, -0.02496296, 0.049786877, -0.05341511], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 454, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.21116632, 0.07226604, 0.442127, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 4.0, -0.0796907, -0.02496296, 0.049786877, -0.05341511], "split_indices": [2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.486927, 14.539699, 12.947228, 5.8897696, 8.649928, 6.6263275, 6.3209004], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.04206863, 0.08244279, -0.18767905, 0.067788005, 0.010609372, -0.01725452, -0.07881106, -0.011835075, 0.034710545], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 455, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.7379823, 0.19553141, 0.13623631, 0.0, 0.09551039, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 5.0, 0.067788005, 5.0, -0.01725452, -0.07881106, -0.011835075, 0.034710545], "split_indices": [2, 2, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.270798, 20.493694, 17.777103, 5.655918, 14.837776, 7.633261, 10.143841, 9.82894, 5.008836], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.002086542, -0.1484156, 0.107004456, -0.09010284, 0.01365941, 0.008326759, 0.14223418, 0.014641158, 0.05797392], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 456, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.5867025, 0.48068178, 0.041669726, 0.0, 0.0, 0.0, 0.035042733, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 6.0, 5.0, -0.09010284, 0.01365941, 0.008326759, 7.0, 0.014641158, 0.05797392], "split_indices": [1, 2, 2, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.412224, 13.984333, 21.427893, 7.823144, 6.1611896, 8.017974, 13.409918, 6.1192904, 7.2906275], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.020484108, -0.044859085, 0.0561462, 0.061474994, 0.015204524, 0.019822022, -0.04161391], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 457, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.22176167, 0.0, 0.16719714, 0.0, 0.20835757, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.044859085, 3.0, 0.061474994, 7.0, 0.019822022, -0.04161391], "split_indices": [2, 0, 2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.731327, 5.1556, 30.575726, 5.207082, 25.368645, 19.98435, 5.3842936], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.040676203, 0.061726846, -0.10904038, 0.04333576, -0.011790982, -0.018633457, -0.089097016, -0.03748521, 0.046461284], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 458, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.33004734, 0.16889963, 0.44269475, 0.0, 0.0, 0.39435995, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 6.0, 8.0, 0.04333576, -0.011790982, 8.0, -0.089097016, -0.03748521, 0.046461284], "split_indices": [1, 2, 2, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.507626, 17.271257, 27.23637, 9.803743, 7.4675136, 19.491724, 7.744646, 12.616799, 6.874924], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.035167377, 0.02594715, -0.08368085, -0.039570272, 0.03323564, 0.01769329, -0.046633147], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 459, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.4495897, 0.1391795, 0.0, 0.16763216, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 4.0, -0.08368085, 5.0, 0.03323564, 0.01769329, -0.046633147], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [26.994251, 21.969643, 5.024608, 12.032367, 9.937276, 6.301277, 5.73109], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.011876815, -0.08286966, 0.0940595, -0.07964167, 0.012618451, 0.040330954, 0.0047832998], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 460, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.23805617, 0.34457347, 0.03578283, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 3.0, -0.07964167, 0.012618451, 0.040330954, 0.0047832998], "split_indices": [0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.428104, 12.635629, 15.792477, 5.0012155, 7.634413, 9.091829, 6.7006474], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.045695525, 0.035147026, -0.09326217, -0.05931243, -0.0033347164, 0.016397333, -0.018656768], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 461, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.2349101, 0.0, 0.20136257, 0.0, 0.052753165, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, 0.035147026, 7.0, -0.05931243, 7.0, 0.016397333, -0.018656768], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.22327, 5.5741005, 22.64917, 9.409733, 13.239437, 5.8486505, 7.390786], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.027542105, -0.052712098, 0.027845792, 0.037614133, -0.044517707, -0.054618765, 0.02304091], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 462, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.26620105, 0.0, 0.17062023, 0.0, 0.24003077, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.052712098, 5.0, 0.037614133, 7.0, -0.054618765, 0.02304091], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.039724, 7.8020563, 21.237669, 9.3221445, 11.915524, 5.736021, 6.179503], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.02481557, -0.061645187, 0.13213588, -0.0, -0.034845166, 0.07040014, 0.003935845], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 463, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.27587155, 0.053808365, 0.13527995, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 6.0, 4.0, -0.0, -0.034845166, 0.07040014, 0.003935845], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.09323, 14.724498, 12.3687315, 7.3067446, 7.4177527, 5.671155, 6.697576], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.08778606, 0.042890206, -0.0028825917], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 464, "left_children": [1, -1, -1], "loss_changes": [0.16594785, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.042890206, -0.0028825917], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [27.140678, 17.68159, 9.45909], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.02183335, -0.16420272, 0.07821832, -0.06824425, -0.013780092, 0.16138794, -0.013831033, 0.08803505, -0.010930449], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 465, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.6272195, 0.09906775, 0.27995145, 0.0, 0.0, 0.4498839, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [6.0, 7.0, 6.0, -0.06824425, -0.013780092, 9.0, -0.013831033, 0.08803505, -0.010930449], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [41.527733, 17.287148, 24.240587, 10.1093445, 7.177802, 14.827442, 9.413145, 8.904856, 5.9225864], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.020936497, 0.079093754, -0.039715134, 0.14908724, 0.006705405, 0.061869953, 0.022362057, -0.044679765, 0.030159665], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 466, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.3503881, 0.13121736, 0.0, 0.01653558, 0.2450824, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, -0.039715134, 3.0, 3.0, 0.061869953, 0.022362057, -0.044679765, 0.030159665], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.719135, 28.17006, 9.549075, 13.080919, 15.089139, 5.645386, 7.435533, 5.053459, 10.03568], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.11564177, -0.17010733, 0.0034850037, -0.00022316995, -0.230947, -0.0, -0.10516117], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 467, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.26400423, 0.23927301, 0.0, 0.0, 0.4751147, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 2.0, 0.0034850037, -0.00022316995, 3.0, -0.0, -0.10516117], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.517513, 24.486563, 10.030951, 7.1733966, 17.313168, 6.497424, 10.815743], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.04155042, -0.21261178, 0.026178557, -0.02880005, -0.08232065, 0.06235123, -0.046486896, -0.058797605, 0.0032798313], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 468, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.51778054, 0.027489543, 0.43487176, 0.0, 0.0, 0.0, 0.2143106, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 3.0, 2.0, -0.02880005, -0.08232065, 0.06235123, 4.0, -0.058797605, 0.0032798313], "split_indices": [2, 1, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [41.601357, 11.611586, 29.989773, 5.4667263, 6.14486, 8.430371, 21.5594, 5.796122, 15.763279], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.019111007, 0.05326493, -0.0892832, 0.04678666, -0.009567444, -0.09019078, 0.02486469], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 469, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.13641208, 0.12526202, 0.55164087, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 5.0, 7.0, 0.04678666, -0.009567444, -0.09019078, 0.02486469], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [24.213663, 11.336753, 12.876909, 5.294724, 6.042029, 5.7063494, 7.17056], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.036275215, 0.028815562, -0.088525094, -0.2156394, 0.05505166, -0.10880639, -0.015381095], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 470, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.20806518, 0.0, 0.79774153, 0.3168252, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, 0.028815562, 5.0, 7.0, 0.05505166, -0.10880639, -0.015381095], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.972044, 7.13921, 20.832832, 14.590088, 6.242745, 6.7428436, 7.8472447], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.041549776, -0.08999504, 0.020238223, 0.031415816, -0.0061719436, 0.022339916, -0.02595675], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 471, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.52458704, 0.0, 0.067210585, 0.0, 0.13338798, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.08999504, 6.0, 0.031415816, 7.0, 0.022339916, -0.02595675], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.720083, 5.3030415, 24.417042, 6.01083, 18.406212, 8.602044, 9.804168], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0028506427, 0.062155947, -0.039179374, 0.03220946, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 472, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.25473297, 0.04637529, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [6.0, 5.0, -0.039179374, 0.03220946, -0.0], "split_indices": [1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.872763, 18.294111, 9.578651, 9.489561, 8.804549], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.055398736, 0.056478888, 0.00302846, -0.05272287, 0.051828425, -0.053876657, 0.0269813], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 473, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.17718779, 0.0, 0.22789621, 0.32866773, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.056478888, 8.0, 7.0, 0.051828425, -0.053876657, 0.0269813], "split_indices": [1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.29595, 6.443744, 20.852205, 15.771904, 5.0803013, 8.582087, 7.1898174], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0749057, -0.08522839, -0.008860828, 0.054106064, -0.033212785, -0.019235773, 0.0460018], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 474, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.367693, 0.0, 0.16154473, 0.18896693, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [4.0, -0.08522839, 8.0, 6.0, -0.033212785, -0.019235773, 0.0460018], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.916477, 5.543253, 22.373224, 13.508575, 8.864648, 5.8660097, 7.6425657], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0054567293, 0.050242987, -0.043582417, -0.12097439, 0.045341868, -0.074084535, 0.040410873, 0.06327999, -0.02713445], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 475, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.26167503, 0.0, 0.24992204, 0.6653694, 0.39409932, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.050242987, 7.0, 8.0, 5.0, -0.074084535, 0.040410873, 0.06327999, -0.02713445], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.460674, 6.041506, 33.419167, 18.27678, 15.142388, 12.528938, 5.74784, 6.905011, 8.237377], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.011450543, 0.12291993, -0.10655399, 0.23172888, -0.045210533, -0.21214965, 0.03047561, 0.107629426, -0.0, -0.029814841, -0.09037886], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 476, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.5075028, 0.6433707, 0.43475807, 0.46844363, 0.0, 0.058435917, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 7.0, 7.0, 4.0, -0.045210533, 3.0, 0.03047561, 0.107629426, -0.0, -0.029814841, -0.09037886], "split_indices": [2, 1, 1, 2, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.461803, 19.164455, 17.297348, 14.082825, 5.0816307, 11.781435, 5.515914, 8.93197, 5.1508546, 6.6295667, 5.151868], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.15409766, 0.13131751, -0.096119605, -0.004231245, 0.06563345, 0.00750001], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 477, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.7610308, 0.3686272, 0.15689424, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 4.0, -0.096119605, -0.004231245, 0.06563345, 0.00750001], "split_indices": [0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.547443, 16.464684, 19.08276, 6.609794, 9.854889, 9.373452, 9.709307], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0074084224, -0.08147993, 0.13804571, -0.012880776, -0.14839749, 0.07102855, -0.007300447, -0.052283984, 0.025181217, -0.0667936, -0.016061349], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 478, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.4882614, 0.123199865, 0.26795617, 0.28971684, 0.064290196, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 6.0, 7.0, 2.0, 8.0, 0.07102855, -0.007300447, -0.052283984, 0.025181217, -0.0667936, -0.016061349], "split_indices": [1, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [43.79949, 29.767782, 14.031711, 16.025963, 13.74182, 8.824684, 5.207027, 6.096192, 9.92977, 6.36139, 7.3804307], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.008836845, 0.03854154, -0.061031207, -0.005510632, -0.029333591], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 479, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.24124184, 0.0, 0.0136625245, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, 0.03854154, 7.0, -0.005510632, -0.029333591], "split_indices": [0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [26.116425, 9.847337, 16.269087, 9.481988, 6.7871], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.10164475, -0.036548685, -0.09600001, -0.1618344, 0.092756994, -0.020253122, -0.0629194, -0.004044117, 0.059770573], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 480, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.38521904, 0.42571187, 0.0, 0.018730342, 0.16275948, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, -0.09600001, 2.0, 5.0, -0.020253122, -0.0629194, -0.004044117, 0.059770573], "split_indices": [0, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.432487, 23.925245, 5.5072417, 12.416528, 11.508717, 5.6962137, 6.7203145, 5.7279563, 5.780761], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.08485763, -0.07995861, 0.009079359, 0.033434562, -0.054409653, 0.019961143], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 481, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.2007276, 0.0029523969, 0.25372052, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 8.0, 0.009079359, 0.033434562, -0.054409653, 0.019961143], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.613005, 13.119255, 14.49375, 6.229162, 6.8900933, 8.827671, 5.6660786], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.017882742, -0.04458134, 0.031373058, -0.01141354, 0.03228937, 0.03213358, -0.04789358], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 482, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.1943819, 0.0, 0.07724479, 0.25249624, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.04458134, 7.0, 5.0, 0.03228937, 0.03213358, -0.04789358], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [26.64436, 7.2955813, 19.348778, 12.099205, 7.2495728, 6.5025644, 5.596641], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.062431302, 0.070256546, -0.17750423, 0.028116433, 0.0071676355, -0.0060300766, -0.08071625], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 483, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.43903425, 0.00010123849, 0.18575981, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 5.0, 0.028116433, 0.0071676355, -0.0060300766, -0.08071625], "split_indices": [2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [26.234573, 11.961964, 14.27261, 5.832114, 6.129849, 6.098747, 8.173862], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.04608833, -0.038515553, 0.124003455, -0.029541176, -0.0, 0.05720463, 0.018203424], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 484, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.21012837, 0.03779196, 0.03251788, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 2.0, -0.029541176, -0.0, 0.05720463, 0.018203424], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [28.855255, 13.460951, 15.394304, 5.1098638, 8.351087, 5.7497716, 9.644533], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.054042585, 0.07843353, -0.23933391, -0.018396884, 0.19744329, -0.098239474, -0.019668099, 0.117651105, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 485, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.9623605, 0.39946795, 0.18907511, 0.0, 0.5088593, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 7.0, -0.018396884, 7.0, -0.098239474, -0.019668099, 0.117651105, -0.0], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.785107, 21.471107, 15.313999, 9.711307, 11.759801, 9.192192, 6.121807, 5.542922, 6.216879], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.023848902, 0.090510696, -0.034913514, 0.024450237, 0.05599608, -0.048438024, 0.010806649, 0.03666066, -0.018976219, 0.023938926, -0.012889044], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 486, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.1644318, 0.09944072, 0.1386085, 0.1288802, 0.0, 0.0, 0.06580155, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 6.0, 3.0, 0.05599608, -0.048438024, 8.0, 0.03666066, -0.018976219, 0.023938926, -0.012889044], "split_indices": [1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.75077, 18.699266, 20.051504, 12.443606, 6.2556596, 5.2303667, 14.821137, 6.237912, 6.205694, 7.094403, 7.7267346], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.08814963, -0.2343701, 0.008375807, -0.12009869, -0.010141782, -0.0356795, 0.058467876, 0.016050879, -0.04148587, -0.015352692, 0.055137847], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 487, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.52947164, 0.43139905, 0.053140473, 0.0, 0.0, 0.11944982, 0.18458697, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 5.0, -0.12009869, -0.010141782, 6.0, 6.0, 0.016050879, -0.04148587, -0.015352692, 0.055137847], "split_indices": [2, 0, 0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.77365, 13.671486, 21.102165, 6.66328, 7.008206, 10.3185425, 10.783623, 5.2550187, 5.063524, 5.6416345, 5.1419883], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.070921265, -0.071433745, -0.033542316, -0.0031893637, -0.04673455, -0.023773791, 0.01445121], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 488, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.24574146, 0.0, 0.12603511, 0.133596, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, -0.071433745, 9.0, 4.0, -0.04673455, -0.023773791, 0.01445121], "split_indices": [1, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [43.94709, 6.559913, 37.387177, 31.427626, 5.959549, 13.234053, 18.193573], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.08615666, 0.03505709, -0.0918588, 0.040565897, -0.0034892547], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 489, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.74774694, 0.097013324, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [6.0, 4.0, -0.0918588, 0.040565897, -0.0034892547], "split_indices": [1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [25.61609, 16.766235, 8.849854, 5.408557, 11.357678], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.026785921, 0.07835493, -0.023257978, 0.01160748, 0.055660944, -0.030479077, 0.043595906], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 490, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.15935989, 0.122623794, 0.0, 0.23238184, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [6.0, 4.0, -0.023257978, 7.0, 0.055660944, -0.030479077, 0.043595906], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.48004, 19.197077, 8.282963, 13.087877, 6.109199, 6.81252, 6.2753577], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.040442593, -0.041887872, 0.05905162, -0.04769632, -0.003368319, 0.04234256], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 491, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.1733321, 0.0, 0.27606878, 0.0834232, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, 0.040442593, 7.0, 4.0, -0.04769632, -0.003368319, 0.04234256], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [27.157343, 6.441041, 20.716301, 10.933457, 9.782844, 5.6992517, 5.234206], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.007374281, -0.08001073, 0.0404742, -0.0, -0.04083968, -0.03150793, 0.07752627], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 492, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.11322194, 0.0535755, 0.6204531, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 6.0, -0.0, -0.04083968, -0.03150793, 0.07752627], "split_indices": [2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [29.507431, 12.281399, 17.226032, 5.8024807, 6.4789186, 10.3654, 6.860632], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.01739417, -0.03450184, 0.09263204, 0.0029864481, 0.05220025], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 493, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.2909338, 0.0, 0.10849784, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [4.0, -0.03450184, 6.0, 0.0029864481, 0.05220025], "split_indices": [0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [27.360935, 9.241125, 18.11981, 10.153925, 7.965886], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.123163044, 0.024599856, -0.26275325, -0.11108263, -0.019281462], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 494, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.7990296, 0.0, 0.28078175, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [3.0, 0.024599856, 7.0, -0.11108263, -0.019281462], "split_indices": [1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [25.516878, 10.208756, 15.308123, 9.041026, 6.2670965], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.07461912, -0.0335564, 0.12116138, 0.0075516384, 0.15369555, 0.08334008, 0.015362955], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 495, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.35552877, 0.0, 0.07929051, 0.0, 0.24784678, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.0335564, 3.0, 0.0075516384, 6.0, 0.08334008, 0.015362955], "split_indices": [2, 0, 1, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.2959, 6.9397154, 31.35618, 9.164506, 22.191675, 8.822151, 13.369525], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0012836134, -0.058089625, 0.07043099, -0.13017187, 0.01523266, 0.12170873, -0.0023015144, -0.017658623, -0.05433779, 0.060832445, 0.0053231483], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 496, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.17412625, 0.18633512, 0.09333511, 0.016223282, 0.0, 0.08426058, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 8.0, 3.0, 0.01523266, 4.0, -0.0023015144, -0.017658623, -0.05433779, 0.060832445, 0.0053231483], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.115734, 20.891245, 19.224491, 13.003901, 7.887344, 12.041067, 7.183424, 7.1355333, 5.8683667, 5.691147, 6.3499207], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.008880884, 0.18686716, -0.111898094, 0.0171385, 0.075598344, -0.0052490984, -0.15847324, -0.06561877, -0.017803209], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 497, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.7937204, 0.08229077, 0.07415858, 0.0, 0.0, 0.0, 0.040227324, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 2.0, 4.0, 0.0171385, 0.075598344, -0.0052490984, 7.0, -0.06561877, -0.017803209], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.60094, 14.101299, 20.499641, 5.8534303, 8.247869, 7.9364147, 12.563228, 6.435872, 6.127355], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.039421268, 0.013893617, 0.044895254, -0.06054653, 0.088247746, 0.014439819, -0.05374511, -0.015695436, 0.043567307], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 498, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.1070875, 0.21876442, 0.0, 0.26584762, 0.17802271, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, 0.044895254, 2.0, 2.0, 0.014439819, -0.05374511, -0.015695436, 0.043567307], "split_indices": [0, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [43.607494, 37.0585, 6.548997, 17.939264, 19.119232, 9.2313795, 8.707885, 5.0318666, 14.087366], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.008377098, -0.122740105, 0.038718678, 0.04347837, -0.10046048], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 499, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.44544595, 0.8118217, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [5.0, 4.0, 0.038718678, 0.04347837, -0.10046048], "split_indices": [0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [26.10916, 12.108437, 14.000723, 5.285037, 6.8233995], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "5E-1", "boost_from_average": "1", "num_class": "10", "num_feature": "3", "num_target": "1"}, "objective": {"name": "multi:softprob", "softmax_multiclass_param": {"num_class": "10"}}}, "version": [3, 0, 2]}