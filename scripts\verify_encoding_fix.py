#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import ast

def test_syntax():
    try:
        # 测试百位训练脚本
        with open('scripts/train_hundreds_predictor.py', 'r', encoding='utf-8') as f:
            ast.parse(f.read())
        print("✅ 百位训练脚本语法正确")
        
        # 测试十位训练脚本
        with open('scripts/train_tens_predictor.py', 'r', encoding='utf-8') as f:
            ast.parse(f.read())
        print("✅ 十位训练脚本语法正确")
        
        # 测试个位训练脚本
        with open('scripts/train_units_predictor.py', 'r', encoding='utf-8') as f:
            ast.parse(f.read())
        print("✅ 个位训练脚本语法正确")
        
        print("🎉 所有训练脚本编码修复成功！")
        return True
        
    except Exception as e:
        print(f"❌ 编码修复失败: {e}")
        return False

if __name__ == "__main__":
    test_syntax()
