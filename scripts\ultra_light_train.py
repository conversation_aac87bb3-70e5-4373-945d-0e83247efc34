#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超轻量级训练脚本 - 专门解决OOM问题
仅使用最少内存进行模型训练
"""

import sys
import gc
import os
import argparse
import sqlite3
import numpy as np
from pathlib import Path

# 设置最严格的内存优化
os.environ['PYTHONHASHSEED'] = '0'
os.environ['PYTHONOPTIMIZE'] = '2'
os.environ['PYTHONDONTWRITEBYTECODE'] = '1'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'

# 强制垃圾回收
gc.collect()

def ultra_light_xgb_train(position, issue=None, limit=1000):
    """超轻量级XGBoost训练"""
    print(f"[START] 启动超轻量级{position}位XGBoost训练")
    
    try:
        # 延迟导入，减少内存占用
        import xgboost as xgb
        
        # 连接数据库
        db_path = Path("data/fucai3d.db")
        if not db_path.exists():
            print("[ERROR] 数据库文件不存在")
            return False
            
        conn = sqlite3.connect(str(db_path))
        
        # 查询最少的数据
        query = f"""
        SELECT issue, hundreds, tens, units
        FROM lottery_data
        WHERE hundreds IS NOT NULL AND tens IS NOT NULL AND units IS NOT NULL
        ORDER BY issue DESC
        LIMIT {limit}
        """
        
        cursor = conn.execute(query)
        data = cursor.fetchall()
        conn.close()
        
        if len(data) < 100:
            print("[ERROR] 数据不足，无法训练")
            return False

        print(f"[DATA] 加载数据: {len(data)}条")
        
        # 准备训练数据（最简单的特征）
        X = []
        y = []
        
        position_map = {'hundreds': 0, 'tens': 1, 'units': 2}
        target_col = position_map[position]
        
        for i in range(len(data) - 1):
            # 只使用前一期的数字作为特征
            prev_data = data[i + 1]
            curr_data = data[i]
            
            # 简单特征：前一期的三个位置数字
            features = [prev_data[1], prev_data[2], prev_data[3]]
            target = curr_data[target_col + 1]  # +1因为第0列是issue
            
            X.append(features)
            y.append(target)
        
        # 转换为numpy数组（使用float32节省内存）
        X = np.array(X, dtype=np.float32)
        y = np.array(y, dtype=np.int32)
        
        print(f"[FEATURE] 特征维度: {X.shape}")
        print(f"[TARGET] 目标维度: {y.shape}")
        
        # 分割数据（80%训练，20%测试）
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        # 创建DMatrix（XGBoost的数据格式）
        dtrain = xgb.DMatrix(X_train, label=y_train)
        dtest = xgb.DMatrix(X_test, label=y_test)
        
        # 超轻量级参数
        params = {
            'objective': 'multi:softprob',
            'num_class': 10,
            'max_depth': 3,  # 很浅的树
            'learning_rate': 0.3,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'min_child_weight': 5,
            'reg_alpha': 0.1,
            'reg_lambda': 1.0,
            'tree_method': 'hist',  # 内存友好的方法
            'verbosity': 0
        }
        
        # 训练模型（很少的轮次）
        print("[TRAIN] 开始训练...")
        model = xgb.train(
            params,
            dtrain,
            num_boost_round=50,  # 只训练50轮
            evals=[(dtest, 'test')],
            verbose_eval=False
        )

        # 预测测试集
        y_pred = model.predict(dtest)
        y_pred_class = np.argmax(y_pred, axis=1)

        # 计算准确率
        accuracy = np.mean(y_pred_class == y_test)
        print(f"[RESULT] 训练完成，测试准确率: {accuracy:.3f}")

        # 保存模型
        model_dir = Path(f"models/{position}")
        model_dir.mkdir(parents=True, exist_ok=True)

        model_path = model_dir / f"ultra_light_xgb_{issue or 'latest'}.json"
        model.save_model(str(model_path))
        print(f"[SAVE] 模型已保存: {model_path}")
        
        # 清理内存
        del X, y, X_train, X_test, y_train, y_test
        del dtrain, dtest, model
        gc.collect()
        
        return True
        
    except Exception as e:
        print(f"[ERROR] 训练失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='超轻量级训练脚本')
    parser.add_argument('--position', choices=['hundreds', 'tens', 'units'],
                       default='hundreds', help='训练位置')
    parser.add_argument('--issue', type=str, help='期号')
    parser.add_argument('--limit', type=int, default=1000, help='数据限制')

    args = parser.parse_args()

    print("[ULTRA_LIGHT] 超轻量级训练模式启动")
    print(f"[POSITION] 位置: {args.position}")
    print(f"[ISSUE] 期号: {args.issue or '最新'}")
    print(f"[LIMIT] 数据限制: {args.limit}条")

    # 强制垃圾回收
    gc.collect()

    # 执行训练
    success = ultra_light_xgb_train(args.position, args.issue, args.limit)

    if success:
        print("[SUCCESS] 超轻量级训练成功完成！")
        print("[INFO] 提示: 这是应急模式，模型精度可能较低")
    else:
        print("[ERROR] 超轻量级训练失败")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
