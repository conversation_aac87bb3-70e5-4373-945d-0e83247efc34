#!/usr/bin/env python3
"""
阶段2调试验证脚本
验证训练脚本的修复效果
"""

import sys
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_script_syntax(script_name):
    """测试脚本语法"""
    try:
        script_path = project_root / 'scripts' / script_name
        with open(script_path, 'r', encoding='utf-8') as f:
            script_content = f.read()
        
        compile(script_content, str(script_path), 'exec')
        print(f"   ✅ {script_name}: 语法正确")
        return True
        
    except SyntaxError as e:
        print(f"   ❌ {script_name}: 语法错误 - {e}")
        return False
    except Exception as e:
        print(f"   ❌ {script_name}: 检查失败 - {e}")
        return False

def test_help_output(script_name):
    """测试帮助输出"""
    try:
        result = subprocess.run([
            'python', f'scripts/{script_name}', '--help'
        ], capture_output=True, text=True, cwd=project_root, timeout=10)
        
        help_output = result.stdout
        
        # 检查关键参数
        required_params = [
            '--issue',
            '--auto-issue',
            '--train-until',
            'data/fucai3d.db'  # 检查数据库路径是否修复
        ]
        
        missing_params = []
        for param in required_params:
            if param not in help_output:
                missing_params.append(param)
        
        if missing_params:
            print(f"   ❌ {script_name}: 帮助输出缺少 {missing_params}")
            return False
        else:
            print(f"   ✅ {script_name}: 帮助输出正确")
            return True
            
    except subprocess.TimeoutExpired:
        print(f"   ❌ {script_name}: 帮助输出超时")
        return False
    except Exception as e:
        print(f"   ❌ {script_name}: 帮助输出测试失败 - {e}")
        return False

def test_parameter_parsing(script_name):
    """测试参数解析"""
    try:
        # 导入脚本模块
        script_module_name = script_name.replace('.py', '')
        script_path = f'scripts.{script_module_name}'
        
        # 临时替换sys.argv进行测试
        original_argv = sys.argv
        sys.argv = [script_name, '--issue', '2025217', '--auto-issue']
        
        try:
            # 动态导入并测试参数解析
            import importlib
            module = importlib.import_module(script_path)
            
            if hasattr(module, 'parse_arguments'):
                args = module.parse_arguments()
                
                # 验证参数解析
                checks = [
                    (args.issue == '2025217', "issue参数"),
                    (args.auto_issue == True, "auto_issue参数"),
                    (args.db_path == 'data/fucai3d.db', "数据库路径默认值")
                ]
                
                all_passed = True
                for check, name in checks:
                    if not check:
                        print(f"   ❌ {script_name}: {name} 错误")
                        all_passed = False
                
                if all_passed:
                    print(f"   ✅ {script_name}: 参数解析正确")
                    return True
                else:
                    return False
            else:
                print(f"   ❌ {script_name}: 没有parse_arguments函数")
                return False
                
        finally:
            sys.argv = original_argv
            
    except Exception as e:
        print(f"   ❌ {script_name}: 参数解析测试失败 - {e}")
        return False

def test_issue_logic():
    """测试期号逻辑"""
    try:
        from src.utils.dynamic_issue_manager import issue_manager
        
        # 测试期号获取
        latest_issue = issue_manager.get_latest_issue()
        print(f"   ✅ 最新期号获取: {latest_issue}")
        
        # 测试文件名生成
        filename = issue_manager.generate_model_filename('xgb', 'hundreds', latest_issue)
        expected = f"xgb_hundreds_model_{latest_issue}.pkl"
        
        if filename == expected:
            print(f"   ✅ 文件名生成: {filename}")
            return True
        else:
            print(f"   ❌ 文件名生成错误: 期望 {expected}, 实际 {filename}")
            return False
            
    except Exception as e:
        print(f"   ❌ 期号逻辑测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 阶段2调试验证")
    print("=" * 50)
    
    scripts_to_test = [
        'train_hundreds_predictor.py',
        'train_tens_predictor.py', 
        'train_units_predictor.py'
    ]
    
    total_tests = 0
    passed_tests = 0
    
    # 测试1: 脚本语法
    print("\n📋 测试脚本语法...")
    for script in scripts_to_test:
        total_tests += 1
        if test_script_syntax(script):
            passed_tests += 1
    
    # 测试2: 帮助输出
    print("\n📋 测试帮助输出...")
    for script in scripts_to_test:
        total_tests += 1
        if test_help_output(script):
            passed_tests += 1
    
    # 测试3: 参数解析
    print("\n📋 测试参数解析...")
    for script in scripts_to_test:
        total_tests += 1
        if test_parameter_parsing(script):
            passed_tests += 1
    
    # 测试4: 期号逻辑
    print("\n📋 测试期号逻辑...")
    total_tests += 1
    if test_issue_logic():
        passed_tests += 1
    
    print("\n" + "=" * 50)
    print(f"调试验证结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎉 所有调试验证通过！阶段2修复成功")
        return True
    else:
        print("⚠️ 部分调试验证失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
