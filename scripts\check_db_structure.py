#!/usr/bin/env python3
"""
检查数据库表结构
"""

import sqlite3

def check_lottery_data_table():
    """检查lottery_data表结构"""
    try:
        conn = sqlite3.connect('data/fucai3d.db')
        cursor = conn.cursor()
        
        # 获取表结构
        cursor.execute('PRAGMA table_info(lottery_data)')
        columns = cursor.fetchall()
        
        print("lottery_data表结构:")
        print("=" * 50)
        for col in columns:
            col_id, name, data_type, not_null, default_val, pk = col
            null_str = "NOT NULL" if not_null else "NULL"
            pk_str = " PRIMARY KEY" if pk else ""
            default_str = f" DEFAULT {default_val}" if default_val else ""
            print(f"{name:15} {data_type:10} {null_str:8}{default_str}{pk_str}")
        
        # 查看最新几条数据
        cursor.execute('SELECT * FROM lottery_data ORDER BY issue DESC LIMIT 3')
        recent_data = cursor.fetchall()
        
        print("\n最新3条数据:")
        print("=" * 50)
        for row in recent_data:
            print(row)
        
        conn.close()
        
    except Exception as e:
        print(f"检查失败: {e}")

if __name__ == "__main__":
    check_lottery_data_table()
