#!/usr/bin/env python3
"""
简单的训练测试
"""

import subprocess
import sys
import os

def simple_test():
    """简单测试"""
    print("🔍 简单训练测试")
    
    # 直接运行训练脚本
    cmd = [
        sys.executable,
        "scripts/ultra_light_train.py",
        "--position", "units",
        "--limit", "100",
        "--issue", "2025231"
    ]
    
    print(f"🚀 执行命令: {' '.join(cmd)}")
    
    try:
        # 运行命令并捕获输出
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            encoding='utf-8',
            cwd=os.getcwd(),
            timeout=60
        )
        
        print(f"📊 返回码: {result.returncode}")
        
        if result.stdout:
            print("📝 标准输出:")
            lines = result.stdout.split('\n')
            for i, line in enumerate(lines):
                if line.strip():
                    print(f"  {i+1}: {line}")
                    if "[TRAINING_INFO]" in line:
                        print(f"  🔍 找到训练信息: {line}")
        
        if result.stderr:
            print("❌ 错误输出:")
            print(result.stderr)
        
        # 检查是否包含训练信息
        has_training_info = "[TRAINING_INFO]" in result.stdout
        print(f"\n✅ 训练信息输出: {'是' if has_training_info else '否'}")
        
        return has_training_info
        
    except subprocess.TimeoutExpired:
        print("❌ 命令超时")
        return False
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False

if __name__ == "__main__":
    success = simple_test()
    print(f"\n🎯 测试结果: {'成功' if success else '失败'}")
