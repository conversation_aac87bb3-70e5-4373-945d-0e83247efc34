#!/usr/bin/env python3
"""
测试百位训练脚本的期号支持功能
"""

import sys
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_help_output():
    """测试帮助输出是否包含新参数"""
    print("🔍 测试帮助输出...")
    
    try:
        result = subprocess.run([
            'python', 'scripts/train_hundreds_predictor.py', '--help'
        ], capture_output=True, text=True, cwd=project_root)
        
        help_output = result.stdout
        
        # 检查新参数是否在帮助中
        required_params = [
            '--issue',
            '--train-until', 
            '--auto-issue',
            '训练期号标识',
            '自动获取最新期号'
        ]
        
        missing_params = []
        for param in required_params:
            if param not in help_output:
                missing_params.append(param)
        
        if missing_params:
            print(f"❌ 帮助输出缺少参数: {missing_params}")
            return False
        else:
            print("✅ 帮助输出包含所有新参数")
            return True
            
    except Exception as e:
        print(f"❌ 测试帮助输出失败: {e}")
        return False

def test_issue_parameter_parsing():
    """测试期号参数解析"""
    print("\n🔍 测试期号参数解析...")
    
    try:
        # 测试脚本导入和参数解析
        from scripts.train_hundreds_predictor import parse_arguments
        
        # 模拟命令行参数
        test_args = [
            '--model', 'xgb',
            '--issue', '2025217',
            '--train-until', '2025216',
            '--auto-issue'
        ]
        
        # 临时替换sys.argv
        original_argv = sys.argv
        sys.argv = ['train_hundreds_predictor.py'] + test_args
        
        try:
            args = parse_arguments()
            
            # 验证参数解析
            checks = [
                (args.model == 'xgb', "model参数"),
                (args.issue == '2025217', "issue参数"),
                (args.train_until == '2025216', "train_until参数"),
                (args.auto_issue == True, "auto_issue参数")
            ]
            
            all_passed = True
            for check, name in checks:
                if check:
                    print(f"   ✅ {name}: 正确")
                else:
                    print(f"   ❌ {name}: 错误")
                    all_passed = False
            
            return all_passed
            
        finally:
            sys.argv = original_argv
            
    except Exception as e:
        print(f"❌ 参数解析测试失败: {e}")
        return False

def test_dynamic_issue_integration():
    """测试动态期号管理器集成"""
    print("\n🔍 测试动态期号管理器集成...")
    
    try:
        from src.utils.dynamic_issue_manager import issue_manager
        
        # 测试期号获取
        latest_issue = issue_manager.get_latest_issue()
        print(f"   ✅ 获取最新期号: {latest_issue}")
        
        # 测试文件名生成
        test_issue = "2025217"
        filename = issue_manager.generate_model_filename('xgb', 'hundreds', test_issue)
        expected = f"xgb_hundreds_model_{test_issue}.pkl"
        
        if filename == expected:
            print(f"   ✅ 文件名生成: {filename}")
            return True
        else:
            print(f"   ❌ 文件名生成错误: 期望 {expected}, 实际 {filename}")
            return False
            
    except Exception as e:
        print(f"❌ 动态期号管理器集成测试失败: {e}")
        return False

def test_save_function_exists():
    """测试保存函数是否存在"""
    print("\n🔍 测试保存函数...")
    
    try:
        from scripts.train_hundreds_predictor import save_models_with_issue
        
        # 检查函数签名
        import inspect
        sig = inspect.signature(save_models_with_issue)
        params = list(sig.parameters.keys())
        
        expected_params = ['predictor', 'model_types', 'issue']
        
        if params == expected_params:
            print(f"   ✅ save_models_with_issue函数存在，参数正确: {params}")
            return True
        else:
            print(f"   ❌ save_models_with_issue函数参数错误: 期望 {expected_params}, 实际 {params}")
            return False
            
    except ImportError:
        print("   ❌ save_models_with_issue函数不存在")
        return False
    except Exception as e:
        print(f"   ❌ 测试保存函数失败: {e}")
        return False

def test_script_syntax():
    """测试脚本语法正确性"""
    print("\n🔍 测试脚本语法...")
    
    try:
        # 尝试编译脚本
        script_path = project_root / 'scripts' / 'train_hundreds_predictor.py'
        
        with open(script_path, 'r', encoding='utf-8') as f:
            script_content = f.read()
        
        compile(script_content, str(script_path), 'exec')
        print("   ✅ 脚本语法正确")
        return True
        
    except SyntaxError as e:
        print(f"   ❌ 脚本语法错误: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 脚本语法检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 百位训练脚本期号支持功能测试")
    print("=" * 50)
    
    tests = [
        ("帮助输出测试", test_help_output),
        ("参数解析测试", test_issue_parameter_parsing),
        ("动态期号集成测试", test_dynamic_issue_integration),
        ("保存函数测试", test_save_function_exists),
        ("脚本语法测试", test_script_syntax)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: 通过")
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！百位训练脚本期号支持功能正常")
        return True
    else:
        print("⚠️ 部分测试失败，需要检查问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
