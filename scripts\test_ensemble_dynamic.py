#!/usr/bin/env python3
"""
测试集成模型动态期号保存功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_ensemble_filename_generation():
    """测试集成模型文件名生成"""
    try:
        from src.utils.dynamic_issue_manager import issue_manager
        
        print("🔍 测试集成模型文件名生成")
        print("=" * 40)
        
        # 获取最新期号
        latest_issue = issue_manager.get_latest_issue()
        print(f"最新期号: {latest_issue}")
        
        # 测试集成模型文件名生成
        ensemble_filename = issue_manager.generate_model_filename('ensemble', 'hundreds', latest_issue)
        print(f"✅ 集成模型文件名: {ensemble_filename}")
        
        # 测试文件路径生成
        ensemble_filepath = issue_manager.get_model_filepath('ensemble', 'hundreds', latest_issue)
        print(f"✅ 集成模型文件路径: {ensemble_filepath}")
        
        # 验证文件名格式
        expected_filename = f"ensemble_hundreds_model_{latest_issue}_ensemble.pkl"
        
        if ensemble_filename == expected_filename:
            print("✅ 集成模型文件名格式正确")
        else:
            print(f"❌ 集成模型文件名格式错误: 期望 {expected_filename}, 实际 {ensemble_filename}")
            return False
        
        print("\n🎉 集成模型文件名生成测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_ensemble_save_logic():
    """测试集成模型保存逻辑"""
    try:
        from src.utils.dynamic_issue_manager import issue_manager
        
        print("\n🔍 测试集成模型保存逻辑")
        print("=" * 40)
        
        latest_issue = issue_manager.get_latest_issue()
        
        # 模拟集成模型保存路径生成
        model_dir = Path('models/hundreds/')
        model_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成集成模型的基础文件名（不含扩展名）
        filename_base = f"ensemble_hundreds_model_{latest_issue}"
        filepath_base = model_dir / filename_base
        
        # 集成模型会生成多个文件
        ensemble_file = Path(f"{filepath_base}_ensemble.pkl")
        xgb_file = Path(f"{filepath_base}_xgb.pkl")
        lgb_file = Path(f"{filepath_base}_lgb.pkl")
        lstm_file = Path(f"{filepath_base}_lstm.h5")
        
        print(f"✅ 集成模型基础路径: {filepath_base}")
        print(f"✅ 集成配置文件: {ensemble_file}")
        print(f"✅ XGBoost基础模型: {xgb_file}")
        print(f"✅ LightGBM基础模型: {lgb_file}")
        print(f"✅ LSTM基础模型: {lstm_file}")
        
        # 验证路径格式
        expected_ensemble = f"models/hundreds/ensemble_hundreds_model_{latest_issue}_ensemble.pkl"
        expected_xgb = f"models/hundreds/ensemble_hundreds_model_{latest_issue}_xgb.pkl"
        expected_lgb = f"models/hundreds/ensemble_hundreds_model_{latest_issue}_lgb.pkl"
        expected_lstm = f"models/hundreds/ensemble_hundreds_model_{latest_issue}_lstm.h5"
        
        if str(ensemble_file) == expected_ensemble:
            print("✅ 集成配置文件路径格式正确")
        else:
            print(f"❌ 集成配置文件路径格式错误: 期望 {expected_ensemble}, 实际 {ensemble_file}")
            return False
        
        if str(xgb_file) == expected_xgb:
            print("✅ XGBoost基础模型路径格式正确")
        else:
            print(f"❌ XGBoost基础模型路径格式错误: 期望 {expected_xgb}, 实际 {xgb_file}")
            return False
        
        print("\n🎉 集成模型保存逻辑测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_all_positions_ensemble():
    """测试所有位置的集成模型文件名"""
    try:
        from src.utils.dynamic_issue_manager import issue_manager
        
        print("\n🔍 测试所有位置的集成模型文件名")
        print("=" * 40)
        
        latest_issue = issue_manager.get_latest_issue()
        positions = ['hundreds', 'tens', 'units']
        
        for position in positions:
            print(f"\n{position.upper()} 位集成模型:")
            
            # 集成配置文件
            ensemble_filename = issue_manager.generate_model_filename('ensemble', position, latest_issue)
            print(f"  配置文件: {ensemble_filename}")
            
            # 验证格式
            expected_ensemble = f"ensemble_{position}_model_{latest_issue}_ensemble.pkl"
            
            if ensemble_filename != expected_ensemble:
                print(f"❌ {position} 位集成模型文件名格式错误")
                return False
        
        print("\n✅ 所有位置的集成模型文件名格式正确")
        print("\n🎉 所有位置测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_complete_model_structure():
    """测试完整的模型文件结构"""
    try:
        from src.utils.dynamic_issue_manager import issue_manager
        
        print("\n🔍 测试完整的模型文件结构")
        print("=" * 40)
        
        latest_issue = issue_manager.get_latest_issue()
        positions = ['hundreds', 'tens', 'units']
        algorithms = ['xgb', 'lgb', 'lstm', 'ensemble']
        
        print(f"期号: {latest_issue}")
        print()
        
        for position in positions:
            print(f"{position.upper()} 位所有模型文件:")
            
            for algorithm in algorithms:
                if algorithm == 'lstm':
                    # LSTM有两个文件
                    main_file = issue_manager.generate_model_filename(algorithm, position, latest_issue, 'model')
                    comp_file = issue_manager.generate_model_filename(algorithm, position, latest_issue, 'components')
                    print(f"  {algorithm.upper()}: {main_file}")
                    print(f"  {algorithm.upper()}: {comp_file}")
                else:
                    filename = issue_manager.generate_model_filename(algorithm, position, latest_issue)
                    print(f"  {algorithm.upper()}: {filename}")
            print()
        
        print("✅ 完整模型文件结构测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 集成模型动态期号保存功能测试")
    print("=" * 50)
    
    success_count = 0
    total_tests = 4
    
    # 测试1: 文件名生成
    if test_ensemble_filename_generation():
        success_count += 1
    
    # 测试2: 保存逻辑
    if test_ensemble_save_logic():
        success_count += 1
    
    # 测试3: 所有位置
    if test_all_positions_ensemble():
        success_count += 1
    
    # 测试4: 完整结构
    if test_complete_model_structure():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有集成模型测试通过！")
        return True
    else:
        print("⚠️ 部分集成模型测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
