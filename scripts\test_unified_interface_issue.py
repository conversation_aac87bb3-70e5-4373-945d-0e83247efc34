#!/usr/bin/env python3
"""
测试统一预测器接口的期号支持功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_unified_interface_methods():
    """测试统一预测器接口的新方法"""
    print("🔍 测试统一预测器接口期号支持功能")
    print("=" * 50)
    
    try:
        from src.predictors.unified_predictor_interface import UnifiedPredictorInterface
        
        # 初始化统一预测器接口
        interface = UnifiedPredictorInterface(db_path="data/fucai3d.db")
        print("✅ 统一预测器接口初始化成功")
        
        # 测试1: 检查新方法是否存在
        required_methods = [
            'load_models_by_issue',
            'get_available_model_issues',
            'check_models_exist_for_issue',
            'get_current_loaded_issue'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(interface, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ 缺少方法: {missing_methods}")
            return False
        else:
            print("✅ 所有新方法都存在")
        
        # 测试2: 获取可用模型期号
        print("\n🔍 测试获取可用模型期号...")
        available_issues = interface.get_available_model_issues()
        
        if isinstance(available_issues, dict):
            print("✅ 获取可用模型期号成功")
            for position, issues in available_issues.items():
                print(f"   {position}: {len(issues)} 个可用期号")
        else:
            print("❌ 获取可用模型期号失败")
            return False
        
        # 测试3: 检查模型存在性
        print("\n🔍 测试检查模型存在性...")
        test_issue = "2025217"
        exists_result = interface.check_models_exist_for_issue(test_issue)
        
        if isinstance(exists_result, dict):
            print(f"✅ 检查期号 {test_issue} 模型存在性成功")
            for position, exists in exists_result.items():
                status = "存在" if exists else "不存在"
                print(f"   {position}: {status}")
        else:
            print("❌ 检查模型存在性失败")
            return False
        
        # 测试4: 获取当前加载期号
        print("\n🔍 测试获取当前加载期号...")
        current_issue = interface.get_current_loaded_issue()
        
        if current_issue:
            print(f"✅ 当前加载期号: {current_issue}")
        else:
            print("⚠️ 无法获取当前加载期号")
        
        print("\n🎉 统一预测器接口期号支持功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_method_signatures():
    """测试方法签名"""
    print("\n🔍 测试方法签名...")
    
    try:
        from src.predictors.unified_predictor_interface import UnifiedPredictorInterface
        import inspect
        
        interface = UnifiedPredictorInterface(db_path="data/fucai3d.db")
        
        # 检查方法签名
        method_signatures = {
            'load_models_by_issue': ['issue', 'positions'],
            'get_available_model_issues': [],
            'check_models_exist_for_issue': ['issue'],
            'get_current_loaded_issue': []
        }
        
        for method_name, expected_params in method_signatures.items():
            if hasattr(interface, method_name):
                method = getattr(interface, method_name)
                sig = inspect.signature(method)
                actual_params = [p for p in sig.parameters.keys() if p != 'self']
                
                # 检查必需参数是否存在
                missing_params = [p for p in expected_params if p not in actual_params]
                
                if missing_params:
                    print(f"   ❌ {method_name}: 缺少参数 {missing_params}")
                    return False
                else:
                    print(f"   ✅ {method_name}: 参数正确 {actual_params}")
            else:
                print(f"   ❌ {method_name}: 方法不存在")
                return False
        
        print("✅ 所有方法签名正确")
        return True
        
    except Exception as e:
        print(f"❌ 方法签名测试失败: {e}")
        return False

def test_dynamic_issue_integration():
    """测试动态期号管理器集成"""
    print("\n🔍 测试动态期号管理器集成...")
    
    try:
        from src.utils.dynamic_issue_manager import issue_manager
        
        # 测试期号获取
        latest_issue = issue_manager.get_latest_issue()
        print(f"   ✅ 最新期号: {latest_issue}")
        
        # 测试可用模型期号
        available_models = issue_manager.get_available_model_issues('hundreds')
        print(f"   ✅ 百位可用模型: {len(available_models)} 个")
        
        # 测试模型完整性检查
        complete = issue_manager.check_models_complete_for_issue(latest_issue, 'hundreds')
        status = "完整" if complete else "不完整"
        print(f"   ✅ 期号 {latest_issue} 百位模型: {status}")
        
        print("✅ 动态期号管理器集成正常")
        return True
        
    except Exception as e:
        print(f"❌ 动态期号管理器集成测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 统一预测器接口期号支持功能测试")
    print("=" * 60)
    
    tests = [
        ("统一接口方法测试", test_unified_interface_methods),
        ("方法签名测试", test_method_signatures),
        ("动态期号集成测试", test_dynamic_issue_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: 通过")
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！统一预测器接口期号支持功能正常")
        return True
    else:
        print("⚠️ 部分测试失败，需要检查问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
