#!/usr/bin/env python3
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.data.span_data_access import SpanDataAccess
import sqlite3

# 测试span_predictions表创建
db_path = 'data/fucai3d.db'
print(f"数据库路径: {db_path}")
print(f"数据库文件存在: {os.path.exists(db_path)}")

# 创建SpanDataAccess实例（会自动创建表）
span_access = SpanDataAccess(db_path)
print("✅ SpanDataAccess初始化成功")

# 检查表是否存在
conn = sqlite3.connect(db_path)
cursor = conn.cursor()
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='span_predictions'")
result = cursor.fetchone()
if result:
    print("✅ span_predictions表已存在")
    cursor.execute("SELECT COUNT(*) FROM span_predictions")
    count = cursor.fetchone()[0]
    print(f"✅ span_predictions表包含 {count} 条记录")
else:
    print("❌ span_predictions表不存在")

conn.close()
