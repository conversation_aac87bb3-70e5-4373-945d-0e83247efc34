{"learner": {"attributes": {}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "500"}, "iteration_indptr": [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130, 140, 150, 160, 170, 180, 190, 200, 210, 220, 230, 240, 250, 260, 270, 280, 290, 300, 310, 320, 330, 340, 350, 360, 370, 380, 390, 400, 410, 420, 430, 440, 450, 460, 470, 480, 490, 500], "tree_info": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "trees": [{"base_weights": [-0.0017494955, 0.3285151, -0.12109923, -0.0, 0.22968751, -0.19818334, -0.0152130015, -0.10331634, -0.034522448, 0.025817553, -0.046153855], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [2.2721164, 2.1089869, 0.3261279, 0.0, 0.0, 0.20871669, 0.29687926, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 2.0, 5.0, -0.0, 0.22968751, 5.0, 8.0, -0.10331634, -0.034522448, 0.025817553, -0.046153855], "split_indices": [0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.159996, 14.219999, 41.94, 8.82, 5.3999996, 23.22, 18.72, 6.8399997, 16.38, 10.62, 8.099999], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.032423228, -0.19602275, 0.20081966, -0.11416491, -0.11967093, -0.016741091, 0.21256684, 0.06916426, -0.070673086, 0.059579436, -0.060810823], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [2.274969, 0.4003687, 2.7763534, 0.0, 1.1402562, 0.7582702, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 6.0, -0.11416491, 3.0, 8.0, 0.21256684, 0.06916426, -0.070673086, 0.059579436, -0.060810823], "split_indices": [0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.6, 34.199997, 23.4, 8.46, 25.74, 16.92, 6.4799995, 5.9399996, 19.8, 7.5599995, 9.36], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.005183117, 0.073671944, -0.13972604, 0.017323496, 0.13593751, 0.02745297, -0.10331634], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [1.8619602, 1.0479679, 0.0, 1.2454238, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, -0.13972604, 7.0, 0.13593751, 0.02745297, -0.10331634], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.879997, 50.579998, 6.2999997, 45.179996, 5.3999996, 38.34, 6.8399997], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.1295251, -0.31114954, -0.013130273, -0.13014707, -0.034155603, 0.06535269, -0.091711976, -0.071986616, 0.035942487], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [1.236546, 0.45590472, 0.698526, 0.0, 0.0, 0.0, 0.9638982, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 6.0, 5.0, -0.13014707, -0.034155603, 0.06535269, 8.0, -0.071986616, 0.035942487], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.219997, 22.14, 37.079998, 12.599999, 9.54, 8.639999, 28.439999, 16.92, 11.5199995], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.014039994, 0.22819471, -0.09409307, -0.0695122, 0.51916933, 0.057534244, -0.1595745, 0.21884498, 0.06916426, -0.087160274, 0.025817553], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [1.3543792, 2.7879682, 0.743588, 0.0, 0.495795, 0.0, 1.0735408, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 2.0, -0.0695122, 7.0, 0.057534244, 8.0, 0.21884498, 0.06916426, -0.087160274, 0.025817553], "split_indices": [2, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.979996, 18.72, 37.26, 7.2, 11.5199995, 6.2999997, 30.96, 5.58, 5.9399996, 20.339998, 10.62], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.1264329, -0.079046436, 0.26588553, 0.11957204, 0.17426471, -0.015861034, 0.07062436], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [3.2958527, 0.0, 1.905895, 0.6655421, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.079046436, 7.0, 6.0, 0.17426471, -0.015861034, 0.07062436], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.319996, 14.94, 43.379997, 30.779999, 12.599999, 12.24, 18.539999], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.130597, 0.060767166, 0.18082191, 0.109122634, -0.06505103, -0.0, 0.066904545], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [1.840913, 0.7197807, 0.0, 0.56946266, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, 0.18082191, 5.0, -0.06505103, -0.0, 0.066904545], "split_indices": [2, 2, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.96, 51.659996, 6.2999997, 44.82, 6.8399997, 23.4, 21.419998], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.041594476, 0.29097962, -0.22321431, 0.3981623, 0.024532706, -0.0, -0.31318283, 0.06320225, 0.15561959, -5.0953823e-09, 0.0049668825, -0.13014707, -0.052489918], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [3.5433724, 0.38180923, 0.7960508, 0.1130023, 0.0, 0.0016556255, 0.32109928, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 2.0, 2.0, 0.024532706, 1.0, 6.0, 0.06320225, 0.15561959, -5.0953823e-09, 0.0049668825, -0.13014707, -0.052489918], "split_indices": [2, 1, 1, 2, 0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.699997, 19.619999, 37.079998, 12.059999, 7.5599995, 10.62, 26.46, 6.12, 5.9399996, 5.58, 5.04, 12.599999, 13.86], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.11159958, 0.30350438, -0.1064585, 0.15983605, 0.19976635, 0.096810915, -0.123924285, 0.12344721, -0.031150166, -0.03579953, 0.08251473], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [2.5268104, 1.4749296, 1.8279915, 1.6925242, 0.0, 0.7248907, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 8.0, 5.0, 4.0, 0.19976635, 2.0, -0.123924285, 0.12344721, -0.031150166, -0.03579953, 0.08251473], "split_indices": [0, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.14, 30.96, 27.179998, 23.4, 7.5599995, 16.56, 10.62, 11.879999, 11.5199995, 7.3799996, 9.179999], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.02528656, 0.24223033, -0.096253924, -0.03476822, 0.36223277, -0.11208793, -0.006591978, 0.18281251, 0.055069927, -0.093195274, 0.021969076], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [1.6020305, 1.0069249, 0.9155087, 0.0, 0.55243206, 0.0, 0.78286296, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 5.0, -0.03476822, 5.0, -0.11208793, 2.0, 0.18281251, 0.055069927, -0.093195274, 0.021969076], "split_indices": [0, 2, 0, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.319996, 20.88, 37.44, 5.04, 15.839999, 8.099999, 29.339998, 5.3999996, 10.44, 5.7599998, 23.58], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0036290274, 0.24386579, -0.10098629, -0.035011053, 0.41860563, 0.02414064, -0.09733608, 0.19107004, 0.03832369, -0.086343475, 0.033416003], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [1.436185, 1.1318009, 1.2532525, 0.0, 0.5824704, 0.79030454, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 6.0, -0.035011053, 5.0, 1.0, -0.09733608, 0.19107004, 0.03832369, -0.086343475, 0.033416003], "split_indices": [0, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.174973, 15.708901, 42.466072, 5.049432, 10.65947, 27.507929, 14.958142, 5.2132964, 5.4461727, 5.2590213, 22.248907], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.1407976, -0.23648453, 0.04702907, -0.17887469, -0.11530528, -0.060842104, 0.13491274, -0.106848784, -0.030849233], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [1.0993862, 0.21511841, 2.1845117, 0.3366993, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 8.0, 5.0, 2.0, -0.11530528, -0.060842104, 0.13491274, -0.106848784, -0.030849233], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.497322, 38.935345, 19.561975, 30.095793, 8.839551, 12.225124, 7.336852, 7.5335307, 22.562263], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.018813707, 0.05042803, -0.05859825, -0.0, 0.11850792, -0.102147646, 0.019356525], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.40143034, 0.91694117, 0.0, 1.0618212, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, -0.05859825, 1.0, 0.11850792, -0.102147646, 0.019356525], "split_indices": [0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.981476, 52.46709, 6.514385, 46.71967, 5.7474217, 6.6852865, 40.03438], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.07946824, -0.2328041, 0.008553332, -0.124008365, 0.0022101533, 0.22731484, -0.12845404, 0.029623156, 0.086819395, -0.06102847, 0.011171484], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.78365064, 0.9527736, 1.126704, 0.0, 0.0, 0.0492813, 0.3058307, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 6.0, -0.124008365, 0.0022101533, 4.0, 9.0, 0.029623156, 0.086819395, -0.06102847, 0.011171484], "split_indices": [0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.252644, 19.964756, 35.287888, 11.22285, 8.741906, 13.616687, 21.671202, 5.802096, 7.8145905, 15.178746, 6.4924555], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.06766541, -0.16398083, -0.009297116, -0.00046137406, -0.27063155, 0.08847197, -0.15574208, -0.041366026, -0.100095116, -0.03868652, 0.07724976, 0.022266053, -0.09107238], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.32258856, 0.35717052, 0.5864048, 0.0, 0.01584953, 0.9254862, 0.6096264, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 5.0, -0.00046137406, 5.0, 2.0, 6.0, -0.041366026, -0.100095116, -0.03868652, 0.07724976, 0.022266053, -0.09107238], "split_indices": [0, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.73552, 21.211336, 38.524185, 9.180421, 12.030915, 22.929253, 15.594931, 5.409993, 6.6209216, 9.873716, 13.055536, 6.032509, 9.562422], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.07800949, -0.2284159, 0.17261362, -0.111600794, 0.0023057838, 0.2521334, -0.02369522, 0.0029933208, 0.12899798], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [1.7869291, 0.5290609, 0.9780935, 0.0, 0.0, 1.5140076, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 6.0, 8.0, -0.111600794, 0.0023057838, 4.0, -0.02369522, 0.0029933208, 0.12899798], "split_indices": [0, 2, 2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.473804, 13.447029, 46.026775, 8.267217, 5.179812, 35.27357, 10.753207, 15.622258, 19.651312], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.10131867, -0.09320707, 0.1506231, 0.15384099, 0.060920175, -0.04232189, 0.06694754], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [1.2783163, 0.0, 1.7222744, 0.0, 1.5481566, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, -0.09320707, 3.0, 0.15384099, 5.0, -0.04232189, 0.06694754], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.412525, 5.673409, 54.739117, 9.708614, 45.030502, 19.841333, 25.189169], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.009586811, 0.12049846, -0.20258051, 0.12925902, -0.0037133389, -0.25544924, -0.02710143, 0.04596639, -0.057332773, -0.04762312, -0.098291084], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [1.5362519, 1.4357975, 0.08949661, 0.0, 0.81356263, 0.008984804, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 6.0, 0.12925902, 3.0, 3.0, -0.02710143, 0.04596639, -0.057332773, -0.04762312, -0.098291084], "split_indices": [2, 1, 1, 0, 2, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.892246, 35.0946, 23.797646, 9.555303, 25.539299, 14.704631, 9.093015, 13.70815, 11.831149, 8.244408, 6.4602227], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.033678334, 0.176524, -0.11905037, 0.1443254, -0.065644845, -0.2856118, -0.002374725, -0.032615412, -0.14115718, -0.027521139, 0.025854], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [1.0654236, 2.1743953, 0.79747593, 0.0, 0.0, 0.45246732, 0.21725903, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 5.0, 6.0, 0.1443254, -0.065644845, 6.0, 5.0, -0.032615412, -0.14115718, -0.027521139, 0.025854], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.51949, 15.966891, 41.552597, 9.039293, 6.927598, 16.167515, 25.385084, 9.350867, 6.8166475, 13.179094, 12.205989], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.026087595, -0.22927505, 0.08922441, -0.03351012, -0.08682363, -0.032447174, 0.37255606, 0.0771999, -0.036318284, 0.040191837, 0.16735762], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.9328547, 0.007858038, 1.6698935, 0.0, 0.0, 0.880968, 0.49614024, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 4.0, 7.0, -0.03351012, -0.08682363, 1.0, 6.0, 0.0771999, -0.036318284, 0.040191837, 0.16735762], "split_indices": [2, 1, 1, 0, 0, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.459232, 10.356433, 46.1028, 5.0342364, 5.3221965, 32.625088, 13.47771, 6.9301133, 25.694975, 6.880933, 6.5967774], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.009800665, 0.049776234, -0.09046182, -0.062057536, 0.09218529, 0.07399096, 0.006681355], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.72551364, 0.59016776, 0.0, 0.0, 0.4747269, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 1.0, -0.09046182, -0.062057536, 3.0, 0.07399096, 0.006681355], "split_indices": [2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.10981, 52.526768, 5.583039, 6.564797, 45.96197, 13.088776, 32.873196], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.08183575, -0.17561714, 0.13635321, -0.25420418, -0.05627212, 0.0149281565, 0.07154112, -0.023112226, -0.10680964, -0.046410337, 0.010823367], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [1.1391965, 0.3180127, 0.10582307, 0.3364421, 0.17353445, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 7.0, 3.0, 4.0, 0.0149281565, 0.07154112, -0.023112226, -0.10680964, -0.046410337, 0.010823367], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.311447, 37.7425, 15.568947, 21.570635, 16.171864, 9.75961, 5.8093367, 8.8725395, 12.698095, 8.025625, 8.146239], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.04124536, -0.09460878, -0.0, 0.08987517, -0.045113314, 0.0064158253, -0.0622601], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.60181975, 0.0, 0.66230965, 0.0, 0.500923, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, -0.09460878, 2.0, 0.08987517, 7.0, 0.0064158253, -0.0622601], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.622578, 6.002703, 48.619877, 5.376169, 43.243706, 30.861, 12.382707], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0905022, -0.23751639, 0.020785311, 0.0070622573, -0.1033078, -0.06657436, 0.10494511, 0.016096584, -0.13447598], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.9348186, 0.7203156, 0.9750725, 0.0, 0.0, 1.2560127, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 1.0, 8.0, 0.0070622573, -0.1033078, 6.0, 0.10494511, 0.016096584, -0.13447598], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.51112, 23.3842, 31.126923, 6.745684, 16.638514, 24.986593, 6.14033, 19.455494, 5.5310984], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.033112254, -0.12374771, 0.047606084, 0.02681956, -0.18503833, 0.12609841, -0.060746714, -0.019926175, -0.08083624, 0.02120141, -0.08516474], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.45646644, 0.4099617, 1.3604645, 0.0, 0.18393552, 0.0, 0.791664, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 6.0, 0.02681956, 5.0, 0.12609841, 5.0, -0.019926175, -0.08083624, 0.02120141, -0.08516474], "split_indices": [0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.54737, 28.383003, 31.164366, 5.8720393, 22.510965, 6.5785437, 24.585821, 10.594887, 11.916078, 15.6208725, 8.964949], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.07952259, -0.14188342, 0.1452297, 0.004844363, -0.07413675, 0.0017904006, 0.07722491, -0.02373837, 0.02035492], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.89582497, 0.2585988, 0.73344344, 0.0, 0.0, 0.12248472, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 4.0, 4.0, 0.004844363, -0.07413675, 6.0, 0.07722491, -0.02373837, 0.02035492], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.13801, 12.920383, 46.217625, 5.1233277, 7.7970552, 21.120619, 25.097004, 8.723405, 12.397214], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.13668726, -0.061235424, 0.20168969, 0.17719927, 0.12582812, -0.019158203, 0.07857204], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [1.4225103, 0.0, 1.4216483, 0.0, 1.222378, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.061235424, 3.0, 0.17719927, 5.0, -0.019158203, 0.07857204], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.365086, 9.240248, 52.12484, 7.184189, 44.94065, 18.687204, 26.253448], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.07256856, 0.10130947, -0.16027251, -0.033464685, -0.10713714, -0.054731518, 0.008032194], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [2.1704736, 0.0, 1.1906915, 0.30462453, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.10130947, 6.0, 4.0, -0.10713714, -0.054731518, 0.008032194], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.30997, 9.566869, 48.743103, 30.666395, 18.076706, 8.723685, 21.94271], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.116448976, 0.18628633, -0.044101264, 0.12720113, 0.13772677, -0.18651445, 0.059492055, -0.023413457, 0.0687753, -0.090640634, -0.010483959], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.7120173, 0.38738883, 0.6923343, 0.0, 0.78223795, 0.17925394, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [7.0, 1.0, 9.0, 0.12720113, 3.0, 6.0, 0.059492055, -0.023413457, 0.0687753, -0.090640634, -0.010483959], "split_indices": [0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.501816, 42.484253, 18.017563, 5.5455837, 36.938667, 11.754799, 6.262764, 10.720167, 26.218502, 5.730339, 6.02446], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.014863024, -0.2624106, 0.1196595, -0.0, -0.11801805, -0.044845212, 0.17022441, -0.039815918, 0.026080484], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [1.7181088, 0.5170257, 3.276743, 0.0, 0.0, 0.39276272, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 3.0, 7.0, -0.0, -0.11801805, 4.0, 0.17022441, -0.039815918, 0.026080484], "split_indices": [2, 1, 1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.53896, 15.107871, 42.431087, 5.599862, 9.508009, 31.525255, 10.905832, 19.36432, 12.160935], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.012644133, 0.14966513, -0.12934105, -0.08620892, 0.28630447, -0.1392513, -0.02810066, 0.10818991, 0.02672408, 0.047633268, -0.067541115], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [1.118247, 1.5401728, 1.1142652, 0.0, 0.20547295, 0.0, 1.0713168, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 1.0, 5.0, -0.08620892, 3.0, -0.1392513, 4.0, 0.10818991, 0.02672408, 0.047633268, -0.067541115], "split_indices": [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.12376, 23.415964, 33.707794, 5.1402917, 18.275673, 6.7641835, 26.943611, 12.303306, 5.9723663, 13.667442, 13.276169], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.08387427, -0.29788086, 0.16871662, -0.13883199, -0.0, 0.1229953, 0.033724837, -0.024049945, 0.050145634], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [3.016057, 1.4350045, 0.7716195, 0.0, 0.0, 0.0, 0.29096478, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [6.0, 6.0, 3.0, -0.13883199, -0.0, 0.1229953, 7.0, -0.024049945, 0.050145634], "split_indices": [2, 1, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.62625, 29.147635, 24.478617, 18.191782, 10.9558525, 7.785637, 16.69298, 8.816804, 7.8761764], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.02227302, -0.15550685, 0.16410367, -0.037967224, -0.09355484, 0.15733457, 0.033636615, -0.044311307, 0.04539064, -0.058191136, 0.036108453], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [1.5290327, 0.44284225, 1.5225632, 0.36914578, 0.0, 0.0, 0.53308207, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 2.0, 7.0, -0.09355484, 0.15733457, 4.0, -0.044311307, 0.04539064, -0.058191136, 0.036108453], "split_indices": [2, 2, 1, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.59451, 25.64628, 32.94823, 15.714476, 9.931805, 7.751803, 25.196426, 10.420623, 5.293853, 6.29243, 18.903997], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.022318307, 0.15372239, -0.23578419, -0.013146824, 0.26913714, -0.30911732, 0.006519599, 0.050946485, -0.0544967, -0.0, 0.14225179, -0.10603798, -0.04764536], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.009578, 0.58780164, 0.4940833, 0.41617438, 1.0122279, 0.02107048, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 9.0, 3.0, 3.0, 7.0, 0.006519599, 0.050946485, -0.0544967, -0.0, 0.14225179, -0.10603798, -0.04764536], "split_indices": [2, 1, 2, 2, 2, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.293648, 28.00771, 23.285938, 11.459279, 16.54843, 18.27087, 5.015068, 5.2056766, 6.2536025, 7.3929944, 9.155436, 12.550942, 5.719928], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.037237663, 0.14819914, -0.032341495, 0.06798189, 0.004182803, -0.08284474, 0.054197762, 0.011964356, -0.067451455, 0.023634456, 0.00054621295], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.4388975, 0.20252076, 0.15552387, 0.0, 0.0, 0.42013806, 0.007722281, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [4.0, 5.0, 6.0, 0.06798189, 0.004182803, 7.0, 8.0, 0.011964356, -0.067451455, 0.023634456, 0.00054621295], "split_indices": [2, 1, 1, 0, 0, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.72611, 20.824106, 32.902004, 12.241967, 8.582138, 21.533535, 11.368468, 11.558403, 9.975133, 6.0945625, 5.273906], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.037209496, -0.20518717, 0.105004475, 0.004710118, -0.10542533, 0.14956, -0.03729989, 0.009836096, 0.078888714], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [1.0377939, 0.48048258, 0.5298367, 0.0, 0.0, 0.52467585, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 4.0, 9.0, 0.004710118, -0.10542533, 6.0, -0.03729989, 0.009836096, 0.078888714], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.450512, 12.665215, 48.785297, 5.127973, 7.537242, 41.505527, 7.279769, 21.518436, 19.987091], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.11128696, -0.04584944, 0.16748731, 0.019905008, -0.048813306, 0.14947768, 0.112781, -0.037477497, 0.05416384, 0.011671832, 0.10882839], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.57593894, 0.14741905, 0.73435986, 0.28779247, 0.0, 0.0, 0.72019595, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [3.0, 2.0, 4.0, 1.0, -0.048813306, 0.14947768, 8.0, -0.037477497, 0.05416384, 0.011671832, 0.10882839], "split_indices": [2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.970036, 15.904847, 46.06519, 10.161332, 5.743515, 5.1274104, 40.93778, 5.1331983, 5.0281334, 32.80023, 8.13755], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.061398253, 0.19052772, -0.066108465, 0.006430522, -0.09331094, -0.0276606, 0.065498486], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [4.1864285, 0.0, 0.8582822, 0.7961207, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.19052772, 8.0, 6.0, -0.09331094, -0.0276606, 0.065498486], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.87906, 9.397968, 45.48109, 35.525238, 9.955854, 24.304922, 11.220317], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.06880529, -0.12713169, 0.13611774, -0.0049356725, -0.065686926, 0.29041544, 0.06581494, 0.1353545, 0.03459786, 0.04671923, -0.066564575], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.80347717, 0.12897737, 0.43534046, 0.0, 0.0, 0.26859534, 0.8757882, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 1.0, 4.0, -0.0049356725, -0.065686926, 4.0, 8.0, 0.1353545, 0.03459786, 0.04671923, -0.066564575], "split_indices": [1, 1, 1, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.540062, 14.361497, 44.178566, 7.5406723, 6.8208246, 12.403197, 31.775368, 5.346269, 7.056928, 24.823423, 6.951945], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.048820253, -0.30856588, 0.04369522, -0.014395295, -0.14198357, -0.046458784, 0.11829975, 0.05786576, -0.021124013], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [1.3911852, 0.5765159, 0.6376174, 0.0, 0.0, 0.0, 0.46922004, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 3.0, 2.0, -0.014395295, -0.14198357, -0.046458784, 8.0, 0.05786576, -0.021124013], "split_indices": [2, 1, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.2292, 14.175579, 41.05362, 6.225323, 7.9502554, 10.552434, 30.501184, 22.224909, 8.276277], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.059869073, 0.08877249, -0.18469813, -0.037684854, 0.15193486, -0.3681997, -0.017991377, -0.0, 0.05952151, -0.14244995, -0.05088297, 0.05968055, -0.047483515], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [1.0921273, 0.38107038, 0.9312619, 0.0, 0.14514661, 0.16966438, 0.5771151, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 1.0, 7.0, -0.037684854, 2.0, 5.0, 8.0, -0.0, 0.05952151, -0.14244995, -0.05088297, 0.05968055, -0.047483515], "split_indices": [0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.45895, 25.537567, 30.921383, 5.2262797, 20.311287, 13.858353, 17.06303, 5.1266594, 15.184628, 7.7840867, 6.0742664, 6.256966, 10.806064], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.031890333, -0.13338217, 0.13701364, -0.06015419, -0.07407776, -0.0, 0.07680081, -0.08109129, 0.003962368], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.89467835, 0.22736621, 0.31033865, 0.3655696, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 6.0, 3.0, -0.07407776, -0.0, 0.07680081, -0.08109129, 0.003962368], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.14615, 31.825218, 18.32093, 20.812849, 11.012368, 9.051289, 9.269642, 5.043634, 15.769216], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.020287767, -0.25381595, 0.15029268, -0.32550573, -0.1751308, 0.2491425, -0.06364047, -0.036022853, -0.13667172, 0.00042218078, -0.08387365, 0.12243531, 0.044903416], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.3994708, 0.036951065, 1.2920371, 0.19580662, 0.30482423, 0.3222804, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 8.0, 5.0, 3.0, 2.0, -0.06364047, -0.036022853, -0.13667172, 0.00042218078, -0.08387365, 0.12243531, 0.044903416], "split_indices": [2, 2, 1, 1, 2, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.01724, 24.531452, 33.48579, 10.529947, 14.001505, 26.772303, 6.713486, 5.125953, 5.4039936, 5.215455, 8.78605, 8.722498, 18.049805], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.09138461, 0.05234788, -0.13410352, -0.2695648, 0.0009460019, -0.13634165, -0.0501122, 0.06295927, -0.057753023], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.6277908, 0.0, 0.8877101, 0.3142314, 1.0195625, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.05234788, 6.0, 4.0, 8.0, -0.13634165, -0.0501122, 0.06295927, -0.057753023], "split_indices": [1, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.568382, 6.5259657, 46.042416, 22.858572, 23.183842, 6.5810733, 16.2775, 11.354222, 11.82962], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.008824309, 0.13687402, -0.103655666, 0.076955244, -0.0036547354, -0.12537359, 4.7679896e-05, -0.016818978, -0.06770668], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.78579646, 0.42217723, 0.09135994, 0.0, 0.0, 0.16573033, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 5.0, 9.0, 0.076955244, -0.0036547354, 7.0, 4.7679896e-05, -0.016818978, -0.06770668], "split_indices": [2, 1, 2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.086227, 21.127783, 33.958443, 11.6924925, 9.43529, 28.708286, 5.2501574, 18.489302, 10.218985], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.012566886, -0.2296494, 0.10195929, -0.063897155, -0.13818109, -0.08285061, 0.26007673, 0.0018697792, -0.041656688, -0.0, -0.08929051, 0.1889446, 0.039443925], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [1.3801408, 0.5710493, 1.4183352, 0.073755264, 0.0, 0.4114914, 1.0756992, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 2.0, 4.0, 4.0, -0.13818109, 8.0, 5.0, 0.0018697792, -0.041656688, -0.0, -0.08929051, 0.1889446, 0.039443925], "split_indices": [2, 2, 1, 1, 0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.249413, 16.056177, 46.193233, 10.342895, 5.7132816, 21.22672, 24.966515, 5.128007, 5.214888, 15.859385, 5.367336, 5.2362313, 19.730284], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.07659785, -0.0024444095, 0.16753106, -0.07353372, 0.070241615, 0.12838031, 0.024608232, -0.039406046, 0.046858612, -0.062246244, 0.044293843], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.48134583, 0.6514396, 1.0849435, 0.0, 0.49719837, 0.0, 0.6271364, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 4.0, -0.07353372, 1.0, 0.12838031, 6.0, -0.039406046, 0.046858612, -0.062246244, 0.044293843], "split_indices": [1, 2, 2, 0, 1, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.615425, 33.96516, 29.650265, 7.598971, 26.366188, 9.552291, 20.097975, 7.3756967, 18.990492, 6.4436684, 13.654306], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.033839304, 0.1906274, -0.2600982, 0.26032913, -0.014988109, -0.0072026243, -0.33485425, 0.0111210365, 0.122304805, -0.036737587, -0.11595106], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [3.0322466, 0.533224, 0.4824419, 0.71561754, 0.0, 0.0, 0.14588618, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 8.0, 2.0, 2.0, -0.014988109, -0.0072026243, 6.0, 0.0111210365, 0.122304805, -0.036737587, -0.11595106], "split_indices": [2, 0, 0, 2, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.591434, 28.767405, 28.824028, 22.56391, 6.203496, 7.5713987, 21.25263, 9.738729, 12.82518, 5.1952925, 16.057337], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.08922352, 0.1822889, -0.063673876, 0.06289496, 0.26824334, -0.0, -0.061188594, 0.049177602, -0.04254108, 0.039633542, 0.16432218, -0.055786517, 0.03926988], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.9074056, 0.35313535, 0.22590466, 0.40258157, 0.69845223, 0.4367275, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 6.0, 7.0, 8.0, 2.0, -0.061188594, 0.049177602, -0.04254108, 0.039633542, 0.16432218, -0.055786517, 0.03926988], "split_indices": [0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.120235, 38.28113, 22.839104, 17.2355, 21.04563, 16.085419, 6.7536855, 12.000354, 5.235147, 15.38345, 5.66218, 6.2709846, 9.814434], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.01785934, 0.22742853, -0.11639261, 0.12438654, 0.11492746, -0.14461702, -0.0, 0.05663488, 0.017505089, -0.050492078, 0.023612803], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [1.7678845, 0.2968756, 1.6306227, 0.03526771, 0.0, 0.0, 0.40219247, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 5.0, 1.0, 0.11492746, -0.14461702, 4.0, 0.05663488, 0.017505089, -0.050492078, 0.023612803], "split_indices": [0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.500103, 23.643341, 36.856762, 15.695265, 7.9480767, 8.2240715, 28.632692, 6.238544, 9.45672, 8.597753, 20.034939], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.07610092, 0.11337375, -0.19353424, -0.053530045, 0.09815503, -0.006234497, -0.0760854], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [1.2613634, 1.4134595, 0.32914197, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [3.0, 4.0, 3.0, -0.053530045, 0.09815503, -0.006234497, -0.0760854], "split_indices": [1, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.342476, 20.480656, 33.86182, 8.532821, 11.947834, 9.514887, 24.346935], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.053383984, -0.08838828, -0.01605857, -0.1671137, 0.031783387, 0.022239009, -0.13217296, 0.03748726, -0.026142197], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.44483966, 0.0, 0.36314362, 0.869023, 0.41933498, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.08838828, 2.0, 1.0, 7.0, 0.022239009, -0.13217296, 0.03748726, -0.026142197], "split_indices": [0, 0, 1, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.377605, 5.7354326, 46.642174, 11.122239, 35.51993, 6.1022177, 5.0200214, 20.359158, 15.160776], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.046031013, -0.12443266, 0.15373394, -0.14517407, 0.13929273, 0.2126935, -0.040235765, 0.10961846, -0.022436567, 0.10133185, -0.0008488214], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [1.0699973, 2.215386, 0.6437238, 0.0, 0.708658, 0.85333276, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [4.0, 5.0, 9.0, -0.14517407, 7.0, 7.0, -0.040235765, 0.10961846, -0.022436567, 0.10133185, -0.0008488214], "split_indices": [0, 2, 0, 0, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.078968, 21.293495, 34.785473, 8.818022, 12.475473, 29.376858, 5.408614, 5.9508185, 6.5246544, 18.523472, 10.853385], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.06749893, -0.10356352, 0.043091554, -0.0, -0.09246781, -0.028182572, 0.094683446], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.41499946, 0.96729624, 0.0, 0.96526325, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 6.0, 0.043091554, 8.0, -0.09246781, -0.028182572, 0.094683446], "split_indices": [0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.073307, 45.315964, 6.7573423, 30.905403, 14.410561, 24.471725, 6.4336767], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.037380934, 0.08314319, -0.086313054, -0.115508996, 0.031151233, -0.046969183, 0.041812442], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.883388, 0.0, 0.2931674, 0.49079686, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, 0.08314319, 9.0, 9.0, 0.031151233, -0.046969183, 0.041812442], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.08439, 6.647598, 49.43679, 43.652733, 5.784057, 38.267025, 5.3857117], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.010932017, -0.07410319, 0.07410518, 0.13721864, -0.046088383, 0.058258884, -0.051541135], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [1.0313128, 0.0, 0.76496184, 0.7660608, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.07410319, 8.0, 9.0, -0.046088383, 0.058258884, -0.051541135], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.227695, 11.358154, 50.86954, 40.48593, 10.383613, 34.770176, 5.7157545], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.007177581, 0.20728752, -0.06633974, -0.045866825, 0.38400996, -0.116504334, 0.05015668, 0.17785011, 0.04350031, -0.011541459, -0.05714615], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [1.014197, 1.2671146, 0.59341043, 0.0, 0.48726118, 0.21009022, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 9.0, -0.045866825, 4.0, 6.0, 0.05015668, 0.17785011, 0.04350031, -0.011541459, -0.05714615], "split_indices": [0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.046036, 17.621628, 48.42441, 5.6583595, 11.963267, 40.62667, 7.797742, 5.371432, 6.5918355, 21.169731, 19.456938], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.030611005, 0.43540728, -0.07368885, 0.24216042, -0.0, 0.11199713, -0.17461503, 0.012239811, 0.044115152, -0.01977655, -0.13672358], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [2.3240273, 1.8052278, 0.8343141, 0.0, 0.0, 0.013779238, 0.79445344, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 5.0, 3.0, 0.24216042, -0.0, 5.0, 8.0, 0.012239811, 0.044115152, -0.01977655, -0.13672358], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.428722, 10.287252, 42.141468, 5.0546603, 5.232592, 14.451957, 27.689512, 6.422921, 8.029036, 21.115744, 6.57377], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.018096356, 0.09070451, -0.06249124, 0.04403524, -0.20505542, 0.07138772, -0.00876624, -0.10621546, -0.00356446], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.8708451, 0.0, 0.86318755, 0.48249435, 0.65509117, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.09070451, 5.0, 4.0, 8.0, 0.07138772, -0.00876624, -0.10621546, -0.00356446], "split_indices": [0, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.572487, 6.389535, 54.182953, 31.005085, 23.177868, 8.258882, 22.746204, 12.322767, 10.855101], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.024939625, -0.09754376, 0.12346324, 0.05877971, -0.17340457, 0.12788063, -0.0531804, -0.08936904, -0.0042916364, 0.010966325, -0.048237767], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.6474145, 0.939387, 1.0998048, 0.0, 0.6220335, 0.0, 0.14343408, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [7.0, 1.0, 8.0, 0.05877971, 4.0, 0.12788063, 9.0, -0.08936904, -0.0042916364, 0.010966325, -0.048237767], "split_indices": [1, 1, 1, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.209587, 39.76946, 18.44013, 7.5726843, 32.196774, 6.481827, 11.9583025, 17.22084, 14.975935, 6.374136, 5.5841665], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.016901307, 0.22855687, -0.14138408, 0.13968614, -0.0044501624, -0.0, -0.22668977, -0.05929974, 0.051340025, -0.0817551, -0.027049705], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [1.7563862, 1.1594844, 0.46810192, 0.0, 0.0, 0.5551702, 0.0898875, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 5.0, 3.0, 0.13968614, -0.0044501624, 7.0, 8.0, -0.05929974, 0.051340025, -0.0817551, -0.027049705], "split_indices": [0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.709915, 18.194391, 37.515522, 8.962593, 9.231798, 14.42429, 23.091234, 6.578623, 7.8456674, 15.993943, 7.0972905], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.039938886, -0.08950716, -0.0, 0.1052887, -0.051590674, -0.05539993, 0.030499727], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.5309999, 0.0, 0.8612612, 0.0, 0.90207154, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.08950716, 3.0, 0.1052887, 5.0, -0.05539993, 0.030499727], "split_indices": [0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.899258, 5.9309826, 46.968277, 5.065136, 41.90314, 22.667784, 19.235357], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0042278725, -0.19870311, 0.122604296, -0.2325163, -0.024992373, 0.23976135, -0.08149932, -0.10570296, -0.031866945, 0.0137680825, 0.09004967, 0.0613884, -0.103322215], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [1.5275202, 0.04513794, 0.92025745, 0.18635291, 0.0, 0.23355365, 1.1323632, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 6.0, 2.0, -0.024992373, 1.0, 7.0, -0.10570296, -0.031866945, 0.0137680825, 0.09004967, 0.0613884, -0.103322215], "split_indices": [2, 1, 1, 2, 0, 1, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.632683, 23.626818, 36.005867, 16.817421, 6.8093967, 23.07556, 12.930306, 7.304872, 9.512548, 6.2931, 16.78246, 6.115748, 6.814559], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0009887327, 0.0778544, -0.09155302, 0.09382024, 0.0211301, -0.048378218, 0.0477602], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.2542492, 0.49998194, 0.0, 0.0, 0.88319457, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 1.0, -0.09155302, 0.09382024, 4.0, -0.048378218, 0.0477602], "split_indices": [1, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.35863, 39.528473, 9.830156, 6.468414, 33.06006, 13.859488, 19.200573], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.02298364, -0.013302924, 0.07833242, 0.04909702, -0.14748466, -0.02826077, 0.03803511, -0.06944598, -0.014463409], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.5123546, 0.4290627, 0.0, 0.39178014, 0.09744072, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 6.0, 0.07833242, 3.0, 5.0, -0.02826077, 0.03803511, -0.06944598, -0.014463409], "split_indices": [0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.025524, 48.20413, 6.821396, 32.827057, 15.377069, 11.012943, 21.814114, 7.06216, 8.314909], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04581748, -0.02214487, 0.27781454, 0.12816486, -0.089873165, -0.05601615, 0.1862321, 0.0010122468, 0.0659906, -0.08378219, -0.0042141555], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [1.0202533, 0.5073729, 2.487311, 0.15106148, 0.46772435, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 2.0, 5.0, 1.0, 3.0, -0.05601615, 0.1862321, 0.0010122468, 0.0659906, -0.08378219, -0.0042141555], "split_indices": [1, 1, 2, 1, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.696136, 48.08375, 13.612383, 14.200247, 33.883507, 5.851101, 7.761282, 6.91991, 7.2803373, 8.542886, 25.34062], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.07663332, 0.024199717, 0.1291459, 0.07666437, -0.08107522, -0.05867923, 0.036211602], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 66, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [1.1928401, 0.92706335, 0.0, 0.6387709, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, 0.1291459, 1.0, -0.08107522, -0.05867923, 0.036211602], "split_indices": [2, 2, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.30304, 58.922894, 7.3801465, 50.927902, 7.994992, 6.2957315, 44.63217], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.021456527, 0.13743728, -0.18453133, 0.11970373, 0.04010593, -0.09463816, 0.01794801, 0.031390995, -0.03824138], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 67, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [1.4985727, 0.6775774, 0.9628533, 0.0, 0.25438935, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 6.0, 0.11970373, 8.0, -0.09463816, 0.01794801, 0.031390995, -0.03824138], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.67358, 27.967308, 27.706272, 6.4652357, 21.502073, 18.117184, 9.589088, 16.226063, 5.2760105], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.06501676, -0.11596684, 0.13286221, -0.0, -0.088471, 0.13517049, 0.07867499, -0.0036770122, 0.00092707423, -0.105202265, 0.053520907], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 68, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.78466827, 0.33819222, 0.70931226, 0.0010450379, 0.0, 0.0, 1.8036244, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [2.0, 6.0, 1.0, 1.0, -0.088471, 0.13517049, 3.0, -0.0036770122, 0.00092707423, -0.105202265, 0.053520907], "split_indices": [1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.511322, 16.176334, 45.334988, 10.63157, 5.544764, 5.2708564, 40.064133, 5.57115, 5.06042, 6.888542, 33.17559], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.050077412, -0.11646592, 0.07676475, -0.03229194, -0.20481828, -0.025459837, 0.06329307, -0.048150174, 0.050133493, -0.022828046, -0.13552861], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 69, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5187908, 0.26975304, 0.48010254, 0.59959304, 0.5142424, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 6.0, 4.0, 5.0, -0.025459837, 0.06329307, -0.048150174, 0.050133493, -0.022828046, -0.13552861], "split_indices": [1, 2, 2, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.074635, 39.349503, 19.725134, 21.430151, 17.91935, 8.777286, 10.947848, 13.467605, 7.962546, 12.905283, 5.0140676], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.002715759, -0.11044258, 0.2185587, 0.013400435, -0.10167847, 0.3352049, -0.0015049502, -0.058107026, 0.04484404, 0.05251306, 0.12527664], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 70, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [1.2793953, 1.0752952, 0.48907554, 0.714168, 0.0, 0.02278483, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 7.0, 2.0, -0.10167847, 4.0, -0.0015049502, -0.058107026, 0.04484404, 0.05251306, 0.12527664], "split_indices": [0, 0, 2, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.11317, 35.690273, 16.422897, 23.471834, 12.218437, 10.758061, 5.664836, 8.848173, 14.623661, 5.127777, 5.630284], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0121613955, 0.05079689, -0.06244512, 0.23928443, -0.033561293, 0.11811168, 0.01003387, -0.058796607, 0.055502143], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 71, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.4384708, 0.7526003, 0.0, 0.40715247, 1.1721878, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 3.0, -0.06244512, 5.0, 6.0, 0.11811168, 0.01003387, -0.058796607, 0.055502143], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.119404, 44.53309, 6.586315, 13.5673895, 30.965698, 6.952823, 6.614566, 18.086035, 12.879663], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.010333929, -0.13452245, 0.06561889, -0.05999285, -0.07245414, 0.13903186, -0.03567688, -0.060749445, 0.05493579, -0.0, 0.05221167], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 72, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.57523084, 0.1414609, 0.5195335, 0.58077365, 0.0, 0.13925004, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 7.0, 2.0, -0.07245414, 6.0, -0.03567688, -0.060749445, 0.05493579, -0.0, 0.05221167], "split_indices": [2, 2, 1, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.317802, 22.338371, 35.97943, 14.637411, 7.700961, 26.30391, 9.67552, 9.592788, 5.044623, 5.282086, 21.021824], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.06186959, 0.029025555, -0.19151083, -0.21026033, 0.09525397, -0.032011993, -0.08161038, -0.03872799, -0.089614294], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 73, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.6153945, 2.1502569, 0.078869104, 0.039453685, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 6.0, 6.0, 0.09525397, -0.032011993, -0.08161038, -0.03872799, -0.089614294], "split_indices": [0, 0, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.37333, 29.02666, 20.346666, 15.825084, 13.201577, 11.647277, 8.699389, 10.124565, 5.7005186], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.022250844, 0.07150861, -0.09106471, -0.09356442, 0.23486556, -0.022111366, -0.09475554, 0.09719951, 0.04257385, -0.04734259, 0.013659905], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 74, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.35393667, 1.481567, 0.45421338, 0.0, 0.04502517, 0.25573605, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 8.0, -0.09356442, 6.0, 6.0, -0.09475554, 0.09719951, 0.04257385, -0.04734259, 0.013659905], "split_indices": [2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.544918, 21.609474, 30.935446, 6.001923, 15.607552, 24.854553, 6.080892, 6.1494126, 9.458138, 8.316578, 16.537975], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.052990302, -0.032717425, 0.08556395, -0.0, 0.14572538, 0.042699065, -0.03904779, 0.0663207, 0.0317839], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 75, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.34985167, 0.0, 0.2811758, 0.46154857, 0.04055363, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.032717425, 4.0, 7.0, 6.0, 0.042699065, -0.03904779, 0.0663207, 0.0317839], "split_indices": [0, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.9558, 9.797396, 54.158405, 22.901653, 31.256752, 10.96422, 11.937432, 8.356949, 22.899803], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.013460525, -0.019697635, 0.07379919, 0.0784701, -0.096112184, -0.0023032713, 0.03162876, -0.05065818, 0.0033334275], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 76, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.5623112, 0.4688264, 0.0, 0.07280369, 0.29430974, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 4.0, 0.07379919, 2.0, 7.0, -0.0023032713, 0.03162876, -0.05065818, 0.0033334275], "split_indices": [0, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.364655, 60.257584, 8.107067, 25.790134, 34.467453, 5.4334507, 20.356684, 20.748154, 13.719297], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.001640668, 0.19161972, -0.22045739, 0.043136097, 0.2838464, -0.0234665, -0.10954574, 0.050872143, -0.038858894, 0.11158299, 0.032358542, -0.0, -0.013681098], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 77, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.4618187, 0.3894546, 0.7133608, 0.3261187, 0.20605063, 0.008121967, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 4.0, 1.0, 5.0, 7.0, -0.10954574, 0.050872143, -0.038858894, 0.11158299, 0.032358542, -0.0, -0.013681098], "split_indices": [2, 2, 1, 2, 1, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.41242, 30.721354, 25.691065, 12.748482, 17.972872, 11.664313, 14.0267515, 7.7040834, 5.0443983, 10.85646, 7.116412, 5.4055963, 6.258717], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.026867336, -0.07229325, 0.124902345, -0.12507513, 0.033207107, 0.10631033, -0.04759378, -0.018390039, -0.10336074], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 78, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.42082286, 0.47596282, 0.97333026, 0.46893668, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [8.0, 8.0, 5.0, 6.0, 0.033207107, 0.10631033, -0.04759378, -0.018390039, -0.10336074], "split_indices": [2, 1, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.4928, 46.6982, 12.7946, 36.913433, 9.784767, 7.1285954, 5.6660037, 29.946114, 6.967321], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0008769362, 0.16761243, -0.11657563, -0.0, 0.1247733, -0.12209995, 0.017717345, -0.03450808, 0.020990595, 0.031195054, -0.017443929], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 79, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [1.1541021, 0.98123205, 1.4347193, 0.13001087, 0.0, 0.0, 0.17468879, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 6.0, 2.0, 0.1247733, -0.12209995, 8.0, -0.03450808, 0.020990595, 0.031195054, -0.017443929], "split_indices": [0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.462414, 22.859552, 34.60286, 14.312079, 8.547474, 10.534573, 24.068287, 5.016769, 9.29531, 11.705373, 12.362914], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.015708672, -0.09951677, 0.07345297, 0.005320682, -0.15186973, 0.041377407, -0.00529192, -0.094125934, -0.021253053, -0.0, -0.0040107034], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 80, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.41655585, 0.19445601, 0.1478397, 0.0, 0.21188027, 0.0, 0.00086803146, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 8.0, 0.005320682, 5.0, 0.041377407, 6.0, -0.094125934, -0.021253053, -0.0, -0.0040107034], "split_indices": [0, 2, 0, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.418396, 28.054136, 25.36426, 8.274117, 19.78002, 14.231073, 11.133185, 5.2281733, 14.551846, 5.3754435, 5.757742], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.07095333, -0.026426287, -0.09725, -0.14698426, 0.14073831, -0.06823007, -0.010803618, 0.021621335, 0.05149438], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 81, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.5328412, 0.92632186, 0.0, 0.20586216, 0.0035410821, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 6.0, -0.09725, 6.0, 6.0, -0.06823007, -0.010803618, 0.021621335, 0.05149438], "split_indices": [0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.12862, 43.90696, 6.2216606, 25.9273, 17.979658, 13.899945, 12.027355, 7.6194954, 10.360164], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.022283098, -0.037126914, 0.11123608, 0.04744668, -0.122432254, 0.103604734, 0.020669704, -0.04196507, 0.06996795, 0.026665747, -0.07489024, -0.014610094, 0.05516241], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 82, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.30510068, 0.25413662, 0.44748875, 0.6299203, 0.5083019, 0.0, 0.22713451, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 3.0, 6.0, 5.0, 0.103604734, 8.0, -0.04196507, 0.06996795, 0.026665747, -0.07489024, -0.014610094, 0.05516241], "split_indices": [1, 1, 2, 2, 2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.549038, 32.38089, 22.168148, 15.894386, 16.486504, 5.090887, 17.077261, 7.769161, 8.125225, 6.012733, 10.47377, 12.046002, 5.0312595], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.008551752, -0.057280757, 0.05123992, 0.05312829, -0.1373149, -0.013166257, -0.13258448], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 83, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.42752406, 0.7668166, 0.0, 0.0, 0.801405, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, 0.05123992, 0.05312829, 6.0, -0.013166257, -0.13258448], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.09738, 38.79511, 9.302274, 9.24869, 29.54642, 23.738453, 5.8079667], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.012017265, 0.075318374, -0.055558003, 0.008681388, -0.14443366, 0.07985434, -0.022783842, -0.08494356, 0.02483699], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 84, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.6379693, 0.0, 0.2965895, 0.6697992, 0.7184464, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.075318374, 5.0, 3.0, 8.0, 0.07985434, -0.022783842, -0.08494356, 0.02483699], "split_indices": [2, 0, 1, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.184357, 6.8333845, 48.35097, 27.935978, 20.414995, 6.671784, 21.264194, 12.796933, 7.6180615], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.01867013, -0.11453423, 0.16979349, -0.10588241, 0.02294175, 0.33025712, -0.012828635, -0.018747227, 0.040509723, 0.0195307, 0.18632755, 0.02246657, -0.03466416], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 85, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [1.3382, 1.1855867, 0.9537008, 0.0, 0.23422872, 1.2018212, 0.15074006, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 6.0, -0.10588241, 7.0, 4.0, 8.0, -0.018747227, 0.040509723, 0.0195307, 0.18632755, 0.02246657, -0.03466416], "split_indices": [0, 2, 2, 0, 2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.23636, 33.919323, 30.317038, 12.098513, 21.820808, 15.998702, 14.318335, 12.127735, 9.693073, 9.1888685, 6.809833, 7.34415, 6.974185], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.025498137, 0.13069665, -0.05079218, 0.002820365, 0.2857112, -0.098004594, 0.007701344, 0.06343933, -0.037510324, 0.025871022, 0.13160315, -0.01468707, 0.02587505], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 86, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5323959, 0.5243838, 0.6406174, 0.4800167, 0.28409684, 0.0, 0.14890203, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 5.0, 3.0, 2.0, -0.098004594, 8.0, 0.06343933, -0.037510324, 0.025871022, 0.13160315, -0.01468707, 0.02587505], "split_indices": [1, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.514206, 26.949936, 36.56427, 15.679076, 11.270861, 5.8931904, 30.671078, 6.0060687, 9.673008, 5.854212, 5.4166484, 17.297684, 13.373396], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.009071853, 0.144702, -0.11952381, 0.33033413, -0.061139826, -0.062056094, 0.009993423, 0.17337847, 0.049096756], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 87, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [1.0069622, 1.9157286, 0.41426665, 0.61879754, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 6.0, 2.0, -0.061139826, -0.062056094, 0.009993423, 0.17337847, 0.049096756], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.597878, 27.376354, 28.221523, 18.08324, 9.293113, 18.150951, 10.070573, 6.0415177, 12.041723], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.025097063, 0.051316798, -0.0, 0.100095585, -0.12502863, 0.01498456, 0.03960286, -0.011632701, -0.09492487], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 88, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.21839377, 0.0, 0.70315164, 0.02582261, 0.36014733, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.051316798, 5.0, 6.0, 7.0, 0.01498456, 0.03960286, -0.011632701, -0.09492487], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.16008, 7.823466, 54.336613, 30.624832, 23.711782, 14.126343, 16.49849, 17.534557, 6.177225], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.007577878, -0.025909549, 0.08893214, 0.020697912, -0.055982355, -0.0061845467, 0.077317774], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 89, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.58830917, 0.4091086, 0.0, 0.42701402, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, 0.08893214, 7.0, -0.055982355, -0.0061845467, 0.077317774], "split_indices": [2, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.227367, 50.891685, 5.3356814, 39.64293, 11.248756, 34.177124, 5.4658065], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0025926332, 0.06657421, -0.12795416, -0.063632205, 0.18105277, -0.05272234, -0.082729645, 0.015219756, -0.084269, -0.01175633, 0.10247096, -0.038531356, 0.0062205186], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 90, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.4922314, 0.55447984, 0.17990714, 0.45979595, 0.73770374, 0.098142624, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 6.0, 3.0, 2.0, 3.0, -0.082729645, 0.015219756, -0.084269, -0.01175633, 0.10247096, -0.038531356, 0.0062205186], "split_indices": [1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.078148, 34.63817, 19.43998, 16.004963, 18.633204, 14.230353, 5.2096257, 10.686157, 5.3188057, 7.9112706, 10.721933, 7.3092647, 6.921089], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.023608511, -0.15067637, 0.09783671, -0.06330013, -0.23181929, 0.25939637, -0.04088993, 0.023114974, -0.060643714, -0.12988113, -0.0, 0.122417755, 0.011453767], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 91, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.76772505, 0.12933308, 0.99250805, 0.29609644, 0.55320054, 0.4454913, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 7.0, 5.0, 6.0, 8.0, -0.04088993, 0.023114974, -0.060643714, -0.12988113, -0.0, 0.122417755, 0.011453767], "split_indices": [2, 2, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.451912, 23.473925, 23.97799, 12.753363, 10.720562, 14.376844, 9.601145, 6.205483, 6.5478797, 5.3118877, 5.4086742, 7.8229775, 6.553867], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.029571788, -0.0, -0.08291953, -0.057089858, 0.093093075, 0.010675769, -0.07809652], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 92, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.41644466, 0.96241146, 0.0, 0.912516, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, -0.08291953, 7.0, 0.093093075, 0.010675769, -0.07809652], "split_indices": [0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.305527, 53.175545, 5.1299834, 45.763702, 7.4118414, 31.659504, 14.104199], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.012048472, 0.07972837, -0.056414098, -0.21103264, 0.035322066, -0.10962289, -0.0047959154, 0.047240146, -0.03345208], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 93, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.60210335, 0.0, 0.64451987, 0.46226442, 0.52185136, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.07972837, 4.0, 6.0, 6.0, -0.10962289, -0.0047959154, 0.047240146, -0.03345208], "split_indices": [2, 0, 1, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.333508, 5.658256, 42.67525, 15.812839, 26.862411, 8.052903, 7.759935, 14.996028, 11.866384], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.017145418, 0.13717239, -0.07010948, 0.006220899, 0.20542732, -0.12239004, 0.04603776, 0.09540104, 0.011239003, 0.041508343, -0.0667393], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 94, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.60682875, 0.16827357, 0.39938477, 0.0, 0.23289764, 0.757455, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 9.0, 0.006220899, 2.0, 2.0, 0.04603776, 0.09540104, 0.011239003, 0.041508343, -0.0667393], "split_indices": [2, 1, 2, 0, 2, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.383915, 23.555367, 31.828548, 9.728472, 13.826895, 26.525522, 5.3030257, 7.38711, 6.4397845, 6.9735527, 19.55197], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.054392006, -0.034380127, 0.15960912, 0.008991956, -0.063342266, -0.03690602, 0.25743192, -0.014025719, 0.021833204, 0.12999822, 0.0037793266], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 95, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.6202954, 0.29259834, 0.88008404, 0.10853278, 0.0, 0.0, 0.9413762, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 2.0, 6.0, -0.063342266, -0.03690602, 8.0, -0.014025719, 0.021833204, 0.12999822, 0.0037793266], "split_indices": [0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.482086, 34.281483, 29.200603, 27.834965, 6.4465184, 7.179059, 22.021544, 14.159632, 13.675333, 12.129246, 9.892298], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.03969493, -0.06612231, 0.08667313, 0.10003945, 0.033416312, -0.014658037, 0.027378513], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 96, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.85387135, 0.0, 0.73860747, 0.0, 0.24980468, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.06612231, 3.0, 0.10003945, 5.0, -0.014658037, 0.027378513], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.41081, 9.575289, 58.83552, 9.175799, 49.659725, 19.851093, 29.80863], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.13532805, 0.052177846, -0.24296331, -0.0, 0.05048536, 0.01914772, -0.314364, 0.0049859257, -0.0068102675, -0.122443356, -0.072893135], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 97, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [1.0926442, 0.119129464, 0.7879348, 0.0058720466, 0.0, 0.0, 0.022780657, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [3.0, 7.0, 1.0, 3.0, 0.05048536, 0.01914772, 5.0, 0.0049859257, -0.0068102675, -0.122443356, -0.072893135], "split_indices": [2, 1, 1, 1, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.565422, 18.654514, 32.910908, 13.558422, 5.0960927, 5.9690566, 26.941853, 7.9734583, 5.5849633, 9.000186, 17.941666], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.023600621, -0.2845518, 0.067323275, -0.14492725, -0.029648108, 0.23324494, -0.17158152, 0.1573464, 0.03550624, -0.023918519, -0.09307736], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 98, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [1.5465995, 0.51626086, 1.9325213, 0.0, 0.0, 0.8357843, 0.18889743, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 4.0, 5.0, -0.14492725, -0.029648108, 4.0, 6.0, 0.1573464, 0.03550624, -0.023918519, -0.09307736], "split_indices": [1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.460983, 15.873725, 46.58726, 6.6380916, 9.235633, 27.754065, 18.833197, 6.5965695, 21.157495, 12.710407, 6.122789], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.039216533, 0.16500898, -0.049361095, 0.081038475, 0.06605756, -0.09708142, 0.023289274, 0.0038363317, 0.0287003, -0.08747494, 0.035220295], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 99, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.6771854, 0.20939416, 0.7334778, 0.0, 0.011167973, 0.0, 0.85418504, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 5.0, 3.0, 0.081038475, 7.0, -0.09708142, 5.0, 0.0038363317, 0.0287003, -0.08747494, 0.035220295], "split_indices": [0, 2, 2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.006657, 24.078272, 33.928387, 10.346425, 13.731846, 6.670497, 27.257889, 6.624034, 7.107812, 5.544996, 21.712893], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0069611394, -0.114463955, 0.04753691, -0.13636874, 0.0317464, 0.1113827, -0.08212985, 0.07309296, -0.05718798, -0.0, 0.041004125], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 100, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.33656704, 1.0283393, 0.7699171, 0.0, 0.7299973, 0.09906697, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 8.0, -0.13636874, 2.0, 5.0, -0.08212985, 0.07309296, -0.05718798, -0.0, 0.041004125], "split_indices": [2, 1, 1, 0, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.37635, 18.539648, 35.8367, 5.1455703, 13.394077, 30.700758, 5.1359425, 7.039889, 6.354189, 5.345858, 25.3549], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.05370974, -0.04265901, 0.16345277, 0.066998966, -0.0757253, 0.07922678, 0.095968775, -0.04843787, 0.06355356, 0.08977813, -0.014015376], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 101, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.5158801, 0.61787814, 0.2332837, 0.6052789, 0.0, 0.49572095, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 7.0, 7.0, 2.0, -0.0757253, 2.0, 0.095968775, -0.04843787, 0.06355356, 0.08977813, -0.014015376], "split_indices": [0, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.97782, 24.320927, 21.656895, 16.10525, 8.215676, 15.472455, 6.1844397, 5.9366274, 10.168622, 5.4204483, 10.052007], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.027270008, 0.13529281, -0.09911794, -0.0066064387, 0.06645161, -0.20189905, -0.027811382, -0.007950971, -0.0869584, 0.020849582, -0.04767036], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 102, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.6769725, 0.2622311, 0.26910183, 0.0, 0.0, 0.20603639, 0.34801298, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 4.0, 4.0, -0.0066064387, 0.06645161, 2.0, 7.0, -0.007950971, -0.0869584, 0.020849582, -0.04767036], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.12255, 16.50455, 39.617996, 5.695843, 10.808708, 14.903011, 24.714987, 5.7919936, 9.111018, 14.016974, 10.698011], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.027118728, 0.072982535, -0.12073693, -0.034167293, 0.06630574, -0.094571345, -0.04243066, -0.062627405, 0.03819023, 0.0049857395, -0.032715406], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 103, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.48925936, 0.41702974, 0.36393514, 0.45099744, 0.0, 0.0, 0.09311226, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 3.0, 5.0, 0.06630574, -0.094571345, 6.0, -0.062627405, 0.03819023, 0.0049857395, -0.032715406], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.758408, 23.636904, 26.121504, 13.766391, 9.870512, 6.2628345, 19.85867, 6.7712636, 6.9951267, 10.129664, 9.729005], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.028599802, -0.07200285, 0.12535943, 0.049340706, -0.25551188, 0.11245572, 0.024266317, -0.024606235, -0.10849323, -0.025875565, 0.04541669], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 104, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.5342283, 1.1929046, 0.659588, 0.0, 0.21035016, 0.0, 0.3194925, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 6.0, 0.049340706, 4.0, 0.11245572, 5.0, -0.024606235, -0.10849323, -0.025875565, 0.04541669], "split_indices": [0, 0, 0, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.405766, 25.311417, 27.094349, 10.866955, 14.444463, 6.7466817, 20.347666, 6.512962, 7.931501, 10.635881, 9.711784], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0075940695, -0.14336815, 0.031881824, -0.08976968, 0.03330672, -0.010136157, 0.08996426, -0.08195035, 0.047525086, 0.1228992, -0.017368527], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 105, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.37906852, 0.68735576, 0.13767684, 0.0, 0.0, 1.4077718, 1.1579504, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 7.0, 7.0, -0.08976968, 0.03330672, 5.0, 4.0, -0.08195035, 0.047525086, 0.1228992, -0.017368527], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.79149, 15.022891, 51.768597, 9.449838, 5.573053, 29.518286, 22.250313, 11.517514, 18.00077, 6.690304, 15.560008], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.07432192, 0.11615813, -0.06746714, 0.056867037, 0.22863674, -0.09951765, 0.042358346, 0.029887505, -0.0028476347, 0.025316792, 0.13105673], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 106, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.40090454, 0.3015288, 0.8928912, 0.11097014, 0.4169258, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 7.0, 9.0, 4.0, 6.0, -0.09951765, 0.042358346, 0.029887505, -0.0028476347, 0.025316792, 0.13105673], "split_indices": [2, 0, 2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.44235, 50.444317, 13.998035, 34.600124, 15.844196, 6.12744, 7.870595, 21.653511, 12.946611, 10.473822, 5.370374], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.015073784, 0.16209699, -0.056565367, 0.28722513, -0.033300117, -0.009541777, -0.079110906, 0.1265829, 0.02381601, -0.060067452, 0.022495288], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 107, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.56875926, 0.6575711, 0.31431642, 0.2728846, 0.0, 0.51472646, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [2.0, 6.0, 8.0, 1.0, -0.033300117, 2.0, -0.079110906, 0.1265829, 0.02381601, -0.060067452, 0.022495288], "split_indices": [1, 2, 2, 1, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.12731, 16.7942, 34.33311, 11.73286, 5.0613403, 29.226957, 5.1061554, 6.2017097, 5.5311503, 8.919548, 20.30741], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.036268577, 0.011718834, 0.06500794, -0.07010007, 0.07646727, 0.059535276, -0.07830286, -0.0022060683, 0.09987989], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 108, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.25530165, 0.3096781, 0.0, 1.3481393, 0.74786615, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 4.0, 0.06500794, 4.0, 6.0, 0.059535276, -0.07830286, -0.0022060683, 0.09987989], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.368347, 56.32377, 6.0445795, 24.16312, 32.16065, 9.805907, 14.357212, 24.676897, 7.483753], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.08021012, 0.1509242, -0.06614796, -0.038175434, 0.099315725, 0.017640859, 0.070040114, -0.034998022, 0.08332598, -0.059581287], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 109, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.68975604, 0.18599616, 0.43297696, 0.0, 0.66583717, 0.0, 0.7782727, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [7.0, 2.0, 8.0, -0.06614796, 4.0, 0.099315725, 4.0, 0.070040114, -0.034998022, 0.08332598, -0.059581287], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.086502, 36.35622, 18.730282, 6.854866, 29.501354, 7.0152674, 11.715014, 5.884779, 23.616575, 5.3989, 6.3161144], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.12329599, -0.061415818, 0.17203453, 0.13379867, 0.12636486, -0.008594762, 0.04914063], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 110, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.9290905, 0.0, 0.5078145, 0.0, 0.27516615, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, -0.061415818, 2.0, 0.13379867, 4.0, -0.008594762, 0.04914063], "split_indices": [2, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.1169, 6.4507256, 48.666176, 5.388225, 43.27795, 7.9448385, 35.33311], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.02066933, -0.118359305, 0.13779908, -0.07270524, 0.05187406, 0.12593606, -0.03652589, 0.048595175, -0.016502397, -0.043725874, 0.01600065], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 111, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.78773075, 0.70353657, 0.99371517, 0.0, 0.1831803, 0.0, 0.13988794, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 4.0, -0.07270524, 8.0, 0.12593606, 7.0, 0.048595175, -0.016502397, -0.043725874, 0.01600065], "split_indices": [2, 1, 1, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.956467, 30.833122, 18.123344, 17.934273, 12.898849, 6.6048775, 11.518468, 6.560399, 6.3384495, 5.420577, 6.097891], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.029315282, -0.16062176, 0.049776923, -0.0, -0.09437584, 0.09886271, -0.043153934, -0.034774613, 0.028735576, 0.04989513, -0.054246172], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 112, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.61634064, 0.53740823, 0.9834793, 0.14616543, 0.0, 0.0, 0.83244646, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 5.0, 6.0, 2.0, -0.09437584, 0.09886271, 3.0, -0.034774613, 0.028735576, 0.04989513, -0.054246172], "split_indices": [2, 1, 2, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.51012, 21.33084, 35.17928, 11.079026, 10.251813, 8.4053135, 26.773968, 5.1404266, 5.9385996, 10.247322, 16.526646], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.017285222, 0.088552415, -0.076452695, -0.2573716, 0.0023867632, -0.037890457, -0.09607408, -0.052369226, 0.026730718], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 113, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.88767606, 0.0, 0.61665547, 0.021703362, 0.45877844, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.088552415, 4.0, 2.0, 3.0, -0.037890457, -0.09607408, -0.052369226, 0.026730718], "split_indices": [1, 0, 1, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.97744, 6.575213, 40.40223, 11.960184, 28.442045, 5.368405, 6.591779, 8.64168, 19.800365], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.012571867, -0.0888398, 0.099370465, -0.25222856, 0.0438631, 0.070372134, 0.036885604, -0.13484314, -0.029498491, 0.040401977, -0.052632757], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 114, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.4925362, 1.0185063, 0.22227722, 0.37257522, 0.0, 0.0, 0.48848385, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 6.0, 6.0, 2.0, 0.0438631, 0.070372134, 5.0, -0.13484314, -0.029498491, 0.040401977, -0.052632757], "split_indices": [0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.83227, 24.284142, 29.54813, 14.5337515, 9.750389, 7.9631567, 21.584974, 5.243279, 9.290473, 15.351531, 6.233443], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.002942908, 0.038281497, -0.102119595, 0.08677178, -0.0049245465, -0.07975234, 0.014592188], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 115, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.93041104, 0.6529429, 0.0, 0.0, 0.72881305, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 2.0, -0.102119595, 0.08677178, 4.0, -0.07975234, 0.014592188], "split_indices": [0, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.772705, 56.465027, 6.30768, 7.797006, 48.66802, 7.932527, 40.735493], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.03439733, 0.11474384, -0.010712641, 0.0814161, 0.027472528, -0.09252566, 0.03712112, -0.04119587, 0.053257134, -0.0068493774, 0.07454525], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 116, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.2580912, 0.30896902, 0.6604087, 0.0, 0.46833193, 0.0, 0.5126137, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 5.0, 0.0814161, 6.0, -0.09252566, 8.0, -0.04119587, 0.053257134, -0.0068493774, 0.07454525], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.999374, 24.332787, 42.666584, 7.541327, 16.791458, 5.4416804, 37.224903, 7.7254643, 9.065994, 29.32565, 7.8992553], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.030145008, 0.1749034, -0.04544452, 0.011949193, 0.08650759, 0.040461097, -0.096383244, -0.05472717, 0.013493873], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 117, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.59372205, 0.24102056, 0.3281607, 0.0, 0.0, 0.0, 0.36369032, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [2.0, 1.0, 2.0, 0.011949193, 0.08650759, 0.040461097, 7.0, -0.05472717, 0.013493873], "split_indices": [1, 1, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.350243, 17.618622, 33.73162, 9.062202, 8.55642, 6.6347704, 27.09685, 17.103636, 9.993214], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.05223799, 0.082323015, -0.05537887, 0.0022182206, 0.19982798, 0.028285341, -0.05535759, -0.08925814, 0.113687314], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 118, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.45524156, 0.5171766, 0.0, 0.6186035, 2.138336, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 6.0, -0.05537887, 7.0, 2.0, 0.028285341, -0.05535759, -0.08925814, 0.113687314], "split_indices": [2, 2, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.194683, 56.138103, 6.056578, 34.493385, 21.64472, 23.793552, 10.699831, 5.408569, 16.23615], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.08871687, -0.073902, -0.0068178694, -0.09459461, 0.12903096, 0.013757395, -0.13716936, 0.11726322, -0.036072243], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 119, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.6971241, 0.0, 0.46617505, 1.3011554, 1.0552533, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.073902, 6.0, 4.0, 8.0, 0.013757395, -0.13716936, 0.11726322, -0.036072243], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.093384, 17.733862, 37.359524, 23.326946, 14.032577, 17.245417, 6.081529, 6.7622705, 7.270306], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.021765085, -0.08795828, 0.02787506, -0.09364344, 0.064142734, 0.07548285, -0.057828423, 0.058895662, -0.011008477, 0.007614346, -0.038967155], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 120, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.18844257, 0.88289815, 0.6321331, 0.0, 0.22584802, 0.0, 0.15194204, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 2.0, -0.09364344, 2.0, 0.07548285, 7.0, 0.058895662, -0.011008477, 0.007614346, -0.038967155], "split_indices": [2, 1, 1, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.77515, 23.577736, 30.197412, 9.370903, 14.206833, 8.150641, 22.046772, 6.1674576, 8.039375, 9.888587, 12.158185], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.009070956, -0.17532451, 0.16745105, -0.25970107, -0.015232263, 0.006152813, 0.20856233, -0.025607334, -0.12810141, 0.01384122, 0.100327276], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 121, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [1.501525, 0.20273829, 0.14156973, 0.29990697, 0.0, 0.0, 0.37964606, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 6.0, 7.0, 3.0, -0.015232263, 0.006152813, 6.0, -0.025607334, -0.12810141, 0.01384122, 0.100327276], "split_indices": [2, 0, 2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.435734, 22.500704, 26.93503, 12.288122, 10.212583, 6.747856, 20.187174, 7.0497003, 5.2384214, 9.756933, 10.430241], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.043925304, -0.07480535, 0.110442325, -0.09069754, 0.03353755, 0.106820844, 0.03831444, 0.09202594, -0.09109595, -0.015956841, 0.08527242], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 122, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.45976305, 0.54042816, 0.6076749, 0.0, 1.4234562, 0.0, 0.719293, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 2.0, -0.09069754, 7.0, 0.106820844, 8.0, 0.09202594, -0.09109595, -0.015956841, 0.08527242], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.71813, 19.422192, 36.295937, 5.985405, 13.436786, 7.0305977, 29.265339, 7.6362205, 5.8005657, 21.60405, 7.6612897], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.029497053, -0.17616254, 0.065161966, -0.034661457, -0.13177572, 0.15432973, -0.08142152, 0.022883244, -0.03982677, 0.070079096, -0.005554677, -0.0, -0.043538235], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 123, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.72790384, 0.70472455, 0.42755133, 0.17614028, 0.0, 0.3020971, 0.059121847, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 6.0, 5.0, -0.13177572, 8.0, 7.0, 0.022883244, -0.03982677, 0.070079096, -0.005554677, -0.0, -0.043538235], "split_indices": [1, 1, 2, 2, 0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.804142, 19.612213, 30.19193, 13.736159, 5.876054, 19.163809, 11.028121, 6.0720344, 7.664125, 13.294153, 5.869655, 5.43511, 5.593011], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.029964624, -0.016171008, 0.1111396, 0.15558958, -0.08164367, 0.006285175, 0.083234966, -0.083992206, -0.010760255], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 124, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.90367633, 0.5567765, 0.0, 0.18512961, 0.29049453, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 3.0, 0.1111396, 6.0, 4.0, 0.006285175, 0.083234966, -0.083992206, -0.010760255], "split_indices": [0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.888554, 48.038475, 5.8500776, 12.456474, 35.582, 6.8645825, 5.591892, 5.303461, 30.27854], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.044028535, -0.12347669, 0.03514263, 0.023326283, -0.19024281, -0.05008592, 0.16632372, -0.11560721, -0.017221339, 0.004231514, -0.04563193, 0.092234254, -0.0088939825], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 125, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.42197853, 0.47332907, 0.38556623, 0.0, 0.5987027, 0.14776623, 0.41224977, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 5.0, 0.023326283, 6.0, 3.0, 8.0, -0.11560721, -0.017221339, 0.004231514, -0.04563193, 0.092234254, -0.0088939825], "split_indices": [0, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.992954, 32.29172, 31.701231, 7.6306987, 24.661024, 19.128986, 12.572244, 8.914118, 15.746905, 11.668655, 7.460331, 7.2748513, 5.2973924], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.053924598, 0.09691277, -0.04746202, 0.17116262, 0.0047251, -0.07052233, 0.050490007, 0.025455223, 0.08835902, -0.036990054, 0.0676223, -0.0, 0.036219154], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 126, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.30275983, 0.31197473, 0.39805427, 0.21503168, 0.6923691, 0.0, 0.058855325, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 3.0, 4.0, 9.0, -0.07052233, 6.0, 0.025455223, 0.08835902, -0.036990054, 0.0676223, -0.0, 0.036219154], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.27547, 47.265312, 19.010157, 25.10434, 22.160973, 6.390715, 12.619441, 16.240402, 8.863937, 14.017242, 8.14373, 7.1996074, 5.419834], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.027265994, -0.019919528, 0.11252811, 0.099592716, -0.15799843, -0.032276373, 0.09462506, 0.07444128, -0.0012238197, -0.07449773, -0.014208121], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 127, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.22886565, 0.5970855, 0.954751, 0.3167769, 0.128117, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 3.0, 2.0, 3.0, -0.032276373, 0.09462506, 0.07444128, -0.0012238197, -0.07449773, -0.014208121], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.028297, 33.874878, 19.153421, 17.91945, 15.955426, 9.175581, 9.977839, 7.1893735, 10.730078, 7.601257, 8.354169], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0024136277, 0.23923711, -0.06305751, -0.0, 0.1484298, -0.19921038, 0.04245586, 0.010241433, -0.08906022, 0.05257528, -0.024696413], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 128, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.9382036, 0.7473738, 0.77745324, 0.0, 0.0, 0.57156986, 0.5200455, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 5.0, 6.0, -0.0, 0.1484298, 2.0, 8.0, 0.010241433, -0.08906022, 0.05257528, -0.024696413], "split_indices": [2, 1, 2, 0, 0, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.193718, 11.755814, 51.437904, 6.7203345, 5.035479, 22.484274, 28.953632, 6.5233536, 15.960921, 14.218456, 14.735175], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.05943462, -0.23617885, 0.015694031, -0.023285467, -0.10900693, 0.09382267, -0.031811588, 0.011570478, -0.041747298], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 129, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.8034069, 0.2892232, 0.62559414, 0.0, 0.0, 0.0, 0.29128408, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 4.0, 1.0, -0.023285467, -0.10900693, 0.09382267, 8.0, 0.011570478, -0.041747298], "split_indices": [2, 0, 0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.564495, 16.927954, 40.63654, 8.601062, 8.326892, 5.1232266, 35.513313, 21.274288, 14.239024], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.009427641, -0.070368044, 0.025932875, -0.050529387, 0.07088508, 0.045068815, -0.013806064], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 130, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.48251036, 0.0, 0.4388162, 0.0, 0.40421858, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, -0.070368044, 2.0, -0.050529387, 6.0, 0.045068815, -0.013806064], "split_indices": [1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.226, 7.2515597, 48.97444, 8.265335, 40.709106, 24.590042, 16.119066], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.034977376, -0.12896486, 0.018880213, 0.23503505, -0.09661509, 0.09467771, 0.009988993, 0.028945602, -0.05123413], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 131, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [1.0960451, 0.0, 1.136188, 0.21352363, 0.44087812, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.12896486, 5.0, 7.0, 2.0, 0.09467771, 0.009988993, 0.028945602, -0.05123413], "split_indices": [0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.21931, 5.171884, 43.047424, 14.932108, 28.115318, 9.913455, 5.018653, 7.344232, 20.771086], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.011702509, 0.07178196, -0.105974, 0.023289498, 0.09456398, -0.0698972, -0.0, -0.027474355, 0.017850436, 0.0383166, -0.04282642], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 132, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.41515654, 0.4267521, 0.24024297, 0.14566033, 0.0, 0.0, 0.23901446, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 5.0, 3.0, 0.09456398, -0.0698972, 8.0, -0.027474355, 0.017850436, 0.0383166, -0.04282642], "split_indices": [1, 1, 2, 2, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.12902, 38.64083, 18.48819, 33.47829, 5.162541, 7.457362, 11.0308275, 6.9958086, 26.482483, 5.527205, 5.5036225], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.015057478, 0.0722907, -0.1514068, 0.14091152, -0.048088994, -0.2805441, 0.04151933, 0.0014603685, 0.12315221, -0.036301117, -0.10596515], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 133, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.5866433, 0.48809677, 0.77853656, 0.8176315, 0.0, 0.07191455, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 8.0, 9.0, 6.0, -0.048088994, 4.0, 0.04151933, 0.0014603685, 0.12315221, -0.036301117, -0.10596515], "split_indices": [2, 1, 2, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.67362, 28.28809, 18.38553, 22.479893, 5.8081965, 12.979058, 5.4064713, 15.863716, 6.616177, 5.3265853, 7.6524734], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.034987047, 0.18954834, -0.10418219, -0.0, 0.097131155, -0.26996538, -0.026466815, -0.09991878, -0.04151629, -0.038227268, 0.025696281], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 134, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.85413724, 0.3119034, 0.51024556, 0.0, 0.0, 0.014955819, 0.35950726, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 5.0, 5.0, -0.0, 0.097131155, 6.0, 4.0, -0.09991878, -0.04151629, -0.038227268, 0.025696281], "split_indices": [0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.25368, 11.79497, 41.45871, 5.4342146, 6.3607554, 12.031813, 29.426899, 6.5834565, 5.448356, 15.881812, 13.545086], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.041488145, 0.016092611, 0.11547748, -0.03219303, 0.17106856, 0.05100423, -0.0, 0.024317352, -0.044946164, 0.019240942, 0.07231979], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 135, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.10485364, 0.39101422, 0.090819776, 0.5280787, 0.047188193, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 8.0, 6.0, 5.0, 9.0, 0.05100423, -0.0, 0.024317352, -0.044946164, 0.019240942, 0.07231979], "split_indices": [0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.794083, 48.661076, 14.133008, 37.21352, 11.447556, 9.123552, 5.0094557, 18.684742, 18.528776, 5.852235, 5.5953207], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.019103475, -0.035651557, 0.1214262, 0.0064410325, -0.09304285, -0.0056937626, 0.1827929, 0.035086542, -0.03785296, 0.076785296, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 136, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.4210068, 0.5821732, 0.2460661, 0.6282542, 0.0, 0.0, 0.26310068, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 2.0, 6.0, -0.09304285, -0.0056937626, 7.0, 0.035086542, -0.03785296, 0.076785296, -0.0], "split_indices": [0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [71.84013, 46.58552, 25.254612, 40.922394, 5.6631284, 7.395372, 17.85924, 22.797073, 18.12532, 12.63512, 5.224119], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.023844773, 0.22662805, -0.041325588, 0.123572595, -0.0027003272, -0.0, -0.08293416, -0.061691582, 0.025603276], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 137, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.7205356, 0.6235364, 0.39178437, 0.0, 0.0, 0.63300896, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [2.0, 4.0, 9.0, 0.123572595, -0.0027003272, 4.0, -0.08293416, -0.061691582, 0.025603276], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.41133, 12.274651, 39.13668, 6.6902375, 5.5844126, 34.11324, 5.0234437, 9.569574, 24.543663], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.05678995, 0.17879888, -0.002260819, 0.13035527, 0.060348313, -0.16135414, 0.089986965, -0.059449524, 0.06814321, -0.0, -0.13205735, -0.059987586, 0.08996475], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 138, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.4792394, 0.573749, 0.6610852, 0.0, 0.74135697, 0.77716744, 1.7632002, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 3.0, 6.0, 0.13035527, 1.0, 6.0, 4.0, -0.059449524, 0.06814321, -0.0, -0.13205735, -0.059987586, 0.08996475], "split_indices": [2, 0, 2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.01099, 20.441319, 42.56967, 5.3591228, 15.082196, 15.746932, 26.822739, 5.6059847, 9.476212, 10.485967, 5.260966, 11.083341, 15.739397], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.0026821499, -0.053054404, 0.05443726, -0.003473636, 0.14895809, 0.03367106, -0.0815641, 0.070311114, 0.021097705], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 139, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.5264311, 0.0, 0.26354206, 0.9306634, 0.073432595, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.053054404, 7.0, 5.0, 8.0, 0.03367106, -0.0815641, 0.070311114, 0.021097705], "split_indices": [2, 0, 1, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.93796, 11.544109, 44.393852, 27.469286, 16.924566, 19.340738, 8.128548, 6.553588, 10.370978], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.13592437, 0.057334013, 0.17324497, -0.014677071, -0.0, 0.0720442, -0.04631238, 0.005364758], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 140, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [1.4485476, 0.0, 0.42765218, 0.22491544, 0.15573949, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.13592437, 3.0, 1.0, 4.0, -0.0, 0.0720442, -0.04631238, 0.005364758], "split_indices": [2, 0, 1, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.30188, 5.2712317, 48.03065, 18.398615, 29.632034, 5.4029217, 12.995693, 5.4743285, 24.157705], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.009520051, -0.06282792, 0.19015838, 0.027244864, -0.050776087, -0.019826228, 0.16209419, 0.038293026, -0.015569549], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 141, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.68415976, 0.36762494, 1.4357085, 0.17359574, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, 7.0, 5.0, -0.050776087, -0.019826228, 0.16209419, 0.038293026, -0.015569549], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.462063, 35.38751, 14.074555, 19.060305, 16.327204, 8.430641, 5.643914, 8.68876, 10.371546], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.055000607, -0.025984049, -0.083679534, -0.14994773, 0.051125187, -0.0927924, -0.01625741, 0.08316363, -0.012246931], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 142, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.34010372, 0.52394795, 0.0, 0.26837966, 0.7190923, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 4.0, -0.083679534, 5.0, 5.0, -0.0927924, -0.01625741, 0.08316363, -0.012246931], "split_indices": [0, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.15018, 51.977623, 5.1725564, 20.067926, 31.909698, 6.3033133, 13.764613, 8.957432, 22.952267], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.07285465, -0.030087637, -0.0749083, 0.068619944, -0.19614154, -0.021592284, 0.06614625, -0.11387027, 0.028690329], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 143, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.33493972, 0.6987869, 0.0, 0.58797467, 0.9175211, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, -0.0749083, 4.0, 9.0, -0.021592284, 0.06614625, -0.11387027, 0.028690329], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.979942, 40.033775, 7.9461684, 25.098812, 14.934963, 12.964389, 12.134423, 9.19582, 5.7391434], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.015532665, 0.039318666, -0.17179278, -0.057521306, 0.14205092, -0.07264572, -0.016511347, 0.0043878914, -0.05951826, -0.016107848, 0.12786287], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 144, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.48316824, 0.41793036, 0.07771346, 0.234463, 1.1927632, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 6.0, 6.0, 6.0, -0.07264572, -0.016511347, 0.0043878914, -0.05951826, -0.016107848, 0.12786287], "split_indices": [1, 1, 2, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.03828, 39.33803, 13.700246, 19.975203, 19.362831, 7.3440266, 6.3562193, 13.333041, 6.6421614, 11.736932, 7.625899], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.010800754, -0.12415898, 0.054393668, 0.032818776, -0.08486521, 0.21332982, -0.053061172, 0.08200111, 0.022832971, -0.08059332, 0.051242165], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 145, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.374223, 0.6148671, 0.87037826, 0.0, 0.0, 0.112936914, 1.4975691, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 3.0, 6.0, 0.032818776, -0.08486521, 6.0, 6.0, 0.08200111, 0.022832971, -0.08059332, 0.051242165], "split_indices": [2, 1, 2, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.645718, 14.283425, 48.362293, 5.650216, 8.633209, 19.485973, 28.876322, 12.360096, 7.1258764, 14.836035, 14.040287], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.038380075, 0.08379119, -0.1147258, -0.08505782, 0.20921798, -0.18034047, 0.024677685, 0.049912397, -0.096772246, 0.09245228, 0.03712116, -0.009050317, -0.08280965], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 146, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.64516467, 0.5840558, 0.57597435, 0.75710756, 0.05092281, 0.4308418, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 5.0, 9.0, 2.0, 7.0, 6.0, 0.024677685, 0.049912397, -0.096772246, 0.09245228, 0.03712116, -0.009050317, -0.08280965], "split_indices": [0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.80755, 25.151625, 41.655922, 10.517537, 14.634088, 31.691713, 9.964208, 5.0500755, 5.4674616, 5.032827, 9.601261, 13.253208, 18.438505], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.07850219, 0.26229233, 0.010760214, 0.010681884, 0.14434692, 0.049549665, -0.06820334, -0.06362607, -0.00080502377], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 147, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.61966884, 0.6067108, 0.5034815, 0.0, 0.0, 0.0, 0.22743495, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [2.0, 1.0, 2.0, 0.010681884, 0.14434692, 0.049549665, 4.0, -0.06362607, -0.00080502377], "split_indices": [0, 0, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.19119, 12.615654, 38.575535, 6.9926667, 5.622987, 13.111686, 25.46385, 6.7898703, 18.673979], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.056509875, -0.10781075, 0.10344255, -0.0774423, 0.022582803, 0.22201417, 0.010912297, 0.14026083, 0.01679653, 0.033077832, -0.05759956], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 148, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.52700186, 0.44752032, 0.5563935, 0.0, 0.0, 0.8480294, 0.64448893, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 5.0, 5.0, -0.0774423, 0.022582803, 4.0, 5.0, 0.14026083, 0.01679653, 0.033077832, -0.05759956], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.94581, 13.88918, 52.056633, 7.7016788, 6.187501, 21.730404, 30.326227, 7.7908263, 13.939578, 20.995876, 9.330349], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.049657654, 0.12054474, -0.17332189, -0.0407178, 0.17896914, -0.011104349, -0.09156654, 0.13925913, 0.03382139], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 149, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.92109, 0.69334793, 0.20417765, 0.0, 0.5871192, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [8.0, 2.0, 4.0, -0.0407178, 1.0, -0.011104349, -0.09156654, 0.13925913, 0.03382139], "split_indices": [1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.285435, 43.39702, 12.888416, 7.493422, 35.903595, 7.340946, 5.54747, 5.349049, 30.554546], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.050081898, -0.07717515, 0.106380716, -0.016002227, -0.021533316, 0.070989326, 0.016111614, -0.04007086], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 150, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.723146, 0.18286332, 0.0, 0.635167, 0.19000378, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, -0.07717515, 3.0, 4.0, -0.021533316, 0.070989326, 0.016111614, -0.04007086], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.437836, 45.26396, 8.173874, 24.929394, 20.334568, 10.393786, 14.535607, 12.572652, 7.7619157], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04352915, 0.003408546, 0.051467996, 0.0658755, -0.094390616, 0.04769226, -0.017548189], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 151, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.24226007, 0.79461396, 0.0, 0.42106116, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 9.0, 0.051467996, 5.0, -0.094390616, 0.04769226, -0.017548189], "split_indices": [0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.682926, 39.20505, 10.477875, 33.694427, 5.5106225, 19.570467, 14.123962], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.053466797, 0.15488909, -0.04669371, 0.06865753, 0.091971226, -0.092044994, 0.023196686, -0.051527906, 0.07996031, 0.04886159, -0.0061968295], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 152, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.58333564, 0.31258905, 0.5494234, 0.997226, 0.0, 0.0, 0.15790913, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 6.0, 4.0, 0.091971226, -0.092044994, 7.0, -0.051527906, 0.07996031, 0.04886159, -0.0061968295], "split_indices": [0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.64634, 27.376724, 27.269617, 18.800493, 8.57623, 5.3602624, 21.909355, 8.319299, 10.4811945, 5.1314607, 16.777893], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.015831076, 0.078509785, -0.06792517, 0.099255286, 0.00123101, -0.09446373, 0.041016266, -0.028394634, 0.05256293, 0.05223538, -0.039496385], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 153, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.24945025, 0.5051576, 0.5695147, 0.0, 0.39926538, 0.0, 0.3547485, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 7.0, 0.099255286, 4.0, -0.09446373, 6.0, -0.028394634, 0.05256293, 0.05223538, -0.039496385], "split_indices": [1, 2, 1, 0, 1, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.299305, 26.62852, 18.670784, 5.1703405, 21.45818, 5.41756, 13.253224, 13.757567, 7.700611, 7.8064528, 5.446772], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.01806199, 0.015247991, -0.0893885, -0.050036237, 0.088025585, 0.00025864897, 0.063832685], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 154, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.5579803, 0.6820475, 0.0, 0.0, 0.38576433, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 2.0, -0.0893885, -0.050036237, 6.0, 0.00025864897, 0.063832685], "split_indices": [1, 1, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.32737, 49.96897, 5.3583984, 13.510344, 36.45863, 22.527248, 13.93138], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.03440419, 0.075002335, -0.08181238, 0.012128294, 0.23989764, 0.069749, -0.10487658, -0.047931474, 0.015015931, 0.027704764, 0.09583736, 0.048854355, -0.0039851028], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 155, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.3233466, 0.5015931, 0.7538184, 0.24789232, 0.08910084, 0.107038215, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 8.0, 6.0, 1.0, 4.0, 3.0, -0.10487658, -0.047931474, 0.015015931, 0.027704764, 0.09583736, 0.048854355, -0.0039851028], "split_indices": [0, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.3759, 50.092194, 16.283703, 37.48086, 12.611333, 10.632044, 5.65166, 5.7171903, 31.76367, 5.615241, 6.996092, 5.1268044, 5.5052395], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.038592037, -0.0094332285, 0.12973374, 0.013302417, -0.049264062, 0.047538538, -0.0, 0.037560135, -0.04529855], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 156, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.31475028, 0.17677207, 0.07625115, 0.7495167, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 9.0, 7.0, 4.0, -0.049264062, 0.047538538, -0.0, 0.037560135, -0.04529855], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [67.88033, 44.324005, 23.556326, 38.880795, 5.4432125, 18.496304, 5.060021, 23.632227, 15.248567], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.051593505, -0.011436235, -0.07231735, -0.05197195, 0.038755625, -0.07035863, 0.032644004], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 157, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.37329197, 0.38566166, 0.0, 0.0, 0.6772166, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, -0.07231735, -0.05197195, 1.0, -0.07035863, 0.032644004], "split_indices": [0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.7443, 44.00544, 7.7388606, 10.309971, 33.69547, 6.105154, 27.590315], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.007732022, -0.19667529, 0.038202222, -0.026520366, -0.085577324, 0.1537679, -0.01838423, 0.10170965, -0.03663611, -0.03637091, 0.06886207], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 158, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.57900137, 0.06314671, 0.35584548, 0.0, 0.0, 0.97040606, 0.91359514, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 6.0, 3.0, -0.026520366, -0.085577324, 7.0, 8.0, 0.10170965, -0.03663611, -0.03637091, 0.06886207], "split_indices": [1, 2, 2, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.021526, 12.108887, 50.912636, 6.8558493, 5.253038, 16.75137, 34.161266, 10.101566, 6.6498036, 24.790998, 9.37027], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.061004795, -0.161037, 0.10730332, -0.07516306, -0.1132825, 0.18729655, -0.017038612, -0.041065753, 0.03529702, 0.08766418, 0.008606932], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 159, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.9416487, 0.5774305, 0.29377776, 0.33517516, 0.0, 0.2003972, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 9.0, 4.0, -0.1132825, 4.0, -0.017038612, -0.041065753, 0.03529702, 0.08766418, 0.008606932], "split_indices": [1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.584396, 34.008904, 19.575493, 25.647907, 8.360996, 13.474902, 6.1005907, 20.041277, 5.6066303, 7.223641, 6.2512617], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.07853403, 0.1420591, -0.047242925, 0.26288158, 0.0791149, 0.008475233, -0.047930144, -0.007717535, 0.16230658, -0.0054300344, 0.03726283, -0.019156938, 0.0297137], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 160, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.46864006, 0.2370528, 0.13701533, 1.0430839, 0.13220179, 0.096404515, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 5.0, 2.0, 6.0, 8.0, -0.047930144, -0.007717535, 0.16230658, -0.0054300344, 0.03726283, -0.019156938, 0.0297137], "split_indices": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.626743, 37.40203, 18.224712, 11.17887, 26.223162, 12.213157, 6.011556, 5.755142, 5.4237275, 7.796494, 18.426668, 6.340401, 5.8727555], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.01592321, -0.01935975, 0.08425291, -0.11414431, 0.06451773, -0.09390772, 0.027405813, 0.110452674, -0.039472], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 161, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.5117268, 0.37750608, 0.0, 0.96972114, 1.5278264, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 6.0, 0.08425291, 5.0, 3.0, -0.09390772, 0.027405813, 0.110452674, -0.039472], "split_indices": [1, 2, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.367657, 44.932796, 5.4348598, 21.48516, 23.447634, 10.899711, 10.585451, 9.056122, 14.391513], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.016149806, 0.03935146, -0.12897474, -0.03855501, 0.08892013, -0.13592558, 0.0028215428, 0.023144342, -0.06310088, 0.03277681, -0.042499218], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 162, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.38528284, 0.8364442, 0.916695, 0.6499399, 0.0, 0.0, 0.24690913, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 8.0, 7.0, 5.0, 0.08892013, -0.13592558, 5.0, 0.023144342, -0.06310088, 0.03277681, -0.042499218], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.286102, 38.910156, 19.375944, 30.213898, 8.696259, 5.1159325, 14.260012, 18.035137, 12.1787615, 8.750179, 5.509832], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.06341766, -0.2414939, 0.051048398, -0.0050225025, -0.115699485, 0.1197785, -0.017817523, -0.014857042, 0.095298745, -0.047342047, 0.054208856], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 163, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.9688291, 0.5531273, 0.14712605, 0.0, 0.0, 0.5487571, 0.4255431, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [4.0, 1.0, 5.0, -0.0050225025, -0.115699485, 7.0, 8.0, -0.014857042, 0.095298745, -0.047342047, 0.054208856], "split_indices": [1, 1, 2, 0, 0, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.99054, 17.511524, 27.479013, 7.550533, 9.960991, 14.109819, 13.369194, 7.735775, 6.374045, 8.255983, 5.1132107], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.041377828, -0.12822841, -0.0, -0.08562881, -0.032790683, 0.051939625, -0.091533236, 0.008381584, -0.035052583, -0.0687197, -0.006432653], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 164, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.20611933, 0.2330367, 0.6209732, 0.0, 0.07799424, 0.0, 0.21286044, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 3.0, -0.08562881, 2.0, 0.051939625, 5.0, 0.008381584, -0.035052583, -0.0687197, -0.006432653], "split_indices": [0, 2, 2, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.143036, 17.277674, 36.865364, 5.379027, 11.898646, 12.675727, 24.189638, 6.604539, 5.2941074, 6.918788, 17.27085], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.033987872, 0.07238589, -0.07583747, 0.0030136695, 0.26614162, 0.019248664, -0.051304538, 0.014491194, -0.05707802, 0.032406542, 0.10982208], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 165, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.2754339, 0.6265557, 0.24041978, 0.32092574, 0.10964191, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 8.0, 4.0, 7.0, 4.0, 0.019248664, -0.051304538, 0.014491194, -0.05707802, 0.032406542, 0.10982208], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.094055, 47.67646, 15.417593, 36.23468, 11.441782, 5.9462605, 9.471333, 30.38254, 5.8521395, 5.6242723, 5.81751], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.00025232238, -0.024471274, 0.046059687, -0.09226669, 0.04333095, -0.06436341, -0.0067657195, 0.03223519, -0.0529547], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 166, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.23983023, 0.28091353, 0.0, 0.23270693, 0.42833602, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, 0.046059687, 3.0, 9.0, -0.06436341, -0.0067657195, 0.03223519, -0.0529547], "split_indices": [2, 1, 0, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.84901, 58.177917, 7.671085, 29.647474, 28.530445, 9.490081, 20.157393, 22.836222, 5.6942225], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.060849667, 0.0072749965, -0.16463406, -0.046734814, 0.052038327, -0.0046453746, -0.07806408, 0.036763504, -0.030582149], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 167, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.40526423, 0.32177645, 0.28757918, 0.2500704, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 3.0, 2.0, 0.052038327, -0.0046453746, -0.07806408, 0.036763504, -0.030582149], "split_indices": [1, 1, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.924377, 32.53475, 21.389626, 24.642939, 7.8918114, 9.216211, 12.173415, 5.314779, 19.328161], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.07664828, -0.040026844, -0.16505083, 0.12153819, -0.01846751, -0.08792024, 0.120585136, 0.004279311], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 168, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.65676725, 0.0, 1.1766299, 0.38278478, 0.70801914, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.07664828, 6.0, 3.0, 7.0, -0.01846751, -0.08792024, 0.120585136, 0.004279311], "split_indices": [0, 0, 2, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.70309, 7.6611633, 56.041927, 31.930498, 24.11143, 18.90729, 13.023209, 5.6536684, 18.45776], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0024263847, -0.047253966, 0.046827096, 0.07358602, -0.17027561, -0.03088744, 0.09332086, -0.08473988, -0.021919038], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 169, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.43811217, 0.64413446, 0.0, 0.9497875, 0.17767084, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.046827096, 3.0, 6.0, -0.03088744, 0.09332086, -0.08473988, -0.021919038], "split_indices": [2, 2, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.997166, 40.836292, 13.1608715, 20.393465, 20.442827, 11.780944, 8.61252, 8.128054, 12.3147745], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.08602076, 0.035396595, -0.13302754, -0.19282103, -0.06975868, -0.113752656, 0.021719633, 0.020065568, -0.07409249], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 170, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.5675981, 0.0, 0.14004135, 1.1973097, 0.65119505, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.035396595, 6.0, 6.0, 6.0, -0.113752656, 0.021719633, 0.020065568, -0.07409249], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.394, 9.879517, 46.514484, 22.04871, 24.465776, 12.918802, 9.129908, 13.8471365, 10.61864], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.04291438, -0.079777814, 0.0047471873, -0.1301793, 0.13008268, -0.0, -0.05450947, 0.07451786, -0.00061834994], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 171, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.57429373, 0.0, 0.7442204, 0.15352634, 0.38754642, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.079777814, 6.0, 5.0, 8.0, -0.0, -0.05450947, 0.07451786, -0.00061834994], "split_indices": [2, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.454426, 8.447354, 42.007072, 19.826689, 22.180384, 5.4857674, 14.340921, 11.637874, 10.54251], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.046370342, -0.06516148, -0.018079491, 0.035193764, -0.10953495, -0.006122032, 0.0824805, 0.003329423, -0.077992156], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 172, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.24752805, 0.0, 0.25687268, 0.4565192, 0.38143933, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.06516148, 6.0, 5.0, 6.0, -0.006122032, 0.0824805, 0.003329423, -0.077992156], "split_indices": [1, 0, 1, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.896397, 6.4905944, 49.405804, 30.91779, 18.488012, 25.501314, 5.4164753, 10.405567, 8.082445], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.11216166, 0.0978968, 0.05116521, 0.14978284, -0.037625547, 0.018295433, 0.10325627], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 173, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.54018855, 0.0, 0.66517985, 0.3622352, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.0978968, 6.0, 8.0, -0.037625547, 0.018295433, 0.10325627], "split_indices": [2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.520237, 8.541474, 35.978764, 23.54663, 12.432133, 17.418493, 6.1281376], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.003906874, -0.03221415, 0.06803988, 0.01352554, -0.12986638, 0.043068778, -0.025112849, -0.0645273, 0.0076037296], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 174, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.4580823, 0.22149299, 0.0, 0.4218255, 0.2314887, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 6.0, 0.06803988, 4.0, 7.0, 0.043068778, -0.025112849, -0.0645273, 0.0076037296], "split_indices": [0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.572414, 45.59097, 6.981448, 30.93749, 14.653477, 13.472312, 17.465178, 9.626309, 5.0271673], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.016001437, -0.05395918, 0.012670951, 0.045470286, -0.10131618, -0.0, 0.07919611, -0.009861429, -0.043083064], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 175, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.32312468, 0.0, 0.20654197, 0.42948207, 0.012459271, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.05395918, 8.0, 7.0, 9.0, -0.0, 0.07919611, -0.009861429, -0.043083064], "split_indices": [0, 0, 2, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.730743, 9.224931, 54.50581, 43.474567, 11.031245, 36.992264, 6.482301, 5.794911, 5.236335], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.007671015, -0.046409424, 0.012491704, -0.09031185, 0.09168131, -0.09031156, 0.038103648, 0.064190306, -0.022722729], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 176, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.21642545, 0.0, 0.49473843, 1.24393, 0.74230695, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.046409424, 5.0, 6.0, 8.0, -0.09031156, 0.038103648, 0.064190306, -0.022722729], "split_indices": [1, 0, 2, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.55767, 7.820144, 58.737526, 24.947527, 33.789997, 12.704061, 12.243465, 19.711786, 14.078213], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.056946382, 0.11623389, 0.006564112, 0.066476, -0.111298405, 0.049870163, -0.015368779, -0.05734272, 0.0066091483], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 177, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.8249305, 0.0, 0.33094317, 0.39560744, 0.18577445, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.11623389, 6.0, 4.0, 6.0, 0.049870163, -0.015368779, -0.05734272, 0.0066091483], "split_indices": [2, 0, 2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.060375, 5.6066513, 45.453724, 30.986895, 14.466829, 17.008823, 13.978071, 9.246171, 5.2206583], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.021502638, -0.00274793, 0.071294785, 0.059228495, -0.089247905, 0.008561731, 0.07192306], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 178, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.3575524, 1.096625, 0.0, 0.23380306, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 7.0, 0.071294785, 9.0, -0.089247905, 0.008561731, 0.07192306], "split_indices": [0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.607346, 56.763412, 5.8439326, 47.281292, 9.482121, 41.874302, 5.406988], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0059774546, -0.09588501, 0.05114871, 0.09014274, -0.31837115, -0.06612166, 0.14911023, -0.041661493, -0.13099281, 0.08409837, -0.038601935], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 179, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.25913188, 1.5913671, 1.0973461, 0.0, 0.12589705, 0.0, 1.1619053, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [3.0, 4.0, 4.0, 0.09014274, 1.0, -0.06612166, 8.0, -0.041661493, -0.13099281, 0.08409837, -0.038601935], "split_indices": [1, 2, 2, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.22403, 15.94073, 39.2833, 5.443352, 10.497378, 9.812323, 29.470976, 5.3688364, 5.1285415, 20.254234, 9.2167425], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0013282165, -0.03546664, 0.12733644, 0.07449601, -0.09460692, 0.017172629, 0.051569525, -0.0, 0.045517758, -0.09636803, -0.0011946615], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 180, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.25421408, 0.27125496, 0.0064653903, 0.09149776, 0.52583086, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 4.0, 4.0, 5.0, 0.017172629, 0.051569525, -0.0, 0.045517758, -0.09636803, -0.0011946615], "split_indices": [1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.873886, 39.317863, 11.556021, 13.068453, 26.24941, 6.251406, 5.3046145, 6.7023625, 6.366091, 6.4563546, 19.793056], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.013129319, -0.05960961, 0.11616134, -0.20221709, 0.06693959, 0.08840962, -0.0, -0.011427131, -0.09588905, 0.13716951, -0.054867852, -0.045474358, 0.035501983], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 181, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.37096262, 0.5345891, 0.44912583, 0.21880895, 1.6009744, 0.0, 0.25893855, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 3.0, 2.0, 7.0, 0.08840962, 6.0, -0.011427131, -0.09588905, 0.13716951, -0.054867852, -0.045474358, 0.035501983], "split_indices": [2, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.819874, 27.12262, 19.697254, 12.842624, 14.279996, 7.3871694, 12.310085, 6.263397, 6.579226, 5.405521, 8.874475, 5.523839, 6.786246], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.01332521, -0.06353211, 0.016679378, 0.06752549, -0.06394425, -0.00066171907, 0.047778327, 0.011319791, -0.03667908], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 182, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.36240277, 0.0, 0.20963563, 0.22016639, 0.12626252, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.06353211, 7.0, 5.0, 2.0, -0.00066171907, 0.047778327, 0.011319791, -0.03667908], "split_indices": [2, 0, 2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.993668, 6.9961762, 48.99749, 30.9079, 18.08959, 17.49514, 13.412762, 6.097392, 11.992199], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.020436633, -0.12416034, 0.23606008, -0.19719632, 0.038213424, 0.10129503, 0.02387352, 0.0020855363, -0.09134181], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 183, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [1.2371972, 0.6486527, 0.1422208, 0.61680853, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 8.0, 9.0, 3.0, 0.038213424, 0.10129503, 0.02387352, 0.0020855363, -0.09134181], "split_indices": [0, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.821743, 32.560528, 12.261218, 25.709253, 6.851273, 6.3450885, 5.9161296, 8.857107, 16.852146], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.00043685758, -0.07635811, 0.070413545, -0.066533804, -0.0, -0.005950709, 0.15191205, 0.035361443, -0.020949336, 0.024033481, -0.033624865, 0.062400214, 0.023051664], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 184, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.2857466, 0.26204565, 0.19323152, 0.0, 0.14929454, 0.14984481, 0.016528994, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 4.0, -0.066533804, 2.0, 2.0, 6.0, 0.035361443, -0.020949336, 0.024033481, -0.033624865, 0.062400214, 0.023051664], "split_indices": [0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.28356, 23.725782, 27.557777, 7.258654, 16.467129, 14.072131, 13.485647, 5.5697827, 10.897346, 7.3858852, 6.686246, 5.9429083, 7.5427384], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.012489559, -0.08965278, 0.12966935, 0.010469493, -0.25407445, 0.09664617, -0.02532177, -0.038600024, 0.097595155, -0.108788036, -0.03701848], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 185, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.731858, 0.74875164, 1.006183, 1.2807156, 0.1479528, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 5.0, 4.0, 5.0, 0.09664617, -0.02532177, -0.038600024, 0.097595155, -0.108788036, -0.03701848], "split_indices": [2, 2, 0, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.02293, 42.841526, 22.181398, 26.831184, 16.01034, 11.659812, 10.521587, 18.79784, 8.033344, 7.3580356, 8.652305], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.031863667, 0.07691524, -0.104046926, 0.045373116, 0.08793991, 0.003261982, 0.05457556], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 186, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [1.1825224, 0.38261148, 0.0, 0.24527785, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, -0.104046926, 9.0, 0.08793991, 0.003261982, 0.05457556], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.33219, 62.01532, 6.3168736, 55.579636, 6.4356823, 45.815727, 9.763907], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.074424714, 0.031946886, -0.20669974, -0.06395365, 0.11014994, -0.121203035, -0.080923624, 0.053085975, -0.07303652, 0.0852705, -0.02180695, -0.06445422, 0.00046990727], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 187, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8061969, 0.24608329, 0.06474137, 0.65938103, 0.61980647, 0.17387658, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 7.0, 1.0, 7.0, 6.0, -0.080923624, 0.053085975, -0.07303652, 0.0852705, -0.02180695, -0.06445422, 0.00046990727], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.586437, 30.257082, 24.329355, 13.098211, 17.15887, 12.239666, 12.08969, 5.36073, 7.737481, 8.785964, 8.372907, 6.9800477, 5.2596183], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.024356801, 0.089828745, -0.015334451, 0.15008444, -0.0112931505, 0.060269505, -0.09996484, -0.009654922, 0.07085255, -0.018803291, 0.07239478, -0.088918574, 0.025159184], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 188, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.17743605, 0.21341252, 0.26783553, 0.31211507, 0.0, 0.5067465, 0.7688097, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 5.0, 1.0, -0.0112931505, 8.0, 8.0, -0.009654922, 0.07085255, -0.018803291, 0.07239478, -0.088918574, 0.025159184], "split_indices": [2, 1, 1, 2, 0, 2, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.883026, 24.585213, 39.29781, 17.10626, 7.478953, 20.28373, 19.014082, 5.31602, 11.79024, 12.140603, 8.143126, 9.158936, 9.855147], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.034638107, 0.07216957, -0.08432384, -0.16586687, 0.008368277, -0.08536843, -0.02379859, 0.096202485, -0.03605813], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 189, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.7336351, 0.0, 0.3663912, 0.19992304, 0.9420422, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.07216957, 7.0, 4.0, 3.0, -0.08536843, -0.02379859, 0.096202485, -0.03605813], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.3122, 7.104857, 45.20734, 24.20401, 21.00333, 8.753714, 15.450296, 5.9158616, 15.087468], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.054691304, -0.027623229, 0.11310894, -0.04808037, 0.024899552, 0.17589967, -0.00052001583, 0.104806356, 0.025628097], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 190, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.2782194, 0.3600195, 0.25805452, 0.0, 0.0, 0.2738064, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 6.0, 6.0, -0.04808037, 0.024899552, 5.0, -0.00052001583, 0.104806356, 0.025628097], "split_indices": [1, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.430935, 22.102417, 32.328518, 10.257732, 11.844685, 21.120085, 11.208436, 5.880057, 15.240027], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.064423904, -0.04865537, 0.21171245, -0.12364635, 0.017519785, 0.12288517, 0.106014684, -0.0, -0.08046394, -0.019762587, 0.04529053, -0.040606238, 0.0745383], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 191, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8066376, 0.14638384, 0.34243292, 0.23293266, 0.17948502, 0.0, 0.57131714, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 6.0, 7.0, 3.0, 3.0, 0.12288517, 8.0, -0.0, -0.08046394, -0.019762587, 0.04529053, -0.040606238, 0.0745383], "split_indices": [1, 0, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.83385, 25.922691, 19.91116, 12.453177, 13.469513, 5.6015534, 14.309606, 7.356139, 5.097038, 8.139787, 5.329726, 5.0686975, 9.240909], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.048132278, 0.08403966, -0.03193978, 0.04092037, 0.06453387, -0.016319329, 0.06693291], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 192, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.31682348, 0.22794494, 0.0, 0.66699094, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, -0.03193978, 5.0, 0.06453387, -0.016319329, 0.06693291], "split_indices": [1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.865017, 45.419098, 9.445919, 35.73759, 9.681503, 23.562609, 12.174984], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.044989552, -0.07257706, 0.04828829, 0.073884375, -0.00063528627, -0.070525154, 0.027960865], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 193, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.87909114, 0.0, 0.3329126, 0.0, 0.61041194, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [4.0, -0.07257706, 2.0, 0.073884375, 4.0, -0.070525154, 0.027960865], "split_indices": [0, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.165768, 14.349243, 30.816525, 5.711634, 25.10489, 7.0503955, 18.054495], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.009074156, -0.14578485, 0.059396256, 0.045416974, -0.14312603, 0.075537115, -0.003193908, -0.040281247, 0.032615352], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 194, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.41444167, 1.3694284, 0.5234192, 0.0, 0.0, 0.0, 0.48339286, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [2.0, 1.0, 2.0, 0.045416974, -0.14312603, 0.075537115, 6.0, -0.040281247, 0.032615352], "split_indices": [2, 2, 1, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.179844, 11.844836, 40.335007, 6.3711653, 5.473671, 9.551077, 30.78393, 14.497484, 16.286446], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.030608453, -0.04240333, 0.10304406, 0.020886071, -0.07447275, 0.018882867, 0.18559705, -0.04696626, 0.02667403, 0.019641556, -0.009617973, 0.07598247, 0.010391751], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 195, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.36726773, 0.12646213, 0.21816668, 0.0, 0.3870327, 0.050461378, 0.13488233, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 5.0, 0.020886071, 8.0, 8.0, 8.0, -0.04696626, 0.02667403, 0.019641556, -0.009617973, 0.07598247, 0.010391751], "split_indices": [0, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.471664, 32.62312, 33.84854, 6.3060226, 26.317097, 17.96928, 15.879262, 17.952192, 8.364904, 10.262052, 7.7072296, 10.038537, 5.840725], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.10361425, -0.14778306, 0.06002338, 0.071933135, -0.19156188, -0.012632466, 0.0354007, -0.0, -0.08321757, -0.004732799], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 196, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [1.0832129, 0.20176655, 0.10371441, 0.12121575, 0.0, 0.26360685, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 8.0, 7.0, 3.0, 0.071933135, 9.0, -0.012632466, 0.0354007, -0.0, -0.08321757, -0.004732799], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.68204, 40.46923, 28.212809, 32.303043, 8.166186, 18.704775, 9.508033, 16.321934, 15.98111, 11.797561, 6.907214], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.07368263, -0.025852622, -0.100309715, -0.08604978, 0.11045342, -0.043833524, 0.03298045, 0.08783415, -0.006194646], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 197, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.6308788, 0.3942561, 0.0, 0.4175901, 0.37710536, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, -0.100309715, 7.0, 6.0, -0.043833524, 0.03298045, 0.08783415, -0.006194646], "split_indices": [2, 2, 0, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.18024, 46.188396, 6.9918447, 32.803604, 13.384789, 25.694056, 7.1095505, 5.4018483, 7.982941], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.019574603, 0.033854842, -0.14941125, -0.05699912, 0.118299544, -0.07574668, -0.004853822, 0.05029756, -0.049800787, 0.006811989, 0.07141756], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 198, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.46811715, 0.36870608, 0.2396664, 0.5773461, 0.25395885, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 4.0, 1.0, 6.0, -0.07574668, -0.004853822, 0.05029756, -0.049800787, 0.006811989, 0.07141756], "split_indices": [1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.0853, 45.379925, 18.70537, 21.446484, 23.933443, 9.618292, 9.087078, 6.5308366, 14.915647, 14.435492, 9.4979515], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.06474189, -0.13121024, 0.1480901, -0.0020784254, -0.057364862, -0.025962383, 0.22992839, 0.15180674, 0.016070768], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 199, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.87724465, 0.09680417, 0.7563079, 0.0, 0.0, 0.0, 1.3031542, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 3.0, 3.0, -0.0020784254, -0.057364862, -0.025962383, 6.0, 0.15180674, 0.016070768], "split_indices": [2, 1, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.419308, 14.815039, 36.604267, 5.729749, 9.085289, 9.134929, 27.46934, 9.757487, 17.711851], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007336353, -0.12081789, 0.040076345, -0.07805361, 0.017639091, 0.12759967, -0.03570075, 0.090066895, 0.008318258, -0.026175003, 0.003322996], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 200, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.28628492, 0.41139305, 0.29072726, 0.0, 0.0, 0.30731282, 0.06296533, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 7.0, 5.0, -0.07805361, 0.017639091, 3.0, 4.0, 0.090066895, 0.008318258, -0.026175003, 0.003322996], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.782314, 14.019503, 40.76281, 7.955301, 6.064202, 19.21039, 21.552422, 5.9385657, 13.271824, 10.858639, 10.693783], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.033074845, 0.037662234, -0.06344721, 0.023352692, -0.1173315, -0.07543357, -0.018660756], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 201, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.24291664, 0.0, 0.3420326, 0.0, 0.19099131, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.037662234, 4.0, 0.023352692, 3.0, -0.07543357, -0.018660756], "split_indices": [1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.059, 6.8515954, 42.207405, 11.017658, 31.189747, 7.5201993, 23.669548], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.00461185, -0.0750105, 0.053071283, 0.005639168, -0.06420815, 0.14424321, -0.002851522, -0.0152908955, 0.023592925, 0.07314994, 0.0023367223, 0.04690734, -0.01918955], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 202, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.2141962, 0.25145584, 0.19600204, 0.062436126, 0.0, 0.1663078, 0.2183142, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 3.0, 5.0, -0.06420815, 7.0, 6.0, -0.0152908955, 0.023592925, 0.07314994, 0.0023367223, 0.04690734, -0.01918955], "split_indices": [2, 2, 1, 1, 0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.050343, 19.461765, 34.588573, 12.468398, 6.993368, 13.2293625, 21.359213, 6.4212523, 6.0471454, 6.7691703, 6.460192, 5.0384903, 16.320723], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.005843946, 0.07485544, -0.15694936, 0.13066483, -0.041397013, -0.090716265, -0.010997367, 0.009157822, 0.070506446], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 203, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.58544475, 0.3793049, 0.25065202, 0.22783989, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 8.0, 8.0, 5.0, -0.041397013, -0.090716265, -0.010997367, 0.009157822, 0.070506446], "split_indices": [2, 1, 2, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.373753, 29.475101, 15.898653, 24.030172, 5.44493, 6.13547, 9.763183, 13.411504, 10.618668], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.04169527, -0.13344862, 0.03393806, -0.11678181, 0.025652008, 0.21268082, -0.06642963, 0.0428817, -0.04653637, 0.028596217, 0.083663136, -0.0887434, 0.009014012], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 204, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.39477733, 1.0867609, 0.5712424, 0.0, 0.36989188, 0.025107324, 0.47294772, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 3.0, -0.11678181, 8.0, 7.0, 5.0, 0.0428817, -0.04653637, 0.028596217, 0.083663136, -0.0887434, 0.009014012], "split_indices": [0, 2, 2, 0, 2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.800945, 24.561869, 29.239079, 9.104391, 15.457478, 10.470212, 18.768866, 9.837383, 5.620095, 5.2006154, 5.2695966, 5.252031, 13.516835], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.03348733, -0.05316106, 0.059774056, -0.07221927, 0.09926284, -0.0, -0.03756382, 0.044564106, -0.006240754], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 205, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.3607064, 0.0, 0.31874442, 0.06099631, 0.291712, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.05316106, 3.0, 4.0, 8.0, -0.0, -0.03756382, 0.044564106, -0.006240754], "split_indices": [1, 0, 2, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.336365, 6.0776873, 58.25868, 12.641749, 45.616932, 5.2130847, 7.428664, 32.763783, 12.8531475], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.0357348, 0.073692024, -0.008411511, -0.06422234, -0.015537448, 0.074028395], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 206, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.61414886, 0.27708104, 0.0, 0.6033714, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, 0.073692024, 7.0, -0.06422234, -0.015537448, 0.074028395], "split_indices": [2, 2, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.668335, 60.797882, 7.870449, 54.114876, 6.683006, 47.268112, 6.8467655], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.041905746, -0.010123679, -0.04885227, -0.0863765, 0.058297373, 0.019601636, -0.05373838, -0.0114396075, 0.0539574], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 207, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.17437351, 0.22462457, 0.0, 0.31499907, 0.27663782, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, -0.04885227, 1.0, 3.0, 0.019601636, -0.05373838, -0.0114396075, 0.0539574], "split_indices": [2, 2, 0, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.296623, 40.550457, 8.746164, 19.744751, 20.805708, 7.194298, 12.550452, 11.526139, 9.279569], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.019986901, 0.056575496, -0.006288487, -0.07656054, 0.030774316, 0.04766448, -0.011018069], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 208, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.30851132, 0.0, 0.55535626, 0.0, 0.45112067, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.056575496, 1.0, -0.07656054, 4.0, 0.04766448, -0.011018069], "split_indices": [0, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.25892, 8.344483, 55.914436, 6.8334618, 49.080975, 16.981089, 32.099888], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.030497085, 0.05946521, 0.00034367797, -0.022994965, 0.05109261, 0.012842341, -0.033276033], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 209, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.23644164, 0.0, 0.19506569, 0.23991182, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, 0.05946521, 9.0, 6.0, 0.05109261, 0.012842341, -0.033276033], "split_indices": [1, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.69186, 6.2176785, 43.474182, 38.468285, 5.005898, 21.64504, 16.823246], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0026260107, -0.071735844, 0.04535004, 0.02980454, -0.06666254, 0.14406222, -0.06648111, 0.020973103, 0.070556805, -0.061542965, 0.026527429], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 210, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.17475533, 0.53723466, 0.41781533, 0.0, 0.0, 0.088599145, 0.39965075, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 7.0, 0.02980454, -0.06666254, 6.0, 5.0, 0.020973103, 0.070556805, -0.061542965, 0.026527429], "split_indices": [2, 0, 2, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.812275, 18.533964, 35.278313, 8.520973, 10.01299, 19.06013, 16.218182, 12.088241, 6.9718895, 8.727295, 7.490887], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.028195541, -0.07382241, 0.011742298, -0.017222071, 0.051253896, 0.00708575, -0.03829261], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 211, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.4574521, 0.0, 0.21387106, 0.17702103, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, -0.07382241, 9.0, 7.0, 0.051253896, 0.00708575, -0.03829261], "split_indices": [1, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.54586, 7.1084533, 41.437405, 35.278336, 6.1590705, 25.665756, 9.612579], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0072752577, 0.04187129, -0.020155247, -0.061688893, 0.030343674, 0.04222478, -0.025958423], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 212, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.21805222, 0.0, 0.46068707, 0.0, 0.4964807, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.04187129, 4.0, -0.061688893, 4.0, 0.04222478, -0.025958423], "split_indices": [0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.021225, 9.373035, 45.64819, 9.535204, 36.112984, 18.90514, 17.207844], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0353952, 0.075378366, -0.027287006, 0.12910707, -0.02481166, -0.05899982, 0.023990821, -0.015632745, 0.07193291], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 213, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.1271657, 0.27476725, 0.36264908, 0.5057733, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 8.0, 8.0, 3.0, -0.02481166, -0.05899982, 0.023990821, -0.015632745, 0.07193291], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.000202, 29.546284, 17.45392, 22.660448, 6.885835, 6.8054724, 10.648447, 8.449806, 14.210642], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.053649504, 0.17216325, -0.09106623, 0.037244637, 0.10591219, 0.017179685, -0.09968567, 0.02167128, -0.0069815936, -0.0023679584, 0.021397978], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 214, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.9482786, 0.68873113, 0.6780659, 0.04644054, 0.0, 0.030808132, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 6.0, 5.0, 4.0, 0.10591219, 3.0, -0.09968567, 0.02167128, -0.0069815936, -0.0023679584, 0.021397978], "split_indices": [0, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.903633, 29.360466, 23.543165, 17.824059, 11.536407, 16.605675, 6.9374905, 12.217944, 5.6061134, 10.671573, 5.934103], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.009723436, -0.14649065, 0.05042337, 0.008844449, -0.09612279, 0.014380507, 0.071974106, -0.01484191, 0.036583006], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 215, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.55217016, 0.6700933, 0.28134614, 0.0, 0.0, 0.28574857, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 2.0, 9.0, 0.008844449, -0.09612279, 7.0, 0.071974106, -0.01484191, 0.036583006], "split_indices": [2, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.05831, 19.635773, 44.42254, 9.887268, 9.748506, 38.67064, 5.751901, 24.021122, 14.649517], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0072132642, -0.02843581, 0.07166831, 0.10191046, -0.0861108, 0.054958854, -0.01549842, -0.056057036, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 216, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.62103015, 0.47903544, 0.0, 0.26202938, 0.37931296, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 3.0, 0.07166831, 6.0, 5.0, 0.054958854, -0.01549842, -0.056057036, -0.0], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [70.82172, 61.763695, 9.058024, 18.178717, 43.584976, 12.186598, 5.992118, 19.332363, 24.252615], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.008840727, -0.057155948, 0.03675216, 0.14740713, -0.037413027, 0.08585983, 0.00096783764, -0.044175196, 0.040010788], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 217, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.43389076, 0.0, 0.3457026, 0.3071175, 0.4763324, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.057155948, 3.0, 4.0, 6.0, 0.08585983, 0.00096783764, -0.044175196, 0.040010788], "split_indices": [1, 0, 2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.67987, 9.567959, 39.11191, 15.839725, 23.272186, 7.215412, 8.624312, 14.6034155, 8.668771], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.08887883, 0.093903854, 0.05771727, 0.15593544, -0.0, -0.006060529, 0.074545704, -0.02091258, 0.06038496], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 218, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.4040451, 0.0, 0.32182792, 0.37612373, 0.5731443, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.093903854, 3.0, 1.0, 8.0, -0.006060529, 0.074545704, -0.02091258, 0.06038496], "split_indices": [1, 0, 2, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.7446, 6.4070506, 58.33755, 20.36458, 37.97297, 6.911731, 13.452849, 28.346205, 9.626766], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.022851888, -0.14747539, 0.014459004, -0.07079371, -0.00016287006, 0.14913325, -0.079350434, 0.116435446, 0.002136596, -0.05558959, 0.020722084], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 219, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.28221872, 0.15732118, 0.58036584, 0.0, 0.0, 0.60371447, 0.43979394, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 6.0, 6.0, -0.07079371, -0.00016287006, 4.0, 5.0, 0.116435446, 0.002136596, -0.05558959, 0.020722084], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.375355, 12.926533, 43.448822, 7.2643104, 5.662222, 18.034487, 25.414337, 5.8136897, 12.220798, 15.077212, 10.337124], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.15069288, -0.06353722, 0.07569413, 0.0017143963, -0.098703675, -0.0, 0.04617882, -0.04132018], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 220, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.51705396, 0.20698717, 0.6306092, 0.0, 0.0, 0.0, 0.70667964, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 4.0, 2.0, 0.07569413, 0.0017143963, -0.098703675, 6.0, 0.04617882, -0.04132018], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.546852, 14.898652, 37.6482, 7.918296, 6.9803557, 6.2668643, 31.381336, 14.541997, 16.83934], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.056432433, -0.102146044, 0.045427367, -0.0, -0.19762443, -0.0042631906, 0.035712022, -0.042747714, 0.04487837, -0.03308144, -0.08020973], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 221, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.23851393, 0.35841274, 0.07888596, 0.39151484, 0.045319617, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, 7.0, 3.0, 5.0, -0.0042631906, 0.035712022, -0.042747714, 0.04487837, -0.03308144, -0.08020973], "split_indices": [2, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.008434, 33.81262, 14.195817, 16.335255, 17.477364, 7.53763, 6.6581864, 8.063215, 8.272038, 9.541449, 7.935915], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.028370779, -0.03302486, 0.053257227, 0.0068277176, 0.056043588, 0.021454688, -0.030359536], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 222, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.19801602, 0.0, 0.28912446, 0.2744372, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.03302486, 8.0, 7.0, 0.056043588, 0.021454688, -0.030359536], "split_indices": [2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.416645, 7.461815, 48.95483, 37.661808, 11.293024, 24.34643, 13.315377], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.031963117, -0.014722834, 0.1370327, -0.08333272, 0.03359864, 0.073140174, 0.010689089, -0.0, -0.037019912], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 223, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.24213162, 0.28823343, 0.1262865, 0.075856626, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 8.0, 2.0, 0.03359864, 0.073140174, 0.010689089, -0.0, -0.037019912], "split_indices": [1, 2, 1, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.45676, 31.408869, 14.047893, 21.110441, 10.298429, 5.678486, 8.369407, 7.04564, 14.064801], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.08209534, -0.17305207, -0.030188685, -0.013417783, -0.074310854, -0.0837443, 0.049912035, 0.03837396, -0.055028453], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 224, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.22422689, 0.13508648, 0.3803668, 0.0, 0.0, 0.6416707, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 1.0, 8.0, -0.013417783, -0.074310854, 2.0, 0.049912035, 0.03837396, -0.055028453], "split_indices": [2, 2, 1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.904366, 17.3105, 34.59387, 7.447244, 9.863255, 28.037445, 6.556424, 8.565041, 19.472403], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.013431217, -0.040709775, 0.0615048, -0.0, -0.080814354, -0.030396817, 0.026963899], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 225, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.38184118, 0.5421787, 0.0, 0.47934082, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, 0.0615048, 6.0, -0.080814354, -0.030396817, 0.026963899], "split_indices": [2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.43557, 58.41271, 6.0228605, 50.58432, 7.8283863, 23.930328, 26.653992], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.09696632, -0.03624293, -0.039513983, 0.06998295, -0.13117608, 0.068522446, -0.053072136, -0.0, 0.04008235, -0.025037289], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 226, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.245886, 0.6411586, 0.50604475, 0.0, 0.0, 0.16557828, 0.2497558, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 3.0, 7.0, -0.039513983, 0.06998295, 8.0, 8.0, -0.053072136, -0.0, 0.04008235, -0.025037289], "split_indices": [0, 2, 0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.604324, 18.257315, 48.34701, 6.5570455, 11.700268, 25.732197, 22.614813, 18.988533, 6.743664, 16.379269, 6.235546], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.07600624, -0.048509076, -0.0075529967, 0.1443781, 0.009361349, -0.08651907, 0.07972905, 0.0024771788, -0.052151963, 0.051393017, -0.06652522, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 227, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.1946148, 0.15700129, 0.08003597, 0.0, 0.18420392, 0.40772933, 0.2398908, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [2.0, 3.0, 4.0, -0.0075529967, 1.0, 6.0, 6.0, 0.07972905, 0.0024771788, -0.052151963, 0.051393017, -0.06652522, -0.0], "split_indices": [1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.955696, 19.445925, 31.509771, 7.50642, 11.939505, 11.747288, 19.762484, 5.419569, 6.5199356, 5.1877947, 6.559493, 7.039092, 12.723392], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0101768235, 0.034044042, -0.15073007, 0.09040346, -0.04106801, -0.106412314, 0.012073481, 0.101526424, -0.0, -0.05316071, 0.028193321], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 228, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.42330992, 0.22041841, 0.68372595, 0.66259414, 0.41788784, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 6.0, 2.0, 4.0, -0.106412314, 0.012073481, 0.101526424, -0.0, -0.05316071, 0.028193321], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.40379, 49.025654, 15.378141, 28.653563, 20.372091, 7.2745633, 8.103578, 6.901457, 21.752106, 10.3584585, 10.013633], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.04992426, 0.13764161, -0.058999438, 0.08248632, 0.04449805, -0.09384387, 0.008233415, -0.0, 0.037506938, -0.0, -0.04760918], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 229, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.5598227, 0.3694104, 0.08627095, 0.0, 0.08294643, 0.10951464, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 7.0, 0.08248632, 4.0, 3.0, 0.008233415, -0.0, 0.037506938, -0.0, -0.04760918], "split_indices": [1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.919548, 31.337538, 24.58201, 11.442858, 19.89468, 18.28343, 6.2985806, 12.941176, 6.9535036, 8.191928, 10.091503], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.00031816546, -0.15992992, 0.056242228, -0.069225326, -0.01151384, 0.09547208, -0.031446796, 0.07411957, -0.0070685786], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 230, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.49046707, 0.084711015, 0.2737357, 0.0, 0.0, 0.6544202, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [2.0, 7.0, 8.0, -0.069225326, -0.01151384, 5.0, -0.031446796, 0.07411957, -0.0070685786], "split_indices": [1, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.882412, 12.995742, 40.88667, 7.1239076, 5.8718348, 33.705326, 7.181344, 14.721542, 18.983784], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.038490977, 0.083171, -0.097372845, 0.0952201, -0.06904237, -0.011431506], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 231, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.548933, 0.91756034, 0.0, 0.26509103, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 9.0, 0.083171, 2.0, 0.0952201, -0.06904237, -0.011431506], "split_indices": [1, 2, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.667313, 42.36117, 5.3061404, 37.211884, 5.149289, 10.074112, 27.137772], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.037053343, -0.076064505, 0.10359261, -0.086623184, 0.020192822, 0.10475476, 0.039781228, 0.08163683, -0.045268614, -0.0032123006, 0.028054254], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 232, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.42409733, 0.44960198, 0.50460255, 0.0, 0.68380797, 0.0, 0.08981109, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 2.0, -0.086623184, 6.0, 0.10475476, 6.0, 0.08163683, -0.045268614, -0.0032123006, 0.028054254], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.932503, 19.365244, 34.567257, 5.7343774, 13.630867, 5.8836646, 28.683592, 5.51051, 8.120357, 14.241536, 14.442057], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.01936907, 0.08881084, -0.020120032, -0.060048018, 0.03319639, -0.032891147, 0.037816823], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 233, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.54973274, 0.0, 0.4261705, 0.0, 0.45059064, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.08881084, 3.0, -0.060048018, 4.0, -0.032891147, 0.037816823], "split_indices": [2, 0, 2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.24661, 5.2812195, 40.96539, 9.158882, 31.806505, 12.035834, 19.770672], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.05359312, -0.09480123, 0.03070893, -0.05010702, -0.1902069, -0.034458216, 0.051763944, 0.0028383748, -0.13123083], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 234, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.35605964, 0.15298727, 0.0, 0.4734506, 0.6830006, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.03070893, 4.0, 5.0, -0.034458216, 0.051763944, 0.0028383748, -0.13123083], "split_indices": [1, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.0112, 42.74902, 10.262182, 30.863777, 11.8852415, 24.633068, 6.23071, 6.883341, 5.0019007], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.072210975, -0.23998807, -0.009992728, -0.04186444, -0.089845285, 0.04969677, -0.07263743, -0.0047822203, 0.06472335, -0.055188045, 0.0149650695], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 235, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.6717449, 0.01971674, 0.19411953, 0.0, 0.0, 0.30190963, 0.37425345, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 7.0, -0.04186444, -0.089845285, 8.0, 5.0, -0.0047822203, 0.06472335, -0.055188045, 0.0149650695], "split_indices": [0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.975266, 16.639883, 49.33539, 8.059805, 8.580077, 24.551992, 24.783394, 17.81257, 6.7394223, 13.150009, 11.633384], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.03594148, -0.03997693, 0.068100974, -0.04274896, 0.18119751, -0.03723484, 0.07350442, 0.11974216, 0.010376645], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 236, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.3667224, 0.0, 0.7411375, 0.705489, 0.87094235, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.03997693, 5.0, 9.0, 4.0, -0.03723484, 0.07350442, 0.11974216, 0.010376645], "split_indices": [0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.92551, 9.530487, 56.39502, 28.347975, 28.047047, 22.813496, 5.5344787, 10.312416, 17.73463], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.057900514, 0.077712834, -0.011017327, 0.046314348, 0.058453742, 0.023124512, -0.021295253], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 237, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.10732508, 0.13407049, 0.0, 0.14264101, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 6.0, -0.011017327, 4.0, 0.058453742, 0.023124512, -0.021295253], "split_indices": [1, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.539303, 44.46489, 8.07441, 36.839012, 7.62588, 30.131037, 6.7079744], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.040297367, -0.058292843, 0.03598835, -0.009266726, -0.1157137, 0.09281787, -0.030910904, -0.1455596, -4.332171e-05], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 238, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.19287807, 0.15844749, 0.0, 1.0567178, 1.109301, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 6.0, 0.03598835, 2.0, 2.0, 0.09281787, -0.030910904, -0.1455596, -4.332171e-05], "split_indices": [0, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.59389, 60.10494, 5.488951, 33.988422, 26.116518, 6.9623046, 27.026117, 5.254337, 20.86218], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0111989165, -0.08725929, 0.025395587, 0.13265988, -0.05251019, 0.0069048335, 0.103446126, 0.035059333, -0.054124463], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 239, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.59514356, 0.0, 0.42524362, 0.45492926, 0.64736736, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.08725929, 4.0, 3.0, 6.0, 0.0069048335, 0.103446126, 0.035059333, -0.054124463], "split_indices": [2, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.836643, 5.7416563, 48.094986, 20.479137, 27.615849, 14.543129, 5.936009, 11.582496, 16.033352], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.033530816, -0.08594127, 0.033613756, 0.05307975, -0.19738527, 0.05464336, -0.039096117, 0.09841334, -0.04813538, -0.105278105, -0.016169362, -0.050878808, 0.027496474], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 240, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.20079139, 0.51861537, 0.27936187, 0.9247157, 0.34374696, 0.0, 0.30326915, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 7.0, 2.0, 7.0, 0.05464336, 5.0, 0.09841334, -0.04813538, -0.105278105, -0.016169362, -0.050878808, 0.027496474], "split_indices": [2, 0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.7501, 30.86638, 22.88372, 13.567712, 17.29867, 7.478001, 15.405719, 5.901002, 7.6667104, 7.321509, 9.97716, 7.9443493, 7.46137], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0067976406, -0.059544217, 0.13590272, 0.14858691, -0.17191972, -0.054571327, 0.1353005, 0.098653264, -0.013868914, -0.10519809, -0.020817872], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 241, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.37243634, 0.90003586, 1.4238203, 0.51047814, 0.39163363, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 3.0, 5.0, 5.0, 6.0, -0.054571327, 0.1353005, 0.098653264, -0.013868914, -0.10519809, -0.020817872], "split_indices": [0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.419712, 36.289474, 12.1302395, 12.298766, 23.990707, 6.091615, 6.038624, 6.2973638, 6.0014024, 7.507295, 16.483412], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0054502287, -0.033715665, 0.026117923, 0.13511604, -0.028879499, 0.09756056, -0.03043321, 0.029388795, -0.027099237], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 242, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.21062773, 0.0, 0.28798968, 0.7786274, 0.24608569, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.033715665, 5.0, 6.0, 4.0, 0.09756056, -0.03043321, 0.029388795, -0.027099237], "split_indices": [0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.969315, 13.351049, 44.618263, 15.084379, 29.533886, 8.390377, 6.694002, 8.913662, 20.620224], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.029491587, 0.04845367, -0.008988599, 0.059754647, -0.041458603, -0.007467458, 0.07781727], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 243, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.26016665, 0.0, 0.34890357, 0.44644937, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.04845367, 6.0, 8.0, -0.041458603, -0.007467458, 0.07781727], "split_indices": [2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.924004, 10.455153, 36.46885, 23.649776, 12.819072, 16.87089, 6.7788873], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.02121757, 0.061294608, -0.039572578, -0.014394035, 0.15112238, 0.069570705, -0.08659546, 0.021013292, -0.036639377, 0.024708405, 0.0630918, 0.050304797, -0.004736959], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 244, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.13091643, 0.2377092, 0.59159726, 0.17768024, 0.016950577, 0.13997611, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 5.0, 2.0, 6.0, 8.0, -0.08659546, 0.021013292, -0.036639377, 0.024708405, 0.0630918, 0.050304797, -0.004736959], "split_indices": [0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.848797, 31.631708, 19.217087, 17.018492, 14.613217, 13.58397, 5.633117, 9.216665, 7.8018255, 8.6475525, 5.9656653, 6.4162927, 7.1676774], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.039296363, -0.066190206, 0.051875185, -0.1642601, -0.0013084402, 0.030089045, -0.0816002, -0.027527465, 0.055452734], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 245, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.35934693, 0.3452973, 0.0, 0.672526, 0.6115681, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, 0.051875185, 2.0, 6.0, 0.030089045, -0.0816002, -0.027527465, 0.055452734], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.827953, 55.90272, 5.9252334, 21.035002, 34.867718, 5.838789, 15.196214, 24.168594, 10.699124], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.007434764, 0.05601825, -0.13463868, -0.026838502, 0.1590689, -0.30805108, 0.027554443, 0.015266158, -0.042960413, 0.12278264, -0.026817683, -0.11197082, -0.047589917], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 246, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.56691104, 0.4082899, 0.97519684, 0.24885197, 1.3791127, 0.022233486, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 9.0, 3.0, 8.0, 6.0, 0.027554443, 0.015266158, -0.042960413, 0.12278264, -0.026817683, -0.11197082, -0.047589917], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [67.32791, 44.769207, 22.558702, 24.68814, 20.081066, 12.822442, 9.73626, 14.623523, 10.064617, 9.883437, 10.19763, 7.4187493, 5.4036922], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.029581642, 0.17255294, -0.098921396, -0.0, 0.094826646, -0.19145097, 0.003919694, -0.11887227, -0.010983378, 0.04944446, -0.018470116], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 247, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.70786035, 0.2838128, 0.38198638, 0.0, 0.0, 0.59490085, 0.21549329, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 1.0, 6.0, -0.0, 0.094826646, 2.0, 7.0, -0.11887227, -0.010983378, 0.04944446, -0.018470116], "split_indices": [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.811367, 11.718805, 37.09256, 6.087887, 5.6309185, 19.583439, 17.509123, 7.4824004, 12.101039, 5.0679283, 12.441194], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.05068306, -0.30846226, 0.02185774, -0.14308697, -0.036362864, 0.06364444, -0.040052835, -0.0, 0.04675381, -0.07281689, 0.024724495], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 248, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [1.2435977, 0.34169102, 0.13698453, 0.0, 0.0, 0.19889465, 0.5379603, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 4.0, 5.0, -0.14308697, -0.036362864, 8.0, 7.0, -0.0, 0.04675381, -0.07281689, 0.024724495], "split_indices": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.611046, 13.557987, 50.053055, 6.0409966, 7.5169907, 30.830896, 19.22216, 18.333302, 12.497594, 7.178002, 12.044159], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.05374471, 0.00097055803, -0.16814025, -0.046669208, 0.09395878, -0.06744216, -0.019062568, 0.052442424, -0.042351943, -0.0, 0.04449772], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 249, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.36248004, 0.17635821, 0.066307604, 0.5429188, 0.0660743, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 7.0, 7.0, 3.0, 4.0, -0.06744216, -0.019062568, 0.052442424, -0.042351943, -0.0, 0.04449772], "split_indices": [2, 1, 1, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.21485, 36.753193, 17.461658, 23.920794, 12.832399, 9.960309, 7.5013494, 6.5836353, 17.337158, 5.2785015, 7.553898], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.01419099, -0.14853632, 0.068295255, -0.05751683, -0.021709695, 0.14055681, -0.028242374, 0.07680385, 0.023881195, 0.02412396, -0.03551683], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 250, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.48791626, 0.0045264065, 0.312068, 0.0, 0.0, 0.122680455, 0.19411361, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 1.0, 6.0, -0.05751683, -0.021709695, 3.0, 4.0, 0.07680385, 0.023881195, 0.02412396, -0.03551683], "split_indices": [1, 1, 1, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.177498, 12.621785, 41.555714, 6.2489614, 6.372824, 24.097692, 17.45802, 6.6536493, 17.444044, 7.4622903, 9.995729], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.047788724, -0.22870784, 0.025074968, 0.0027165664, -0.11037185, 0.052345157, -0.010597195, -0.019559147, 0.047131967], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 251, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.6772251, 0.52487844, 0.20861214, 0.0, 0.0, 0.0, 0.26876637, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 3.0, 5.0, 0.0027165664, -0.11037185, 0.052345157, 8.0, -0.019559147, 0.047131967], "split_indices": [0, 1, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.393524, 13.674239, 34.719284, 5.1293697, 8.544869, 6.482027, 28.237259, 22.212858, 6.024401], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0036090415, -0.07263252, 0.032454733, 0.13030778, -0.009200499, 0.06270361, 0.00062412105, 0.009790943, -0.024540344], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 252, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.5175534, 0.0, 0.21660632, 0.1366472, 0.11450401, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.07263252, 3.0, 7.0, 6.0, 0.06270361, 0.00062412105, 0.009790943, -0.024540344], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.821278, 6.9458756, 48.875404, 14.674855, 34.200546, 8.219785, 6.45507, 21.172636, 13.027912], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.07497366, -0.10800749, 0.024621194, -0.05875417, -0.0928606, -0.006846564, 0.024671726, 0.03383688, -0.03774799], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 253, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.17703474, 0.33047637, 0.04234417, 0.3933756, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [8.0, 8.0, 5.0, 2.0, -0.0928606, -0.006846564, 0.024671726, 0.03383688, -0.03774799], "split_indices": [2, 1, 1, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.467133, 37.836906, 11.630226, 31.842392, 5.994514, 5.757658, 5.872568, 8.339878, 23.502514], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.015252117, 0.025698354, -0.051672906, -0.03782305, 0.11779656, 0.00730075, -0.059483733, 0.09448827, 0.003018752], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 254, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.36369282, 0.26330534, 0.0, 0.27646095, 0.35491914, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, -0.051672906, 4.0, 2.0, 0.00730075, -0.059483733, 0.09448827, 0.003018752], "split_indices": [0, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.603973, 41.879257, 10.724716, 24.491, 17.38826, 17.814838, 6.67616, 5.1364026, 12.251858], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.034146212, -0.04320166, 0.066679224, 0.14793536, -0.034309633, 0.0006645468, 0.13043752, 0.00873213, -0.042183153], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 255, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.35897452, 0.0, 0.4534735, 1.219784, 0.17709294, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.04320166, 7.0, 8.0, 6.0, 0.0006645468, 0.13043752, 0.00873213, -0.042183153], "split_indices": [0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.61431, 8.356055, 52.258255, 29.24939, 23.008863, 20.295702, 8.953688, 14.276987, 8.731877], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.052558005, -0.07064246, -0.016520206, 0.081577085, -0.07383346, -0.0002994957, 0.04818579, -0.06590329, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 256, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.42247647, 0.0, 0.33561477, 0.15646198, 0.41069967, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.07064246, 4.0, 5.0, 6.0, -0.0002994957, 0.04818579, -0.06590329, -0.0], "split_indices": [0, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [67.50295, 9.742752, 57.7602, 20.501577, 37.25862, 9.89084, 10.610738, 11.832248, 25.426374], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.10140375, -0.0, 0.12646046, 0.20954774, 0.057478596, 0.026696231, 0.103155546, -0.020447332, 0.068430975], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 257, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.14030904, 0.0, 0.20023698, 0.21560752, 0.56354874, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.0, 3.0, 2.0, 6.0, 0.026696231, 0.103155546, -0.020447332, 0.068430975], "split_indices": [1, 0, 2, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.203545, 9.507048, 40.6965, 16.878866, 23.817633, 10.16302, 6.715845, 13.725538, 10.092093], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.051648602, -0.08093931, -0.017891767, 0.049234018, -0.16075976, 0.07108879, -0.033578265, -0.104512066, -0.007199252], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 258, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.42874718, 0.0, 0.5545671, 1.1971381, 0.43061972, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.08093931, 7.0, 4.0, 4.0, 0.07108879, -0.033578265, -0.104512066, -0.007199252], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.784077, 6.945427, 54.83865, 37.278828, 17.559822, 17.289433, 19.989395, 6.4428606, 11.1169615], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.049765702, 0.00032012802, 0.0544837, 0.089454666, -0.08148401, 0.0711978, -0.00026398854, -0.07853931, 0.01677483], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 259, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.3226102, 0.29667625, 0.0, 0.2910428, 0.54500514, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, 0.0544837, 4.0, 7.0, 0.0711978, -0.00026398854, -0.07853931, 0.01677483], "split_indices": [2, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.285545, 38.578564, 12.706981, 19.002344, 19.57622, 7.0089703, 11.993375, 8.389239, 11.18698], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.09866489, 0.07741034, 0.0649096, -0.03609636, 0.13235079, 0.0127417045, 0.07222047], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 260, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.089156985, 0.5394683, 0.0, 0.0, 0.33649248, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 2.0, 0.0649096, -0.03609636, 5.0, 0.0127417045, 0.07222047], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.077263, 47.08235, 5.994911, 9.582958, 37.499393, 21.76505, 15.734343], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.06740225, 0.0009308396, -0.1638113, -0.08345081, 0.10217265, -0.109070584, -0.03189005, 0.009823443, -0.0472004, -0.060077783, 0.041141108], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 261, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.32911497, 0.861265, 0.48378998, 0.2161428, 0.0, 0.0, 0.4214742, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 8.0, 3.0, 0.10217265, -0.109070584, 6.0, 0.009823443, -0.0472004, -0.060077783, 0.041141108], "split_indices": [0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.461735, 27.162508, 19.299227, 22.125502, 5.037007, 6.676134, 12.623093, 8.290512, 13.83499, 6.527672, 6.0954204], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0729927, -0.079218194, -0.03784006, 0.044562303, -0.17415607, -0.006355803, 0.06715894, -0.0063375714, -0.13564801], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 262, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.35400686, 0.0, 0.5955996, 0.40503407, 0.7915418, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.079218194, 6.0, 8.0, 6.0, -0.006355803, 0.06715894, -0.0063375714, -0.13564801], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.689278, 7.510467, 50.178814, 31.236193, 18.94262, 23.09247, 8.143723, 13.143375, 5.799244], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.029209463, -0.04740243, 0.058419973, 0.010894554, -0.07390747, -0.054058734, -0.0010554517], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 263, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.6104844, 0.07746875, 0.0, 0.0, 0.1721367, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 2.0, 0.058419973, 0.010894554, 6.0, -0.054058734, -0.0010554517], "split_indices": [0, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.25159, 31.008795, 14.242796, 6.460371, 24.548424, 8.592712, 15.955711], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.037096187, -0.15686888, 0.061158933, 0.022979036, -0.23993325, -0.026353171, 0.118901685, -0.093866214, -0.01147917, 0.04842665, -0.07976884, 0.06825755, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 264, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6756501, 0.5365671, 0.16856068, 0.0, 0.24194765, 0.61184317, 0.26179793, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 4.0, 0.022979036, 9.0, 2.0, 6.0, -0.093866214, -0.01147917, 0.04842665, -0.07976884, 0.06825755, -0.0], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.7843, 24.878592, 29.905712, 6.2331915, 18.6454, 11.415987, 18.489725, 12.923104, 5.7222958, 6.348289, 5.0676985, 9.379755, 9.109969], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.094751015, -0.14109087, 0.029571882, -0.08554669, -0.14153585, -0.07050875, 0.061018903, -0.0, -0.04697485], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 265, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.38094914, 0.76705074, 0.8483045, 0.2634398, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [8.0, 7.0, 5.0, 5.0, -0.14153585, -0.07050875, 0.061018903, -0.0, -0.04697485], "split_indices": [1, 1, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.464382, 45.991203, 16.47318, 40.7139, 5.2773, 6.163503, 10.309677, 18.51838, 22.195522], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.02005211, -0.043062247, 0.038606975, 0.021720512, -0.10320158, -0.065309174, 0.032426085, 0.045206428, -0.052081663], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 266, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.2311486, 0.24642591, 0.0, 0.61618805, 0.60746324, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 4.0, 0.038606975, 3.0, 2.0, -0.065309174, 0.032426085, 0.045206428, -0.052081663], "split_indices": [2, 1, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [67.419174, 59.58437, 7.8348045, 28.191835, 31.392534, 6.763672, 21.428164, 6.259372, 25.133162], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.023313511, 0.07247753, -0.06942578, 0.015455946, 0.16126625, 0.021960841, -0.036729507, 0.05872977, 0.022148538], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 267, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.66283596, 0.20966925, 0.0, 0.23841733, 0.009751648, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, -0.06942578, 6.0, 5.0, 0.021960841, -0.036729507, 0.05872977, 0.022148538], "split_indices": [0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.85644, 44.39596, 7.4604797, 28.434927, 15.961034, 20.883383, 7.551546, 9.736711, 6.2243237], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.018257305, 0.038346186, -0.08565471, -0.11090797, 0.14329204, -0.24631618, 0.047802355, -0.0530485, -0.0, 0.12210402, -0.00034968051, -0.010710778, -0.11039187, -0.062343873, 0.08998659], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 268, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.25975496, 0.5791101, 0.70140535, 0.11043395, 0.8723491, 0.32254928, 1.2034256, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 8.0, 5.0, 4.0, 3.0, 4.0, -0.0530485, -0.0, 0.12210402, -0.00034968051, -0.010710778, -0.11039187, -0.062343873, 0.08998659], "split_indices": [1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.988014, 34.750355, 30.237663, 13.929753, 20.8206, 13.676699, 16.560965, 8.405018, 5.524735, 6.970322, 13.850278, 5.7821035, 7.894595, 8.114403, 8.446561], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.008675727, 0.09197946, -0.05691036, -0.005970758, 0.21897647, -0.10767633, 0.04230939, 0.021803144, -0.044689737, 0.031882618, 0.08516106, -0.011335002, -0.060579386], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 269, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.31434765, 0.34221828, 0.32579058, 0.18753685, 0.016085744, 0.13858673, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 9.0, 3.0, 2.0, 7.0, 0.04230939, 0.021803144, -0.044689737, 0.031882618, 0.08516106, -0.011335002, -0.060579386], "split_indices": [0, 2, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.935295, 24.69341, 30.241884, 14.034472, 10.658938, 24.833954, 5.407929, 8.920554, 5.1139183, 5.421708, 5.2372303, 15.673431, 9.160523], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.017089989, 0.120336026, -0.056429956, -0.021735694, 0.25902534, -0.10290265, 0.0371673, 0.09673528, 0.04404731, 0.00155709, -0.10603077], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 270, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.43865287, 0.68442214, 0.28596768, 0.0, 0.009584665, 0.7707984, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 7.0, -0.021735694, 7.0, 4.0, 0.0371673, 0.09673528, 0.04404731, 0.00155709, -0.10603077], "split_indices": [0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.05213, 23.197979, 31.85415, 9.604112, 13.593866, 26.10915, 5.7450023, 6.918061, 6.675806, 18.644909, 7.4642406], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.06472535, 0.015428648, 0.117242426, 0.09862232, -0.033016805, 0.07967544, 0.03027115, 0.05468008, -0.011411918, -0.022875808, 0.0409453], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 271, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.1098541, 0.29183617, 0.2491881, 0.21862623, 0.0, 0.0, 0.19257122, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 7.0, 7.0, -0.033016805, 0.07967544, 6.0, 0.05468008, -0.011411918, -0.022875808, 0.0409453], "split_indices": [1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.2557, 26.024742, 21.230959, 16.257404, 9.767337, 6.6628942, 14.568065, 10.331804, 5.9256, 6.9441648, 7.6239], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.053731136, -0.062110458, -0.025224626, -0.0, -0.052389726, -0.010633159, 0.025559032], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 272, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.2106797, 0.0, 0.18469249, 0.13042916, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.062110458, 8.0, 5.0, -0.052389726, -0.010633159, 0.025559032], "split_indices": [2, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.71844, 6.8906846, 46.827755, 40.77368, 6.0540733, 28.996086, 11.777595], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.030942498, -0.08584472, 0.0059910617, 0.047422778, -0.046726726, -0.03687836, 0.045306556], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 273, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.48127243, 0.0, 0.3591584, 0.0, 0.49544466, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.08584472, 2.0, 0.047422778, 8.0, -0.03687836, 0.045306556], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.905514, 5.419222, 41.486294, 10.652731, 30.83356, 22.895128, 7.938433], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0007773719, 0.050893083, -0.0515927, -0.06595622, 0.13201904, 0.008744259, -0.059807416, -0.0077495263, 0.055128977], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 274, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.46087763, 0.4208849, 0.0, 0.2473117, 0.23364204, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, -0.0515927, 2.0, 2.0, 0.008744259, -0.059807416, -0.0077495263, 0.055128977], "split_indices": [0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.62125, 41.78397, 10.837282, 16.693672, 25.090296, 9.764404, 6.9292693, 5.8407393, 19.249556], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.013870106, -0.04592704, 0.07598087, 0.019508269, -0.15666392, 0.03949383, -0.027031263, -0.07863787, -0.021064566], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 275, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.5311323, 0.43346655, 0.0, 0.46427402, 0.14928025, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 6.0, 0.07598087, 5.0, 4.0, 0.03949383, -0.027031263, -0.07863787, -0.021064566], "split_indices": [2, 2, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.084404, 56.506054, 5.578349, 35.46833, 21.037727, 17.883799, 17.58453, 8.040221, 12.997506], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.005600709, 0.12691107, -0.07919355, 0.05290759, 0.0827297, -0.0, -0.18624815, 0.050319422, -0.047537647, -0.031831596, 0.060328186, -0.11038511, -0.007490954], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 276, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6702298, 0.21963242, 0.38295284, 0.46147496, 0.0, 0.5936426, 0.5131433, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 6.0, 2.0, 0.0827297, 4.0, 8.0, 0.050319422, -0.047537647, -0.031831596, 0.060328186, -0.11038511, -0.007490954], "split_indices": [2, 2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [67.18668, 23.246159, 43.94053, 16.880964, 6.3651934, 25.775532, 18.164997, 11.378955, 5.50201, 17.147978, 8.627553, 7.6475134, 10.517485], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.042568363, -0.10519654, 0.08426601, -0.1862225, -0.010852694, 0.04694832, -0.000774221, -0.010684216, -0.076083355, -0.016845966, 0.016433591], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 277, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.4401099, 0.26218113, 0.12799168, 0.1584158, 0.059084266, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 8.0, 1.0, 3.0, 0.04694832, -0.000774221, -0.010684216, -0.076083355, -0.016845966, 0.016433591], "split_indices": [1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.026237, 36.140057, 16.88618, 18.348162, 17.791895, 9.409782, 7.4763994, 6.6031203, 11.745041, 11.513228, 6.278666], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.026955185, -0.09936227, 0.06520159, -0.075510725, 0.025296943, 0.12217419, -0.023472076, 0.01477065, 0.04296598, -0.04572661, 0.020225776], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 278, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.3112936, 0.44696814, 0.26626527, 0.0, 0.0, 0.021905154, 0.2506385, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 5.0, 5.0, -0.075510725, 0.025296943, 2.0, 7.0, 0.01477065, 0.04296598, -0.04572661, 0.020225776], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.80027, 13.616406, 49.183865, 7.5384693, 6.077937, 30.40114, 18.782722, 8.619463, 21.78168, 7.9405284, 10.842194], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.018485157, 0.1330977, -0.07947531, 0.068998575, 0.03132038, -0.16377006, 0.019363133, -0.03610529, 0.05098189, -0.012390474, -0.07131496], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 279, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.64451015, 0.2294266, 0.3888336, 0.0, 0.33354178, 0.14213073, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 6.0, 0.068998575, 8.0, 7.0, 0.019363133, -0.03610529, 0.05098189, -0.012390474, -0.07131496], "split_indices": [0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.984867, 25.643927, 29.340942, 11.98819, 13.655736, 18.859587, 10.481355, 6.239321, 7.416415, 8.196157, 10.66343], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011805313, -0.0971469, 0.038488235, 0.15226525, -0.042127103, 0.13488245, 0.0013019997, -0.08012142, 0.015072906], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 280, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.76649284, 0.0, 0.48608997, 0.91683346, 0.66208494, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.0971469, 4.0, 3.0, 4.0, 0.13488245, 0.0013019997, -0.08012142, 0.015072906], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.684566, 5.5885205, 50.096046, 20.920883, 29.175161, 6.0481396, 14.872743, 8.2574625, 20.9177], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.1425889, -0.07855979, -0.07759753, -0.13319013, 0.04250361, -0.0008825123, -0.053807523], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 281, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.27368456, 0.38392264, 0.0, 0.13305756, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 6.0, -0.07759753, 1.0, 0.04250361, -0.0008825123, -0.053807523], "split_indices": [1, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [43.0977, 29.41143, 13.686268, 24.246391, 5.165038, 7.1905, 17.055893], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.012594198, -0.070572905, 0.06592363, -0.000631011, -0.06289096, 0.1609496, -0.03216544, -0.019237785, 0.014223315, 0.014960835, 0.0758153], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 282, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.2516565, 0.29366705, 0.39756036, 0.0749265, 0.0, 0.11499402, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 8.0, 6.0, -0.06289096, 6.0, -0.03216544, -0.019237785, 0.014223315, 0.014960835, 0.0758153], "split_indices": [1, 1, 1, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.156315, 31.368292, 21.788021, 22.017998, 9.350295, 14.485742, 7.30228, 10.236119, 11.781878, 7.7445383, 6.7412033], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0299899, -0.13884959, 0.032580283, -0.077198446, -0.0047078966, -0.02208241, 0.074363954, 0.04147823, -0.029099457], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 283, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.34136337, 0.23500565, 0.38863656, 0.0, 0.0, 0.309383, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 5.0, 8.0, -0.077198446, -0.0047078966, 3.0, 0.074363954, 0.04147823, -0.029099457], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.89237, 17.253613, 29.638754, 7.824709, 9.428904, 23.982716, 5.6560388, 6.9427266, 17.039988], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.036143843, -0.0760818, 0.13362299, -0.056502637, -0.020800883, 0.09861532, -0.003745203, 0.020506963, -0.040948946, -0.016483463, 0.019416206], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 284, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.58612394, 0.12652256, 0.8037834, 0.0, 0.2024641, 0.0, 0.064096354, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 3.0, -0.056502637, 7.0, 0.09861532, 8.0, 0.020506963, -0.040948946, -0.016483463, 0.019416206], "split_indices": [0, 2, 2, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.12261, 23.389, 27.733608, 6.326229, 17.06277, 11.184303, 16.549305, 9.379538, 7.6832333, 10.417232, 6.1320734], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.01764507, -0.066028275, 0.037719745, 0.18262945, -0.011621996, 0.11337348, -0.0015093414, -0.040985756, 0.028113833], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 285, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.7184597, 0.0, 0.36915323, 0.5158175, 0.5033945, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.066028275, 5.0, 5.0, 6.0, 0.11337348, -0.0015093414, -0.040985756, 0.028113833], "split_indices": [2, 0, 2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.731415, 12.790826, 47.940586, 12.006007, 35.93458, 5.6478505, 6.358156, 16.731836, 19.202745], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.015265598, 0.01016021, -0.049695153, -0.0048805946, 0.035973206, 0.0047002872, -0.024735367], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 286, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.27571717, 0.10815836, 0.0, 0.08980839, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, -0.049695153, 6.0, 0.035973206, 0.0047002872, -0.024735367], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.60152, 57.19838, 9.403139, 50.343414, 6.8549643, 39.412136, 10.931278], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.02164534, 0.059890892, -0.07529971, -0.027263058, 0.17269158, -0.07561992, 0.052928854, 0.03362837, -0.030448763, 0.0832257, -0.0040681497], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 287, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.19449215, 0.39615935, 0.68908864, 0.23781814, 0.36851454, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 6.0, 1.0, 4.0, -0.07561992, 0.052928854, 0.03362837, -0.030448763, 0.0832257, -0.0040681497], "split_indices": [2, 2, 1, 2, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.65531, 37.33866, 13.316652, 20.991583, 16.347076, 8.055743, 5.2609086, 6.650121, 14.341461, 10.493903, 5.8531747], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.041471053, 0.08459095, -0.081871025, -0.004311043, 0.21834259, -0.066620514, 0.012903902, 0.019409359, -0.042062916, 0.014543448, 0.15317832], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 288, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.33805722, 0.58343834, 0.30797642, 0.28744355, 0.8779303, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 6.0, 6.0, 7.0, -0.066620514, 0.012903902, 0.019409359, -0.042062916, 0.014543448, 0.15317832], "split_indices": [0, 0, 2, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.209236, 46.18728, 15.021958, 27.87767, 18.309608, 7.087704, 7.9342546, 18.348463, 9.529206, 12.533093, 5.776515], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.015908426, -0.042598482, 0.05721765, -0.025064543, 0.19602858, 0.0029076051, -0.03708876, 0.096676745, -0.0015312587], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 289, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.36607975, 0.0, 0.5444653, 0.11246424, 0.4799229, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.042598482, 7.0, 5.0, 8.0, 0.0029076051, -0.03708876, 0.096676745, -0.0015312587], "split_indices": [2, 0, 1, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.176666, 10.434177, 44.74249, 28.153263, 16.589226, 20.730303, 7.422959, 10.1280365, 6.4611893], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.011900429, 0.12912346, -0.052636214, -0.016956275, 0.06598894, 0.05378924, -0.12237515, -0.07065309, 0.029027417], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 290, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.4293862, 0.37136117, 0.5910063, 0.0, 0.0, 0.0, 0.7369118, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 4.0, 2.0, -0.016956275, 0.06598894, 0.05378924, 7.0, -0.07065309, 0.029027417], "split_indices": [1, 2, 2, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.848778, 19.314693, 34.534084, 6.108342, 13.206351, 7.2887354, 27.245348, 18.220165, 9.025184], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.027000649, 0.035773996, -0.05340085, -0.031983662, 0.08436713, -0.02561252, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 291, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.45082393, 0.5731746, 0.0, 0.049441285, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 6.0, -0.05340085, 6.0, 0.08436713, -0.02561252, -0.0], "split_indices": [1, 1, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.38752, 31.424551, 12.962971, 24.9886, 6.4359508, 9.43044, 15.55816], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.025393447, 0.006338448, -0.08899099, -0.036467336, 0.13492185, -0.03231281, 0.0840634, 0.00042172606, 0.06186972], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 292, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.521438, 0.29917103, 0.0, 0.8921231, 0.10940045, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 7.0, -0.08899099, 8.0, 4.0, -0.03231281, 0.0840634, 0.00042172606, 0.06186972], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.99135, 50.671413, 5.319932, 37.969833, 12.70158, 31.793112, 6.176723, 5.2652965, 7.436284], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.041110598, 0.097746216, -0.10565181, 0.04053343, 0.16477796, -0.057584666, -0.0, -0.0069836224, 0.05058794, 0.02463881, 0.06906732], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 293, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.38649976, 0.10216525, 0.12769371, 0.1846479, 0.027764589, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 5.0, 7.0, 6.0, -0.057584666, -0.0, -0.0069836224, 0.05058794, 0.02463881, 0.06906732], "split_indices": [2, 2, 1, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.351807, 32.734802, 11.617003, 19.331524, 13.403277, 6.2881837, 5.32882, 12.928507, 6.403017, 7.594937, 5.80834], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.039508905, 0.13256092, -0.1504594, 0.073250026, -0.00035053428, -0.049272455, -0.22711688, 0.013618892, -0.040127244, -0.096003294, -0.0071104425], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 294, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.98132706, 0.32133454, 0.20458275, 0.0, 0.0, 0.13780373, 0.27956396, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 7.0, 0.073250026, -0.00035053428, 4.0, 9.0, 0.013618892, -0.040127244, -0.096003294, -0.0071104425], "split_indices": [2, 0, 2, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.263504, 18.882742, 30.380762, 10.250221, 8.632522, 14.365605, 16.015158, 6.4345117, 7.931093, 10.263371, 5.751786], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.061835278, 0.022606207, -0.113501064, -0.124983795, 0.06656038, -0.078395456, -0.027497375, -0.0, -0.08363675, 0.013814929, -0.06662337], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 295, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.27120122, 0.70447284, 0.44459948, 0.26349127, 0.0, 0.0, 0.3920088, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 7.0, 5.0, 0.06656038, -0.078395456, 7.0, -0.0, -0.08363675, 0.013814929, -0.06662337], "split_indices": [0, 2, 0, 2, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.491558, 21.681957, 36.809605, 12.381838, 9.300118, 12.361805, 24.447798, 7.302223, 5.079615, 17.92279, 6.5250077], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.018900562, -0.1593811, 0.08397124, -0.0, -0.078396395, -0.04317022, 0.1843046, -0.10032336, 0.02510073, 0.15413865, 0.01766343], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 296, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.79713273, 0.317716, 0.6731188, 0.0, 0.0, 0.8983158, 1.1173666, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 5.0, 5.0, -0.0, -0.078396395, 5.0, 4.0, -0.10032336, 0.02510073, 0.15413865, 0.01766343], "split_indices": [1, 2, 2, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [67.209335, 17.172825, 50.036514, 6.7602973, 10.412526, 21.88152, 28.154995, 6.381227, 15.500292, 6.689156, 21.46584], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0036400438, -0.038222104, 0.027976569, -0.05535675, 0.090773225, -0.065892845, 0.037701074, 0.058103077, -0.015341799], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 297, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.1570001, 0.0, 0.2423182, 0.60599655, 0.41167313, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.038222104, 4.0, 5.0, 5.0, -0.065892845, 0.037701074, 0.058103077, -0.015341799], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.22827, 6.559966, 43.668304, 18.132238, 25.536066, 9.683412, 8.448826, 14.999511, 10.536556], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.020191463, 0.03162597, -0.14176328, 0.076356135, -0.0, -0.08240267, -0.0, -0.027498012, 0.024411937], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 298, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.43911177, 0.35546273, 0.41121972, 0.0, 0.32286316, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 1.0, 4.0, 0.076356135, 5.0, -0.08240267, -0.0, -0.027498012, 0.024411937], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.24534, 46.38991, 19.855427, 5.218867, 41.171043, 10.005758, 9.8496685, 19.78815, 21.382893], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.06298258, -0.12587726, 0.07828459, 0.00045351163, -0.1711885, 0.065334685, -0.019690707, -0.0386903, -0.07808236], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 299, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.5226268, 0.25062925, 0.3856829, 0.0, 0.041906893, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 2.0, 6.0, 0.00045351163, 8.0, 0.065334685, -0.019690707, -0.0386903, -0.07808236], "split_indices": [0, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.1768, 39.436, 16.740799, 10.021374, 29.414627, 8.593239, 8.147559, 22.378767, 7.0358596], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.01762561, 0.07727135, -0.105264, 0.039269425, 0.05591331, -0.01813519, -0.063646056, -0.0044257087, 0.05279422, -0.012679401, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 300, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.43872607, 0.1398266, 0.15189883, 0.25729784, 0.0, 0.008419247, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 8.0, 6.0, 4.0, 0.05591331, 8.0, -0.063646056, -0.0044257087, 0.05279422, -0.012679401, -0.0], "split_indices": [1, 2, 2, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.126987, 39.93101, 18.195976, 31.30417, 8.626841, 11.184427, 7.0115495, 22.552946, 8.751225, 5.9565635, 5.227864], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.005319079, -0.12709899, 0.1351314, 0.03377119, -0.11147819, -0.031159442, 0.24322626, 0.061802328, -0.031145638, 0.03986137, 0.11940212], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 301, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.8096794, 0.94836116, 0.6624115, 0.37528563, 0.0, 0.0, 0.18778527, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 3.0, 3.0, -0.11147819, -0.031159442, 8.0, 0.061802328, -0.031145638, 0.03986137, 0.11940212], "split_indices": [0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.05726, 21.933767, 23.123491, 13.502441, 8.431326, 6.925446, 16.198044, 6.098196, 7.404246, 10.91817, 5.279875], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.06691536, 0.01067648, -0.07441121, -0.03350843, 0.074883945, -0.023710692, 0.048878234], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 302, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.8068986, 0.44650504, 0.0, 0.3069911, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 6.0, -0.07441121, 8.0, 0.074883945, -0.023710692, 0.048878234], "split_indices": [0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.538433, 38.453896, 16.084534, 32.82503, 5.628867, 27.547235, 5.2777953], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.037181687, 0.10130656, -0.029299254, 0.17706707, -1.2699518e-05, -0.0, -0.01882404, 0.031359784, 0.07186431, -0.023548296, 0.028411632], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 303, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.21791495, 0.21577221, 0.025438454, 0.010008365, 0.0, 0.106489316, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 6.0, 5.0, 3.0, -1.2699518e-05, 8.0, -0.01882404, 0.031359784, 0.07186431, -0.023548296, 0.028411632], "split_indices": [2, 0, 0, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.731415, 24.79525, 22.936167, 14.325907, 10.469343, 12.256084, 10.680082, 8.60722, 5.718687, 6.612902, 5.6431823], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.05480499, -0.044892818, 0.089255676, 0.015377438, -0.053428814, 0.07229213, 0.025115903, 0.022308215, -0.011929606], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 304, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.19201024, 0.19657579, 0.36437264, 0.0, 0.0, 0.0, 0.103088096, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [2.0, 6.0, 4.0, 0.015377438, -0.053428814, 0.07229213, 8.0, 0.022308215, -0.011929606], "split_indices": [2, 0, 2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.4455, 12.645252, 39.800247, 7.265329, 5.3799233, 10.543721, 29.256527, 17.42565, 11.830878], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017013019, -0.07727528, 0.038950913, -0.023856293, -0.041868873, 0.09126583, -0.04144517, -0.028432136, 0.0126276985, 0.06152892, -0.0, -0.0496071, 0.010207937], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 305, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.18679325, 0.05247368, 0.16812882, 0.07353045, 0.0, 0.23796968, 0.15782775, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 8.0, 2.0, -0.041868873, 5.0, 5.0, -0.028432136, 0.0126276985, 0.06152892, -0.0, -0.0496071, 0.010207937], "split_indices": [1, 2, 1, 1, 0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.572998, 20.682486, 36.89051, 12.742631, 7.9398546, 23.019817, 13.870694, 6.6600404, 6.082591, 9.413562, 13.606255, 5.3011346, 8.56956], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.015742842, -0.04907124, 0.042276494, 0.09707918, -0.07211492, 0.012075357, 0.088581495, -0.10687366, 0.014050434], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 306, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.33675233, 0.0, 0.41002157, 0.45252937, 0.7464519, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.04907124, 7.0, 9.0, 3.0, 0.012075357, 0.088581495, -0.10687366, 0.014050434], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [70.87425, 7.9841495, 62.8901, 43.21309, 19.677011, 34.910736, 8.302352, 5.466104, 14.210908], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.00057087647, 0.061990183, -0.06743988, -0.00649592, 0.118151434, 0.015047309, -0.14633778, 0.068537414, -0.0, -0.08185105, 0.0022824588], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 307, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.2347659, 0.15486436, 0.2578504, 0.0, 0.26954934, 0.0, 0.3460746, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 4.0, -0.00649592, 7.0, 0.015047309, 7.0, 0.068537414, -0.0, -0.08185105, 0.0022824588], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.331738, 29.36847, 24.963266, 11.329236, 18.039234, 9.660729, 15.302537, 9.256772, 8.782461, 8.332731, 6.9698057], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.03366875, 0.03294201, -0.19629869, -0.0, 0.0653842, -0.09929812, -0.009979929, -0.019853495, 0.034074355], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 308, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.71607566, 0.2744336, 0.37365246, 0.30920863, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 9.0, 4.0, 4.0, 0.0653842, -0.09929812, -0.009979929, -0.019853495, 0.034074355], "split_indices": [1, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.997017, 44.8214, 18.175615, 38.997173, 5.8242273, 9.052749, 9.122868, 24.781006, 14.2161665], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015443449, -0.03011428, 0.05347908, -0.11460552, 0.06495127, 0.020229235, -0.051736783, 0.07475797, -0.007461538], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 309, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.27260903, 0.40368274, 0.0, 0.30340672, 0.40931898, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, 0.05347908, 1.0, 7.0, 0.020229235, -0.051736783, 0.07475797, -0.007461538], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.905613, 47.71308, 6.1925297, 25.709583, 22.003498, 5.7432513, 19.966331, 6.994879, 15.008619], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.05635633, 0.02382341, 0.0562057, -0.053884875, 0.05917209, 0.003675869], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 310, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.27429357, 0.0, 0.34811816, 0.26839054, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, -0.05635633, 8.0, 3.0, -0.053884875, 0.05917209, 0.003675869], "split_indices": [2, 0, 1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.100758, 5.9173717, 52.183384, 46.066128, 6.1172576, 9.576047, 36.490078], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.006458996, -0.090585016, 0.06349336, -0.073239826, -0.0, 0.12599222, -0.043256477, 0.026654026, -0.041984677, 0.11075482, -0.0026704546], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 311, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.29689884, 0.3109338, 0.36291304, 0.0, 0.21276535, 0.74224734, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 9.0, -0.073239826, 7.0, 4.0, -0.043256477, 0.026654026, -0.041984677, 0.11075482, -0.0026704546], "split_indices": [0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.01398, 22.3137, 25.700283, 7.4266944, 14.887006, 20.4277, 5.272583, 9.090569, 5.7964373, 6.933058, 13.494642], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.029793816, 0.10573776, -0.12268917, 0.08015497, -0.016506603, -0.21399033, 0.033377953, -0.016083015, 0.0025252127, -0.08066952, -0.018644337, 0.024598341, -0.0021713807], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 312, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.69563884, 0.4655438, 0.49530995, 0.0, 0.017143669, 0.12822169, 0.033023793, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 5.0, 6.0, 0.08015497, 8.0, 8.0, 8.0, -0.016083015, 0.0025252127, -0.08066952, -0.018644337, 0.024598341, -0.0021713807], "split_indices": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.087143, 21.095177, 31.991964, 8.931684, 12.163494, 20.354149, 11.637816, 6.0372744, 6.1262193, 13.8987255, 6.455423, 5.9541335, 5.683683], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.041593123, -0.026106518, 0.07713885, 0.027791923, -0.04503591, -0.02411463, 0.14671287, 0.0722422, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 313, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.11938634, 0.25292733, 0.36707747, 0.0, 0.0, 0.0, 0.31912154, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 5.0, 3.0, 0.027791923, -0.04503591, -0.02411463, 7.0, 0.0722422, -0.0], "split_indices": [2, 1, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.653828, 14.840738, 30.813091, 7.2986684, 7.5420704, 8.952126, 21.860964, 12.988995, 8.871969], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0074976054, -0.1454811, 0.031640895, -0.10288047, 0.01197697, 0.061491176, -0.034009036, -0.024650086, 0.04387794], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 314, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.2996667, 0.496127, 0.49263772, 0.0, 0.0, 0.0, 0.26870126, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [2.0, 3.0, 4.0, -0.10288047, 0.01197697, 0.061491176, 9.0, -0.024650086, 0.04387794], "split_indices": [2, 1, 2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.5287, 11.350445, 40.178257, 5.346344, 6.0041013, 10.872806, 29.305449, 23.994392, 5.311056], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.06236385, -0.007149298, 0.09460189, 0.078951, -0.056538958, -0.013812694, 0.15290974, 0.07874273, 0.025567595], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 315, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.14486225, 0.97806233, 0.36496505, 0.0, 0.0, 0.0, 0.17028534, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 3.0, 3.0, 0.078951, -0.056538958, -0.013812694, 6.0, 0.07874273, 0.025567595], "split_indices": [2, 1, 1, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.438465, 18.095793, 41.342674, 6.8621225, 11.23367, 11.675517, 29.667156, 9.665494, 20.001661], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.014787654, -0.109362274, 0.056244675, 0.01120495, -0.15123229, 0.11321884, -0.032862425, -0.061882686, -0.023744995, -0.011662014, 0.05009588, -0.09411589, 0.0484594], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 316, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.4748284, 0.20456776, 0.21202004, 0.0, 0.052253544, 0.22350892, 0.910804, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 8.0, 0.01120495, 3.0, 2.0, 9.0, -0.061882686, -0.023744995, -0.011662014, 0.05009588, -0.09411589, 0.0484594], "split_indices": [2, 2, 2, 0, 2, 1, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [67.97523, 29.528406, 38.446823, 6.049751, 23.478655, 23.971407, 14.475418, 11.47056, 12.008095, 5.8475513, 18.123856, 5.880272, 8.595145], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.011727836, -0.05207519, 0.08774564, 0.09678568, -0.16430652, 0.012497466, 0.048466112, 0.042203166, 0.005365284, -0.013388564, -0.064041175, 0.03631776, -0.03997621], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 317, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.26127672, 0.49526072, 0.11889033, 0.024591394, 0.06399506, 0.23780163, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 4.0, 1.0, 2.0, 2.0, 0.048466112, 0.042203166, 0.005365284, -0.013388564, -0.064041175, 0.03631776, -0.03997621], "split_indices": [0, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.192097, 27.28816, 23.903934, 11.406055, 15.882105, 13.086393, 10.817541, 6.0115666, 5.3944893, 5.772386, 10.109718, 7.990358, 5.0960355], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.022075905, 0.05016951, -0.05435183, 0.0583757, -0.11727671, 0.03629338, -0.019346219, -0.079854436, -0.009832469], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 318, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.37858605, 0.0, 0.39896363, 0.16423441, 0.41240987, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.05016951, 3.0, 8.0, 6.0, 0.03629338, -0.019346219, -0.079854436, -0.009832469], "split_indices": [1, 0, 2, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.32076, 7.8716516, 53.449104, 18.603945, 34.84516, 12.873692, 5.7302537, 11.40842, 23.43674], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.051423486, 0.049683705, 0.027016861, -0.04443124, 0.062005173, -0.017874569, -0.0, -0.03891268, 0.03231461], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 319, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.12931135, 0.0, 0.122409366, 0.004620405, 0.30109587, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.049683705, 4.0, 3.0, 3.0, -0.017874569, -0.0, -0.03891268, 0.03231461], "split_indices": [1, 0, 1, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.915874, 7.7542124, 46.161663, 14.146246, 32.015415, 8.724258, 5.421987, 5.3888, 26.626616], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.008373747, 0.04736858, -0.07358167, -0.03899977, 0.105351806, -0.0035259055, -0.04652373, 0.008528846, -0.047136255, 0.049844135, 0.015980998], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 320, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.18098323, 0.20918165, 0.07086215, 0.14440346, 0.047324866, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 6.0, 3.0, 2.0, -0.0035259055, -0.04652373, 0.008528846, -0.047136255, 0.049844135, 0.015980998], "split_indices": [1, 2, 2, 2, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.36052, 38.624817, 16.735703, 14.953596, 23.671223, 10.864507, 5.871196, 9.4638815, 5.489714, 9.025522, 14.6457], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.029919567, -0.0, 0.0634439, 0.09124934, -0.14199847, -0.03652045, 0.09448303, 0.023430757, -0.10036077], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 321, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.2656548, 0.5502795, 0.0, 1.2753764, 0.7610176, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 7.0, 0.0634439, 6.0, 6.0, -0.03652045, 0.09448303, 0.023430757, -0.10036077], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.179592, 40.295338, 5.8842573, 24.559021, 15.736315, 12.585164, 11.973858, 7.3775597, 8.358755], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.020502733, -0.022258194, 0.17723694, -0.08499841, 0.018819489, 0.013092608, 0.08654759, 0.014892522, -0.09511682, 0.021081524, -0.043866955], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 322, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.39373013, 0.122293174, 0.1412223, 0.62543845, 0.22860457, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, 6.0, 3.0, 7.0, 0.013092608, 0.08654759, 0.014892522, -0.09511682, 0.021081524, -0.043866955], "split_indices": [2, 2, 0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.846264, 43.263016, 11.583245, 17.658072, 25.604946, 6.3042336, 5.2790112, 11.3993225, 6.2587495, 20.419321, 5.185624], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.019980688, 0.003793029, -0.040204704, -0.037509747, 0.04199954, 0.040313415, -0.008853136], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 323, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.14087035, 0.18946931, 0.0, 0.0, 0.22263935, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 3.0, -0.040204704, -0.037509747, 6.0, 0.040313415, -0.008853136], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.970886, 38.114655, 7.8562303, 7.599753, 30.514902, 13.541233, 16.97367], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.013455229, 0.060015325, -0.05147755, 0.034173336, -0.16229726, 0.10375788, -0.030895246, -0.072859414, -0.015356763], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 324, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.4387942, 0.0, 0.469702, 1.2200682, 0.14984143, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.060015325, 5.0, 3.0, 8.0, 0.10375788, -0.030895246, -0.072859414, -0.015356763], "split_indices": [2, 0, 1, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.61359, 7.0627522, 46.55084, 26.138245, 20.412592, 7.745132, 18.393114, 10.623405, 9.789186], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.011159857, -0.118531585, 0.08187947, -0.043364376, -0.05763266, 0.12887551, -0.012727276, -0.028554035, -0.0, 0.0066259927, 0.06093799], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 325, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.63739127, 0.13352463, 0.21015702, 0.041521236, 0.0, 0.1726774, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 9.0, 2.0, -0.05763266, 7.0, -0.012727276, -0.028554035, -0.0, 0.0066259927, 0.06093799], "split_indices": [1, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.46583, 28.906826, 32.55901, 15.757404, 13.149421, 24.183537, 8.375472, 7.061703, 8.695702, 11.003052, 13.180485], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.018354455, 0.104536384, -0.046811983, -0.009195825, 0.17282747, -0.123347566, -0.006632521, 0.02023512, 0.08107424, 0.024502074, -0.09857903, -0.012169111, 0.017995324], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 326, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.38450384, 0.29307383, 0.10283591, 0.0, 0.15668994, 0.5626031, 0.061043635, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 6.0, -0.009195825, 2.0, 6.0, 6.0, 0.02023512, 0.08107424, 0.024502074, -0.09857903, -0.012169111, 0.017995324], "split_indices": [1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.56456, 28.630894, 36.933666, 9.302492, 19.328402, 11.145152, 25.788515, 10.591098, 8.737305, 5.6331367, 5.512015, 18.339529, 7.4489865], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.07508478, 0.037382286, 0.17312506, 0.08519514, -0.051966872, 0.12474638, -0.014251396, 0.006808028, 0.06701067], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 327, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.15784472, 0.39093617, 0.74822044, 0.24854064, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 3.0, 3.0, -0.051966872, 0.12474638, -0.014251396, 0.006808028, 0.06701067], "split_indices": [1, 1, 2, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.148552, 37.231895, 11.916658, 31.215467, 6.0164256, 5.469104, 6.447555, 22.793148, 8.42232], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.067281894, -0.0, -0.16258085, -0.0707699, 0.1329746, -0.0066323644, -0.1968212, 0.0029497657, -0.07928368, -0.016891327, 0.1179414, -0.11437005, -0.012963455], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 328, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.4060682, 0.38055158, 0.10172373, 0.45284194, 0.7357446, 0.0, 0.527245, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 8.0, 2.0, 4.0, 4.0, -0.0066323644, 7.0, 0.0029497657, -0.07928368, -0.016891327, 0.1179414, -0.11437005, -0.012963455], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.64824, 39.042835, 25.605408, 26.235401, 12.807433, 5.913038, 19.69237, 18.826149, 7.409252, 7.6447706, 5.1626625, 7.9797025, 11.712667], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.034648355, -0.071418256, 0.035633527, 0.0034976215, -0.18172067, -0.018644331, 0.042841785, -0.033006348, -0.077254355], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 329, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.32863185, 0.41184098, 0.0, 0.280238, 0.042590916, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, 0.035633527, 3.0, 7.0, -0.018644331, 0.042841785, -0.033006348, -0.077254355], "split_indices": [0, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.588528, 46.60546, 9.983066, 27.79153, 18.81393, 18.77864, 9.01289, 11.605913, 7.2080173], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0033074704, 0.030186476, -0.024311388, -0.08755848, 0.01037225, 0.027811801, -0.042936526], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 330, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.12529851, 0.0, 0.5075094, 0.0, 0.5905144, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.030186476, 1.0, -0.08755848, 6.0, 0.027811801, -0.042936526], "split_indices": [0, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.77473, 8.4435835, 50.331146, 5.255829, 45.075317, 30.020126, 15.055192], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.03743881, -0.056819957, 0.080472365, 0.07394431, 0.01798649, 0.08426309, -0.013856887], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 331, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.46037805, 0.0, 0.38756767, 0.0, 0.5564862, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.056819957, 5.0, 0.07394431, 3.0, 0.08426309, -0.013856887], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.71631, 6.376977, 39.339333, 9.515141, 29.824192, 5.495211, 24.328981], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.004322422, 0.044566333, -0.11434333, 0.047221288, 0.0038066916, -0.0024213768, -0.06648426, -0.02586498, 0.0112083], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 332, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.29970515, 0.15397452, 0.17316145, 0.0, 0.08575626, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 6.0, 0.047221288, 1.0, -0.0024213768, -0.06648426, -0.02586498, 0.0112083], "split_indices": [1, 2, 2, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.475193, 36.113743, 16.361448, 8.172905, 27.94084, 9.227412, 7.134036, 6.3512583, 21.58958], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.027929537, -0.09149848, 0.033379126, -0.060174003, 0.02146618, 0.07482695, -0.0396204, 0.033094835, -0.03634314], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 333, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.19310792, 0.4590493, 0.40931717, 0.0, 0.0, 0.0, 0.24000601, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [5.0, 7.0, 6.0, -0.060174003, 0.02146618, 0.07482695, 2.0, 0.033094835, -0.03634314], "split_indices": [0, 1, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.273903, 23.277203, 22.9967, 14.176389, 9.100814, 5.54354, 17.453161, 5.5908093, 11.862351], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.094746985, -0.32255197, -0.016629232, -0.15737422, -0.000691953, 0.17401564, -0.096059136, 0.12501682, -0.030731842, -0.0724549, 0.008276903], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 334, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.87101495, 0.7665281, 0.6066433, 0.0, 0.0, 0.8520674, 0.54744196, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 6.0, 2.0, -0.15737422, -0.000691953, 6.0, 6.0, 0.12501682, -0.030731842, -0.0724549, 0.008276903], "split_indices": [2, 1, 1, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.152878, 11.680713, 38.472164, 6.5796084, 5.1011043, 10.594305, 27.87786, 5.56393, 5.030375, 12.742921, 15.134937], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.025526954, 0.09600637, -0.02069475, -0.07971829, 0.026212579, 0.058167018, -0.010142622], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 335, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.8527499, 0.0, 0.6340292, 0.0, 0.47152144, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.09600637, 4.0, -0.07971829, 4.0, 0.058167018, -0.010142622], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.961277, 7.488617, 51.47266, 7.863338, 43.60932, 11.346659, 32.262665], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.12215761, -0.06146944, 0.01211772, 0.114419855, -0.08974752, -0.019563954, -0.04281561, 0.048992004, -0.054643854, 0.007975201], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 336, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.5132482, 0.6132183, 0.40901536, 0.4344797, 0.0, 0.0, 0.3171856, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 5.0, 6.0, 0.114419855, -0.08974752, 2.0, -0.04281561, 0.048992004, -0.054643854, 0.007975201], "split_indices": [2, 2, 2, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.8889, 22.037575, 43.851326, 16.476368, 5.561208, 5.2946625, 38.556664, 7.8409023, 8.635466, 8.351994, 30.204672], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.07705191, 0.1516118, -0.15758985, 0.10757809, -0.0446892, -0.09446906, -0.09271259, -0.0, -0.044271532], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 337, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [1.0056231, 0.9492943, 0.31487924, 0.0, 0.0, 0.16624156, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [2.0, 4.0, 7.0, 0.10757809, -0.0446892, 5.0, -0.09271259, -0.0, -0.044271532], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.275814, 13.063036, 39.21278, 7.8186493, 5.2443867, 29.289717, 9.923062, 10.243308, 19.046408], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.033777885, 0.08681252, -0.0547065, -0.059151784, 0.1610957, -0.062399596, 0.005147349, 0.08727665, 0.027080348], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 338, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.71148723, 0.5667383, 0.0, 0.2190719, 0.24970806, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 3.0, -0.0547065, 3.0, 6.0, -0.062399596, 0.005147349, 0.08727665, 0.027080348], "split_indices": [0, 2, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.52314, 49.4501, 11.073039, 16.29277, 33.15733, 5.3811727, 10.911597, 10.108769, 23.04856], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0469384, -0.02113006, 0.11993561, 0.063020736, -0.0761635, 0.21854068, -0.014070686, -0.0037223944, 0.05543501, -0.0, 0.10312737], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 339, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.28411484, 0.59205407, 0.47560647, 0.21581379, 0.0, 0.47886056, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 7.0, 6.0, -0.0761635, 3.0, -0.014070686, -0.0037223944, 0.05543501, -0.0, 0.10312737], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.68114, 27.437155, 26.243988, 20.3714, 7.0657544, 16.676987, 9.567, 12.626467, 7.744934, 6.480238, 10.196748], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.041988093, 0.013733103, -0.12517048, -0.034194473, 0.054969512, -0.0020959666, -0.051230542, 0.06651491, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 340, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.2731582, 0.17843325, 0.106845856, 0.0, 0.2381295, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 2.0, -0.034194473, 3.0, -0.0020959666, -0.051230542, 0.06651491, -0.0], "split_indices": [2, 1, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.158413, 32.811665, 22.346746, 6.985987, 25.825678, 7.153116, 15.19363, 5.4930706, 20.332607], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.07191825, -0.0, 0.14030202, 0.08857989, -0.08339264, 0.017469361, 0.12536533, -0.019215183, 0.05195738, 0.048681978, -0.045693196], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 341, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.23196378, 0.6168956, 0.77475005, 0.26790547, 0.0, 0.47018114, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 8.0, 8.0, 1.0, -0.08339264, 5.0, 0.12536533, -0.019215183, 0.05195738, 0.048681978, -0.045693196], "split_indices": [1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.304356, 23.090925, 23.21343, 18.040821, 5.050104, 17.079008, 6.134423, 6.099342, 11.94148, 9.559233, 7.519775], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.048427444, -0.09109133, 0.11794153, -0.004855856, -0.06131708, -0.019029763, 0.15663141, -0.048956092, 0.04655927, 0.075797774, 0.013477405], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 342, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.5616427, 0.16258164, 0.29042023, 0.3365031, 0.0, 0.0, 0.3084932, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 6.0, 1.0, 3.0, -0.06131708, -0.019029763, 7.0, -0.048956092, 0.04655927, 0.075797774, 0.013477405], "split_indices": [2, 1, 1, 1, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.52556, 17.883463, 37.642097, 11.247845, 6.6356177, 6.0510855, 31.591013, 5.9585886, 5.289256, 15.827776, 15.763236], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.024618166, 0.03484292, -0.13288787, 0.08138907, -0.022810247, -0.090598576, -0.0146590965, -0.031109048, 0.062469617, -0.01498849, 0.0034772295], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 343, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.32866535, 0.4632194, 0.3259317, 0.0, 0.4983523, 0.0, 0.015588107, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 8.0, 0.08138907, 9.0, -0.090598576, 5.0, -0.031109048, 0.062469617, -0.01498849, 0.0034772295], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.809994, 30.702774, 17.107218, 5.632194, 25.07058, 6.051445, 11.055774, 19.298342, 5.7722387, 5.954289, 5.101485], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.087804355, -0.035368178, 0.14568363, 0.010522721, -0.028634831, 0.20761344, 0.026635837, 0.024586568, 0.08180674, -0.023556104, 0.042076327], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 344, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.38716564, 0.0808606, 0.23756158, 0.0, 0.0, 0.13091588, 0.1852899, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 5.0, 0.010522721, -0.028634831, 2.0, 8.0, 0.024586568, 0.08180674, -0.023556104, 0.042076327], "split_indices": [0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.037365, 15.887811, 35.149555, 6.7386622, 9.149148, 22.079592, 13.069963, 8.8284025, 13.251189, 6.491101, 6.5788627], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.030416626, -0.02282095, 0.07971283, 0.018710742, -0.05987096, 0.10034264, -0.0, -0.011289451, 0.020000359, 0.056772087, 0.009962918], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 345, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.17201403, 0.24159153, 0.056262523, 0.0721484, 0.0, 0.13190794, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 9.0, 9.0, 4.0, -0.05987096, 4.0, -0.0, -0.011289451, 0.020000359, 0.056772087, 0.009962918], "split_indices": [0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.682926, 28.938847, 32.74408, 23.698614, 5.2402325, 25.883158, 6.8609233, 10.036906, 13.661708, 9.71287, 16.170288], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.06372281, 0.018807609, -0.096714124, -0.032689855, 0.035134636, -0.076697476, -0.04668648, -0.048629005, 0.0035763027], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 346, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.18748572, 0.2475718, 0.33712268, 0.0, 0.0, 0.0, 0.2738617, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 6.0, 5.0, -0.032689855, 0.035134636, -0.076697476, 3.0, -0.048629005, 0.0035763027], "split_indices": [2, 1, 2, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.32869, 17.628471, 46.70022, 7.175986, 10.452485, 9.717443, 36.982777, 12.435393, 24.547382], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.07283009, 0.21506022, -0.0057995906, 0.32978085, -0.009946782, -0.08583743, 0.06196895, 0.01976608, 0.1451045, 0.051871344, 0.003013996], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 347, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.59877586, 0.5769223, 0.6808026, 0.45122528, 0.0, 0.0, 0.13947952, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [3.0, 7.0, 4.0, 3.0, -0.009946782, -0.08583743, 2.0, 0.01976608, 0.1451045, 0.051871344, 0.003013996], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.55829, 17.834623, 32.723663, 12.248075, 5.5865483, 6.030786, 26.692879, 5.2771745, 6.9708996, 7.153315, 19.539564], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.025476292, 0.10416706, -0.032643173, -0.040121954, 0.17064506, -0.08149776, 0.044500753, 0.07640798, 0.004062446, -0.057036456, 0.020554882], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 348, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.2952792, 0.46012977, 0.32228208, 0.0, 0.26282376, 0.5026567, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 9.0, -0.040121954, 7.0, 5.0, 0.044500753, 0.07640798, 0.004062446, -0.057036456, 0.020554882], "split_indices": [0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.256184, 26.417097, 34.83909, 5.2200155, 21.19708, 28.301455, 6.537634, 12.9899435, 8.207137, 16.646862, 11.6545925], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.03929146, 0.12359017, -0.063297346, 0.17200163, 0.054811165, -0.20459123, 0.06075357, -0.0, 0.08562138, 0.058747157, -0.046409946, -0.010982771, -0.10144894, -0.0, 0.038728494], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 349, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.4939242, 0.0722639, 0.46790954, 0.31537753, 0.48554176, 0.22771996, 0.05195258, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 6.0, 6.0, 2.0, 4.0, 4.0, 8.0, -0.0, 0.08562138, 0.058747157, -0.046409946, -0.010982771, -0.10144894, -0.0, 0.038728494], "split_indices": [1, 2, 2, 1, 1, 2, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.511826, 30.346071, 24.165754, 16.075966, 14.270104, 11.37103, 12.7947235, 7.0022497, 9.073717, 8.853698, 5.4164057, 5.9327083, 5.438322, 7.6221647, 5.1725593], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.0016974439, -0.020309336, 0.030194754, -0.057655502, 0.047827996, -0.0041004703, -0.040802382], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 350, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.13082932, 0.30354533, 0.0, 0.11675358, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, 0.030194754, 5.0, 0.047827996, -0.0041004703, -0.040802382], "split_indices": [2, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.45128, 44.368027, 10.083249, 37.75811, 6.6099186, 25.751741, 12.006369], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.046244383, 0.06837096, -0.021778818, 0.18450212, -0.09784786, -0.0030758854, 0.10891536, 0.028781563, -0.075674124], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 351, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.12878737, 0.8192728, 0.0, 0.9038705, 0.5540626, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 7.0, -0.021778818, 6.0, 6.0, -0.0030758854, 0.10891536, 0.028781563, -0.075674124], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.250393, 40.058212, 6.1921816, 23.871597, 16.186615, 11.600588, 12.271009, 7.05848, 9.128135], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.066755116, 0.03830903, -0.1074367, -0.034976717, -0.1721608, -0.033750523, 0.021446068, 0.0039634393, -0.070168756], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 352, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.4261858, 0.0, 0.18102723, 0.20102592, 0.28167617, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.03830903, 4.0, 7.0, 4.0, -0.033750523, 0.021446068, 0.0039634393, -0.070168756], "split_indices": [0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.397026, 8.097093, 43.299934, 21.884827, 21.415106, 13.174181, 8.710646, 5.141839, 16.273268], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.023162048, 0.027961254, -0.10965431, -0.12109856, 0.1751596, -0.066816, 0.011990829, -0.10450437, 0.040173177, 0.08984912, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 353, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.2264068, 0.6992175, 0.34873134, 0.9654343, 0.35778996, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 9.0, 7.0, 6.0, -0.066816, 0.011990829, -0.10450437, 0.040173177, 0.08984912, -0.0], "split_indices": [0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.66184, 29.65443, 18.00741, 14.484607, 15.169825, 10.359151, 7.648258, 7.6729984, 6.8116083, 8.5196085, 6.650216], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.04347748, -0.09383831, -0.0, 0.14285679, -0.013052531, 0.014016248, 0.07125861, -0.00073786377], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 354, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.70348257, 0.21151891, 0.0, 0.06786165, 0.22549653, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 6.0, -0.09383831, 6.0, 5.0, -0.013052531, 0.014016248, 0.07125861, -0.00073786377], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.45598, 45.157642, 5.298336, 31.517824, 13.639817, 17.292955, 14.224869, 8.291556, 5.348262], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.019688059, 0.13215025, -0.018997522, 0.020236446, 0.052327566, -0.059307545, 0.025147706, -0.07238245, 0.029748406], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 355, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.27807522, 0.008716375, 0.3804022, 0.0, 0.0, 0.0, 0.7363241, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 3.0, 5.0, 0.020236446, 0.052327566, -0.059307545, 2.0, -0.07238245, 0.029748406], "split_indices": [0, 0, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.737698, 15.3328285, 44.40487, 8.020205, 7.3126235, 8.556457, 35.84841, 7.0086865, 28.839727], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.07268791, 0.030629558, 0.11857665, -0.038759854, 0.06326334, 0.0070842747, 0.008713866, -0.046680953], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 356, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.481516, 0.0, 0.38161734, 0.213067, 0.28591833, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.07268791, 4.0, 6.0, 7.0, 0.06326334, 0.0070842747, 0.008713866, -0.046680953], "split_indices": [2, 0, 1, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.70832, 6.23577, 59.472553, 26.551868, 32.920685, 12.311601, 14.240268, 20.76562, 12.155065], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0053720674, 0.09592358, -0.024437966, 0.06331342, -0.01646443, -0.05434422, 0.02036094, 0.017234655, -0.036600146], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 357, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.15839283, 0.28232992, 0.31087402, 0.0, 0.0, 0.0, 0.17294155, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [2.0, 5.0, 4.0, 0.06331342, -0.01646443, -0.05434422, 7.0, 0.017234655, -0.036600146], "split_indices": [0, 2, 0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.1616, 13.695782, 40.465816, 7.916306, 5.779475, 8.815893, 31.649923, 26.139082, 5.5108414], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.053468607, 0.08984728, -0.04498689, 0.19593947, -0.0, -0.05951033, 0.019409915, 0.013689513, 0.086658105, -0.04840144, 0.0475476], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 358, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.23241027, 0.4488415, 0.3086639, 0.25385135, 0.6956807, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 6.0, 3.0, 4.0, -0.05951033, 0.019409915, 0.013689513, 0.086658105, -0.04840144, 0.0475476], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.50761, 45.718163, 15.789447, 20.514105, 25.204058, 6.626474, 9.162973, 8.8178, 11.696305, 12.537148, 12.66691], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.014381088, -0.06461322, 0.04843362, 0.009963838, -0.040008936, -0.0, 0.16617982, 0.02842844, -0.011385001, 0.06596966, 0.021921959], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 359, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.15270936, 0.12524877, 0.24891114, 0.0, 0.0, 0.1031395, 0.017021269, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 8.0, 0.009963838, -0.040008936, 4.0, 7.0, 0.02842844, -0.011385001, 0.06596966, 0.021921959], "split_indices": [0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.208305, 15.475195, 39.733112, 5.999998, 9.475197, 28.25966, 11.473451, 6.8996205, 21.360039, 5.7310286, 5.7424226], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.09056988, -0.1263034, 0.02501471, -0.07270519, -0.23806146, 0.037477892, -0.021230932, 0.050602145, -0.056401398, -0.14653413, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 360, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.2527789, 0.22187197, 0.14696158, 0.93920135, 0.79801303, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, 6.0, 3.0, 4.0, 0.037477892, -0.021230932, 0.050602145, -0.056401398, -0.14653413, -0.0], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.984585, 44.117794, 12.866793, 31.537853, 12.57994, 6.6598024, 6.20699, 9.764882, 21.772972, 5.5706625, 7.0092773], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.004046622, -0.083323516, 0.0679179, 0.020234412, -0.19478597, 0.067005895, 0.015616739, -0.089261, -0.020857962, -0.044141997, 0.032668363], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 361, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.27585265, 0.3678946, 0.21114883, 0.0, 0.10142809, 0.0, 0.36913562, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 3.0, 0.020234412, 3.0, 0.067005895, 6.0, -0.089261, -0.020857962, -0.044141997, 0.032668363], "split_indices": [2, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.694, 19.352003, 28.341997, 8.012066, 11.339937, 5.830795, 22.511202, 5.035552, 6.304385, 7.6002765, 14.910926], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.053373944, 0.14776511, -0.047048323, 0.013935166, 0.2351565, -0.1860218, 0.06149881, -0.014135634, 0.027551379, 0.09487679, 0.019365467, -0.0, -0.095355004], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 362, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.52041674, 0.30081052, 0.9461198, 0.06885907, 0.16983217, 0.4226973, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 7.0, 6.0, 6.0, 4.0, 0.06149881, -0.014135634, 0.027551379, 0.09487679, 0.019365467, -0.0, -0.095355004], "split_indices": [0, 0, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.10626, 27.129984, 24.976273, 11.627511, 15.502473, 16.52393, 8.452344, 6.0205746, 5.6069365, 9.531608, 5.9708652, 7.3377323, 9.186197], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.054383643, 0.034874853, -0.159781, 0.062309217, -0.02416288, -0.100429766, -0.055124935, -0.03159091, 0.024623884, -0.03434268, 0.000320558], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 363, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.44956893, 0.2799015, 0.34041786, 0.0, 0.17652287, 0.0, 0.061987817, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 3.0, 0.062309217, 7.0, -0.100429766, 8.0, -0.03159091, 0.024623884, -0.03434268, 0.000320558], "split_indices": [2, 1, 1, 0, 1, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.867764, 24.142433, 20.72533, 5.9374337, 18.205, 6.5760098, 14.14932, 10.86058, 7.3444204, 7.2424026, 6.906917], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.032894492, 0.119663745, -0.034168128, 0.17919062, -0.0, -0.11409103, 0.030383551, 0.00459112, 0.0837188, -0.053245474, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 364, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.3235381, 0.15926307, 0.34045687, 0.22385985, 0.0, 0.14284462, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [4.0, 5.0, 6.0, 2.0, -0.0, 8.0, 0.030383551, 0.00459112, 0.0837188, -0.053245474, -0.0], "split_indices": [2, 1, 1, 1, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.42864, 23.164255, 29.264383, 14.736294, 8.427962, 18.967947, 10.296437, 6.3956866, 8.340608, 11.785754, 7.182192], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.029560596, -0.06853809, -0.02390519, 0.048831675, 0.020314379, -0.00085984526], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 365, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.442218, 0.11929417, 0.0, 0.0, 0.054747887, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 3.0, -0.06853809, -0.02390519, 7.0, 0.020314379, -0.00085984526], "split_indices": [2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.81231, 55.282013, 6.5302978, 7.0414095, 48.2406, 36.524372, 11.7162285], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.02797687, 0.14462352, -0.049308248, 0.036724992, 0.26120472, -0.095083244, 0.0005448176, 0.043544848, -0.02522018, 0.031451132, 0.1081682, 0.054048713, -0.013957167], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 366, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6075749, 0.29888248, 0.5609173, 0.22229326, 0.10701239, 0.0, 0.31014222, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 2.0, 6.0, 6.0, -0.095083244, 3.0, 0.043544848, -0.02522018, 0.031451132, 0.1081682, 0.054048713, -0.013957167], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.54731, 25.891623, 38.655685, 14.574959, 11.316663, 5.5435424, 33.112144, 8.010693, 6.5642657, 5.586386, 5.730277, 6.729354, 26.38279], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.017712377, -0.04484273, 0.06315184, 0.0147615755, 0.078162596, 0.038150705, -0.035956707], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 367, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.41088292, 0.0, 0.38524082, 0.5682341, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, -0.04484273, 7.0, 3.0, 0.078162596, 0.038150705, -0.035956707], "split_indices": [1, 0, 2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.979057, 10.362265, 42.616795, 35.496265, 7.1205277, 19.778826, 15.717441], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.01869226, 0.018896772, -0.10425385, 0.07361411, -0.029009573, -0.0038782333, -0.059164584, 0.052661344, -0.018995527, -0.03742065, 0.031179097], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 368, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.2100041, 0.11965558, 0.1453724, 0.32088855, 0.30442446, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 5.0, 4.0, 4.0, -0.0038782333, -0.059164584, 0.052661344, -0.018995527, -0.03742065, 0.031179097], "split_indices": [2, 1, 1, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.029636, 42.13099, 18.898647, 20.391335, 21.739655, 10.6503725, 8.248275, 11.947069, 8.444265, 13.112024, 8.62763], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.10991359, -0.1464158, 0.034636676, -0.10863496, -0.08386114, -0.051688444, 0.027800744], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 369, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.46450502, 0.16883075, 0.0, 0.52478194, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, 0.034636676, 6.0, -0.08386114, -0.051688444, 0.027800744], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.03342, 46.324463, 6.708957, 38.08337, 8.241092, 29.42041, 8.66296], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.022356233, -0.051910046, 0.047918733, 0.01959175, -0.056305505, 0.03989208, -0.0052073705], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 370, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.2934932, 0.49180454, 0.0, 0.14887148, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 6.0, 0.047918733, 2.0, -0.056305505, 0.03989208, -0.0052073705], "split_indices": [0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.002083, 47.548683, 6.4534, 31.224455, 16.324226, 7.70104, 23.523417], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.046537895, 0.123857826, -0.0045243376, -0.013283687, 0.091435075, 0.041186947, -0.11392899, -0.048983973, -0.0043229945], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 371, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.2100918, 0.6698626, 0.4838521, 0.0, 0.0, 0.0, 0.06860176, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 7.0, 5.0, -0.013283687, 0.091435075, 0.041186947, 8.0, -0.048983973, -0.0043229945], "split_indices": [1, 2, 2, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.048546, 19.720284, 29.328262, 10.326385, 9.393898, 12.263267, 17.064997, 10.307861, 6.7571344], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.022222523, 0.073271796, -0.27579057, 0.09544734, -0.0010889828, -0.03840896, -0.14001597, 0.05843819, -0.0252195], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 372, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [1.3649566, 0.7647903, 0.3169428, 0.0, 0.5180894, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 7.0, 0.09544734, 2.0, -0.03840896, -0.14001597, 0.05843819, -0.0252195], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.698944, 39.238354, 14.460589, 8.689195, 30.54916, 9.388849, 5.0717406, 8.3304825, 22.218678], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.1077127, -0.080278724, -0.06310007, -0.09993682, 0.011495071, -0.00075485103, -0.08299641, -0.060670238, 0.08195397], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 373, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.28427243, 0.0, 0.11560048, 0.43154308, 0.7728961, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.080278724, 8.0, 7.0, 5.0, -0.00075485103, -0.08299641, -0.060670238, 0.08195397], "split_indices": [0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.85778, 8.424889, 37.43289, 25.728453, 11.704438, 17.603916, 8.1245365, 6.3572474, 5.347191], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.03326732, -0.15967552, 0.011517417, -0.07450281, -3.51228e-05, 0.060894188, -0.048077837, 0.0017049023, -0.044237718], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 374, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.30439004, 0.172452, 0.4574536, 0.0, 0.0, 0.0, 0.17154528, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [2.0, 7.0, 4.0, -0.07450281, -3.51228e-05, 0.060894188, 8.0, 0.0017049023, -0.044237718], "split_indices": [1, 0, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.826202, 12.947745, 36.87846, 7.562225, 5.385521, 8.581251, 28.297207, 18.322264, 9.974943], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.016373781, 0.035264775, -0.04551649, -0.11113674, 0.015097068, 0.035879187, -0.0016841837], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 375, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.24314599, 0.0, 1.0828197, 0.0, 0.1100605, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.035264775, 4.0, -0.11113674, 5.0, 0.035879187, -0.0016841837], "split_indices": [2, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.773853, 9.88467, 51.889183, 7.6133423, 44.27584, 7.3324995, 36.943344], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.014625641, -0.1532567, 0.074248694, -0.063274935, -0.08911509, 0.07305923, 0.041972384, -0.056529272, 0.009841588, 0.03879376, -0.013035418], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 376, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.6863456, 0.17503804, 0.24236894, 0.17357145, 0.0, 0.0, 0.34934846, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [3.0, 7.0, 4.0, 3.0, -0.08911509, 0.07305923, 5.0, -0.056529272, 0.009841588, 0.03879376, -0.013035418], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [67.22084, 16.765661, 50.45518, 11.62596, 5.1396995, 6.5353413, 43.919838, 5.0835905, 6.54237, 22.041117, 21.878723], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0082105175, -0.04692727, 0.032703135, 0.014051226, -0.06554832, -0.0031551647, 0.037704427], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 377, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.23394915, 0.44482914, 0.0, 0.09572134, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 4.0, 0.032703135, 3.0, -0.06554832, -0.0031551647, 0.037704427], "split_indices": [1, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.78942, 39.271313, 11.518109, 29.220858, 10.050456, 23.95797, 5.2628865], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0068592215, 0.07552853, -0.029586256, -0.055064615, 0.0007108978, 0.014250695, -0.025182582], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 378, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.5820935, 0.0, 0.2722372, 0.0, 0.18223657, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.07552853, 3.0, -0.055064615, 7.0, 0.014250695, -0.025182582], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.892517, 7.4780717, 53.414444, 8.493745, 44.9207, 29.973278, 14.947422], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0058738627, 0.0903697, -0.053510275, -0.023319367, 0.08249936, -0.09283959, 0.008262706, 0.0029750117, -0.017561488, -0.004556176, -0.06815586], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 379, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.2862371, 0.5334674, 0.11248603, 0.023451183, 0.0, 0.21120048, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 8.0, 1.0, 0.08249936, 6.0, 0.008262706, 0.0029750117, -0.017561488, -0.004556176, -0.06815586], "split_indices": [0, 0, 2, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.34702, 22.910522, 31.436499, 14.384809, 8.525713, 21.898832, 9.537667, 6.3095427, 8.075266, 15.05464, 6.844192], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0057969973, 0.02771755, -0.043626383, 0.11976556, -0.02309649, -0.00016814645, 0.08609794, -0.02058262, 0.021436984], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 380, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.2638343, 0.2135911, 0.0, 0.3539456, 0.12445831, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 3.0, -0.043626383, 2.0, 5.0, -0.00016814645, 0.08609794, -0.02058262, 0.021436984], "split_indices": [0, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.004837, 42.00701, 9.997827, 15.158641, 26.848372, 9.04739, 6.111251, 19.023558, 7.824813], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.05364344, -0.09957627, 0.067804225, -0.1439376, -0.047563832, -0.0069615105, -0.10350201, -0.034092396, -0.0015249647], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 381, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.6138705, 0.069502026, 0.0, 0.4602566, 0.048132766, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 6.0, 0.067804225, 7.0, 2.0, -0.0069615105, -0.10350201, -0.034092396, -0.0015249647], "split_indices": [2, 2, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.021168, 40.38893, 5.6322355, 19.806238, 20.582693, 13.383078, 6.423161, 6.4776816, 14.105011], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.072433166, -0.07795617, 0.15362035, -0.0652604, -0.0, 0.20667644, 0.07222534, 0.023877809, -0.01796762, 0.13050812, 0.02796259, -0.0031436284, 0.04470098], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 382, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6809547, 0.22694373, 0.112424195, 0.0, 0.06815027, 0.4373138, 0.119973585, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 4.0, -0.0652604, 7.0, 5.0, 6.0, 0.023877809, -0.01796762, 0.13050812, 0.02796259, -0.0031436284, 0.04470098], "split_indices": [0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.171146, 18.22312, 34.94803, 6.2644997, 11.958621, 19.481531, 15.4664955, 5.5794315, 6.379189, 5.2336783, 14.247852, 7.234575, 8.231921], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.013763381, 0.031293813, -0.0046897507, 0.08087004, -0.040853977, 0.0011358596, 0.059237342], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 383, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.0832746, 0.0, 0.42429963, 0.1779533, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, 0.031293813, 6.0, 4.0, -0.040853977, 0.0011358596, 0.059237342], "split_indices": [0, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [42.582672, 7.3385878, 35.244083, 21.123573, 14.120511, 13.853989, 7.2695847], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.022378027, 0.043820813, -0.25543964, -0.04402773, 0.079762116, -0.09497522, -0.03832827, 0.003498965, 0.07874598], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 384, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.82809824, 0.2836977, 0.007939637, 0.0, 0.40902802, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [8.0, 1.0, 5.0, -0.04402773, 6.0, -0.09497522, -0.03832827, 0.003498965, 0.07874598], "split_indices": [2, 1, 1, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.576466, 39.686745, 10.88972, 5.3662767, 34.32047, 5.8333974, 5.056322, 26.1761, 8.14437], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00076400023, -0.038524836, 0.076231495, 0.05298926, -0.22185263, 0.058707435, -0.0, 0.0523057, -0.04914548, -0.14526445, 0.017777355, -0.025270151, 0.026778925], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 385, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.18094827, 0.75820667, 0.18703222, 0.80450815, 1.1801437, 0.0, 0.10599938, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 8.0, 6.0, 6.0, 0.058707435, 9.0, 0.0523057, -0.04914548, -0.14526445, 0.017777355, -0.025270151, 0.026778925], "split_indices": [2, 2, 2, 1, 1, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.523643, 42.485798, 19.037848, 28.449688, 14.036109, 6.9246874, 12.113161, 18.660883, 9.788805, 7.0704436, 6.965665, 6.3953047, 5.7178564], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.037367567, 0.15576045, -0.00078215887, 0.0056719487, 0.08121449, -0.05431491, 0.042047456, -0.041179784, 0.04240169], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 386, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.3181137, 0.23281652, 0.3838781, 0.0, 0.0, 0.63899606, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [2.0, 1.0, 8.0, 0.0056719487, 0.08121449, 8.0, 0.042047456, -0.041179784, 0.04240169], "split_indices": [1, 1, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.973015, 15.96525, 50.007767, 8.217113, 7.7481375, 37.20174, 12.806029, 26.693903, 10.507836], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.036715344, 0.036677327, -0.23143286, -0.075947, 0.09198373, 0.0023841017, -0.109114826, -0.0, -0.043195307, 0.0066407654, 0.037103556], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 387, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.7829713, 0.25064027, 0.51256746, 0.06922824, 0.042695776, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 3.0, 3.0, 2.0, 0.0023841017, -0.109114826, -0.0, -0.043195307, 0.0066407654, 0.037103556], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.831566, 37.859634, 13.9719305, 11.750319, 26.109318, 5.0384026, 8.933529, 5.863627, 5.8866916, 9.664512, 16.444805], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0041411654, 0.10447821, -0.0880925, 0.15426333, -0.0074032987, 0.097485594, -0.1659039, 0.0046651843, 0.07443529, -0.05614978, -0.013614967], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 388, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.60323036, 0.19948047, 1.2563311, 0.24586046, 0.0, 0.0, 0.0522089, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 8.0, 1.0, 3.0, -0.0074032987, 0.097485594, 9.0, 0.0046651843, 0.07443529, -0.05614978, -0.013614967], "split_indices": [0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.32317, 27.41585, 36.907326, 20.20672, 7.2091293, 5.1434035, 31.763924, 9.054993, 11.151729, 25.825182, 5.938741], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.042588737, -0.0694761, 0.0172745, 0.08507367, -0.022378959, -0.01834734, 0.0006192826], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 389, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.6682495, 0.0, 0.49369767, 0.0, 0.042639773, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.0694761, 1.0, 0.08507367, 5.0, -0.01834734, 0.0006192826], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.815895, 13.138219, 42.677677, 5.060802, 37.616875, 15.544627, 22.072248], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.03032251, -0.046808355, 0.056721378, 0.14351851, 0.0045430134, -0.0023846328, 0.07903955, 0.037734512, -0.02583952], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 390, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.26017734, 0.0, 0.20066659, 0.33949175, 0.3617065, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.046808355, 5.0, 4.0, 3.0, -0.0023846328, 0.07903955, 0.037734512, -0.02583952], "split_indices": [2, 0, 2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.107258, 5.3863587, 46.720898, 16.164576, 30.556322, 7.1878576, 8.976718, 13.38084, 17.175484], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.014884857, 0.14076172, -0.05132467, 0.082748845, 0.0061350823, -0.10736592, 0.042278297, 0.062122248, -0.064525075], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 391, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.42630455, 0.25317445, 0.36150962, 0.0, 0.0, 0.9295206, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [5.0, 5.0, 9.0, 0.082748845, 0.0061350823, 3.0, 0.042278297, 0.062122248, -0.064525075], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.210964, 16.750135, 31.460829, 6.8905964, 9.859539, 25.104572, 6.3562574, 5.933444, 19.171127], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.009783847, -0.04472627, 0.19501744, -0.18941382, 0.04141376, 0.09053585, -0.0, 0.0022777955, -0.09364475, 0.049300943, -0.011360462], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 392, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5918604, 0.56938946, 0.24900264, 0.44347584, 0.29062277, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, 9.0, 1.0, 4.0, 0.09053585, -0.0, 0.0022777955, -0.09364475, 0.049300943, -0.011360462], "split_indices": [2, 0, 2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.26453, 42.868088, 12.3964405, 15.983294, 26.884796, 7.32824, 5.0682006, 6.182668, 9.800625, 10.61511, 16.269686], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.09815, 0.04637475, 0.12946136, 0.12963144, -0.016259976, 0.030979436, 0.1984559, 0.086765066, -0.0073280134, 0.023329614, -0.0027539867, 0.11499525, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 393, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.0498451, 0.18063065, 0.15072808, 0.32091844, 0.0, 0.031405453, 0.53842205, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 5.0, 4.0, -0.016259976, 2.0, 6.0, 0.086765066, -0.0073280134, 0.023329614, -0.0027539867, 0.11499525, -0.0], "split_indices": [2, 2, 1, 1, 0, 1, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.2964, 18.831755, 25.464645, 10.688822, 8.142932, 11.684599, 13.780046, 5.1381297, 5.5506926, 6.114651, 5.5699477, 6.6261687, 7.1538777], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.028681828, 0.06740665, -0.076998055, 0.0028646595, 0.12466272, -0.06805195, 0.026965875, 0.024979873, -0.047778506, 0.05112738, 0.012215697], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 394, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.21600875, 0.1313698, 0.37546134, 0.269281, 0.0482879, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 6.0, 3.0, 8.0, -0.06805195, 0.026965875, 0.024979873, -0.047778506, 0.05112738, 0.012215697], "split_indices": [1, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.74859, 38.096943, 12.651643, 19.22364, 18.873304, 6.792637, 5.859006, 13.588202, 5.6354375, 10.776826, 8.0964775], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.038710658, -0.00085566187, 0.113842085, -0.07754954, 0.07151219, 0.12718135, -0.033792417, 0.04125284, -0.054318033, -0.022543099, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 395, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.18910678, 0.7695589, 1.0377022, 0.0, 0.5397252, 0.0, 0.021340985, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 4.0, -0.07754954, 6.0, 0.12718135, 6.0, 0.04125284, -0.054318033, -0.022543099, -0.0], "split_indices": [2, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.781437, 38.27982, 20.501617, 8.133368, 30.14645, 6.2273426, 14.274276, 24.576174, 5.5702763, 5.8684363, 8.405839], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.02507899, 0.050586022, -0.057667907, -0.009833544, 0.09573749, 0.082306206, -0.036606293, -0.017876172, 0.04769874], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 396, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.3721968, 0.17871319, 0.0, 0.86258423, 0.3783709, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, -0.057667907, 2.0, 2.0, 0.082306206, -0.036606293, -0.017876172, 0.04769874], "split_indices": [1, 2, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.95046, 61.01589, 5.934576, 25.483076, 35.53281, 6.5511556, 18.931921, 9.842559, 25.690252], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.067865856, 0.26364037, -0.0, -0.0, 0.17732018, 0.05645862, -0.039481837, -0.03824249, 0.0020835854], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 397, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.69391763, 1.1651852, 0.29878256, 0.0, 0.0, 0.0, 0.14999466, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [2.0, 1.0, 1.0, -0.0, 0.17732018, 0.05645862, 3.0, -0.03824249, 0.0020835854], "split_indices": [0, 0, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.9332, 12.42484, 38.50836, 7.4187355, 5.0061045, 5.959439, 32.548923, 11.382994, 21.165928], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.01135771, 0.046708107, -0.23162371, -0.093075566, 0.09274682, -0.1029807, -0.02311669, 0.00076501287, -0.057363544, 0.004361983, 0.075030886], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 398, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.82964903, 0.33033416, 0.16589338, 0.13522513, 0.44549847, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 2.0, 6.0, 5.0, 6.0, -0.1029807, -0.02311669, 0.00076501287, -0.057363544, 0.004361983, 0.075030886], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.57572, 48.994026, 12.581697, 11.347315, 37.646713, 6.2206798, 6.361017, 5.724393, 5.6229215, 26.30225, 11.344462], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.120772384, -0.0077472376, -0.18138498, -0.05593921, 0.042521417, -0.10636862, -0.23326708, -0.0061805914, -0.0670586, -0.08990898, -0.041825805], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 399, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.36001194, 0.59192777, 0.07945514, 0.0, 0.0, 0.14328352, 0.035059333, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 4.0, -0.05593921, 0.042521417, 8.0, 7.0, -0.0061805914, -0.0670586, -0.08990898, -0.041825805], "split_indices": [2, 2, 0, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.516636, 19.999998, 34.516636, 9.301363, 10.698634, 16.147137, 18.369501, 10.494239, 5.652898, 8.847714, 9.521787], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.09369807, 0.04445588, -0.20939079, 0.061048333, -0.0948115, -0.10009705, -0.33529362, -0.0, -0.04565725, -0.054441076, -0.0, -0.047124457, -0.13508183], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 400, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.88598794, 0.58296865, 0.3337673, 0.0, 0.076255366, 0.12718035, 0.13541245, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 5.0, 4.0, 0.061048333, 7.0, 2.0, 7.0, -0.0, -0.04565725, -0.054441076, -0.0, -0.047124457, -0.13508183], "split_indices": [0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.81744, 23.958675, 28.858765, 11.305289, 12.653386, 16.834429, 12.024335, 5.062236, 7.5911508, 8.288111, 8.546318, 5.984518, 6.039817], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.05065592, -0.0850467, -0.015466251, -0.07820702, 0.029263932, -0.045150183, 0.0042395624, 0.06803713, -0.009658896], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 401, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.37698537, 0.0, 0.13357145, 0.14891991, 0.34347937, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.0850467, 6.0, 7.0, 7.0, -0.045150183, 0.0042395624, 0.06803713, -0.009658896], "split_indices": [0, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.195213, 5.1595025, 44.035713, 18.946966, 25.088745, 10.881086, 8.065881, 5.701567, 19.387177], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0121310465, 0.073451385, -0.05675207, 0.059242096, -0.011775883, -0.1369039, -0.007012296, -0.015707275, -0.054855227, -0.027424613, 0.038435772], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 402, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.20615876, 0.27440023, 0.1297124, 0.0, 0.0, 0.017832339, 0.28493208, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 5.0, 4.0, 0.059242096, -0.011775883, 2.0, 7.0, -0.015707275, -0.054855227, -0.027424613, 0.038435772], "split_indices": [1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.331276, 16.944595, 35.38668, 8.131575, 8.813021, 12.1098, 23.276882, 5.7835336, 6.326267, 14.982407, 8.294476], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.005836649, -0.06856055, 0.17510784, 0.04614488, -0.13733222, 0.08503665, 0.020507485, -0.021391477, -0.08954961], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 403, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.57723373, 0.492035, 0.10895455, 0.0, 0.20216057, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 1.0, 8.0, 0.04614488, 7.0, 0.08503665, 0.020507485, -0.021391477, -0.08954961], "split_indices": [1, 1, 1, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [43.03718, 29.899921, 13.137261, 6.42704, 23.472881, 5.233405, 7.9038568, 18.11455, 5.358331], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.066240236, 0.05575691, 0.07668886, -0.07693443, -0.015936056, 0.12324005, -0.0043773362, 0.057529863, 0.01817738, 0.06177647], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 404, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.19975498, 0.7169565, 0.2265372, 0.17531092, 0.0, 0.0, 0.058065027, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 4.0, 7.0, -0.07693443, -0.015936056, 8.0, -0.0043773362, 0.057529863, 0.01817738, 0.06177647], "split_indices": [1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.974487, 23.987417, 27.987072, 13.736435, 10.250982, 10.223497, 17.763575, 7.6626244, 6.0738106, 11.780007, 5.9835668], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.082832225, -0.008679883, -0.28489962, -0.07110043, 0.04492293, -0.01763271, -0.11609237, -0.03707993, -0.0017035938], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 405, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.8763937, 0.4550519, 0.29918337, 0.102938, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [8.0, 8.0, 4.0, 6.0, 0.04492293, -0.01763271, -0.11609237, -0.03707993, -0.0017035938], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.613884, 44.73529, 14.878595, 32.93925, 11.796038, 5.422268, 9.456327, 16.933071, 16.006182], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.025100948, 0.102132164, -0.0, -0.001644793, 0.058163937, -0.04992396, 0.023912692, -0.013182545, 0.020608103], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 406, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.1294776, 0.18392096, 0.21160586, 0.0, 0.0, 0.0, 0.15215433, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [2.0, 1.0, 2.0, -0.001644793, 0.058163937, -0.04992396, 5.0, -0.013182545, 0.020608103], "split_indices": [1, 1, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.52255, 15.612135, 52.910423, 7.1158905, 8.496244, 5.6444316, 47.26599, 17.911375, 29.354614], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.016102329, 0.006851892, -0.053278547, -0.038163334, 0.083988726, 0.017649509, -0.040804394, -0.0035938465, 0.039102014], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 407, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.21700567, 0.17222191, 0.0, 0.29829934, 0.096056834, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, -0.053278547, 2.0, 2.0, 0.017649509, -0.040804394, -0.0035938465, 0.039102014], "split_indices": [0, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.60604, 46.355522, 6.250519, 28.773085, 17.582436, 14.136801, 14.636285, 5.265201, 12.317235], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04848628, 0.071816005, -0.016258165, 0.12795228, -0.030317623, 0.015653027, 0.10214033, -0.052222196, 0.046316113], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 408, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.1514283, 0.3049595, 0.0, 0.4842416, 0.5120566, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 7.0, -0.016258165, 6.0, 9.0, 0.015653027, 0.10214033, -0.052222196, 0.046316113], "split_indices": [0, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.853313, 49.810474, 10.042839, 32.61205, 17.198425, 25.305185, 7.306863, 10.008067, 7.1903577], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.019384548, 0.064297445, -0.041075848, -0.008970896, 0.062778205, 0.04547266, -0.046305712], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 409, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.5361428, 0.35190904, 0.0, 0.5170781, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [6.0, 4.0, -0.041075848, 4.0, 0.062778205, 0.04547266, -0.046305712], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.877235, 30.07016, 21.807076, 20.139431, 9.930728, 9.223435, 10.915996], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0122327, -0.07482555, 0.075850405, 0.031520654, 0.045324624, 0.02549929, -0.024821388], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 410, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.8762924, 0.0, 0.12153767, 0.18674491, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.07482555, 5.0, 3.0, 0.045324624, 0.02549929, -0.024821388], "split_indices": [2, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.550117, 9.203768, 42.346348, 28.374863, 13.971487, 20.099094, 8.275769], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.005992318, -0.06047813, 0.058627736, 0.020953847, 0.06325301, 0.053090196, -0.009920774], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 411, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.6353531, 0.0, 0.18240777, 0.2814566, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.06047813, 9.0, 4.0, 0.06325301, 0.053090196, -0.009920774], "split_indices": [1, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.200024, 11.57896, 35.621063, 29.992449, 5.628613, 7.60506, 22.387388], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0113216, -0.06531387, 0.04727241, -0.0069654216, 0.065857954, -0.025960451, 0.04509911], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 412, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.6931593, 0.0, 0.42757696, 0.42565444, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.06531387, 7.0, 8.0, 0.065857954, -0.025960451, 0.04509911], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.057243, 11.730128, 42.327114, 32.47934, 9.847778, 22.289572, 10.189765], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.009304838, -0.044979084, 0.071679175, 0.0052341125, -0.0808452, -0.039590597, 0.06968277, 0.013979274, -0.03610544], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 413, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.12713729, 0.38731137, 0.46346417, 0.13680711, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, 4.0, 8.0, -0.0808452, -0.039590597, 0.06968277, 0.013979274, -0.03610544], "split_indices": [0, 0, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [42.814926, 30.887907, 11.927018, 25.676348, 5.2115593, 5.06272, 6.864299, 20.38686, 5.2894864], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.031333134, 0.0564324, -0.14930204, -0.0, 0.05471504, -0.29306534, 0.010260319, 0.01201753, -0.025502281, -0.04352749, -0.10840888], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 414, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.5612842, 0.2221606, 0.6468139, 0.07910215, 0.0, 0.032292128, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 8.0, 8.0, 6.0, 0.05471504, 3.0, 0.010260319, 0.01201753, -0.025502281, -0.04352749, -0.10840888], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.50073, 29.343756, 22.156973, 20.759928, 8.583828, 12.394524, 9.76245, 14.185628, 6.574301, 5.3631954, 7.0313277], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0035943293, 0.047442973, -0.023098517, -0.03906592, -0.0032350726, 0.004963994, -0.040226243], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 415, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.18829106, 0.0, 0.10853133, 0.0, 0.1441702, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.047442973, 2.0, -0.03906592, 9.0, 0.004963994, -0.040226243], "split_indices": [1, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.811504, 5.3351073, 56.4764, 7.177714, 49.298683, 42.919605, 6.37908], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.008066835, 0.066665046, -0.023790201, -0.0073638395, 0.17430767, -0.049899116, 0.034383792, 0.14262034, -0.028326603, -0.016496666, 0.07985316], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 416, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.136019, 0.27376142, 0.39036182, 0.0, 1.0957142, 0.0, 0.7016193, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 5.0, 6.0, -0.0073638395, 8.0, -0.049899116, 8.0, 0.14262034, -0.028326603, -0.016496666, 0.07985316], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.71715, 24.9219, 43.795246, 13.405603, 11.516296, 12.652759, 31.142488, 5.2439065, 6.27239, 22.715439, 8.427051], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.024819126, 0.028834447, 0.009557178, -0.053255282, 0.065417334, -0.11005853, 0.021058727, 0.06704518, -0.0113288555], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 417, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.043071758, 0.0, 0.1575561, 0.83803743, 0.42414165, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.028834447, 3.0, 3.0, 3.0, -0.11005853, 0.021058727, 0.06704518, -0.0113288555], "split_indices": [2, 0, 1, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.139683, 6.5749755, 42.564705, 19.214846, 23.349861, 5.099287, 14.115558, 9.144378, 14.205483], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0066555357, -0.02798892, 0.038622346, 0.007804185, -0.17952305, 0.03265533, -0.0075442595, -0.0, -0.09608928], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 418, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.18119973, 0.33092508, 0.0, 0.16763316, 0.274522, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 7.0, 0.038622346, 2.0, 5.0, 0.03265533, -0.0075442595, -0.0, -0.09608928], "split_indices": [1, 1, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.972687, 56.58836, 7.3843274, 46.009315, 10.579044, 11.494047, 34.515266, 5.2656217, 5.3134227], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0432417, 0.14906682, -0.01741695, 0.01060964, 0.0736971, 0.1377525, -0.1341331, 0.06529961, 0.005817936, -0.09027893, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 419, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.35948303, 0.18521744, 0.6398769, 0.0, 0.0, 0.11265653, 0.45843127, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [5.0, 5.0, 5.0, 0.01060964, 0.0736971, 8.0, 7.0, 0.06529961, 0.005817936, -0.09027893, -0.0], "split_indices": [2, 1, 1, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.545803, 19.21478, 33.331024, 9.928667, 9.286115, 13.863303, 19.46772, 7.2705393, 6.592764, 8.128663, 11.339057], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.002426114, -0.05497355, 0.09216167, 0.032380123, -0.10005298, -0.04877567, 0.07946522, -0.08456119, 0.0076105217], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 420, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.26766255, 0.23472568, 0.9623625, 0.0, 0.6021427, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 3.0, 0.032380123, 5.0, -0.04877567, 0.07946522, -0.08456119, 0.0076105217], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.259712, 29.59937, 19.660343, 5.6324196, 23.96695, 7.733691, 11.926652, 9.600079, 14.366871], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.019855224, -0.08424888, 0.07447813, -0.0, -0.11111133, -0.004881118, 0.06424841, -0.050052565, 0.006880919, 0.046254165, -0.04045301], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 421, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.30700114, 0.068585485, 0.242787, 0.0, 0.19039932, 0.29222316, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 8.0, -0.0, 6.0, 4.0, 0.06424841, -0.050052565, 0.006880919, 0.046254165, -0.04045301], "split_indices": [0, 2, 2, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.34577, 29.426016, 18.919758, 7.37792, 22.048096, 12.224, 6.6957583, 15.927549, 6.120546, 5.066848, 7.1571517], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.039802596, -0.07800318, 0.07347128, -0.036497857, -0.002132337, 0.22426793, -0.0, 0.022719808, 0.097391024, -0.018400466, 0.052226555], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 422, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.211548, 0.021492824, 0.4474908, 0.0, 0.0, 0.13461304, 0.32992607, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 6.0, 4.0, -0.036497857, -0.002132337, 2.0, 8.0, 0.022719808, 0.097391024, -0.018400466, 0.052226555], "split_indices": [0, 2, 2, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.00484, 10.384135, 40.620705, 5.110557, 5.273578, 12.364068, 28.256638, 6.0949926, 6.269076, 21.115637, 7.1410003], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.04616237, 0.068835236, -0.24105543, 0.020397618, 0.049888205, -0.11749552, -0.021861998, 0.021098487, -0.005261728], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 423, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [1.033097, 0.11245911, 0.35786664, 0.046026584, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 8.0, 4.0, 4.0, 0.049888205, -0.11749552, -0.021861998, 0.021098487, -0.005261728], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [43.58645, 27.496002, 16.090448, 19.860554, 7.635449, 7.474333, 8.616115, 9.344224, 10.516329], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0021263096, -0.047824226, 0.057721928, -0.0, -0.05886184, 0.05549391, -0.03070544], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 424, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.4917542, 0.29133922, 0.0, 0.60107625, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, 0.057721928, 3.0, -0.05886184, 0.05549391, -0.03070544], "split_indices": [0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.249275, 38.393658, 9.855618, 29.485348, 8.90831, 10.424697, 19.06065], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.10605422, 0.12890391, -0.014189684, 0.09802292, 0.08303143, 0.055551596, 0.0031578632], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 425, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.2282474, 0.1819045, 0.0, 0.3350891, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, -0.014189684, 4.0, 0.08303143, 0.055551596, 0.0031578632], "split_indices": [1, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.06888, 52.985527, 7.0833497, 45.766697, 7.2188306, 21.797037, 23.969662], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.022518137, -0.08922383, 0.019568624, -0.0, -0.13058737, 0.16214237, -0.054770336, -0.09110219, -0.011768222, 0.09485548, -0.015480095, -0.04304623, 0.0020234045], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 426, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.20480889, 0.11581801, 0.47249243, 0.0, 0.26878324, 0.54841995, 0.1694783, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 6.0, -0.0, 2.0, 6.0, 3.0, -0.09110219, -0.011768222, 0.09485548, -0.015480095, -0.04304623, 0.0020234045], "split_indices": [0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.84952, 27.120598, 41.728924, 8.201415, 18.919182, 14.357303, 27.37162, 5.355116, 13.564066, 8.337275, 6.020028, 11.32776, 16.043861], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.015769549, 0.021133352, -0.11987144, -0.041491758, 0.057569202, -0.06543168, -0.0046233297, -0.002300963, 0.050829552], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 427, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.21741991, 0.22958869, 0.12542106, 0.0, 0.26123446, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 1.0, 3.0, -0.041491758, 5.0, -0.06543168, -0.0046233297, -0.002300963, 0.050829552], "split_indices": [0, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.36566, 38.540062, 13.8256035, 6.1384535, 32.401608, 6.071118, 7.754485, 20.48948, 11.9121275], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.011758853, 0.05664165, -0.20214593, 0.08389601, -0.01512146, -0.08275589, -0.030013645, 0.008221081, 0.07233252], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 428, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.81854975, 0.1393562, 0.06042224, 0.2962344, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 8.0, 4.0, 6.0, -0.01512146, -0.08275589, -0.030013645, 0.008221081, 0.07233252], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.85967, 44.168476, 15.691193, 36.122116, 8.046359, 7.5555673, 8.135626, 27.905128, 8.216988], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.052047953, 0.0048662736, 0.079905055, 0.04695074, -0.027793849, -0.047994178, 0.028157223], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 429, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.50626785, 0.18338314, 0.0, 0.32514724, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, 0.079905055, 1.0, -0.027793849, -0.047994178, 0.028157223], "split_indices": [0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.880856, 43.740173, 8.140682, 31.632309, 12.107863, 5.005919, 26.62639], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.062386416, -0.0, 0.12998374, 0.061784547, -0.031665448, -0.0048569413, 0.19661798, 0.05955529, -0.019710923, 0.084770136, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 430, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.22221196, 0.18338838, 0.26234463, 0.3221203, 0.0, 0.0, 0.2901302, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 2.0, 5.0, -0.031665448, -0.0048569413, 8.0, 0.05955529, -0.019710923, 0.084770136, -0.0], "split_indices": [0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.37027, 25.615183, 23.755087, 15.968762, 9.6464205, 7.2053127, 16.549774, 7.8146396, 8.154123, 11.130882, 5.418892], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.004812887, 0.12872455, -0.05984159, 0.057097066, 0.0105223255, 0.029038103, -0.1067025, -0.052089665, 0.0055025024], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 431, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.3524968, 0.050124705, 0.26899195, 0.0, 0.0, 0.0, 0.2529508, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 5.0, 3.0, 0.057097066, 0.0105223255, 0.029038103, 8.0, -0.052089665, 0.0055025024], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.858368, 12.7534, 34.10497, 6.4263434, 6.3270564, 7.116876, 26.988092, 17.84678, 9.141312], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.032017138, -0.004117125, -0.046597525, -0.0583065, 0.0472224, -0.00064639054, -0.043947756], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 432, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.16730344, 0.39557585, 0.0, 0.15861025, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, -0.046597525, 5.0, 0.0472224, -0.00064639054, -0.043947756], "split_indices": [1, 2, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.451015, 44.277115, 8.173902, 34.096386, 10.180728, 22.15056, 11.945826], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0012731017, 0.030985462, -0.047065333, -0.052957702, 0.15313362, 0.0003664642, -0.022418443, 0.07881803, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 433, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.19264664, 0.40021053, 0.0, 0.033796355, 0.25165814, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, -0.047065333, 2.0, 5.0, 0.0003664642, -0.022418443, 0.07881803, -0.0], "split_indices": [0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [41.64002, 36.263687, 5.3763337, 21.34361, 14.920076, 5.0718923, 16.271719, 8.054389, 6.8656864], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.101291366, -0.04152659, -0.09526066, 0.05671729, -0.1138996, -0.097968474, -0.0133645935], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 434, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.61500424, 0.7111078, 0.0, 0.0, 0.43130517, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 3.0, -0.09526066, 0.05671729, 5.0, -0.097968474, -0.0133645935], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.38562, 40.771748, 9.6138735, 9.024672, 31.747074, 6.5404077, 25.206667], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.022057248, -0.13189143, 0.061691806, -0.056349818, -0.010589698, 0.049834624, 0.019739078, -0.0031752677, 0.049903154], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 435, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.3945356, 0.04131967, 0.20801574, 0.0, 0.0, 0.0, 0.19183466, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 7.0, 5.0, -0.056349818, -0.010589698, 0.049834624, 9.0, -0.0031752677, 0.049903154], "split_indices": [2, 0, 2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.477066, 12.003253, 51.473812, 6.347339, 5.655914, 13.221429, 38.252384, 31.899982, 6.3524017], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.03353079, 0.056258786, -0.021954013, -0.10469246, 0.12922603, -0.09943962, 0.000119488184, 0.124544345, -0.01530755], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 436, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.61767805, 0.0, 0.6587757, 0.85414207, 1.0025603, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, 0.056258786, 7.0, 5.0, 5.0, -0.09943962, 0.000119488184, 0.124544345, -0.01530755], "split_indices": [0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.8693, 18.088102, 50.781197, 33.4458, 17.335396, 10.210426, 23.235374, 6.413562, 10.921834], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.06367097, 0.09662808, -0.04508431, -0.00903988, 0.13800171, 0.0063725864, 0.07477433], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 437, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.37023497, 0.25297695, 0.0, 0.0, 0.42216885, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, -0.04508431, -0.00903988, 4.0, 0.0063725864, 0.07477433], "split_indices": [0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.20787, 44.386368, 5.821503, 10.394442, 33.991924, 17.60462, 16.387304], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.006032846, -0.05705909, 0.03134075, -0.04580426, 0.015174999, 0.07175863, -0.022463772, 0.0872899, 0.0009607223, -0.046988964, 0.021143809], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 438, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.10348057, 0.20235822, 0.11175415, 0.0, 0.0, 0.40437028, 0.27471137, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 5.0, 5.0, -0.04580426, 0.015174999, 4.0, 8.0, 0.0872899, 0.0009607223, -0.046988964, 0.021143809], "split_indices": [0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.29875, 16.917625, 47.381123, 9.229445, 7.68818, 27.927168, 19.453957, 5.5329065, 22.39426, 8.121311, 11.332644], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.05800381, 0.1029712, -0.038099453, -0.007555488, 0.13003471, 0.0010371323, 0.04661449], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 439, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.46997577, 0.17123061, 0.0, 0.0, 0.1064347, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 1.0, -0.038099453, -0.007555488, 3.0, 0.0010371323, 0.04661449], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.210743, 44.40211, 9.808633, 7.109637, 37.292473, 7.0696497, 30.222822], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.024138002, -0.0, -0.08085594, -0.023904765, 0.01865101, -0.035964422, -0.010301065, 0.002394206, -0.04412866], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 440, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.08230066, 0.061548226, 0.012103371, 0.12092708, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 7.0, 3.0, 5.0, 0.01865101, -0.035964422, -0.010301065, 0.002394206, -0.04412866], "split_indices": [2, 1, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.31502, 36.885773, 16.42925, 25.945486, 10.940286, 6.9076433, 9.521608, 20.708878, 5.2366085], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.031603117, 0.034608062, -0.06095562, -0.1625098, 0.021258516, -0.015969086, -0.0780634, 0.024510426, -0.0010716971], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 441, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.21049634, 0.0, 0.3647986, 0.1604935, 0.043107644, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.034608062, 6.0, 3.0, 7.0, -0.015969086, -0.0780634, 0.024510426, -0.0010716971], "split_indices": [1, 0, 1, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.427788, 6.840659, 40.587128, 18.265064, 22.322063, 9.834024, 8.43104, 7.0057325, 15.316331], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.04489463, -0.0735163, -0.010105171, -0.019244295, 0.0023606902, -0.010263107, 0.021805706], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 442, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.36353835, 0.0, 0.039589725, 0.0, 0.108120635, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, -0.0735163, 3.0, -0.019244295, 6.0, -0.010263107, 0.021805706], "split_indices": [1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.226547, 6.856959, 48.369587, 9.792559, 38.57703, 24.781498, 13.795532], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.033066876, 0.06806889, -0.0358113, 0.17366417, -0.026110262, -0.0, 0.08192213, -0.023649344, 0.025768615], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 443, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.2527504, 0.40950972, 0.0, 0.3187253, 0.13071433, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, -0.0358113, 3.0, 9.0, -0.0, 0.08192213, -0.023649344, 0.025768615], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.80891, 38.238293, 7.5706153, 18.129272, 20.10902, 7.1182995, 11.010974, 14.481568, 5.627453], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.013816459, 0.06568397, -0.020487107, -0.064857066, 0.0321703, -0.0023913512, -0.039645918], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 444, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.38594767, 0.0, 0.2550356, 0.11604531, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, 0.06568397, 8.0, 6.0, 0.0321703, -0.0023913512, -0.039645918], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.39006, 6.865851, 43.52421, 33.24884, 10.275372, 19.429409, 13.819429], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.06458785, 0.0055628493, 0.1778497, -0.106826715, 0.065288655, 0.09754839, -0.0032114922, -0.010448956, -0.051033683, 0.046583407, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 445, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.40190923, 0.29170036, 0.61751634, 0.040705845, 0.1836727, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 5.0, 5.0, 4.0, 0.09754839, -0.0032114922, -0.010448956, -0.051033683, 0.046583407, -0.0], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.010056, 42.0357, 19.974354, 13.732156, 28.303545, 11.104571, 8.8697815, 7.8884053, 5.8437505, 11.798261, 16.505283], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.019548237, 0.069175676, -0.07019501, -0.0065223803, 0.11123586, -0.17141888, 0.022356747, 0.057523143, 0.0049913772, -0.097264156, -0.029209275, 0.06330345, -0.017930483], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 446, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.3251222, 0.10955963, 0.4550407, 0.0, 0.11664891, 0.18511045, 0.41036418, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 7.0, -0.0065223803, 7.0, 2.0, 8.0, 0.057523143, 0.0049913772, -0.097264156, -0.029209275, 0.06330345, -0.017930483], "split_indices": [2, 1, 2, 0, 1, 1, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [70.24931, 24.677876, 45.57144, 7.2063136, 17.471561, 21.87504, 23.6964, 8.312842, 9.158718, 5.5974927, 16.277548, 7.080222, 16.616177], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.07002358, -0.13954733, -0.011341287, -0.057780202, -0.10323016, 0.08532581, -0.084197916, 0.0020573752, -0.056873698, -0.05250957, 0.018869814], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 447, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.20335066, 0.33827078, 0.6743609, 0.1777381, 0.0, 0.0, 0.36971012, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [3.0, 2.0, 4.0, 6.0, -0.10323016, 0.08532581, 6.0, 0.0020573752, -0.056873698, -0.05250957, 0.018869814], "split_indices": [1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.929474, 22.816069, 30.113405, 17.565756, 5.2503123, 5.1066475, 25.006758, 11.912661, 5.6530957, 15.762921, 9.243837], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.013904322, 0.08094646, -0.027169086, 0.17863743, -0.04109977, -0.09582048, 0.058726717, -0.0, 0.09870016, 0.0070322105, -0.11620013], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 448, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.18065713, 0.5620904, 0.6025608, 0.4896658, 0.0, 1.1120543, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 8.0, 3.0, -0.04109977, 5.0, 0.058726717, -0.0, 0.09870016, 0.0070322105, -0.11620013], "split_indices": [2, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.854824, 24.054962, 37.799862, 17.051199, 7.003763, 29.678799, 8.121063, 8.205574, 8.845625, 21.47124, 8.207561], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.035923947, 0.052366484, -0.07547971, 0.07850622, -0.0199663, -0.0847986, -0.007365123, 0.017524896, -0.046255868], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 449, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.20109628, 0.46182045, 0.52943027, 0.0, 0.0, 0.0, 0.3207026, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 4.0, 4.0, 0.07850622, -0.0199663, -0.0847986, 8.0, 0.017524896, -0.046255868], "split_indices": [1, 2, 2, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.7876, 16.068953, 38.718647, 5.6914353, 10.377517, 8.437283, 30.281366, 20.857456, 9.42391], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0067854975, 0.06994413, -0.04332479, 0.04214646, -0.0105169965, -0.10078277, -0.0, -0.06490055, -0.0, 0.018006092, -0.051104534], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 450, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.15415953, 0.14283413, 0.09079717, 0.0, 0.0, 0.19201216, 0.25722462, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 4.0, 6.0, 0.04214646, -0.0105169965, 5.0, 8.0, -0.06490055, -0.0, 0.018006092, -0.051104534], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.853977, 16.194923, 37.659054, 10.054366, 6.1405573, 15.083391, 22.575663, 6.593313, 8.490078, 16.938454, 5.637209], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.046670534, -0.1290431, 0.13526034, -0.081856266, -0.0070888414, 0.24027407, -0.022888973, 0.041636325, 0.11904369], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 451, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.7265757, 0.19719735, 0.72622544, 0.0, 0.0, 0.23066056, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 2.0, 7.0, -0.081856266, -0.0070888414, 5.0, -0.022888973, 0.041636325, 0.11904369], "split_indices": [0, 0, 1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.51456, 14.389079, 30.125483, 5.013003, 9.376076, 20.357342, 9.768141, 13.882508, 6.4748335], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.06656008, 0.041414928, 0.04062482, 0.013897488, 0.04482256, 0.01728177, -0.037923694], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 452, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.07038717, 0.10681178, 0.0, 0.21762833, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, 0.04062482, 6.0, 0.04482256, 0.01728177, -0.037923694], "split_indices": [2, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.105816, 41.13699, 11.968826, 34.42098, 6.7160106, 27.23809, 7.18289], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.07046274, 0.038923014, 0.12153987, -0.009946626, 0.061550632, 0.06464548, -0.0, 0.03729466, 0.00053021597], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 453, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.050189063, 0.0536727, 0.16889784, 0.0, 0.077881925, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 2.0, 9.0, -0.009946626, 4.0, 0.06464548, -0.0, 0.03729466, 0.00053021597], "split_indices": [0, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [43.052067, 28.91633, 14.135738, 5.741765, 23.174564, 7.452517, 6.6832213, 9.962767, 13.211799], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.051657878, -0.13499412, 0.006762749, 0.09975209, -0.083265744, 0.054655746, -0.03798076, -0.07055525, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 454, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [1.1864799, 0.0, 0.37375954, 0.44618255, 0.27113372, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.13499412, 5.0, 7.0, 3.0, 0.054655746, -0.03798076, -0.07055525, -0.0], "split_indices": [1, 0, 1, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.951565, 5.4949937, 42.45657, 21.37812, 21.07845, 16.137932, 5.2401886, 6.6694183, 14.40903], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.05561539, 0.026466591, -0.08825758, -0.068649195, -0.058525383, 0.007782211, -0.038272157], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 455, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.29330552, 0.0, 0.1694799, 0.0, 0.26846614, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.026466591, 4.0, -0.068649195, 5.0, 0.007782211, -0.038272157], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.810566, 10.1811695, 49.6294, 6.908141, 42.721256, 18.84476, 23.876495], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.012617506, -0.1257185, 0.06415335, -0.09517821, -0.017389482, -0.0153617505, 0.10420282, 0.029416159, -0.03381272, 0.07513947, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 456, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.61148924, 0.5560571, 0.20056576, 0.0, 0.22939457, 0.0, 0.4831547, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 2.0, -0.09517821, 2.0, -0.0153617505, 5.0, 0.029416159, -0.03381272, 0.07513947, -0.0], "split_indices": [2, 1, 1, 0, 2, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [67.78694, 27.677162, 40.109783, 8.96821, 18.708952, 9.62121, 30.488571, 7.973865, 10.735086, 12.152078, 18.336494], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.027118817, 0.11063593, -0.033851966, 0.089020744, 0.037411284, -0.07030439, 0.028963381, -0.000608311, 0.024555326, 0.03783404, -0.024549615], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 457, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.2816403, 0.27325457, 0.41455805, 0.0, 0.040159576, 0.0, 0.2722405, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [3.0, 2.0, 2.0, 0.089020744, 5.0, -0.07030439, 7.0, -0.000608311, 0.024555326, 0.03783404, -0.024549615], "split_indices": [1, 2, 2, 0, 2, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.06582, 22.319023, 29.746796, 5.0546374, 17.264385, 6.8247075, 22.922089, 8.4602785, 8.804107, 12.620347, 10.301742], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.019793518, -0.0047591324, -0.041496597, -0.06778922, 0.02680647, 0.018724892, -0.0025993853], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 458, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.09658134, 0.426313, 0.0, 0.0, 0.06992262, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 1.0, -0.041496597, -0.06778922, 5.0, 0.018724892, -0.0025993853], "split_indices": [2, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.596848, 56.335033, 5.261816, 6.6615787, 49.673454, 25.816015, 23.857439], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.048250742, 0.007813223, 0.09617922, 0.05968032, -0.048626363, 0.14811055, 0.005920267, -0.040345024, 0.036193725, 0.077515006, -0.0007216844], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 459, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.09194741, 0.2732514, 0.072087646, 0.31061918, 0.0, 0.23927563, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 8.0, 2.0, -0.048626363, 6.0, 0.005920267, -0.040345024, 0.036193725, 0.077515006, -0.0007216844], "split_indices": [0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.19522, 30.072414, 22.122805, 23.987015, 6.0853996, 11.836389, 10.286416, 5.051796, 18.93522, 6.8012877, 5.035101], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.03942433, -0.061575215, 0.1435516, 0.049001813, -0.082934104, 0.08524701, 0.07742844, 0.040932428, -0.0060851406, -0.0, 0.035266567], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 460, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.57974833, 0.68667495, 0.12486708, 0.1271577, 0.0, 0.05774744, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 5.0, 2.0, -0.082934104, 7.0, 0.07742844, 0.040932428, -0.0060851406, -0.0, 0.035266567], "split_indices": [0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.505924, 26.373201, 26.13272, 17.614834, 8.758368, 19.05211, 7.08061, 7.9967904, 9.618043, 5.3286285, 13.723483], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.08503212, -0.06428945, 0.078585565, -0.0, -0.1087777, -0.0, 0.036755886, -0.034536578, -0.0016648675, -0.0682465, -0.021695033, 0.023063842], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 461, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.26445156, 0.31055126, 0.07695404, 0.0, 0.22743511, 0.17294917, 0.074463904, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 8.0, 0.078585565, 3.0, 8.0, 7.0, 0.036755886, -0.034536578, -0.0016648675, -0.0682465, -0.021695033, 0.023063842], "split_indices": [0, 2, 0, 0, 0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.365593, 19.840582, 26.52501, 5.721749, 14.118833, 15.118078, 11.406931, 6.8562655, 7.2625675, 9.096818, 6.0212603, 6.0175896, 5.3893414], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.0056120893, -0.031199194, 0.0369642, 0.12368579, -0.015175801, 0.05724786, -0.0, -0.029834861, 0.037566513], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 462, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.18426953, 0.0, 0.21072872, 0.12908542, 0.3354618, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.031199194, 5.0, 6.0, 7.0, 0.05724786, -0.0, -0.029834861, 0.037566513], "split_indices": [0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.453487, 10.664148, 42.789337, 16.271528, 26.51781, 9.800831, 6.4706984, 17.198385, 9.319426], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.020493751, -0.0, 0.036225956, -0.03561401, 0.028916752, 0.05017205, -0.017582264], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 463, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.09979375, 0.1431607, 0.0, 0.0, 0.41988847, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 3.0, 0.036225956, -0.03561401, 5.0, 0.05017205, -0.017582264], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.386204, 39.177376, 7.20883, 7.2018304, 31.975544, 12.481513, 19.494032], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.10166643, 0.12787572, 0.023671761, 0.09025545, 0.0702784, -0.032062728, 0.054562513, 0.047442816, -0.0294475], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 464, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.08270788, 0.08950174, 0.32848105, 0.38906503, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 8.0, 6.0, 5.0, 0.0702784, -0.032062728, 0.054562513, 0.047442816, -0.0294475], "split_indices": [2, 1, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.354465, 34.754047, 13.600418, 27.73478, 7.019268, 7.282952, 6.317466, 20.896856, 6.8379245], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.015791144, -0.18099959, 0.040430058, -0.10764284, 0.005493158, 0.13814878, -0.097132824, 0.013354259, 0.06668085, -0.0, -0.058580812], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 465, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.595891, 0.619665, 0.6428001, 0.0, 0.0, 0.18424094, 0.18349737, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 6.0, -0.10764284, 0.005493158, 4.0, 7.0, 0.013354259, 0.06668085, -0.0, -0.058580812], "split_indices": [0, 2, 2, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.854305, 15.339065, 45.51524, 7.9490128, 7.390052, 27.03408, 18.481161, 14.079809, 12.95427, 9.8484125, 8.632749], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.053696282, 0.09276149, -0.001447165, 0.054798406, 0.085504465, -0.07229271, 0.03805048, -0.0015167958, 0.07868052, 0.008902544, -0.06286436], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 466, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.16147487, 0.26011252, 0.26929435, 0.48787498, 0.0, 0.30362538, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 6.0, 8.0, 0.085504465, 3.0, 0.03805048, -0.0015167958, 0.07868052, 0.008902544, -0.06286436], "split_indices": [0, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [69.65726, 41.434868, 28.22239, 36.10969, 5.3251777, 18.964804, 9.257586, 28.407776, 7.7019153, 10.912819, 8.051986], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.07867604, -0.1606222, 0.04219214, -0.0056321453, -0.24548765, 0.04553549, -0.03754231, -0.032040842, 0.025433602, -0.037777007, -0.09729108], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 467, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.54260063, 0.39741665, 0.4191248, 0.12873508, 0.106239796, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 5.0, 1.0, 3.0, 0.04553549, -0.03754231, -0.032040842, 0.025433602, -0.037777007, -0.09729108], "split_indices": [0, 0, 2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.90233, 31.228493, 20.673836, 11.843455, 19.385036, 12.909466, 7.7643695, 6.0255537, 5.8179016, 9.2377615, 10.147275], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.010750556, -0.097933695, 0.0438708, 0.012643149, -0.18421999, 0.08083921, -0.00076516287, -0.02993994, -0.075035825, -0.02550151, 0.06514165], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 468, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.29822996, 0.31631574, 0.40202785, 0.0, 0.027376473, 0.0, 0.58736813, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 1.0, 0.012643149, 2.0, 0.08083921, 8.0, -0.02993994, -0.075035825, -0.02550151, 0.06514165], "split_indices": [2, 1, 1, 0, 2, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.58873, 23.35261, 36.236122, 8.654883, 14.697726, 5.523482, 30.71264, 8.2109785, 6.4867477, 22.920557, 7.7920823], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0122879315, -0.039104767, 0.10381754, 0.06714903, -0.14137842, 0.072322525, 0.013956434, -0.0, 0.041772556, -0.009467241, -0.09317627, 0.05669703, -0.036746167], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 469, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.25701025, 0.38186574, 0.2155319, 0.07895828, 0.28887028, 0.0, 0.3516543, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 3.0, 6.0, 7.0, 0.072322525, 8.0, -0.0, 0.041772556, -0.009467241, -0.09317627, 0.05669703, -0.036746167], "split_indices": [1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.398315, 32.58338, 18.81493, 15.6696205, 16.913763, 6.338169, 12.476762, 8.720417, 6.9492035, 11.334727, 5.5790353, 5.617836, 6.8589253], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.015328662, -0.049566958, 0.058169097, -0.16377425, 0.035384472, -0.020952577, -0.06270417, 0.052444622, -0.014602512], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 470, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.3695417, 0.4660701, 0.0, 0.04399854, 0.33483717, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 4.0, 0.058169097, 4.0, 6.0, -0.020952577, -0.06270417, 0.052444622, -0.014602512], "split_indices": [0, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.259693, 45.11757, 6.142124, 19.351294, 25.766273, 7.8358245, 11.51547, 9.732606, 16.033667], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0067861006, -0.060889304, 0.0636269, 0.23221439, -0.028030967, 0.1032231, 0.026980344, -0.027126176, 0.03743321], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 471, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.5730258, 0.0, 0.6298649, 0.15220636, 0.25366896, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.060889304, 5.0, 7.0, 9.0, 0.1032231, 0.026980344, -0.027126176, 0.03743321], "split_indices": [0, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.14856, 9.129476, 38.019085, 13.234401, 24.784683, 6.2216763, 7.012725, 18.353771, 6.430912], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.009105883, 0.05611242, -0.09725143, -0.007588682, 0.058777384, 0.036807884, -0.1685014, 0.03554274, -0.056697022, -0.004159103, -0.06714174], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 472, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.3231305, 0.29936346, 0.40469813, 0.5324473, 0.0, 0.0, 0.13607687, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 8.0, 2.0, 5.0, 0.058777384, 0.036807884, 7.0, 0.03554274, -0.056697022, -0.004159103, -0.06714174], "split_indices": [1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.544407, 30.33136, 23.213047, 20.995766, 9.335594, 5.159736, 18.05331, 12.266684, 8.729082, 5.56151, 12.4918], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.05446204, 0.08749638, -0.030800786, 0.052082185, 0.06343213, 0.030343069, -0.025791118], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 473, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.23266044, 0.12466213, 0.0, 0.21790442, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, -0.030800786, 6.0, 0.06343213, 0.030343069, -0.025791118], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [42.38399, 35.91336, 6.470632, 29.664444, 6.248917, 22.6181, 7.0463443], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.058419682, -0.13281648, 0.018131137, -0.13442412, -0.014092269, 0.040041525, -0.01356371, -0.028982338, 0.0053788405, -0.039591707, 0.035093762], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 474, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.3025193, 0.9388013, 0.1059137, 0.0, 0.06377425, 0.0, 0.32597962, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 1.0, -0.13442412, 1.0, 0.040041525, 8.0, -0.028982338, 0.0053788405, -0.039591707, 0.035093762], "split_indices": [2, 0, 0, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.682312, 25.48537, 24.19694, 5.9825935, 19.502777, 5.2037883, 18.99315, 5.7616773, 13.7411, 10.355395, 8.637755], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.025970409, 0.036547497, -0.057707377, -0.08187499, 0.019670822, 0.012126738, -0.032963995, 0.01709869, -0.0017280289], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 475, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.2889817, 0.0, 0.10478358, 0.15072948, 0.016994629, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.036547497, 8.0, 3.0, 6.0, 0.012126738, -0.032963995, 0.01709869, -0.0017280289], "split_indices": [2, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.47713, 9.632094, 50.845036, 39.684555, 11.16048, 6.646253, 33.038303, 5.6131377, 5.5473413], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.03887184, 0.05681734, -0.14215115, 0.18243217, -0.095767595, -0.08335503, -0.06713085, 0.031621646, 0.10445043, 0.030121768, -0.050919376], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 476, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.69348717, 1.7339127, 0.29019928, 0.2708521, 0.0, 0.0, 0.42814457, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 8.0, 7.0, 5.0, -0.095767595, -0.08335503, 8.0, 0.031621646, 0.10445043, 0.030121768, -0.050919376], "split_indices": [2, 1, 2, 2, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [67.49103, 34.763737, 32.727287, 26.644194, 8.119545, 10.21445, 22.512836, 19.719023, 6.9251714, 8.188509, 14.324327], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.00039711964, -0.18166074, 0.13517608, -0.104236536, -0.060887594, 0.074340396, 0.0598382, 0.01840901, -0.046939928, -0.027219156, 0.04571256], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 477, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [1.2671229, 0.37318194, 0.20976883, 0.0, 0.18427509, 0.0, 0.29702818, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 2.0, -0.104236536, 6.0, 0.074340396, 6.0, 0.01840901, -0.046939928, -0.027219156, 0.04571256], "split_indices": [0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.828796, 20.733044, 29.095753, 7.5979056, 13.135138, 10.212371, 18.883383, 5.4614043, 7.6737328, 6.783953, 12.099429], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0013537912, -0.054774743, 0.10484482, 0.07087101, -0.14466994, 0.06155032, 0.05401531, -0.023589166, 0.07234172, 0.0450086, -0.07945878, -0.014034261, 0.043977696], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 478, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.3778399, 0.4807689, 0.08126229, 0.47580862, 0.92593926, 0.0, 0.17931479, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 2.0, 2.0, 1.0, 0.06155032, 6.0, -0.023589166, 0.07234172, 0.0450086, -0.07945878, -0.014034261, 0.043977696], "split_indices": [1, 2, 2, 2, 1, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.1955, 40.003822, 22.191677, 16.30862, 23.695202, 5.7799544, 16.411722, 8.657716, 7.650905, 6.551483, 17.143719, 7.5767717, 8.834951], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.06354347, 0.11504071, 0.00921021, 0.01903951, 0.21695437, -0.023524322, 0.07317159, 0.032735974, -0.027072279, 0.029802887, 0.082854524, 0.045133486, 0.0012834737], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 479, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.13869393, 0.23536554, 0.16492885, 0.16381097, 0.021681488, 0.0, 0.07784528, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 5.0, 4.0, 2.0, -0.023524322, 7.0, 0.032735974, -0.027072279, 0.029802887, 0.082854524, 0.045133486, 0.0012834737], "split_indices": [0, 2, 2, 2, 0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.388092, 25.89085, 27.497242, 14.448559, 11.442291, 10.847364, 16.649878, 8.422443, 6.0261154, 5.2944665, 6.1478252, 6.6055684, 10.04431], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.10249092, -0.07783793, -0.06925045, -0.01528977, -0.070590004, 0.01965834, -0.013692765], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 480, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.23739141, 0.0, 0.39674214, 0.092639044, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.07783793, 8.0, 2.0, -0.070590004, 0.01965834, -0.013692765], "split_indices": [0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.24066, 7.7638974, 46.476765, 36.356426, 10.120341, 8.710446, 27.645977], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.07184332, -0.0382086, 0.0521152, -0.0, -0.08512956, 0.01801163, 0.03963613, -0.007120613], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 481, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.1380144, 0.12777372, 0.47433233, 0.0, 0.0, 0.0, 0.14116134, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 4.0, 3.0, 0.0521152, -0.0, -0.08512956, 5.0, 0.03963613, -0.007120613], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.733944, 16.670345, 31.0636, 6.256302, 10.414043, 5.3644204, 25.699179, 6.9454603, 18.75372], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.036068164, -0.0, -0.09097313, -0.041868553, 0.03626937, 0.05678141, -0.0065295226], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 482, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.49355745, 0.2551975, 0.0, 0.0, 0.3637598, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 3.0, -0.09097313, -0.041868553, 3.0, 0.05678141, -0.0065295226], "split_indices": [2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.2308, 47.11047, 5.1203322, 9.494101, 37.616367, 10.196382, 27.419985], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0433148, 0.040285006, -0.13790175, 0.06452228, -0.037306175, -0.09989578, -0.03431718, -0.058657467, 0.034888946, 0.03383628, -0.06680095], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 483, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.3784455, 0.35928807, 0.4031341, 0.0, 0.45561683, 0.0, 0.48044714, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 3.0, 0.06452228, 7.0, -0.09989578, 7.0, -0.058657467, 0.034888946, 0.03383628, -0.06680095], "split_indices": [2, 1, 1, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.92231, 23.589668, 21.332642, 7.103828, 16.48584, 6.295708, 15.036934, 8.307149, 8.178691, 8.370098, 6.6668363], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.022873648, -0.01906331, 0.1045808, 0.029520642, -0.07139036, 0.02731523, 0.062661946, 0.027052717, -0.012379886, -0.060163498, 0.0023942282, -0.0, 0.013201068], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 484, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.18315034, 0.09101429, 0.11611144, 0.08158974, 0.19848672, 0.0041308347, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 4.0, 5.0, 7.0, 9.0, 0.062661946, 0.027052717, -0.012379886, -0.060163498, 0.0023942282, -0.0, 0.013201068], "split_indices": [0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.372818, 32.324677, 17.04814, 15.996882, 16.327795, 11.068634, 5.979505, 9.228263, 6.76862, 6.113305, 10.21449, 5.284863, 5.783771], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0525158, -0.0023494523, -0.14855322, -0.13086356, 0.08606909, -0.00088872225, -0.21688238, -0.011731793, -0.05889757, 0.037793912, -0.0063914754, -0.09851831, -0.025552128], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 485, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.26705432, 0.47271517, 0.1705561, 0.072387725, 0.11585732, 0.0, 0.11771327, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 4.0, 7.0, 6.0, -0.00088872225, 6.0, -0.011731793, -0.05889757, 0.037793912, -0.0063914754, -0.09851831, -0.025552128], "split_indices": [2, 2, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.634823, 39.215725, 18.4191, 16.249062, 22.966661, 6.6840563, 11.735044, 8.066813, 8.182248, 17.295876, 5.6707854, 5.13493, 6.6001134], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0030339966, 0.07126362, -0.062195696, -0.031169824, 0.23060873, -0.09389078, -0.013692299, 0.012601592, -0.043452162, 0.09328958, 0.035897017, 0.023227464, -0.032430302], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 486, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.30810094, 0.52387065, 0.44785875, 0.17460401, 0.030368567, 0.0, 0.31060943, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 5.0, 2.0, 9.0, -0.09389078, 4.0, 0.012601592, -0.043452162, 0.09328958, 0.035897017, 0.023227464, -0.032430302], "split_indices": [1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.291405, 29.433191, 38.858215, 18.02367, 11.409521, 5.079875, 33.77834, 10.813347, 7.210324, 5.048892, 6.3606286, 16.741346, 17.036993], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.09873736, -0.05413475, -0.012961351, 0.11264153, -0.11896072, 0.053476214, 0.0014233397, -0.09538265, 0.0027223832], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 487, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.32416862, 0.0, 0.35683385, 0.06485923, 0.41290864, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [5.0, -0.05413475, 7.0, 4.0, 3.0, 0.053476214, 0.0014233397, -0.09538265, 0.0027223832], "split_indices": [0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.344116, 23.596298, 24.74782, 10.866092, 13.881728, 5.7752566, 5.0908346, 5.160117, 8.721611], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.04878737, 0.01373887, -0.13089536, 0.054343615, -0.036672074, -0.14069396, -0.0069589955, 0.030737072, -0.04737106, 0.015968071, -0.01990309], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 488, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.32616317, 0.31464982, 1.085218, 0.0, 0.47893894, 0.0, 0.080155805, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 2.0, 0.054343615, 2.0, -0.14069396, 6.0, 0.030737072, -0.04737106, 0.015968071, -0.01990309], "split_indices": [2, 1, 1, 0, 2, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.898907, 33.785557, 26.113348, 7.678116, 26.107443, 6.0416107, 20.071737, 11.844418, 14.263025, 9.183788, 10.887949], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.081741735, -0.024775984, -0.27792725, -0.07396247, 0.0038339912, -0.1460522, -0.004438344, -0.047237154, 0.019994553, 0.01751751, -0.032785296], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 489, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5710426, 0.06874647, 0.588732, 0.2237809, 0.16961251, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, 9.0, 5.0, 6.0, -0.1460522, -0.004438344, -0.047237154, 0.019994553, 0.01751751, -0.032785296], "split_indices": [1, 2, 1, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.420788, 42.667557, 10.753231, 16.393614, 26.273943, 5.3084207, 5.4448104, 10.656919, 5.736696, 18.647812, 7.6261315], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.029484946, 0.032737825, -0.06507046, 0.07366576, -0.027535236, -0.020142477, 0.039164454], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 490, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.64215535, 0.20965368, 0.0, 0.26651746, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [7.0, 8.0, -0.06507046, 2.0, -0.027535236, -0.020142477, 0.039164454], "split_indices": [1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.877758, 39.16622, 12.7115345, 30.389326, 8.776897, 8.1863575, 22.202969], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.026530115, 0.09142679, -0.096335456, 0.0072053955, 0.049217228, -0.17922279, 0.017829528, -0.06825889, -0.029768389], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 491, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.38770863, 0.060806885, 0.40855435, 0.0, 0.0, 0.022718549, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 6.0, 8.0, 0.0072053955, 0.049217228, 7.0, 0.017829528, -0.06825889, -0.029768389], "split_indices": [1, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.9569, 16.066118, 28.890783, 9.736835, 6.3292828, 19.177431, 9.713352, 10.027622, 9.149809], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.012806143, -0.037297346, 0.015800739, 0.096875645, -0.023723787, 0.06450265, -0.033604696, -0.033757318, 0.02491773], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 492, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.19274077, 0.0, 0.15400164, 0.4241116, 0.299282, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.037297346, 4.0, 7.0, 7.0, 0.06450265, -0.033604696, -0.033757318, 0.02491773], "split_indices": [0, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.399105, 11.340414, 44.058693, 14.793169, 29.265524, 9.736418, 5.056752, 16.460058, 12.805466], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.009227127, -0.053805098, 0.08525507, -0.09733263, 0.024112381, 0.044790577, -0.0014815183, -0.006898402, -0.06168056], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 493, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.19824111, 0.20271698, 0.09977998, 0.18160668, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 8.0, 9.0, 5.0, 0.024112381, 0.044790577, -0.0014815183, -0.006898402, -0.06168056], "split_indices": [0, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.725346, 32.07898, 13.646367, 24.991638, 7.08734, 8.244956, 5.4014106, 16.084194, 8.907444], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00867489, 0.03174368, -0.10911756, -0.009738543, 0.06264784, -0.08188774, 0.040526118, 0.049644127, -0.0434319], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 494, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.20769347, 0.27593696, 0.6437474, 0.6965063, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 9.0, 3.0, 0.06264784, -0.08188774, 0.040526118, 0.049644127, -0.0434319], "split_indices": [2, 2, 2, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.400112, 33.576965, 13.823148, 27.512384, 6.0645814, 8.447382, 5.3757653, 11.554018, 15.958365], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.060340974, -0.048565157, 0.0873923, 0.10522, -0.014803306, 0.010231389, 0.04990121], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 495, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.37050456, 0.0, 0.14485729, 0.18996781, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, -0.048565157, 9.0, 5.0, -0.014803306, 0.010231389, 0.04990121], "split_indices": [2, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.51863, 5.516875, 54.001755, 48.623993, 5.3777614, 23.95014, 24.673855], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.015582808, 0.028671823, -0.014367311, -0.026197614, 0.08328003, 0.019557247, -0.058684394, -0.044717696, 0.04177323], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 496, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.059965186, 0.19461128, 0.0, 0.5046972, 0.440871, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, -0.014367311, 3.0, 2.0, 0.019557247, -0.058684394, -0.044717696, 0.04177323], "split_indices": [0, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [71.83733, 61.316853, 10.520478, 29.931353, 31.3855, 19.459358, 10.471994, 5.4226456, 25.962854], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.005442049, 0.082016245, -0.101272434, -0.014659265, 0.17240702, -0.0016356707, -0.16070138, 0.100191474, -0.003954559, -0.07296575, -0.00799985], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 497, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.4251787, 0.3270961, 0.12269026, 0.0, 0.51587355, 0.0, 0.12706017, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 4.0, -0.014659265, 7.0, -0.0016356707, 7.0, 0.100191474, -0.003954559, -0.07296575, -0.00799985], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.5095, 24.892416, 23.617083, 9.904457, 14.98796, 10.102596, 13.514486, 7.873222, 7.1147375, 7.3988037, 6.115682], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.049395386, 0.04157515, 0.033050153, 0.10619247, 0.008282004, -0.009991672, 0.0740306, -0.0149989305, 0.055215582], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 498, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.06852193, 0.0, 0.08656727, 0.27868032, 0.46191457, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.04157515, 2.0, 5.0, 7.0, -0.009991672, 0.0740306, -0.0149989305, 0.055215582], "split_indices": [0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.918777, 7.329349, 53.589428, 11.703413, 41.886017, 5.9001927, 5.8032207, 31.566448, 10.319568], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.07495385, 0.013099874, -0.10297038, -0.070894465, -0.06692866, 0.026860224, -0.03950971], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 499, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.18915242, 0.0, 0.16764715, 0.0, 0.3910134, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.013099874, 3.0, -0.070894465, 5.0, 0.026860224, -0.03950971], "split_indices": [0, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.7792, 9.295361, 43.48384, 7.479704, 36.004135, 9.9769745, 26.02716], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "5E-1", "boost_from_average": "1", "num_class": "10", "num_feature": "3", "num_target": "1"}, "objective": {"name": "multi:softprob", "softmax_multiclass_param": {"num_class": "10"}}}, "version": [3, 0, 2]}