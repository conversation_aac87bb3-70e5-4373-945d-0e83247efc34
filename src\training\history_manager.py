#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练历史管理
提供训练记录查询、模型版本管理、性能对比等功能
"""

import sys
import json
import sqlite3
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

@dataclass
class TrainingRecord:
    """训练记录"""
    record_id: str
    task_id: str
    position: str
    model_type: str
    issue: Optional[str]
    train_until: Optional[str]
    status: str
    duration_seconds: float
    memory_peak_mb: float
    cpu_avg_percent: float
    model_path: Optional[str]
    model_size_mb: float
    accuracy_score: Optional[float]
    created_at: str
    completed_at: Optional[str]
    error_message: Optional[str]
    metadata: Dict[str, Any]

@dataclass
class ModelVersion:
    """模型版本"""
    version_id: str
    position: str
    model_type: str
    version_number: str
    model_path: str
    model_size_mb: float
    accuracy_score: Optional[float]
    training_duration: float
    created_at: str
    is_active: bool
    description: str

class HistoryManager:
    """训练历史管理器"""
    
    def __init__(self, db_path: str = "data/training_history.db"):
        """
        初始化历史管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        self.logger = logging.getLogger('HistoryManager')
        self.logger.setLevel(logging.INFO)
        
        # 初始化数据库
        self._init_database()
        
    def _init_database(self):
        """初始化数据库表"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 训练记录表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS training_records (
                    record_id TEXT PRIMARY KEY,
                    task_id TEXT NOT NULL,
                    position TEXT NOT NULL,
                    model_type TEXT NOT NULL,
                    issue TEXT,
                    train_until TEXT,
                    status TEXT NOT NULL,
                    duration_seconds REAL NOT NULL,
                    memory_peak_mb REAL NOT NULL,
                    cpu_avg_percent REAL NOT NULL,
                    model_path TEXT,
                    model_size_mb REAL NOT NULL,
                    accuracy_score REAL,
                    created_at TEXT NOT NULL,
                    completed_at TEXT,
                    error_message TEXT,
                    metadata TEXT
                )
            """)
            
            # 模型版本表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS model_versions (
                    version_id TEXT PRIMARY KEY,
                    position TEXT NOT NULL,
                    model_type TEXT NOT NULL,
                    version_number TEXT NOT NULL,
                    model_path TEXT NOT NULL,
                    model_size_mb REAL NOT NULL,
                    accuracy_score REAL,
                    training_duration REAL NOT NULL,
                    created_at TEXT NOT NULL,
                    is_active BOOLEAN NOT NULL DEFAULT 0,
                    description TEXT
                )
            """)
            
            # 创建索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_records_position_model ON training_records(position, model_type)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_records_created_at ON training_records(created_at)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_versions_position_model ON model_versions(position, model_type)")
            
            conn.commit()
            
    def add_training_record(self, record: TrainingRecord) -> bool:
        """添加训练记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO training_records (
                        record_id, task_id, position, model_type, issue, train_until,
                        status, duration_seconds, memory_peak_mb, cpu_avg_percent,
                        model_path, model_size_mb, accuracy_score, created_at,
                        completed_at, error_message, metadata
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    record.record_id, record.task_id, record.position, record.model_type,
                    record.issue, record.train_until, record.status, record.duration_seconds,
                    record.memory_peak_mb, record.cpu_avg_percent, record.model_path,
                    record.model_size_mb, record.accuracy_score, record.created_at,
                    record.completed_at, record.error_message, json.dumps(record.metadata)
                ))
                
                conn.commit()
                self.logger.info(f"训练记录已添加: {record.record_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"添加训练记录失败: {e}")
            return False
            
    def get_training_records(self, position: Optional[str] = None, 
                           model_type: Optional[str] = None,
                           status: Optional[str] = None,
                           days: int = 30,
                           limit: int = 100) -> List[TrainingRecord]:
        """获取训练记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 构建查询条件
                conditions = []
                params = []
                
                if position:
                    conditions.append("position = ?")
                    params.append(position)
                    
                if model_type:
                    conditions.append("model_type = ?")
                    params.append(model_type)
                    
                if status:
                    conditions.append("status = ?")
                    params.append(status)
                    
                # 时间范围
                cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
                conditions.append("created_at >= ?")
                params.append(cutoff_date)
                
                where_clause = " AND ".join(conditions) if conditions else "1=1"
                
                query = f"""
                    SELECT * FROM training_records 
                    WHERE {where_clause}
                    ORDER BY created_at DESC 
                    LIMIT ?
                """
                params.append(limit)
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                # 转换为TrainingRecord对象
                records = []
                for row in rows:
                    metadata = json.loads(row[16]) if row[16] else {}
                    record = TrainingRecord(
                        record_id=row[0], task_id=row[1], position=row[2], model_type=row[3],
                        issue=row[4], train_until=row[5], status=row[6], duration_seconds=row[7],
                        memory_peak_mb=row[8], cpu_avg_percent=row[9], model_path=row[10],
                        model_size_mb=row[11], accuracy_score=row[12], created_at=row[13],
                        completed_at=row[14], error_message=row[15], metadata=metadata
                    )
                    records.append(record)
                    
                return records
                
        except Exception as e:
            self.logger.error(f"获取训练记录失败: {e}")
            return []
            
    def add_model_version(self, version: ModelVersion) -> bool:
        """添加模型版本"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 如果设置为活跃版本，先将其他版本设为非活跃
                if version.is_active:
                    cursor.execute("""
                        UPDATE model_versions 
                        SET is_active = 0 
                        WHERE position = ? AND model_type = ?
                    """, (version.position, version.model_type))
                
                cursor.execute("""
                    INSERT INTO model_versions (
                        version_id, position, model_type, version_number, model_path,
                        model_size_mb, accuracy_score, training_duration, created_at,
                        is_active, description
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    version.version_id, version.position, version.model_type,
                    version.version_number, version.model_path, version.model_size_mb,
                    version.accuracy_score, version.training_duration, version.created_at,
                    version.is_active, version.description
                ))
                
                conn.commit()
                self.logger.info(f"模型版本已添加: {version.version_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"添加模型版本失败: {e}")
            return False
            
    def get_model_versions(self, position: str, model_type: str) -> List[ModelVersion]:
        """获取模型版本列表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT * FROM model_versions 
                    WHERE position = ? AND model_type = ?
                    ORDER BY created_at DESC
                """, (position, model_type))
                
                rows = cursor.fetchall()
                
                versions = []
                for row in rows:
                    version = ModelVersion(
                        version_id=row[0], position=row[1], model_type=row[2],
                        version_number=row[3], model_path=row[4], model_size_mb=row[5],
                        accuracy_score=row[6], training_duration=row[7], created_at=row[8],
                        is_active=bool(row[9]), description=row[10]
                    )
                    versions.append(version)
                    
                return versions
                
        except Exception as e:
            self.logger.error(f"获取模型版本失败: {e}")
            return []
            
    def get_active_model_version(self, position: str, model_type: str) -> Optional[ModelVersion]:
        """获取活跃的模型版本"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT * FROM model_versions 
                    WHERE position = ? AND model_type = ? AND is_active = 1
                    LIMIT 1
                """, (position, model_type))
                
                row = cursor.fetchone()
                if row:
                    return ModelVersion(
                        version_id=row[0], position=row[1], model_type=row[2],
                        version_number=row[3], model_path=row[4], model_size_mb=row[5],
                        accuracy_score=row[6], training_duration=row[7], created_at=row[8],
                        is_active=bool(row[9]), description=row[10]
                    )
                    
                return None
                
        except Exception as e:
            self.logger.error(f"获取活跃模型版本失败: {e}")
            return None
            
    def set_active_model_version(self, version_id: str) -> bool:
        """设置活跃的模型版本"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取版本信息
                cursor.execute("SELECT position, model_type FROM model_versions WHERE version_id = ?", (version_id,))
                row = cursor.fetchone()
                if not row:
                    return False
                    
                position, model_type = row
                
                # 将所有版本设为非活跃
                cursor.execute("""
                    UPDATE model_versions 
                    SET is_active = 0 
                    WHERE position = ? AND model_type = ?
                """, (position, model_type))
                
                # 设置指定版本为活跃
                cursor.execute("UPDATE model_versions SET is_active = 1 WHERE version_id = ?", (version_id,))
                
                conn.commit()
                self.logger.info(f"活跃模型版本已设置: {version_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"设置活跃模型版本失败: {e}")
            return False
            
    def compare_model_performance(self, version_ids: List[str]) -> Dict:
        """比较模型性能"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                placeholders = ','.join(['?'] * len(version_ids))
                cursor.execute(f"""
                    SELECT * FROM model_versions 
                    WHERE version_id IN ({placeholders})
                """, version_ids)
                
                rows = cursor.fetchall()
                
                comparison = {
                    'versions': [],
                    'metrics': {
                        'accuracy': [],
                        'training_duration': [],
                        'model_size': []
                    }
                }
                
                for row in rows:
                    version_data = {
                        'version_id': row[0],
                        'position': row[1],
                        'model_type': row[2],
                        'version_number': row[3],
                        'accuracy_score': row[6],
                        'training_duration': row[7],
                        'model_size_mb': row[5],
                        'created_at': row[8]
                    }
                    
                    comparison['versions'].append(version_data)
                    
                    if row[6] is not None:  # accuracy_score
                        comparison['metrics']['accuracy'].append(row[6])
                    comparison['metrics']['training_duration'].append(row[7])
                    comparison['metrics']['model_size'].append(row[5])
                    
                # 计算统计信息
                for metric, values in comparison['metrics'].items():
                    if values:
                        comparison['metrics'][metric] = {
                            'values': values,
                            'min': min(values),
                            'max': max(values),
                            'avg': sum(values) / len(values)
                        }
                        
                return comparison
                
        except Exception as e:
            self.logger.error(f"比较模型性能失败: {e}")
            return {}
            
    def get_training_statistics(self, days: int = 30) -> Dict:
        """获取训练统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
                
                # 总体统计
                cursor.execute("""
                    SELECT 
                        COUNT(*) as total_tasks,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_tasks,
                        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_tasks,
                        AVG(CASE WHEN status = 'completed' THEN duration_seconds ELSE NULL END) as avg_duration,
                        AVG(CASE WHEN status = 'completed' THEN memory_peak_mb ELSE NULL END) as avg_memory
                    FROM training_records 
                    WHERE created_at >= ?
                """, (cutoff_date,))
                
                row = cursor.fetchone()
                
                # 按模型类型统计
                cursor.execute("""
                    SELECT 
                        model_type,
                        COUNT(*) as count,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                        AVG(CASE WHEN status = 'completed' THEN duration_seconds ELSE NULL END) as avg_duration
                    FROM training_records 
                    WHERE created_at >= ?
                    GROUP BY model_type
                """, (cutoff_date,))
                
                model_stats = {}
                for model_row in cursor.fetchall():
                    model_stats[model_row[0]] = {
                        'total': model_row[1],
                        'completed': model_row[2],
                        'success_rate': model_row[2] / model_row[1] * 100 if model_row[1] > 0 else 0,
                        'avg_duration': model_row[3] or 0
                    }
                    
                return {
                    'period_days': days,
                    'total_tasks': row[0] or 0,
                    'completed_tasks': row[1] or 0,
                    'failed_tasks': row[2] or 0,
                    'success_rate': (row[1] / row[0] * 100) if row[0] > 0 else 0,
                    'avg_duration_seconds': row[3] or 0,
                    'avg_memory_mb': row[4] or 0,
                    'model_statistics': model_stats
                }
                
        except Exception as e:
            self.logger.error(f"获取训练统计失败: {e}")
            return {}
            
    def cleanup_old_records(self, days: int = 90) -> int:
        """清理旧记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
                
                # 删除旧的训练记录
                cursor.execute("DELETE FROM training_records WHERE created_at < ?", (cutoff_date,))
                deleted_records = cursor.rowcount
                
                # 删除旧的非活跃模型版本
                cursor.execute("""
                    DELETE FROM model_versions 
                    WHERE created_at < ? AND is_active = 0
                """, (cutoff_date,))
                deleted_versions = cursor.rowcount
                
                conn.commit()
                
                total_deleted = deleted_records + deleted_versions
                self.logger.info(f"清理完成: 删除 {deleted_records} 条训练记录, {deleted_versions} 个模型版本")
                
                return total_deleted
                
        except Exception as e:
            self.logger.error(f"清理旧记录失败: {e}")
            return 0
            
    def backup_database(self, backup_path: Optional[str] = None) -> str:
        """备份数据库"""
        if not backup_path:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = f"data/backups/training_history_backup_{timestamp}.db"
            
        backup_file = Path(backup_path)
        backup_file.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            shutil.copy2(self.db_path, backup_file)
            self.logger.info(f"数据库备份完成: {backup_file}")
            return str(backup_file)
            
        except Exception as e:
            self.logger.error(f"数据库备份失败: {e}")
            raise

# 全局历史管理器实例
history_manager = HistoryManager()

if __name__ == "__main__":
    # 测试代码
    print("📚 测试训练历史管理器")
    
    # 创建测试记录
    test_record = TrainingRecord(
        record_id="test-001",
        task_id="task-001",
        position="hundreds",
        model_type="xgb",
        issue="2025217",
        train_until="2025216",
        status="completed",
        duration_seconds=280.5,
        memory_peak_mb=1450.0,
        cpu_avg_percent=75.0,
        model_path="models/hundreds/xgb_2025217.pkl",
        model_size_mb=12.5,
        accuracy_score=0.85,
        created_at=datetime.now().isoformat(),
        completed_at=datetime.now().isoformat(),
        error_message=None,
        metadata={"test": True}
    )
    
    # 添加记录
    success = history_manager.add_training_record(test_record)
    print(f"添加记录: {'成功' if success else '失败'}")
    
    # 获取记录
    records = history_manager.get_training_records(position="hundreds", limit=10)
    print(f"获取记录: {len(records)} 条")
    
    # 获取统计信息
    stats = history_manager.get_training_statistics()
    print(f"统计信息: {stats}")
