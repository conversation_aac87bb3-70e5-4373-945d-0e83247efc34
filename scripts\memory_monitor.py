#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存监控工具
用于监控训练过程中的内存使用情况
"""

import psutil
import time
import sys
import os
import gc
from datetime import datetime
from typing import Dict, List, Optional
import threading
import json

class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self, log_file: Optional[str] = None, interval: float = 1.0):
        """
        初始化内存监控器
        
        Args:
            log_file: 日志文件路径，None则输出到控制台
            interval: 监控间隔（秒）
        """
        self.log_file = log_file
        self.interval = interval
        self.monitoring = False
        self.monitor_thread = None
        self.memory_history = []
        self.start_time = None
        
        # 获取进程信息
        self.process = psutil.Process()
        
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            return
            
        self.monitoring = True
        self.start_time = datetime.now()
        self.memory_history = []
        
        self.monitor_thread = threading.Thread(target=self._monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        self._log("🔍 内存监控已启动")
        
    def stop_monitoring(self):
        """停止监控"""
        if not self.monitoring:
            return
            
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
            
        self._log("⏹️ 内存监控已停止")
        
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                # 获取内存信息
                memory_info = self._get_memory_info()
                self.memory_history.append(memory_info)
                
                # 检查内存使用是否过高
                if memory_info['memory_percent'] > 80:
                    self._log(f"⚠️ 内存使用过高: {memory_info['memory_percent']:.1f}%")
                    
                # 如果内存使用超过90%，强制垃圾回收
                if memory_info['memory_percent'] > 90:
                    self._log("🧹 强制执行垃圾回收")
                    gc.collect()
                    
                time.sleep(self.interval)
                
            except Exception as e:
                self._log(f"❌ 监控错误: {e}")
                break
                
    def _get_memory_info(self) -> Dict:
        """获取内存信息"""
        # 进程内存信息
        process_memory = self.process.memory_info()
        process_percent = self.process.memory_percent()
        
        # 系统内存信息
        system_memory = psutil.virtual_memory()
        
        # CPU信息
        cpu_percent = self.process.cpu_percent()
        
        return {
            'timestamp': datetime.now().isoformat(),
            'process_memory_mb': process_memory.rss / 1024 / 1024,
            'process_memory_vms_mb': process_memory.vms / 1024 / 1024,
            'memory_percent': process_percent,
            'system_memory_total_gb': system_memory.total / 1024 / 1024 / 1024,
            'system_memory_available_gb': system_memory.available / 1024 / 1024 / 1024,
            'system_memory_percent': system_memory.percent,
            'cpu_percent': cpu_percent
        }
        
    def _log(self, message: str):
        """记录日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        
        if self.log_file:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_message + '\n')
        else:
            print(log_message)
            
    def get_memory_stats(self) -> Dict:
        """获取内存统计信息"""
        if not self.memory_history:
            return {}
            
        memory_values = [item['process_memory_mb'] for item in self.memory_history]
        cpu_values = [item['cpu_percent'] for item in self.memory_history]
        
        return {
            'duration_seconds': (datetime.now() - self.start_time).total_seconds() if self.start_time else 0,
            'memory_peak_mb': max(memory_values),
            'memory_avg_mb': sum(memory_values) / len(memory_values),
            'memory_current_mb': memory_values[-1] if memory_values else 0,
            'cpu_avg_percent': sum(cpu_values) / len(cpu_values) if cpu_values else 0,
            'samples_count': len(self.memory_history)
        }
        
    def save_history(self, filename: str):
        """保存监控历史到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump({
                'stats': self.get_memory_stats(),
                'history': self.memory_history
            }, f, indent=2, ensure_ascii=False)
            
    def print_summary(self):
        """打印监控摘要"""
        stats = self.get_memory_stats()
        if not stats:
            print("📊 无监控数据")
            return
            
        print("\n" + "=" * 50)
        print("📊 内存监控摘要")
        print("=" * 50)
        print(f"⏱️ 监控时长: {stats['duration_seconds']:.1f} 秒")
        print(f"📈 内存峰值: {stats['memory_peak_mb']:.1f} MB")
        print(f"📊 内存平均: {stats['memory_avg_mb']:.1f} MB")
        print(f"📍 当前内存: {stats['memory_current_mb']:.1f} MB")
        print(f"🖥️ CPU平均: {stats['cpu_avg_percent']:.1f}%")
        print(f"📋 采样次数: {stats['samples_count']}")
        print("=" * 50)

def monitor_command(pid: Optional[int] = None, duration: int = 60, interval: float = 1.0):
    """命令行监控模式"""
    if pid:
        try:
            process = psutil.Process(pid)
            print(f"🔍 监控进程: {process.name()} (PID: {pid})")
        except psutil.NoSuchProcess:
            print(f"❌ 进程 {pid} 不存在")
            return
    else:
        print("🔍 监控当前进程")
        
    monitor = MemoryMonitor(interval=interval)
    monitor.start_monitoring()
    
    try:
        time.sleep(duration)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断监控")
    finally:
        monitor.stop_monitoring()
        monitor.print_summary()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="内存监控工具")
    parser.add_argument('--pid', type=int, help='要监控的进程ID')
    parser.add_argument('--duration', type=int, default=60, help='监控时长（秒）')
    parser.add_argument('--interval', type=float, default=1.0, help='监控间隔（秒）')
    parser.add_argument('--log', type=str, help='日志文件路径')
    
    args = parser.parse_args()
    
    if args.log:
        print(f"📝 日志将保存到: {args.log}")
        
    monitor_command(args.pid, args.duration, args.interval)
