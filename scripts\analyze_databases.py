#!/usr/bin/env python3
"""
分析两个数据库的关系和用途
"""

import sqlite3
import os
from pathlib import Path

def analyze_database(db_path: str, db_name: str):
    """分析数据库结构和内容"""
    print(f"\n{'='*60}")
    print(f"📊 分析数据库: {db_name}")
    print(f"📁 路径: {db_path}")
    print(f"{'='*60}")
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        # 获取文件大小
        file_size = os.path.getsize(db_path) / 1024  # KB
        print(f"📦 文件大小: {file_size:.2f} KB")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"📋 表数量: {len(tables)}")
        print("📝 表列表:")
        
        for table in tables:
            table_name = table[0]
            
            # 获取表结构
            cursor.execute(f'PRAGMA table_info({table_name})')
            columns = cursor.fetchall()
            
            # 获取记录数
            cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
            count = cursor.fetchone()[0]
            
            print(f"  🔸 {table_name}: {count} 条记录")
            
            # 如果是开奖数据相关的表，显示详细信息
            if any(keyword in table_name.lower() for keyword in ['lottery', 'data', 'records']):
                print(f"    📊 表结构:")
                for col in columns:
                    col_id, name, data_type, not_null, default_val, pk = col
                    null_str = "NOT NULL" if not_null else "NULL"
                    pk_str = " [PK]" if pk else ""
                    print(f"      - {name}: {data_type} {null_str}{pk_str}")
                
                # 显示最新几条数据
                if count > 0:
                    try:
                        # 尝试按期号排序
                        if any(col[1] in ['issue', 'period'] for col in columns):
                            issue_col = 'issue' if any(col[1] == 'issue' for col in columns) else 'period'
                            cursor.execute(f'SELECT * FROM {table_name} ORDER BY {issue_col} DESC LIMIT 3')
                        else:
                            cursor.execute(f'SELECT * FROM {table_name} LIMIT 3')
                        
                        recent_data = cursor.fetchall()
                        print(f"    📈 最新数据样例:")
                        for i, row in enumerate(recent_data, 1):
                            print(f"      {i}. {row}")
                    except Exception as e:
                        print(f"    ⚠️ 无法获取数据样例: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def compare_lottery_tables():
    """比较两个数据库中的开奖数据表"""
    print(f"\n{'='*60}")
    print("🔍 开奖数据表对比分析")
    print(f"{'='*60}")
    
    # 分析 fucai3d.db 中的 lottery_data
    try:
        conn1 = sqlite3.connect('data/fucai3d.db')
        cursor1 = conn1.cursor()
        
        cursor1.execute('SELECT COUNT(*) FROM lottery_data')
        count1 = cursor1.fetchone()[0]
        
        cursor1.execute('SELECT MIN(issue), MAX(issue) FROM lottery_data')
        min_issue1, max_issue1 = cursor1.fetchone()
        
        cursor1.execute('SELECT issue, hundreds, tens, units, draw_date FROM lottery_data ORDER BY issue DESC LIMIT 5')
        recent1 = cursor1.fetchall()
        
        conn1.close()
        
        print("📊 fucai3d.db -> lottery_data:")
        print(f"  📈 记录数: {count1}")
        print(f"  📅 期号范围: {min_issue1} - {max_issue1}")
        print("  🎯 最新5期:")
        for row in recent1:
            print(f"    {row[0]}: {row[1]}{row[2]}{row[3]} ({row[4]})")
        
    except Exception as e:
        print(f"❌ fucai3d.db 分析失败: {e}")
    
    print()
    
    # 分析 lottery.db 中的 lottery_records
    try:
        conn2 = sqlite3.connect('data/lottery.db')
        cursor2 = conn2.cursor()
        
        cursor2.execute('SELECT COUNT(*) FROM lottery_records')
        count2 = cursor2.fetchone()[0]
        
        cursor2.execute('SELECT MIN(period), MAX(period) FROM lottery_records')
        min_period2, max_period2 = cursor2.fetchone()
        
        cursor2.execute('SELECT period, numbers, date FROM lottery_records ORDER BY period DESC LIMIT 5')
        recent2 = cursor2.fetchall()
        
        conn2.close()
        
        print("📊 lottery.db -> lottery_records:")
        print(f"  📈 记录数: {count2}")
        print(f"  📅 期号范围: {min_period2} - {max_period2}")
        print("  🎯 最新5期:")
        for row in recent2:
            print(f"    {row[0]}: {row[1]} ({row[2]})")
        
    except Exception as e:
        print(f"❌ lottery.db 分析失败: {e}")

def find_data_update_scripts():
    """查找数据更新相关的脚本"""
    print(f"\n{'='*60}")
    print("🔍 数据更新脚本分析")
    print(f"{'='*60}")
    
    scripts_dir = Path("scripts")
    update_scripts = []
    
    for script_file in scripts_dir.glob("*.py"):
        try:
            with open(script_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查是否包含数据更新相关的关键词
            keywords = ['lottery.db', 'fucai3d.db', 'lottery_data', 'lottery_records', 'sync', 'update']
            
            found_keywords = []
            for keyword in keywords:
                if keyword in content:
                    found_keywords.append(keyword)
            
            if found_keywords:
                update_scripts.append((script_file.name, found_keywords))
        
        except Exception as e:
            continue
    
    print("📝 相关脚本:")
    for script_name, keywords in update_scripts:
        print(f"  🔸 {script_name}: {', '.join(keywords)}")

def main():
    """主函数"""
    print("🔍 福彩3D数据库关系分析")
    
    # 分析两个数据库
    analyze_database('data/fucai3d.db', 'fucai3d.db')
    analyze_database('data/lottery.db', 'lottery.db')
    
    # 比较开奖数据表
    compare_lottery_tables()
    
    # 查找相关脚本
    find_data_update_scripts()
    
    print(f"\n{'='*60}")
    print("📋 分析总结")
    print(f"{'='*60}")
    print("基于以上分析，可以确定:")
    print("1. 哪个数据库是主要的开奖数据源")
    print("2. 两个数据库的数据同步关系")
    print("3. 数据更新脚本的目标数据库")

if __name__ == "__main__":
    main()
