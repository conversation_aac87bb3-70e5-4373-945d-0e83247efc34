import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Select, 
  Button, 
  Table, 
  Tag, 
  Alert, 
  Spin, 
  Space, 
  Tooltip, 
  Progress,
  Row,
  Col,
  Statistic,
  message
} from 'antd'
import { 
  ReloadOutlined, 
  RocketOutlined, 
  CheckCircleOutlined, 
  ExclamationCircleOutlined,
  Clock<PERSON>ircleOutlined,
  DatabaseOutlined
} from '@ant-design/icons'
import axios from 'axios'

const { Option } = Select

interface ModelIssue {
  issue: string
  display_name: string
  train_date: string
  file_size: number
  status: string
  position: string
}

interface AvailableModels {
  hundreds: ModelIssue[]
  tens: ModelIssue[]
  units: ModelIssue[]
}

interface ModelExistenceCheck {
  hundreds: boolean
  tens: boolean
  units: boolean
}

interface PredictionResult {
  model_issue: string
  target_issue: string
  predicted_number: string
  predictions: any
  confidence: number
}

const ModelIssueSelector: React.FC = () => {
  const [availableModels, setAvailableModels] = useState<AvailableModels | null>(null)
  const [selectedIssue, setSelectedIssue] = useState<string>('')
  const [modelExistence, setModelExistence] = useState<ModelExistenceCheck | null>(null)
  const [loading, setLoading] = useState(false)
  const [predicting, setPredicting] = useState(false)
  const [predictionResult, setPredictionResult] = useState<PredictionResult | null>(null)
  const [currentLoadedIssue, setCurrentLoadedIssue] = useState<string>('')

  // 获取可用模型列表
  const fetchAvailableModels = async () => {
    setLoading(true)
    try {
      const response = await axios.get('/api/prediction/available-models')
      if (response.data.status === 'success') {
        setAvailableModels(response.data.data.available_models)
        setCurrentLoadedIssue(response.data.data.current_loaded_issue || '')
        
        // 如果有可用模型，默认选择最新的
        const hundreds = response.data.data.available_models.hundreds
        if (hundreds && hundreds.length > 0) {
          setSelectedIssue(hundreds[0].issue)
          checkModelExistence(hundreds[0].issue)
        }
      }
    } catch (error) {
      console.error('获取可用模型失败:', error)
      message.error('获取可用模型失败')
    } finally {
      setLoading(false)
    }
  }

  // 检查模型存在性
  const checkModelExistence = async (issue: string) => {
    if (!issue) return
    
    try {
      const response = await axios.get(`/api/prediction/models/check/${issue}`)
      if (response.data.status === 'success') {
        setModelExistence(response.data.data.models_exist)
      }
    } catch (error) {
      console.error('检查模型存在性失败:', error)
    }
  }

  // 加载指定期号的模型
  const loadModels = async (issue: string) => {
    setLoading(true)
    try {
      const response = await axios.post(`/api/prediction/models/load/${issue}`)
      if (response.data.status === 'success') {
        message.success(`成功加载期号 ${issue} 的模型`)
        setCurrentLoadedIssue(issue)
        return true
      }
    } catch (error) {
      console.error('加载模型失败:', error)
      message.error('加载模型失败')
      return false
    } finally {
      setLoading(false)
    }
  }

  // 使用指定模型进行预测
  const predictWithModel = async (issue: string) => {
    setPredicting(true)
    try {
      const response = await axios.get(`/api/prediction/predict/with-model/${issue}/quick`)
      if (response.data.status === 'success') {
        setPredictionResult(response.data.data)
        message.success('预测完成')
      }
    } catch (error) {
      console.error('预测失败:', error)
      message.error('预测失败')
    } finally {
      setPredicting(false)
    }
  }

  // 处理期号选择变化
  const handleIssueChange = (issue: string) => {
    setSelectedIssue(issue)
    checkModelExistence(issue)
    setPredictionResult(null)
  }

  // 处理加载并预测
  const handleLoadAndPredict = async () => {
    if (!selectedIssue) {
      message.warning('请选择模型期号')
      return
    }

    // 先加载模型
    const loadSuccess = await loadModels(selectedIssue)
    if (loadSuccess) {
      // 再进行预测
      await predictWithModel(selectedIssue)
    }
  }

  useEffect(() => {
    fetchAvailableModels()
  }, [])

  useEffect(() => {
    if (selectedIssue) {
      checkModelExistence(selectedIssue)
    }
  }, [selectedIssue])

  // 构建期号选项
  const issueOptions = availableModels?.hundreds?.map(model => ({
    value: model.issue,
    label: `${model.issue}期 (${model.train_date})`,
    model
  })) || []

  // 计算模型完整性
  const getCompletenessInfo = () => {
    if (!modelExistence) return { percentage: 0, status: 'default', text: '未知' }
    
    const existCount = Object.values(modelExistence).filter(Boolean).length
    const totalCount = Object.keys(modelExistence).length
    const percentage = (existCount / totalCount) * 100
    
    let status: 'success' | 'warning' | 'exception' = 'success'
    let text = '完整'
    
    if (percentage < 100) {
      status = percentage > 0 ? 'warning' : 'exception'
      text = percentage > 0 ? '部分' : '缺失'
    }
    
    return { percentage, status, text }
  }

  const completenessInfo = getCompletenessInfo()

  return (
    <Card 
      title={
        <Space>
          <DatabaseOutlined />
          模型期号选择器
        </Space>
      }
      extra={
        <Button 
          icon={<ReloadOutlined />} 
          onClick={fetchAvailableModels}
          loading={loading}
          size="small"
        >
          刷新
        </Button>
      }
    >
      <Row gutter={[16, 16]}>
        {/* 模型选择区域 */}
        <Col span={24}>
          <Card size="small" title="选择模型期号">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Select
                style={{ width: '100%' }}
                placeholder="选择要使用的模型期号"
                value={selectedIssue}
                onChange={handleIssueChange}
                loading={loading}
                showSearch
                optionFilterProp="label"
              >
                {issueOptions.map(option => (
                  <Option key={option.value} value={option.value} label={option.label}>
                    <div>
                      <strong>{option.value}期</strong>
                      <br />
                      <small style={{ color: '#666' }}>训练时间: {option.model.train_date}</small>
                    </div>
                  </Option>
                ))}
              </Select>
              
              {selectedIssue && modelExistence && (
                <div>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Progress
                        percent={completenessInfo.percentage}
                        status={completenessInfo.status}
                        format={() => `${completenessInfo.text} (${Math.round(completenessInfo.percentage)}%)`}
                      />
                    </Col>
                    <Col span={12}>
                      <Space>
                        {Object.entries(modelExistence).map(([position, exists]) => (
                          <Tag 
                            key={position}
                            color={exists ? 'green' : 'red'}
                            icon={exists ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />}
                          >
                            {position === 'hundreds' ? '百位' : position === 'tens' ? '十位' : '个位'}
                          </Tag>
                        ))}
                      </Space>
                    </Col>
                  </Row>
                </div>
              )}
            </Space>
          </Card>
        </Col>

        {/* 当前状态区域 */}
        <Col span={24}>
          <Row gutter={16}>
            <Col span={12}>
              <Statistic
                title="当前加载的模型期号"
                value={currentLoadedIssue || '未加载'}
                prefix={<ClockCircleOutlined />}
              />
            </Col>
            <Col span={12}>
              <Statistic
                title="可用模型总数"
                value={availableModels ? Object.values(availableModels).flat().length : 0}
                prefix={<DatabaseOutlined />}
              />
            </Col>
          </Row>
        </Col>

        {/* 操作按钮区域 */}
        <Col span={24}>
          <Space>
            <Button
              type="primary"
              icon={<RocketOutlined />}
              onClick={handleLoadAndPredict}
              loading={loading || predicting}
              disabled={!selectedIssue || completenessInfo.percentage === 0}
            >
              {loading ? '加载中...' : predicting ? '预测中...' : '加载模型并预测'}
            </Button>
            
            {selectedIssue && selectedIssue !== currentLoadedIssue && (
              <Button
                icon={<DatabaseOutlined />}
                onClick={() => loadModels(selectedIssue)}
                loading={loading}
              >
                仅加载模型
              </Button>
            )}
          </Space>
        </Col>

        {/* 预测结果区域 */}
        {predictionResult && (
          <Col span={24}>
            <Alert
              message="预测完成"
              description={
                <div>
                  <p><strong>使用模型期号:</strong> {predictionResult.model_issue}</p>
                  <p><strong>预测期号:</strong> {predictionResult.target_issue}</p>
                  <p><strong>预测号码:</strong> <Tag color="blue" style={{ fontSize: '16px', padding: '4px 8px' }}>{predictionResult.predicted_number}</Tag></p>
                  <p><strong>整体置信度:</strong> {(predictionResult.confidence * 100).toFixed(1)}%</p>
                </div>
              }
              type="success"
              showIcon
            />
          </Col>
        )}
      </Row>
    </Card>
  )
}

export default ModelIssueSelector
