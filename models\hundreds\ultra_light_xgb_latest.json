{"learner": {"attributes": {}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "500"}, "iteration_indptr": [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130, 140, 150, 160, 170, 180, 190, 200, 210, 220, 230, 240, 250, 260, 270, 280, 290, 300, 310, 320, 330, 340, 350, 360, 370, 380, 390, 400, 410, 420, 430, 440, 450, 460, 470, 480, 490, 500], "tree_info": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "trees": [{"base_weights": [-0.019244248, 0.16761363, -0.3092335, -0.020100523, 0.39263803, -0.34738043, -0.042187504, 0.041940786, -0.07551488, -0.008645539, 0.19401546, -0.06605505, -0.14099218], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3.1633093, 1.5320032, 0.050025463, 0.78340006, 1.8258915, 0.10132575, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 6.0, 7.0, 3.0, 2.0, 4.0, -0.042187504, 0.041940786, -0.07551488, -0.008645539, 0.19401546, -0.06605505, -0.14099218], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.159996, 34.199997, 21.96, 18.9, 15.299999, 16.56, 5.3999996, 11.16, 7.74, 5.9399996, 9.36, 9.9, 6.66], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.27133107, -0.14547415, -0.22456282, 0.032926828, -0.28525048, -0.11884755, -0.061179906], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.40550756, 0.0, 1.0698013, 0.0, 0.24809003, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, -0.14547415, 2.0, 0.032926828, 5.0, -0.11884755, -0.061179906], "split_indices": [1, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.6, 8.28, 49.32, 7.2, 42.12, 15.659999, 26.46], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.03973737, 0.28263795, -0.040890526, -0.029925196, 0.19515307, -0.24576274, 0.030102327, -0.093195274, -0.03476822, 0.041852675, -0.02576688], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [1.1692865, 2.2103229, 0.6692113, 0.0, 0.0, 0.020780802, 0.43891537, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 5.0, 4.0, -0.029925196, 0.19515307, 6.0, 5.0, -0.093195274, -0.03476822, 0.041852675, -0.02576688], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.879997, 13.86, 43.019997, 7.0199995, 6.8399997, 10.799999, 32.219997, 5.7599998, 5.04, 16.92, 15.299999], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.033211537, 0.2097902, -0.23710485, 0.09045801, -5.597265e-09, 0.005328591, -0.1306241], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [2.9214382, 0.7456975, 1.2645365, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 7.0, 4.0, 0.09045801, -5.597265e-09, 0.005328591, -0.1306241], "split_indices": [0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.219997, 36.18, 23.039999, 25.199999, 10.98, 10.259999, 12.78], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.20709018, 0.26468313, -0.08681673, 0.16987035, 0.2244389, -0.0, 0.07514832], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [1.7033951, 2.1536334, 0.0, 0.61274266, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, -0.08681673, 3.0, 0.2244389, -0.0, 0.07514832], "split_indices": [2, 2, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.979996, 50.76, 5.22, 43.739998, 7.0199995, 14.4, 29.339998], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0050573368, -0.20278452, 0.2309384, -0.10983983, -0.13438737, -0.0, 0.313745, -0.0659145, 0.0063424893, 0.21256684, 0.019852936], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [2.8120508, 0.26988542, 0.52168167, 0.0, 0.36025658, 0.0, 1.8383127, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 3.0, -0.10983983, 3.0, -0.0, 7.0, -0.0659145, 0.0063424893, 0.21256684, 0.019852936], "split_indices": [1, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.319996, 32.039997, 26.279999, 7.74, 24.3, 7.2, 19.08, 15.839999, 8.46, 6.4799995, 12.599999], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.018656736, -0.1282672, 0.21995707, -0.10739858, -0.06482029, -0.0035046784, 0.11642599, 0.046997387, -0.038489744], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [1.5611175, 0.5367743, 0.7681168, 0.0, 0.49443188, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 2.0, 5.0, -0.10739858, 4.0, -0.0035046784, 0.11642599, 0.046997387, -0.038489744], "split_indices": [1, 2, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.96, 40.32, 17.64, 7.3799996, 32.94, 7.5599995, 10.08, 6.66, 26.279999], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.076256484, -0.051873203, 0.11012363, 0.06996269, 0.073739275, -0.008205696, 0.044830367], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.49966896, 0.0, 0.18390942, 0.0, 0.33799225, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, -0.051873203, 3.0, 0.06996269, 6.0, -0.008205696, 0.044830367], "split_indices": [2, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.699997, 5.9399996, 50.76, 9.719999, 41.039997, 17.279999, 23.759998], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.010145398, 0.12068964, -0.21494372, 0.36223277, -0.044426516, -0.08152175, -0.023498701, 0.061099794, 0.14962594, -0.10182769, 0.023204414], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [1.4880557, 1.6671097, 0.09532076, 0.19272447, 0.94192463, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 9.0, 2.0, 6.0, -0.08152175, -0.023498701, 0.061099794, 0.14962594, -0.10182769, 0.023204414], "split_indices": [0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.14, 39.6, 18.539999, 15.839999, 23.759998, 11.879999, 6.66, 8.82, 7.0199995, 6.66, 17.099998], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.09271745, -0.109308906, 0.27221525, -0.047909427, -0.08906251, 0.20763724, 0.11391373, -0.034937896, 0.008122738, 0.10497981, -0.06436568], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [2.1971843, 0.27790526, 1.9650052, 0.13011184, 0.0, 0.0, 1.9941617, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 3.0, 2.0, -0.08906251, 0.20763724, 8.0, -0.034937896, 0.008122738, 0.10497981, -0.06436568], "split_indices": [2, 2, 0, 2, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.319996, 27.359999, 30.96, 21.96, 5.3999996, 7.3799996, 23.58, 11.879999, 10.08, 13.86, 9.719999], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.07503417, 0.038174108, -0.28503644, -0.04167489, 0.070101425, -0.35071558, -0.043377813, 0.008880618, -0.09096604, -0.051740136, -0.1380019], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [1.4513273, 0.64187366, 0.09610391, 0.5650992, 0.0, 0.11683774, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 5.0, 3.0, 0.070101425, 3.0, -0.043377813, 0.008880618, -0.09096604, -0.051740136, -0.1380019], "split_indices": [1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.550793, 38.264923, 20.285868, 27.363766, 10.901158, 12.316688, 7.9691796, 21.9, 5.463766, 6.009121, 6.307566], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.21848184, -0.14001507, -0.17119417, 0.014735491, -0.21782729, -0.079499766, -0.020432737], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.5150702, 0.0, 0.5315809, 0.0, 0.23852909, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, -0.14001507, 2.0, 0.014735491, 8.0, -0.079499766, -0.020432737], "split_indices": [1, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.119873, 7.0622706, 48.057602, 8.011334, 40.04627, 29.358374, 10.687894], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.06788149, -0.1506578, 0.04856691, -0.10561324, -0.029962689, 0.07788353, -0.05762423, 0.0731148, -0.04085932, 0.051502153, -0.08269038], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.6024637, 0.8315209, 0.6059188, 0.0, 0.72161645, 0.0, 0.9298218, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 2.0, -0.10561324, 2.0, 0.07788353, 8.0, 0.0731148, -0.04085932, 0.051502153, -0.08269038], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.683395, 35.21564, 24.467752, 12.167753, 23.04789, 8.021908, 16.445845, 5.8197756, 17.228115, 7.895449, 8.550395], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.11482114, 0.2664733, -0.012426048, 0.11731296, 0.2040823, 0.14008193, -0.09482622, 0.06505917, -0.023132652, 0.09516398, 0.018181449], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [1.1630185, 1.5206208, 1.5697037, 0.44479522, 0.0, 0.25478148, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 6.0, 7.0, 0.2040823, 2.0, -0.09482622, 0.06505917, -0.023132652, 0.09516398, 0.018181449], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.780342, 26.221504, 31.558838, 20.395004, 5.8265, 21.147364, 10.411474, 13.752266, 6.642738, 5.2336364, 15.913728], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.17142539, 0.2848852, -0.10082067, 0.1883818, 0.5590222, 0.025394835, 0.20218395, 0.2443939, 0.08148546], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [3.7527125, 1.2136393, 0.0, 1.9192502, 0.62065935, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, -0.10082067, 9.0, 4.0, 0.025394835, 0.20218395, 0.2443939, 0.08148546], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.599808, 51.681202, 10.918607, 39.742695, 11.938506, 33.88421, 5.858489, 5.1635118, 6.774994], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.06587976, -0.12144092, 0.010220529, 0.08217375, -0.06624188, -0.13460909, 0.0018368708], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [1.552825, 0.0, 1.0171959, 0.0, 1.1021501, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.12144092, 4.0, 0.08217375, 2.0, -0.13460909, 0.0018368708], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.515045, 9.995116, 47.519928, 10.395392, 37.12454, 5.2991905, 31.825346], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.072495855, -0.15092959, 0.18103464, 0.048389696, -0.27075678, 0.08745729, -0.0, -0.13658233, -0.064577036], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [1.2050298, 1.7828927, 0.28245902, 0.0, 0.18299937, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [8.0, 3.0, 7.0, 0.048389696, 4.0, 0.08745729, -0.0, -0.13658233, -0.064577036], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.376236, 45.193897, 13.182341, 12.206724, 32.98717, 7.7927523, 5.389588, 5.545483, 27.441689], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.097358674, -0.092511244, 0.14621362, 0.2539388, 0.016489273, 0.029072054, 0.11068147, -0.03261355, 0.13528387], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [1.2549006, 0.0, 0.75278866, 0.46230876, 1.5488123, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.092511244, 5.0, 2.0, 9.0, 0.029072054, 0.11068147, -0.03261355, 0.13528387], "split_indices": [2, 0, 1, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.852238, 5.749024, 55.103214, 29.159939, 25.943275, 13.473747, 15.686192, 20.542868, 5.4004073], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.021815313, 0.15542495, -0.15693933, -0.08077747, -0.21405002, -0.077823415, 0.034128137, -0.015645752, -0.10573762], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [4.2330084, 0.0, 0.15691602, 0.82332563, 0.51043296, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.15542495, 6.0, 5.0, 5.0, -0.077823415, 0.034128137, -0.015645752, -0.10573762], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.404087, 10.575334, 45.828754, 21.423765, 24.40499, 11.262537, 10.161228, 12.215611, 12.189379], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.067183346, -0.11321344, 0.14238428, -0.09882555, 0.083594844, 0.10464787, 0.04859708, -0.10761807, 0.059056647], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.8109271, 1.5659726, 0.7571322, 0.0, 0.0, 0.0, 1.8901082, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 7.0, 2.0, -0.09882555, 0.083594844, 0.10464787, 4.0, -0.10761807, 0.059056647], "split_indices": [2, 1, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.373135, 16.34013, 41.033, 10.797372, 5.542759, 11.6355505, 29.397451, 7.28596, 22.111492], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.09870763, -0.16170426, 0.11449699, -0.36555353, 0.020713398, -0.062527224, -0.14926212, 0.052199304, -0.068043105], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1.8065143, 1.9943783, 0.0, 0.34639812, 1.111531, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, 0.11449699, 5.0, 6.0, -0.062527224, -0.14926212, 0.052199304, -0.068043105], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.384556, 51.501957, 5.882597, 24.106983, 27.394976, 12.528574, 11.578409, 17.351032, 10.043942], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.17292325, -0.25544086, 0.04062793, -0.13361414, -0.21148005, -0.047335055, -0.09139751], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.3130062, 0.17300749, 0.0, 0.0, 0.07933009, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 1.0, 0.04062793, -0.13361414, 7.0, -0.047335055, -0.09139751], "split_indices": [2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.77195, 38.866707, 9.905243, 5.1611366, 33.70557, 23.77255, 9.933019], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.031883713, -0.120410435, 0.015630202, -0.0990691, 0.00031016173, 0.077892944, -0.03767794, 0.08159901, -0.06456738, 0.071505725, -0.022369374], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.24255444, 0.5387734, 0.31947303, 0.0, 0.8470909, 0.66845167, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 7.0, -0.0990691, 2.0, 7.0, -0.03767794, 0.08159901, -0.06456738, 0.071505725, -0.022369374], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.72673, 18.963972, 34.762756, 6.5947514, 12.369221, 24.928698, 9.83406, 5.561199, 6.808021, 12.190686, 12.738011], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.09563715, 0.19049281, -0.05633414, 0.27931216, 0.11548859, 0.085965626, -0.22422586, 0.052337613, 0.11327937, 0.082396194, 0.01291835, -0.054170605, 0.107352726, -0.13217673, 0.0052008075], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.89319587, 0.1797576, 0.5953682, 0.04348612, 0.2190426, 1.0344305, 0.64557755, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 4.0, 6.0, 2.0, 2.0, 7.0, 0.052337613, 0.11327937, 0.082396194, 0.01291835, -0.054170605, 0.107352726, -0.13217673, 0.0052008075], "split_indices": [0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.2864, 36.751694, 22.534704, 14.96869, 21.783005, 12.138683, 10.39602, 9.077289, 5.891401, 5.507341, 16.275665, 6.1134224, 6.0252614, 5.267794, 5.128226], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.09218918, 0.018655298, 0.15326282, -0.09532607, 0.0646687, 0.05208717, -0.012983172], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.980906, 0.88833857, 0.0, 0.0, 0.63033, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 1.0, 0.15326282, -0.09532607, 5.0, 0.05208717, -0.012983172], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.37039, 56.696243, 8.6741495, 5.849677, 50.846565, 25.463226, 25.38334], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0628989, -0.20832776, 0.011342095, 0.009566862, -0.3425578, -0.083546385, 0.068653725, -0.05691996, -0.13456544, 0.062607564, -0.012634282], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.62898386, 0.6709189, 0.6244931, 0.0, 0.059462428, 0.0, 0.5307115, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 2.0, 0.009566862, 2.0, -0.083546385, 5.0, -0.05691996, -0.13456544, 0.062607564, -0.012634282], "split_indices": [1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.21326, 18.505045, 36.708218, 6.6398473, 11.865198, 5.135169, 31.573048, 6.3881226, 5.4770756, 13.952447, 17.6206], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.08806818, -0.1581849, 0.14487752, -0.049714282, -0.11111304, -0.0047968193, 0.0838439, -0.06596106, 0.0011079318], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.95874214, 0.97036946, 0.3215962, 0.30507421, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [8.0, 7.0, 5.0, 2.0, -0.11111304, -0.0047968193, 0.0838439, -0.06596106, 0.0011079318], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.213722, 43.78473, 12.428994, 30.102274, 13.682456, 5.725073, 6.703921, 6.8938904, 23.208384], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.08625841, 0.0018998344, 0.26037732, 0.05631627, -0.09101072, 0.43564555, -0.02289438, 7.903658e-05, 0.10020195, 0.008189069, 0.24221943], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.8943845, 0.71727085, 1.2536659, 0.56041276, 0.0, 1.9506702, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 7.0, 5.0, -0.09101072, 4.0, -0.02289438, 7.903658e-05, 0.10020195, 0.008189069, 0.24221943], "split_indices": [0, 0, 2, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.70972, 42.637554, 19.072166, 37.157124, 5.480433, 12.544903, 6.5272627, 32.048977, 5.1081448, 6.617922, 5.926981], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.015170649, 0.15309188, -0.25298184, 0.2977403, -0.047921773, -0.15150286, -0.33793157, 0.15084949, 0.012948585, -0.093195245, -0.0, -0.13322628, -0.05654596], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [2.464397, 1.6782265, 0.120877385, 1.2290666, 0.0, 0.3366854, 0.05303681, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 7.0, 5.0, 3.0, -0.047921773, 3.0, 8.0, 0.15084949, 0.012948585, -0.093195245, -0.0, -0.13322628, -0.05654596], "split_indices": [2, 0, 0, 2, 0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.369762, 34.73141, 24.638353, 24.001415, 10.729997, 13.031654, 11.606698, 12.535063, 11.466352, 5.826206, 7.205448, 5.2422366, 6.364462], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.07678186, 0.14471062, 0.011964306, -0.19654025, 0.099978976, -0.11623492, 0.033845775, 0.07479426, -0.012081973], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [1.5343971, 0.0, 0.9894553, 1.0068128, 0.8379431, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.14471062, 3.0, 7.0, 5.0, -0.11623492, 0.033845775, 0.07479426, -0.012081973], "split_indices": [1, 0, 2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.466072, 7.0936713, 52.372402, 14.842255, 37.530144, 9.222044, 5.620211, 18.145208, 19.384937], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.047871564, 0.18575417, -0.2081792, -0.0, 0.36526194, -0.03388137, -0.24913615, 0.04119032, -0.035722237, 0.0022068413, 0.16958088, -0.09199765, -0.03884013], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [1.9980068, 1.2310239, 0.012364268, 0.3290189, 1.2821486, 0.0, 0.0005828738, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 6.0, 4.0, 2.0, 2.0, -0.03388137, 7.0, 0.04119032, -0.035722237, 0.0022068413, 0.16958088, -0.09199765, -0.03884013], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.561028, 35.896145, 18.664883, 18.126991, 17.769156, 7.4769793, 11.187903, 8.340055, 9.786936, 6.9210205, 10.848134, 5.9362593, 5.2516446], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.06976063, -0.15638164, 0.17926322, -0.012726459, -0.11055496, -0.056309924, 0.17274323, 0.06433575, -0.061322264], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [1.0968698, 1.1052577, 2.0361528, 1.0783145, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, 5.0, 4.0, -0.11055496, -0.056309924, 0.17274323, 0.06433575, -0.061322264], "split_indices": [2, 1, 1, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.656467, 36.673473, 11.982993, 22.779121, 13.894353, 6.366218, 5.6167755, 10.131202, 12.647921], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.012001309, 0.15045112, -0.14138182, 0.027159235, 0.15041146, 0.00039813336, -0.11867798, 0.07480299, -0.056976102, -0.051808864, 0.08735308], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [1.2737052, 1.3094761, 1.0536242, 1.249541, 0.0, 1.0086969, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 8.0, 7.0, 2.0, 0.15041146, 5.0, -0.11867798, 0.07480299, -0.056976102, -0.051808864, 0.08735308], "split_indices": [1, 2, 2, 1, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.934593, 30.802284, 27.13231, 23.80206, 7.000224, 17.80634, 9.325972, 11.916938, 11.885122, 11.200882, 6.6054573], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.06784871, 0.23307703, 0.0063979286, 0.023507468, 0.092815764, -0.19705924, 0.14483082, -0.01176502, -0.10836502, 0.10405426, 0.019937053], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.579196, 0.12203753, 1.2872521, 0.0, 0.0, 0.42000306, 0.37219834, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 3.0, 6.0, 0.023507468, 0.092815764, 5.0, 2.0, -0.01176502, -0.10836502, 0.10405426, 0.019937053], "split_indices": [2, 1, 2, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.54991, 14.6741, 43.875813, 5.9492044, 8.7248955, 17.301554, 26.574259, 9.7625885, 7.5389647, 6.1138163, 20.460443], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.116616756, 0.16570757, -0.091281205, -0.0024791174, 0.22538613, -0.03787563, 0.03434818, 0.16169834, 0.044678673], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1.3231822, 0.59121716, 0.0, 0.2383172, 0.8638847, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 2.0, -0.091281205, 5.0, 3.0, -0.03787563, 0.03434818, 0.16169834, 0.044678673], "split_indices": [2, 1, 0, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.270683, 55.62649, 5.6441936, 14.380903, 41.245586, 7.3641877, 7.016715, 6.642437, 34.60315], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.028737696, -0.14445342, 0.0850505, 0.011699694, -0.20487498, 0.07167371, -0.017819934, -0.09454436, -0.020878851, 0.062976144, -0.1039929], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.76074016, 0.3434264, 0.4794597, 0.0, 0.2708894, 0.0, 1.4117093, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 4.0, 0.011699694, 3.0, 0.07167371, 8.0, -0.09454436, -0.020878851, 0.062976144, -0.1039929], "split_indices": [1, 1, 0, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.39169, 27.765345, 27.626345, 6.5568624, 21.208483, 10.929253, 16.697092, 10.544374, 10.664107, 9.898766, 6.798326], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.050208222, 0.06489933, -0.21793218, -0.04279233, 0.13954736, -0.027480582, -0.0866167, 0.122492604, 0.010486967], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [1.0917733, 0.53158635, 0.14100385, 0.0, 0.64879715, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 5.0, -0.04279233, 3.0, -0.027480582, -0.0866167, 0.122492604, 0.010486967], "split_indices": [0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.024876, 32.04767, 21.977207, 7.882329, 24.165339, 9.223613, 12.753594, 5.688342, 18.476997], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04538867, -0.0037877392, 0.12171066, -0.15463269, 0.26978305, -0.023502314, -0.110761665, 0.05531465, 0.11734406], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1.136301, 2.3040912, 0.0, 0.52201605, 0.07464385, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 7.0, 0.12171066, 7.0, 5.0, -0.023502314, -0.110761665, 0.05531465, 0.11734406], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.77422, 54.10218, 6.6720376, 35.389156, 18.713028, 27.457685, 7.9314704, 12.946262, 5.766764], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.022816623, 0.16417614, -0.20417787, -0.07217733, 0.27821803, -0.13806267, -0.06376629, 0.11299768, 0.03416428, 0.017571457, -0.04863332], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [1.8750006, 1.7448375, 0.717978, 0.0, 0.3764243, 0.0, 0.20969951, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 7.0, -0.07217733, 3.0, -0.13806267, 4.0, 0.11299768, 0.03416428, 0.017571457, -0.04863332], "split_indices": [2, 1, 2, 0, 2, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.51338, 35.267822, 21.245554, 7.3157163, 27.952106, 6.465815, 14.779739, 16.292257, 11.659848, 6.3135166, 8.466223], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.03846961, -0.193765, 0.12271173, -0.0, -0.08779465, 0.20910297, -0.10771992, 0.08653615, -0.0, -0.099924915, 0.045331694], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [1.1713006, 0.2845717, 0.9132919, 0.0, 0.0, 0.5532479, 0.79221284, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 7.0, -0.0, -0.08779465, 8.0, 7.0, 0.08653615, -0.0, -0.099924915, 0.045331694], "split_indices": [2, 2, 1, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.01822, 14.781671, 43.236546, 5.540446, 9.241225, 31.829763, 11.406784, 22.901012, 8.928751, 6.1507215, 5.256062], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.04206189, 0.078621276, -0.29315072, -0.0, 0.13948645, -0.16612281, -0.13781048, 0.024843328, -0.032049175, -0.081070475, -0.013019355], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [1.7315807, 1.1176018, 0.25288308, 0.29748613, 0.0, 0.1156528, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 6.0, 7.0, 0.13948645, 8.0, -0.13781048, 0.024843328, -0.032049175, -0.081070475, -0.013019355], "split_indices": [1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.675167, 37.121655, 17.553514, 31.861942, 5.2597117, 11.424022, 6.1294928, 18.532526, 13.329415, 5.089568, 6.3344536], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.15830591, -0.26289645, -0.04000418, -0.13603306, -0.16266665, 0.07446287, -0.15844394, -0.020270534, -0.0720752, -0.090923294, -0.014108918], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.524438, 0.3157047, 0.8176787, 0.0, 0.08122015, 0.0, 0.22795084, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 3.0, -0.13603306, 7.0, 0.07446287, 6.0, -0.020270534, -0.0720752, -0.090923294, -0.014108918], "split_indices": [0, 2, 2, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.68286, 22.618395, 22.064466, 6.2378535, 16.380543, 5.861355, 16.203112, 8.779252, 7.6012897, 5.911369, 10.291743], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.06008274, -0.21813783, 0.059410766, -0.31181526, -0.020571453, 0.13874215, -0.062609844, -0.034260903, -0.14028282, -0.035803385, 0.07492306], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [1.1196663, 0.2970283, 0.73058707, 0.34867942, 0.0, 0.8048201, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 8.0, 3.0, -0.020571453, 6.0, -0.062609844, -0.034260903, -0.14028282, -0.035803385, 0.07492306], "split_indices": [1, 2, 2, 2, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.75716, 24.440649, 32.31651, 13.949995, 10.490655, 25.597841, 6.7186694, 7.196601, 6.753394, 7.404878, 18.192963], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.03506028, 0.12057738, -0.12021204, 0.22407031, -0.06156444, -0.083795264, -0.0, -0.007425404, 0.08481625, 0.050715864, -0.03602678], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.8463016, 1.440664, 0.42682907, 0.50066996, 0.0, 0.0, 0.29888546, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 8.0, 3.0, 1.0, -0.06156444, -0.083795264, 5.0, -0.007425404, 0.08481625, 0.050715864, -0.03602678], "split_indices": [0, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.64067, 40.308186, 21.332485, 31.060507, 9.247678, 8.607419, 12.725066, 5.7098274, 25.35068, 5.1119056, 7.613161], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.10642073, 0.16830827, -0.052000087, 0.036896016, 0.49679193, 0.06113075, -0.056075763, 0.04889336, 0.19081734], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1.1707428, 2.2945156, 0.0, 1.5505338, 0.5307605, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, -0.052000087, 3.0, 4.0, 0.06113075, -0.056075763, 0.04889336, 0.19081734], "split_indices": [1, 1, 0, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.96496, 53.815636, 11.149323, 39.450092, 14.365544, 22.907202, 16.54289, 5.0792127, 9.286331], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.026000638, -0.17862803, 0.12794748, -0.0, -0.23701863, -0.03450056, 0.22161122, -0.091080435, -0.0170219, 0.103022285, -0.035917703], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [1.3461146, 0.28677666, 0.677539, 0.0, 0.20515001, 0.0, 0.9357809, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 3.0, -0.0, 7.0, -0.03450056, 8.0, -0.091080435, -0.0170219, 0.103022285, -0.035917703], "split_indices": [1, 1, 2, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.10114, 27.91494, 27.1862, 7.3869853, 20.527954, 7.204185, 19.982016, 14.08657, 6.441384, 14.938525, 5.0434895], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.04114405, -0.08045374, 0.29890063, -0.15523879, 0.04919886, 0.40280464, 0.0033865534, 0.03640126, -0.068775125, 0.06516693, 0.15557955], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [1.7588036, 0.71301, 0.4917624, 0.64580154, 0.0, 0.09170699, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [7.0, 8.0, 9.0, 1.0, 0.04919886, 8.0, 0.0033865534, 0.03640126, -0.068775125, 0.06516693, 0.15557955], "split_indices": [1, 2, 1, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.651157, 36.657658, 16.993502, 28.662813, 7.9948435, 11.933978, 5.0595245, 5.630698, 23.032114, 6.022051, 5.9119267], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.00955414, 0.025077578, -0.09552984, -0.08722855, 0.06448194, 0.035288658, -0.07080958], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.7272425, 0.72580075, 0.0, 0.0, 0.86603045, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 1.0, -0.09552984, -0.08722855, 9.0, 0.035288658, -0.07080958], "split_indices": [2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.49674, 57.63025, 5.86649, 5.4034038, 52.226845, 45.178253, 7.0485935], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.028667511, 0.11762104, -0.17547132, 0.08983091, 0.05648154, -0.07328293, -0.004179676, -0.0009638948, 0.10111185], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [1.0778052, 0.4111517, 0.16890508, 0.0, 0.5847641, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 2.0, 9.0, 0.08983091, 6.0, -0.07328293, -0.004179676, -0.0009638948, 0.10111185], "split_indices": [0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.536186, 40.71033, 16.825853, 8.852315, 31.858017, 11.006553, 5.8193, 26.790003, 5.0680137], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.009431876, 0.084517546, -0.07090346, -0.05577481, 0.18587767, -0.14285967, 0.050025072, 0.0059968224, 0.08603963, 0.038705356, -0.08657183], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.40166658, 0.99088895, 0.55978334, 0.0, 0.4013158, 1.044631, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 8.0, -0.05577481, 3.0, 3.0, 0.050025072, 0.0059968224, 0.08603963, 0.038705356, -0.08657183], "split_indices": [2, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.369774, 33.888504, 30.481272, 8.742448, 25.146057, 24.0263, 6.45497, 10.33951, 14.806546, 8.184192, 15.842109], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.013918207, 0.12851387, -0.14962216, 0.24139906, -0.036743537, -0.26111272, 0.06789424, 0.005004383, 0.14400189, -0.009785127, -0.14709878], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [1.0764145, 0.8011113, 1.2686269, 0.96672475, 0.0, 1.129141, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 6.0, 9.0, 3.0, -0.036743537, 4.0, 0.06789424, 0.005004383, 0.14400189, -0.009785127, -0.14709878], "split_indices": [2, 1, 2, 2, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.586227, 25.8113, 27.774927, 18.10508, 7.70622, 21.862616, 5.9123106, 10.090737, 8.014342, 11.739052, 10.123565], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.12131404, -0.13019738, -0.06852319, -0.22432649, 0.012880531, -0.027732864, -0.09677078, -0.015941462, 0.06927168], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.6890402, 0.0, 0.54810596, 0.1185745, 0.42824522, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.13019738, 4.0, 6.0, 8.0, -0.027732864, -0.09677078, -0.015941462, 0.06927168], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.58954, 5.268615, 40.320923, 13.656217, 26.664707, 7.093103, 6.5631137, 20.695616, 5.969091], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.015035467, 0.11745265, -0.061748937, 0.10527683, -0.03453232, -0.072134055, 0.035989214, -0.022769293, 0.051351544], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.4464163, 1.3957124, 0.58116585, 0.0, 0.0, 0.0, 0.33925617, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [5.0, 5.0, 4.0, 0.10527683, -0.03453232, -0.072134055, 8.0, -0.022769293, 0.051351544], "split_indices": [2, 0, 0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.136192, 23.527397, 30.608795, 11.701333, 11.826064, 10.667706, 19.94109, 10.745857, 9.195231], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.023202466, -0.07986277, 0.15879527, 0.073231466, -0.24600057, 0.11591361, 0.046752915, 0.04789827, -0.010696174, -0.10372909, -0.0064150356, 0.034004588, -0.020503191], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.9115437, 0.96285856, 0.6598968, 0.20100948, 0.35995853, 0.0, 0.16851136, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 7.0, 5.0, 6.0, 0.11591361, 6.0, 0.04789827, -0.010696174, -0.10372909, -0.0064150356, 0.034004588, -0.020503191], "split_indices": [2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.754898, 35.4685, 27.2864, 18.436636, 17.031864, 7.8995667, 19.386833, 10.530203, 7.906432, 11.111967, 5.919897, 12.840988, 6.5458455], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.08390695, 0.124726735, -0.090182856, 0.031615306, 0.2831599, 0.03596299, -0.024658535, 0.12132742, 0.060223293], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1.0862148, 0.8794665, 0.0, 0.42344627, 0.08375442, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 7.0, -0.090182856, 4.0, 8.0, 0.03596299, -0.024658535, 0.12132742, 0.060223293], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.93561, 61.357647, 5.5779576, 39.79865, 21.559, 22.880161, 16.918486, 6.6515417, 14.907458], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.07550814, -0.12146603, 0.07966499, -0.10830283, -0.06555675, 0.08746858, -0.052084323], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.88815594, 0.6016454, 0.0, 0.0, 1.6594961, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 2.0, 0.07966499, -0.10830283, 4.0, 0.08746858, -0.052084323], "split_indices": [0, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.52454, 48.875572, 5.6489677, 7.877735, 40.997837, 8.914094, 32.08374], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.073361516, 0.022229502, -0.13289846, 0.16359398, -0.09211294, -0.05886822, -0.048677444, 0.0731418, 0.018190153, 0.007149069, -0.039726235], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.330312, 1.0385883, 0.15281576, 0.086382836, 0.0, 0.0, 0.11750841, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 6.0, 3.0, -0.09211294, -0.05886822, 7.0, 0.0731418, 0.018190153, 0.007149069, -0.039726235], "split_indices": [0, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.575462, 20.54152, 34.033947, 14.936006, 5.6055126, 17.989616, 16.044329, 7.080593, 7.8554125, 8.342465, 7.701863], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.030300781, -0.08986184, 0.14336137, -0.23397344, 0.06536046, 0.22612306, -0.009526774, -0.01196544, -0.12046289, 0.09189565, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.8310641, 1.3470894, 0.48517758, 0.609288, 0.0, 0.42507482, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 7.0, 3.0, 0.06536046, 9.0, -0.009526774, -0.01196544, -0.12046289, 0.09189565, -0.0], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.83012, 28.19002, 30.6401, 19.583569, 8.60645, 20.99669, 9.643409, 9.92391, 9.659659, 15.51691, 5.479782], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.01918506, 0.06908062, -0.25562677, 0.078565136, -0.022557566, -0.11538543, -0.0020452617, 0.05501207, -0.04582097], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [1.2192947, 0.76757133, 0.46947145, 0.0, 0.79893947, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 9.0, 0.078565136, 3.0, -0.11538543, -0.0020452617, 0.05501207, -0.04582097], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.684177, 40.754917, 14.929258, 12.883013, 27.871904, 9.207845, 5.721414, 10.339295, 17.53261], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0009917619, -0.13467139, 0.09291241, -0.24902466, 0.04160799, 0.09447314, 0.02691128, -0.110195756, -0.031316366, -0.06511594, 0.043876972], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.790919, 0.8369489, 0.52057207, 0.23041451, 0.0, 0.0, 0.9293406, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 6.0, 3.0, 0.04160799, 0.09447314, 7.0, -0.110195756, -0.031316366, -0.06511594, 0.043876972], "split_indices": [2, 1, 2, 2, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.755962, 24.315866, 37.440094, 17.464859, 6.8510075, 7.3687005, 30.071396, 8.380054, 9.084805, 9.334106, 20.73729], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.08076106, -0.19183147, 0.0420294, -0.004913552, -0.2478669, 0.17442976, -0.026392827, -0.008893513, -0.10719011, -0.0061478247, 0.09604761], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.78436273, 0.26364732, 0.48709774, 0.0, 0.4802071, 0.43551585, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 8.0, -0.004913552, 3.0, 7.0, -0.026392827, -0.008893513, -0.10719011, -0.0061478247, 0.09604761], "split_indices": [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.821846, 28.917017, 25.904827, 7.7446284, 21.17239, 13.045995, 12.858832, 7.8010216, 13.371367, 5.6550913, 7.390904], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.116117544, -0.09338841, -0.04600416, 0.029801859, -0.14137793, -0.0096470155, 0.031973798, -0.06156771, -0.0013070477], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.5810356, 0.0, 0.27188063, 0.10605291, 0.12223476, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.09338841, 7.0, 5.0, 9.0, -0.0096470155, 0.031973798, -0.06156771, -0.0013070477], "split_indices": [0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.116783, 10.637806, 34.478977, 19.009031, 15.469947, 10.120556, 8.888474, 9.715316, 5.7546306], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.062013626, -0.06645848, 0.13617681, 0.046095658, -0.17984433, 0.14537242, 0.0632516, -0.0013791963, -0.10164157, 0.041177038, -0.059275772], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.57728326, 0.5727814, 0.88432646, 0.0, 0.38497105, 0.0, 0.6571305, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 1.0, 5.0, 0.046095658, 5.0, 0.14537242, 8.0, -0.0013791963, -0.10164157, 0.041177038, -0.059275772], "split_indices": [2, 2, 2, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.907143, 20.738157, 37.168987, 6.630196, 14.107962, 5.2190495, 31.949936, 7.4988422, 6.6091194, 25.556246, 6.3936896], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.08293765, 0.11130673, -0.04632669, 0.16270286, 0.056468405, -0.0, 0.0612194, 0.07529885, -0.011568211], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.43281126, 0.12859833, 0.0, 0.200674, 0.5833944, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 4.0, -0.04632669, 2.0, 3.0, -0.0, 0.0612194, 0.07529885, -0.011568211], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.36329, 55.662872, 5.7004175, 26.776566, 28.886305, 5.2999177, 21.476648, 9.286855, 19.59945], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.08666609, 0.16655709, -0.019140124, 0.06116521, 0.25262928, -0.101082124, 0.07278441, -0.034335125, 0.07175303, 0.1604841, -0.01601992, -0.033817332, 0.048185408], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.60009015, 0.31495523, 0.9269997, 0.65791494, 1.9007162, 0.0, 0.4113144, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 3.0, 5.0, 5.0, -0.101082124, 7.0, -0.034335125, 0.07175303, 0.1604841, -0.01601992, -0.033817332, 0.048185408], "split_indices": [2, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [67.870575, 38.882534, 28.988047, 18.770844, 20.11169, 6.1497293, 22.838318, 9.345967, 9.424875, 10.241992, 9.869698, 6.8817744, 15.956543], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.019329777, -0.05673392, 0.049105737, 0.1389094, -0.07628143, 0.00188783, 0.12444899], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.6455131, 0.0, 1.073818, 1.0764234, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.05673392, 8.0, 6.0, -0.07628143, 0.00188783, 0.12444899], "split_indices": [1, 0, 2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.551487, 15.010194, 37.541294, 29.639387, 7.901906, 20.953611, 8.685775], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.042567067, -0.14992705, 0.14647382, 0.011034907, -0.09131698, -0.0, 0.24035443, -0.027038964, 0.047167774, 0.1332534, 0.041645046], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 66, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [1.1116354, 0.5934509, 0.48384959, 0.0, 0.0, 0.2364733, 0.33188224, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 5.0, 5.0, 0.011034907, -0.09131698, 8.0, 5.0, -0.027038964, 0.047167774, 0.1332534, 0.041645046], "split_indices": [1, 2, 2, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.4971, 18.261383, 35.235718, 8.319491, 9.941892, 14.403171, 20.832548, 9.183586, 5.219584, 5.477602, 15.354945], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.00768105, -0.032970887, 0.08447102, -0.1716891, 0.07531621, -0.006403395, -0.09891287, 0.054344945, -0.041097388], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 67, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.7429999, 0.86398065, 0.0, 0.5581238, 0.74258274, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, 0.08447102, 3.0, 5.0, -0.006403395, -0.09891287, 0.054344945, -0.041097388], "split_indices": [1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.665245, 55.014267, 7.6509767, 24.266415, 30.747852, 13.341191, 10.925224, 20.93146, 9.816393], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.035348084, -0.08828127, 0.10052024, -0.14334477, 0.04446179, -0.045917336, -0.017665206, -0.058504205], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 68, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.604824, 0.6235869, 0.0, 0.5010052, 0.024571449, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 7.0, -0.08828127, 8.0, 3.0, 0.04446179, -0.045917336, -0.017665206, -0.058504205], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.900955, 51.67654, 5.2244143, 38.59677, 13.079772, 33.169155, 5.427613, 6.511241, 6.568531], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04544864, 0.079170704, 0.003179997, -0.053328086, 0.14309447, -0.060757205, 0.008966049], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 69, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.5685843, 0.0, 1.5534518, 0.64765143, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, 0.079170704, 9.0, 4.0, 0.14309447, -0.060757205, 0.008966049], "split_indices": [1, 0, 2, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.560154, 9.070828, 54.489326, 49.287434, 5.2018933, 17.56433, 31.723104], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.034287293, -0.1587298, 0.025194459, -0.012841306, -0.06348132, 0.08944527, -0.04687488, -0.030244099, 0.05090287], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 70, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.39435247, 0.07063609, 0.41275296, 0.0, 0.0, 0.43197706, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 4.0, 8.0, -0.012841306, -0.06348132, 6.0, -0.04687488, -0.030244099, 0.05090287], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.93175, 16.152193, 33.77956, 6.2187157, 9.933476, 25.727436, 8.052123, 7.1853123, 18.542126], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.04030578, 0.06437707, -0.18744066, -0.017573964, 0.07684107, -0.10771178, -0.003697725, -0.07954569, 0.06601868], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 71, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.69045323, 0.43089318, 0.5182615, 1.1522236, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 8.0, 6.0, 0.07684107, -0.10771178, -0.003697725, -0.07954569, 0.06601868], "split_indices": [1, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [42.244926, 24.614868, 17.630058, 17.516035, 7.098834, 8.085528, 9.54453, 8.727949, 8.788085], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.011964069, 0.23429024, -0.11211051, -0.0046294215, 0.13405102, -0.15504692, 0.03968091, -0.087166876, -0.019356843], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 72, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [1.4456728, 0.9426839, 0.46593028, 0.0, 0.0, 0.39630967, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [2.0, 1.0, 9.0, -0.0046294215, 0.13405102, 4.0, 0.03968091, -0.087166876, -0.019356843], "split_indices": [1, 1, 1, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.051003, 15.817838, 41.233162, 7.4829745, 8.334864, 35.730278, 5.5028863, 12.982236, 22.748041], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.0329946, 0.08525218, 0.04811037, -0.09024487, -0.0, 0.066606864], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 73, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.57955885, 1.2843261, 0.0, 0.35736406, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 7.0, 0.08525218, 6.0, -0.09024487, -0.0, 0.066606864], "split_indices": [2, 2, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.701214, 56.296852, 5.40436, 43.55422, 12.742633, 35.21255, 8.341667], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.06300214, 0.001598303, 0.24822745, 0.13898873, -0.11529778, 0.1388188, -0.0028918688, 0.07378744, -0.031631906, -0.010961777, -0.05394243], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 74, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.73013777, 0.8423074, 0.9417839, 0.67873645, 0.11340085, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, 6.0, 7.0, 6.0, 0.1388188, -0.0028918688, 0.07378744, -0.031631906, -0.010961777, -0.05394243], "split_indices": [0, 0, 2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.42759, 50.300903, 15.126685, 23.429815, 26.871088, 8.035692, 7.090993, 16.577744, 6.852071, 13.502669, 13.368419], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.026451427, -0.076776326, 0.08081422, -0.23338574, 0.08216656, -0.1347036, -0.040512808, 0.08381232, -0.08042704], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 75, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.8072634, 1.2123306, 0.0, 0.39869118, 1.7276134, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 6.0, 0.08081422, 3.0, 5.0, -0.1347036, -0.040512808, 0.08381232, -0.08042704], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.127937, 46.325817, 6.802121, 23.415188, 22.910631, 5.8988333, 17.516354, 14.969891, 7.94074], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.04068747, -0.08614393, 0.08800998, -0.0227349, -0.23638268, 0.045609687, -0.0, -0.023596998, 0.028763141, -0.036198, -0.08844276], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 76, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.33413646, 0.37219405, 0.09519182, 0.21381505, 0.0039876103, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 7.0, 6.0, 6.0, 4.0, 0.045609687, -0.0, -0.023596998, 0.028763141, -0.036198, -0.08844276], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.852, 41.353455, 13.498545, 30.357746, 10.99571, 7.977055, 5.5214896, 21.432808, 8.924938, 5.291598, 5.7041125], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0042613703, -0.09803328, 0.06351958, 0.009288543, 0.10850234, 0.035653796, -0.0452148], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 77, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [1.4388167, 0.0, 0.8195739, 0.8265897, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.09803328, 9.0, 5.0, 0.10850234, 0.035653796, -0.0452148], "split_indices": [2, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.66446, 10.475249, 52.18921, 45.313705, 6.875506, 27.42062, 17.893084], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.04163216, 0.1561396, -0.14346586, 0.04748636, 0.1529656, 0.027427573, -0.282695, -0.06953152, 0.046834312, -0.1316814, -0.039112326], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 78, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [1.1995845, 1.2687118, 0.7445607, 0.88294065, 0.0, 0.0, 0.21765912, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 3.0, 2.0, 0.1529656, 0.027427573, 6.0, -0.06953152, 0.046834312, -0.1316814, -0.039112326], "split_indices": [2, 2, 1, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.480583, 34.088852, 20.39173, 27.162731, 6.926123, 7.4492807, 12.942449, 7.0316763, 20.131054, 5.136004, 7.806444], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.021323388, 0.07691122, -0.107234016, 0.0024564874, 0.16284615, 0.058930684, -0.028921794], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 79, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [1.3412256, 1.9084185, 0.0, 0.9880453, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, -0.107234016, 3.0, 0.16284615, 0.058930684, -0.028921794], "split_indices": [0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.72772, 55.62519, 7.102527, 49.012627, 6.612567, 16.554808, 32.457817], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.11649747, -0.11722072, 0.24236694, -0.06784602, -0.17150027, 0.009392502, -0.0016261718, 0.20839581, -0.06021884, -0.025175635], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 80, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.73651475, 1.2236919, 0.23601481, 2.3689554, 0.0, 0.006722927, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 8.0, 8.0, 6.0, -0.06784602, 6.0, 0.009392502, -0.0016261718, 0.20839581, -0.06021884, -0.025175635], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.933914, 26.064178, 25.869734, 19.489437, 6.5747423, 19.325788, 6.543947, 13.086716, 6.402721, 12.665222, 6.6605654], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.12589192, -0.3076455, 0.03401394, -0.13484423, -0.20119543, -0.116377875, 0.09393092, -0.009252126, -0.09711413, 0.08505717, -0.1392328], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 81, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [1.2740445, 0.15099311, 1.0313584, 0.0, 0.24278879, 2.3182802, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 7.0, -0.13484423, 7.0, 3.0, 0.09393092, -0.009252126, -0.09711413, 0.08505717, -0.1392328], "split_indices": [0, 2, 2, 0, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [41.535095, 19.29982, 22.235273, 6.49026, 12.809559, 14.5951395, 7.6401343, 6.2237296, 6.5858297, 6.7404666, 7.854673], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.09817662, 0.079825364, -0.1641959, 0.048069384, -0.0, -0.10016501, -0.11971932, 0.03475608, -0.052015573], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 82, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.6521259, 0.0985633, 0.21563852, 0.0, 0.0, 0.0, 0.4509104, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 3.0, 1.0, 0.048069384, -0.0, -0.10016501, 2.0, 0.03475608, -0.052015573], "split_indices": [2, 1, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.659412, 13.780481, 38.878933, 6.507414, 7.273067, 6.25593, 32.623, 5.4921937, 27.130808], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.031610515, -0.17766751, 0.071780235, -0.112443574, -0.08330123, 0.005918787, 0.09627779, -0.05117609, -0.0122859, 0.051330913, -0.07561971], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 83, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.9693785, 0.10362446, 0.57243496, 0.048419952, 0.0, 1.3388225, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 8.0, 3.0, -0.08330123, 4.0, 0.09627779, -0.05117609, -0.0122859, 0.051330913, -0.07561971], "split_indices": [0, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.647526, 25.654697, 35.99283, 17.41844, 8.236258, 29.594639, 6.398188, 8.070092, 9.348349, 18.483973, 11.110666], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.06305274, 0.08952679, -0.073692925, 0.10712328, 0.05325259, -0.04433328, -0.0, -0.0025624551, 0.060965847], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 84, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.2700607, 0.53846335, 0.07744955, 0.0, 0.53840685, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [8.0, 1.0, 5.0, 0.10712328, 6.0, -0.04433328, -0.0, -0.0025624551, 0.060965847], "split_indices": [1, 2, 2, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [71.338264, 60.669247, 10.669015, 5.85839, 54.81086, 5.448322, 5.2206926, 39.012093, 15.798766], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.058662254, 0.0074864123, -0.09411987, -0.22920658, 0.09237141, -0.086792596, -0.03329248, 0.12322992, -0.029923424], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 85, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.93764216, 0.87628484, 0.0, 0.008806109, 2.0815363, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 3.0, -0.09411987, 5.0, 5.0, -0.086792596, -0.03329248, 0.12322992, -0.029923424], "split_indices": [2, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.617687, 42.24158, 10.376107, 10.390497, 31.851086, 5.368635, 5.0218616, 11.753823, 20.097261], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.038607337, 0.18738247, -0.12209208, 0.09895055, 0.016626952, -0.31013864, 0.06684267, -0.1353879, -0.0644492, 0.076554246, -0.024055239], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 86, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.9909211, 0.20648095, 1.41822, 0.0, 0.0, 0.10298121, 0.58725274, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 4.0, 5.0, 0.09895055, 0.016626952, 5.0, 8.0, -0.1353879, -0.0644492, 0.076554246, -0.024055239], "split_indices": [0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.655666, 13.006166, 37.649498, 5.1689034, 7.8372636, 18.812855, 18.836643, 5.6543055, 13.158549, 8.233829, 10.602815], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.01809628, -0.12396464, 0.063551776, -0.00762956, -0.1657188, -0.0, 0.18019597, -0.06663343, -0.032560922, 0.028003601, -0.06556753, 0.07582374, 0.020291891], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 87, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.57576084, 0.096695125, 0.25762445, 0.0, 0.0060200095, 0.5260945, 0.053058386, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 6.0, -0.00762956, 4.0, 4.0, 8.0, -0.06663343, -0.032560922, 0.028003601, -0.06556753, 0.07582374, 0.020291891], "split_indices": [0, 0, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.998245, 28.173618, 35.824627, 9.577168, 18.59645, 24.310139, 11.514489, 7.0302453, 11.566205, 17.709335, 6.600802, 5.7039666, 5.810523], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.22196373, -0.055849757, 0.09141971, 0.029581795, -0.14477243, 0.036108725, -0.02458124, -0.07252779, 0.06838651, -0.05170079], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 88, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.73692703, 0.05094254, 0.41323125, 0.0, 0.0, 0.10287672, 1.0095176, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 4.0, 5.0, 0.09141971, 0.029581795, 7.0, 8.0, -0.02458124, -0.07252779, 0.06838651, -0.05170079], "split_indices": [0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.44923, 10.882762, 47.56647, 5.1478057, 5.7349563, 24.44327, 23.123201, 16.578465, 7.8648047, 12.242089, 10.881111], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.026290951, -0.052276134, 0.04997817, 0.09063139, -0.11233086, -0.024141962, 0.064514235, -0.09274736, -0.0114953425], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 89, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.31819105, 0.5070645, 0.0, 0.39515257, 0.5583571, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 2.0, 0.04997817, 5.0, 4.0, -0.024141962, 0.064514235, -0.09274736, -0.0114953425], "split_indices": [2, 1, 0, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.02723, 56.643692, 6.383539, 16.11701, 40.52668, 6.5956726, 9.521337, 9.837388, 30.689293], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.030916778, 0.07519694, -0.08115733, 0.06988106, -0.18650725, 0.055067416, -0.019899582, -0.10823431, -0.026578762], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 90, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.7675138, 0.0, 0.7708599, 0.3274823, 0.40578008, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.07519694, 3.0, 7.0, 5.0, 0.055067416, -0.019899582, -0.10823431, -0.026578762], "split_indices": [0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.015263, 7.1217647, 45.893497, 18.605942, 27.287558, 10.35266, 8.253281, 8.482803, 18.804754], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.003956196, -0.090879984, 0.07002518, -0.0226246, -0.061361972, -0.044913016, 0.07006542], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 91, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.8383409, 0.20943555, 0.0, 0.7019121, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 7.0, 0.07002518, 5.0, -0.061361972, -0.044913016, 0.07006542], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.293007, 29.521095, 9.77191, 19.743528, 9.777568, 13.735491, 6.008038], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0071900715, -0.043102432, 0.19135575, 0.032607634, -0.07620928, 0.009752948, 0.102960184, -0.05251337, 0.044941995], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 92, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.5399583, 0.7366741, 0.25472593, 0.8287852, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, 7.0, 2.0, -0.07620928, 0.009752948, 0.102960184, -0.05251337, 0.044941995], "split_indices": [2, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.806145, 43.225082, 11.581063, 32.081245, 11.143836, 6.5752816, 5.0057817, 11.088292, 20.992954], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.015307087, 0.079518676, -0.10067467, -0.05180936, 0.13276565, -0.04207106, -0.07671227, 0.017405208, -0.098096706, -0.046800926, 0.06363228], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 93, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.51616186, 1.474148, 0.26739925, 0.7241671, 0.0, 0.7879654, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 8.0, 2.0, 0.13276565, 6.0, -0.07671227, 0.017405208, -0.098096706, -0.046800926, 0.06363228], "split_indices": [1, 1, 2, 1, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.512398, 28.619333, 32.893063, 21.44382, 7.1755114, 25.260715, 7.6323485, 15.631081, 5.8127413, 17.98076, 7.2799544], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.030858174, -0.091580115, 0.082433425, 0.07954408, -0.14140765, -0.014687341, 0.3142451, 0.11646704, -0.035596035, 0.08603977, -0.026587192, 0.16623205, 0.026696203], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 94, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.47556856, 1.5005014, 1.2357026, 1.0382706, 0.0, 0.85822535, 0.7563374, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 7.0, 7.0, 1.0, -0.14140765, 1.0, 6.0, 0.11646704, -0.035596035, 0.08603977, -0.026587192, 0.16623205, 0.026696203], "split_indices": [2, 1, 1, 2, 0, 1, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [73.25968, 20.908611, 52.351067, 14.777341, 6.1312704, 37.24994, 15.10113, 5.600584, 9.176757, 6.5027213, 30.747217, 6.3914714, 8.709658], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.049812607, -0.13126443, 0.099385776, -0.10398507, -0.05527994, 0.097635664, -0.032340746, -0.04553717, 0.0037848211], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 95, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.6552776, 0.5114045, 0.93298745, 0.0, 0.19482392, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 4.0, -0.10398507, 3.0, 0.097635664, -0.032340746, -0.04553717, 0.0037848211], "split_indices": [1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.585464, 33.87988, 17.705584, 7.564139, 26.31574, 8.424894, 9.280689, 10.965734, 15.350008], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0013022593, 0.0493421, -0.05873219, 0.0940213, -0.05721815, 0.012206329, 0.09121035], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 96, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.5134705, 0.49346203, 0.0, 0.39176357, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 9.0, -0.05873219, 8.0, -0.05721815, 0.012206329, 0.09121035], "split_indices": [0, 2, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.761368, 44.23546, 9.525909, 38.158665, 6.076791, 31.762402, 6.396265], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.05011486, -0.01581737, 0.18412189, 0.019464264, -0.04737713, 0.12628116, 0.005872015, -0.006346824, 0.053549405, -0.027076703, 0.042939767], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 97, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.5559025, 0.22294964, 0.8240092, 0.23428203, 0.0, 0.0, 0.1897726, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [7.0, 8.0, 4.0, 6.0, -0.04737713, 0.12628116, 8.0, -0.006346824, 0.053549405, -0.027076703, 0.042939767], "split_indices": [2, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.683247, 40.093, 19.59025, 32.246334, 7.8466654, 7.587416, 12.002833, 25.875418, 6.370916, 6.8320513, 5.1707826], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.029301168, 0.0046100067, -0.14380078, -0.06771908, 0.06739859, -0.07214849, -0.0, 0.016527697, -0.05188855], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 98, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.24357635, 0.7597991, 0.22008896, 0.47230977, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 9.0, 4.0, 0.06739859, -0.07214849, -0.0, 0.016527697, -0.05188855], "split_indices": [0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.976353, 44.795666, 13.180691, 33.893124, 10.902542, 7.8752775, 5.3054132, 15.42246, 18.470663], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.003168007, -0.081216015, 0.1593256, -0.07625702, -0.011228398, 0.23358753, -0.08489632, -0.042186357, 0.016037626, 0.069618106, -0.07871383], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 99, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.8834667, 0.48813024, 3.7231796, 0.0, 0.2831781, 0.0, 1.0651374, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 3.0, -0.07625702, 3.0, 0.23358753, 8.0, -0.042186357, 0.016037626, 0.069618106, -0.07871383], "split_indices": [2, 0, 0, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.47033, 41.74942, 22.720907, 10.856609, 30.892809, 5.9228883, 16.79802, 10.420623, 20.472187, 5.7171516, 11.080868], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.016857458, -0.033370387, 0.116100065, 0.13337003, -0.24538141, -0.005521433, 0.09566838, -0.014748961, -0.1427405], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 100, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1.0572528, 1.7573484, 0.0, 0.8106853, 0.91671157, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, 0.116100065, 3.0, 4.0, -0.005521433, 0.09566838, -0.014748961, -0.1427405], "split_indices": [2, 2, 0, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.270874, 47.46907, 5.8018064, 26.515034, 20.954035, 14.783039, 11.731994, 12.206629, 8.747407], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.06706352, -0.07619996, 0.021601558, 0.08541287, -0.025808783, 0.0015828153, 0.056141097], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 101, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.6802357, 0.0, 0.19423774, 0.12792268, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [4.0, -0.07619996, 6.0, 8.0, -0.025808783, 0.0015828153, 0.056141097], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.307377, 12.086989, 26.220388, 17.173958, 9.04643, 10.744506, 6.4294524], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.005663978, -0.11408753, 0.053074364, -0.105592996, 0.0159436, -0.0023734279, 0.09791148, 0.04028487, -0.10826043], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 102, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.35497722, 0.8375121, 0.56353915, 0.0, 0.0, 1.530274, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 4.0, 8.0, -0.105592996, 0.0159436, 5.0, 0.09791148, 0.04028487, -0.10826043], "split_indices": [0, 1, 1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.82968, 18.830719, 33.998962, 7.564071, 11.266648, 28.784454, 5.2145085, 21.091112, 7.6933417], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.01888305, 0.061473574, -0.02238784, 0.013374959, -0.15992792, -0.029111035, 0.020917213, -0.0, -0.08687563], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 103, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.51229155, 0.0, 0.27570665, 0.2660927, 0.23129553, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.061473574, 8.0, 4.0, 6.0, -0.029111035, 0.020917213, -0.0, -0.08687563], "split_indices": [0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.7508, 11.113697, 51.637104, 41.120705, 10.516399, 13.051185, 28.06952, 5.2458115, 5.270588], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.059909035, -0.099300705, 0.12493366, -0.17948401, 0.02260145, 0.18663585, 0.05561488, -0.018504681, -0.0905724, 0.09300611, 0.037597787, -0.027630279, 0.035699364], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 104, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.7318603, 0.30668765, 0.17948961, 0.15451473, 0.0, 0.108961344, 0.25483787, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 7.0, 5.0, 4.0, 0.02260145, 5.0, 7.0, -0.018504681, -0.0905724, 0.09300611, 0.037597787, -0.027630279, 0.035699364], "split_indices": [2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.247734, 19.192007, 49.05573, 13.526394, 5.6656127, 24.29509, 24.760641, 8.075993, 5.450402, 6.1767945, 18.118296, 6.8246975, 17.935944], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.020026702, -0.072740555, 0.07060159, 0.1511091, -0.061047625, 0.08840869, 0.01555239], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 105, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.7210688, 0.0, 1.0546744, 0.47611016, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.072740555, 8.0, 4.0, -0.061047625, 0.08840869, 0.01555239], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.3202, 7.6812496, 45.638954, 35.906063, 9.732889, 13.460282, 22.445782], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.04208872, -0.048451725, 0.09590381, -0.07762178, 0.010824468, 0.035149883, -0.07413789], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 106, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.3246601, 0.51449144, 0.0, 0.0, 0.86798114, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, 0.09590381, -0.07762178, 7.0, 0.035149883, -0.07413789], "split_indices": [2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.010197, 38.061172, 11.949023, 8.000551, 30.060621, 21.946493, 8.114128], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.023216058, -0.100459926, 0.091460824, -0.050908696, -0.19048385, 0.07891371, 0.02219008, -0.04031759, 0.04972404, 0.003743388, -0.10315426, 0.0928488, -0.03026511], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 107, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.55636024, 0.13498855, 0.2606802, 0.49829537, 0.42199564, 0.0, 0.73067856, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 7.0, 7.0, 4.0, 2.0, 0.07891371, 3.0, -0.04031759, 0.04972404, 0.003743388, -0.10315426, 0.0928488, -0.03026511], "split_indices": [1, 2, 1, 1, 1, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.66933, 36.84639, 23.82294, 25.491564, 11.354823, 5.6318307, 18.19111, 19.008377, 6.4831867, 5.001896, 6.3529277, 5.2449636, 12.946147], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.040421847, 0.058253918, -0.094949044, -0.042621292, -0.21145803, -0.06627629, 0.0013612045, -0.030413415, -0.07524121], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 108, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.754932, 0.0, 0.26043102, 0.31877035, 0.00061810017, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.058253918, 7.0, 2.0, 3.0, -0.06627629, 0.0013612045, -0.030413415, -0.07524121], "split_indices": [0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.349617, 9.986766, 47.36285, 34.21843, 13.14442, 6.830231, 27.388199, 5.0721035, 8.072316], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04303312, 0.07419301, -0.056348033, 0.0070203054, 0.13171253, 0.03533955, -0.024914607], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 109, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.4419537, 1.2981507, 0.0, 0.48843405, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, -0.056348033, 4.0, 0.13171253, 0.03533955, -0.024914607], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.07979, 53.91456, 6.165232, 46.623905, 7.2906513, 21.268105, 25.3558], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0050976877, 0.12717117, -0.08581775, -0.0, 0.19877875, 0.03106036, -0.06769875, 0.10056988, 0.02607615, 0.038786136, -0.04057355], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 110, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.60948783, 0.21265426, 0.5303096, 0.0, 0.15907341, 0.29918942, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [3.0, 4.0, 5.0, -0.0, 6.0, 7.0, -0.06769875, 0.10056988, 0.02607615, 0.038786136, -0.04057355], "split_indices": [1, 2, 2, 0, 2, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.56095, 22.735203, 29.825748, 8.6386, 14.0966015, 16.28121, 13.54454, 5.0478616, 9.04874, 10.753597, 5.5276117], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.010985059, 0.10393559, -0.1961658, 0.07285235, 0.031323552, -0.09485535, -0.022835819, -0.05733859, 0.05560955], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 111, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.89791274, 0.22214979, 0.17160785, 0.0, 0.66417336, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 8.0, 0.07285235, 6.0, -0.09485535, -0.022835819, -0.05733859, 0.05560955], "split_indices": [1, 2, 1, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.801533, 24.497461, 15.30407, 7.1321797, 17.365282, 6.419345, 8.884726, 6.740852, 10.62443], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.009816491, -0.065092064, 0.04797983, 0.10395061, -0.027819527, 0.0038562797, -0.0355706], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 112, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.4736272, 0.0, 1.1429949, 0.0, 0.15781966, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, -0.065092064, 2.0, 0.10395061, 7.0, 0.0038562797, -0.0355706], "split_indices": [1, 0, 1, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.472282, 6.7937765, 47.678505, 9.198769, 38.479736, 26.402529, 12.077207], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.057597168, 0.11791793, -0.0023792172, 0.23163691, -0.0534093, -0.080985196, 0.04679319, 0.022333285, 0.17337838, 0.0044816597, -0.051374752], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 113, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.2388611, 1.128183, 0.3986679, 1.1924545, 0.0, 0.2104749, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 7.0, 3.0, -0.0534093, 3.0, 0.04679319, 0.022333285, 0.17337838, 0.0044816597, -0.051374752], "split_indices": [1, 2, 2, 1, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.752308, 31.137383, 30.614923, 22.8951, 8.242284, 21.20776, 9.407163, 16.781284, 6.113815, 10.148997, 11.0587635], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0485235, 0.14974023, -0.010623424, 0.3282873, -0.045578327, -0.091757596, 0.032052364, 0.022531701, 0.14225611, -0.10974128, 0.09602901, -0.022003595, 0.03253633], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 114, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.45362306, 1.0085984, 0.62306833, 0.45804775, 1.7412125, 0.0, 0.34353414, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 6.0, 2.0, 1.0, 9.0, -0.091757596, 6.0, 0.022531701, 0.14225611, -0.10974128, 0.09602901, -0.022003595, 0.03253633], "split_indices": [1, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [72.18284, 26.681854, 45.50098, 13.8512125, 12.830642, 5.2635384, 40.237442, 5.8682103, 7.983002, 6.986017, 5.844624, 16.376705, 23.860739], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.01975473, -0.24538441, 0.10173245, -0.09173706, -0.036139216, 0.116235614, 0.017770872, -0.012161819, 0.07983102], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 115, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [1.1166267, 0.011259079, 0.9088903, 0.0, 0.0, 0.0, 0.49857, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 2.0, 4.0, -0.09173706, -0.036139216, 0.116235614, 9.0, -0.012161819, 0.07983102], "split_indices": [0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.885803, 11.022563, 38.86324, 5.9142904, 5.1082726, 7.7351203, 31.128119, 25.560415, 5.5677023], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.023848027, -0.096357405, 0.04478566, 0.03557536, -0.06596814, 0.056751512, -0.088390246], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 116, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.6829012, 0.65937006, 0.0, 1.0217062, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 4.0, 0.04478566, 6.0, -0.06596814, 0.056751512, -0.088390246], "split_indices": [2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.673702, 37.84502, 14.828685, 18.253468, 19.591553, 12.956656, 5.2968106], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.07575398, -0.051133662, 0.12013176, 0.25140908, 0.042934638, 0.03004132, 0.10933059, 0.033172697, -0.0075376234], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 117, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.6868095, 0.0, 0.49682468, 0.24084044, 0.17270967, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.051133662, 3.0, 7.0, 6.0, 0.03004132, 0.10933059, 0.033172697, -0.0075376234], "split_indices": [0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.44774, 8.4537525, 51.993984, 17.937805, 34.05618, 8.855949, 9.081856, 17.504284, 16.551897], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.040085476, 0.04705688, -0.10688334, -0.056083135, 0.07145015, 0.07775119, -0.22548723, 0.03842682, -0.048082087, -0.025133757, 0.08058379, -0.027224094, -0.13533553], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 118, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.33928674, 0.5148021, 0.7523167, 0.3382985, 0.0, 0.4502195, 0.5313256, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 3.0, 1.0, 0.07145015, 7.0, 8.0, 0.03842682, -0.048082087, -0.025133757, 0.08058379, -0.027224094, -0.13533553], "split_indices": [2, 1, 1, 2, 0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.39561, 23.515942, 31.87967, 15.3922205, 8.123722, 12.273859, 19.60581, 5.1318088, 10.260411, 6.6678295, 5.606029, 13.430755, 6.1750555], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.0145324655, 0.051299565, -0.08950459, -0.0007020218, 0.1001681, 0.026118541, -0.017099645], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 119, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.7049607, 0.86689025, 0.0, 0.242047, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, -0.08950459, 3.0, 0.1001681, 0.026118541, -0.017099645], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.098164, 55.707542, 5.39062, 47.593304, 8.114237, 17.691063, 29.90224], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.05155239, 0.16287456, -0.2644561, -0.0, 0.22724687, -0.03530744, -0.09966251, 0.12784544, 0.03074383], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 120, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [1.8288717, 0.42548227, 0.049619675, 0.0, 0.6000987, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 4.0, -0.0, 5.0, -0.03530744, -0.09966251, 0.12784544, 0.03074383], "split_indices": [1, 2, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.052315, 37.60575, 12.446565, 10.534717, 27.071033, 5.2617135, 7.1848507, 9.195109, 17.875925], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.08574432, -0.09408728, 0.007881552, -0.063674465, 0.04649618, 0.021417715, -0.060146116], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 121, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.90550303, 0.0, 0.33108914, 0.40045187, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [4.0, -0.09408728, 8.0, 7.0, 0.04649618, 0.021417715, -0.060146116], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.892345, 11.243832, 28.648514, 19.197649, 9.450864, 9.533271, 9.664379], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.019422658, 0.10964131, -0.17421, 0.040099464, 0.103425205, -0.07439442, -0.023063505, -0.022202607, 0.033086464], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 122, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.9646954, 0.5713254, 0.07306862, 0.2592399, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 6.0, 4.0, 0.103425205, -0.07439442, -0.023063505, -0.022202607, 0.033086464], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.498623, 37.142937, 16.355684, 29.896029, 7.24691, 7.8389955, 8.516689, 10.822454, 19.073574], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.025806848, -0.079148814, 0.19713333, -0.0, -0.20092261, 0.012527528, 0.10181475, -0.03643183, 0.111567125, -0.08232034, -0.03700924], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 123, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.78011346, 0.51853067, 0.2246092, 1.5616452, 0.047106206, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, 6.0, 3.0, 5.0, 0.012527528, 0.10181475, -0.03643183, 0.111567125, -0.08232034, -0.03700924], "split_indices": [1, 1, 2, 1, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.279564, 52.75147, 11.528096, 32.565914, 20.185555, 6.4640923, 5.064004, 25.05243, 7.5134864, 8.400051, 11.785503], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.038242478, -0.03253894, 0.06149989, -0.00056487706, 0.17366868, 0.01955673, -0.064596765, 0.011555713, 0.10513141], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 124, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.25550404, 0.0, 0.4724565, 0.62924564, 0.51765144, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.03253894, 7.0, 8.0, 6.0, 0.01955673, -0.064596765, 0.011555713, 0.10513141], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [73.30552, 8.941304, 64.36421, 41.503258, 22.860958, 31.9513, 9.551958, 13.966463, 8.894495], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.008685206, -0.01562131, 0.05056466, -0.044089958, 0.027626432, 0.028817298, 0.00038866905], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 125, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.21908666, 0.2733664, 0.0, 0.0, 0.052042276, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 3.0, 0.05056466, -0.044089958, 5.0, 0.028817298, 0.00038866905], "split_indices": [0, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.76917, 44.308952, 6.460217, 10.954299, 33.354652, 7.4769373, 25.877716], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.011889701, 0.15571864, -0.055729333, 0.12044702, -0.0, -0.09131821, 0.05550515, 0.08490705, -0.025977848], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 126, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.5210537, 0.6618852, 1.0209483, 0.0, 0.0, 0.0, 0.85326207, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 4.0, 3.0, 0.12044702, -0.0, -0.09131821, 6.0, 0.08490705, -0.025977848], "split_indices": [0, 1, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.655216, 16.255333, 34.399883, 5.7018366, 10.553497, 10.373165, 24.026718, 9.126282, 14.900437], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.01398292, 0.06905501, -0.020808645, 0.10121885, -0.16818, 0.011856715, 0.082631655, -0.024101187, -0.059792057], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 127, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.5089378, 0.0, 1.0281616, 0.28875843, 0.027242303, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.06905501, 6.0, 9.0, 3.0, 0.011856715, 0.082631655, -0.024101187, -0.059792057], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.240044, 8.386037, 54.854008, 29.807335, 25.04667, 23.34206, 6.4652753, 8.330602, 16.71607], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0026490232, -0.060767006, 0.08394241, 0.049877513, -0.13274905, 0.05232287, -0.020105332, -0.01807768, -0.06434622], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 128, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.29307783, 0.52559394, 0.369227, 0.0, 0.10441685, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 6.0, 0.049877513, 3.0, 0.05232287, -0.020105332, -0.01807768, -0.06434622], "split_indices": [1, 1, 2, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.410767, 30.017134, 24.393633, 6.576486, 23.440647, 15.569977, 8.823656, 13.99221, 9.448437], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.038392812, -0.0012252397, 0.06087495, -0.05102645, 0.0786, 0.015029264, -0.14205295, 0.118070155, -0.050450478], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 129, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.4506745, 0.2147441, 0.0, 1.5378206, 1.662975, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 7.0, 0.06087495, 6.0, 3.0, 0.015029264, -0.14205295, 0.118070155, -0.050450478], "split_indices": [0, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.00945, 52.703056, 12.306398, 33.448673, 19.254381, 27.50896, 5.9397135, 8.375066, 10.879316], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.034274112, 0.026659412, -0.06274652, -0.14888798, 0.005351258, -0.062498648, 0.00343762, 0.044461675, -0.03614738], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 130, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.1914646, 0.0, 0.27861235, 0.21758169, 0.4772589, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.026659412, 4.0, 8.0, 6.0, -0.062498648, 0.00343762, 0.044461675, -0.03614738], "split_indices": [1, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.76421, 8.851137, 43.913074, 19.512495, 24.40058, 14.472078, 5.040417, 11.7168, 12.683779], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.09774212, 0.08212056, 0.03701018, 0.06833553, 0.035707537, -0.0138563225], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 131, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [1.0871342, 0.0, 0.17887503, 0.19401103, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.09774212, 8.0, 7.0, 0.06833553, 0.035707537, -0.0138563225], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.709217, 7.1745205, 31.534697, 25.559763, 5.9749327, 13.259815, 12.299948], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.019546943, -0.17705311, 0.07389298, -0.14260623, 0.055414196, 0.15096341, -0.118589714, 0.006071759, 0.0711026, -0.0014418485, -0.057198998], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 132, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.60307306, 1.4187253, 0.69050413, 0.0, 0.0, 0.34102076, 0.08688632, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 5.0, 7.0, -0.14260623, 0.055414196, 6.0, 6.0, 0.006071759, 0.0711026, -0.0014418485, -0.057198998], "split_indices": [2, 1, 1, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.185104, 11.063671, 44.121433, 6.043142, 5.020529, 32.03128, 12.090154, 13.666239, 18.36504, 5.640142, 6.4500113], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.12075202, 0.1931045, 0.040757984, 0.07837597, 0.13423237, -0.023542715, 0.16583857, 0.0456319, -0.025506012, 0.035101976, -0.025583258, -0.03734279, 0.12647776], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 133, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.3386069, 0.86451006, 0.2777016, 0.30841953, 0.0, 0.19477259, 0.94423354, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 7.0, 2.0, 0.13423237, 6.0, 7.0, 0.0456319, -0.025506012, 0.035101976, -0.025583258, -0.03734279, 0.12647776], "split_indices": [1, 1, 2, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.904373, 31.630945, 31.273428, 22.970528, 8.660417, 20.663269, 10.610158, 16.297565, 6.6729627, 5.538602, 15.124667, 5.0266004, 5.5835576], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.021582445, -0.07667203, 0.069918066, 0.02349813, -0.095633395, -0.07989068, 0.10990256, -0.021481553, 0.025432568, -0.063337296, 0.0172948], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 134, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.37609693, 1.1764591, 1.2760123, 0.2047859, 0.0, 0.3671881, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [7.0, 7.0, 6.0, 2.0, -0.095633395, 9.0, 0.10990256, -0.021481553, 0.025432568, -0.063337296, 0.0172948], "split_indices": [0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [72.41419, 45.98376, 26.430431, 32.87273, 13.111032, 17.818382, 8.612049, 12.17555, 20.69718, 9.216515, 8.601867], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0029471884, -0.033295557, 0.06363672, -0.047376364, 0.008534146, 0.02341092, -0.027627362], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 135, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.42061004, 0.25056338, 0.0, 0.0, 0.24353155, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 3.0, 0.06363672, -0.047376364, 7.0, 0.02341092, -0.027627362], "split_indices": [0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.19103, 43.940445, 7.250585, 10.945422, 32.99502, 20.23613, 12.758891], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.04337747, -0.008092532, 0.13739873, 0.09954039, -0.06799616, 0.05433539, 0.014406433, 0.112989925, -0.052429568, -0.09120069, 0.03734732], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 136, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.28268933, 0.23657835, 0.048030972, 1.0534514, 1.1644161, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 5.0, 3.0, 3.0, 0.05433539, 0.014406433, 0.112989925, -0.052429568, -0.09120069, 0.03734732], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.513863, 35.131943, 19.381918, 11.728151, 23.403791, 11.5747795, 7.8071384, 5.830214, 5.897937, 10.486547, 12.917245], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.027012108, -0.0, -0.06673789, 0.1466375, -0.07995607, 0.005630455, 0.095476344, -0.041060917, 0.025049824], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 137, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.34257108, 0.66380477, 0.0, 0.40225413, 0.35258377, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 3.0, -0.06673789, 7.0, 9.0, 0.005630455, 0.095476344, -0.041060917, 0.025049824], "split_indices": [0, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.074387, 54.236897, 6.83749, 19.138464, 35.098434, 11.936675, 7.201789, 26.608162, 8.4902725], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.046628486, 0.1685016, -0.1314137, 0.017599082, 0.08211617, 0.06658152, -0.24821582, -0.12523672, -0.039780393], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 138, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [1.0564141, 0.13783875, 1.7793884, 0.0, 0.0, 0.0, 0.51648057, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [2.0, 5.0, 2.0, 0.017599082, 0.08211617, 0.06658152, 5.0, -0.12523672, -0.039780393], "split_indices": [2, 0, 0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.907238, 15.192258, 40.714977, 8.660013, 6.5322447, 9.678654, 31.036325, 11.186096, 19.85023], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0022294163, 0.08804826, -0.10288955, -0.0111412825, 0.117816776, -0.1770752, 0.041830156, 0.027633697, -0.05795946, -0.029172383, -0.08741515], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 139, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.59098816, 1.060622, 0.5890678, 0.5192753, 0.0, 0.15758973, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 8.0, 2.0, 0.117816776, 6.0, 0.041830156, 0.027633697, -0.05795946, -0.029172383, -0.08741515], "split_indices": [1, 1, 2, 1, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.873837, 32.68866, 30.185177, 25.13948, 7.549177, 23.632256, 6.552922, 16.006283, 9.133199, 15.519113, 8.113142], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.072758965, 0.13232432, -0.090136275, 0.18437168, -0.03255055, 0.0055381507, 0.08139358], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 140, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [1.1489823, 0.5867046, 0.0, 0.5053582, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, -0.090136275, 3.0, -0.03255055, 0.0055381507, 0.08139358], "split_indices": [1, 2, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.652668, 43.60641, 6.0462556, 36.3912, 7.21521, 13.325059, 23.066141], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0008722699, -0.071597345, 0.17297944, 0.02374851, -0.081476435, 0.1075034, -0.008285654, -0.03338513, 0.03530105], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 141, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.53709954, 0.5905165, 0.5236182, 0.2729945, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [8.0, 7.0, 9.0, 3.0, -0.081476435, 0.1075034, -0.008285654, -0.03338513, 0.03530105], "split_indices": [2, 0, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.25675, 28.33051, 11.92624, 19.442501, 8.888008, 6.048854, 5.8773856, 7.4631157, 11.979385], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0027477574, 0.110583216, -0.110697135, 0.0707585, -0.0, -0.13946958, -0.0018404832, 0.040828653, -0.027025387, -0.029421568, 0.07536206], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 142, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.70859665, 0.3943207, 1.1131632, 0.0, 0.20192707, 0.0, 0.59974873, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 5.0, 2.0, 0.0707585, 2.0, -0.13946958, 9.0, 0.040828653, -0.027025387, -0.029421568, 0.07536206], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.918713, 26.81556, 29.103155, 12.20255, 14.61301, 5.856782, 23.246372, 5.448557, 9.164453, 17.571663, 5.6747103], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.052709606, 0.07166351, -0.08654516, 0.05522232, -0.0062365597, 0.00013924488, -0.118569784, -0.042003028, 0.052864973, -0.09103878, -0.023430608], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 143, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.29110318, 0.16895965, 0.16184583, 0.0, 0.0, 0.391837, 0.23965198, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.0, 5.0, 2.0, 0.05522232, -0.0062365597, 1.0, 3.0, -0.042003028, 0.052864973, -0.09103878, -0.023430608], "split_indices": [0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.34723, 13.318282, 53.028946, 6.0512652, 7.267017, 13.732039, 39.296906, 7.4439263, 6.2881126, 5.445775, 33.85113], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.02357667, 0.06252479, -0.061502587, 0.037121538, 0.15109311, 0.02153939, -0.057005577, 0.06452224, 0.015341628], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 144, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.64079237, 0.116806835, 0.0, 0.40340346, 0.044166, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 8.0, -0.061502587, 7.0, 4.0, 0.02153939, -0.057005577, 0.06452224, 0.015341628], "split_indices": [1, 2, 0, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [71.13581, 61.77419, 9.361617, 49.985504, 11.788687, 44.34042, 5.645084, 5.875553, 5.913134], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.043259837, -0.062380236, 0.08851204, 0.035528272, 0.068387285, 0.027763136, -0.056168128], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 145, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.5964328, 0.0, 0.29815897, 0.44382557, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.062380236, 8.0, 7.0, 0.068387285, 0.027763136, -0.056168128], "split_indices": [2, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.90166, 6.8839836, 44.017677, 33.326744, 10.69093, 27.357826, 5.9689198], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0010848806, -0.12563743, 0.19508968, -0.18597232, 0.03142727, 0.30621865, -0.016166039, -0.03286715, -0.10274839, 0.04561688, 0.14974572], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 146, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [1.3161305, 0.47111064, 0.6324554, 0.22810727, 0.0, 0.31073833, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [7.0, 8.0, 9.0, 5.0, 0.03142727, 5.0, -0.016166039, -0.03286715, -0.10274839, 0.04561688, 0.14974572], "split_indices": [1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.25497, 30.905022, 20.34995, 24.98809, 5.91693, 14.188669, 6.1612816, 18.362322, 6.625768, 9.168823, 5.019846], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.022664951, -0.1851873, 0.0404356, -0.085331485, -0.011401897, -0.00831123, 0.068893805, 0.04704191, -0.031703204], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 147, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.6363747, 0.21080267, 0.4255715, 0.0, 0.0, 0.5767274, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [2.0, 7.0, 8.0, -0.085331485, -0.011401897, 5.0, 0.068893805, 0.04704191, -0.031703204], "split_indices": [1, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.91189, 16.37815, 42.533737, 8.819046, 7.559105, 34.160442, 8.373294, 12.064098, 22.096346], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0032570267, -0.091208465, 0.116436884, 0.08169289, -0.23092449, 0.2917079, -0.041549783, -0.0065888525, 0.05777345, -0.022963172, -0.11551307, 0.033935472, 0.12279572, -0.0, -0.021233644], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 148, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.61586934, 0.8629018, 0.71310174, 0.20241177, 0.38984883, 0.15345049, 0.015938628, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 7.0, 2.0, 3.0, 5.0, 3.0, -0.0065888525, 0.05777345, -0.022963172, -0.11551307, 0.033935472, 0.12279572, -0.0, -0.021233644], "split_indices": [0, 1, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.733227, 33.321754, 23.411472, 14.757996, 18.563757, 10.997434, 12.41404, 7.576019, 7.181977, 10.345853, 8.217904, 5.483274, 5.514159, 5.6098022, 6.804237], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.04301681, 0.13467637, -0.11490264, 0.06077977, 0.062495735, -0.08892141, -0.060357388, 0.032725763, -0.0, -0.02677445, 0.019937702], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 149, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.8418611, 0.062400937, 0.41283935, 0.033391483, 0.0, 0.0, 0.14708494, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [2.0, 5.0, 4.0, 3.0, 0.062495735, -0.08892141, 9.0, 0.032725763, -0.0, -0.02677445, 0.019937702], "split_indices": [1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.849567, 17.738226, 46.111343, 10.442735, 7.2954907, 9.207034, 36.90431, 5.358382, 5.084353, 30.940386, 5.9639235], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.036971785, -0.0024689739, -0.08231439, -0.05100446, 0.11738308, -0.062807836, 0.003281711, -0.0037787196, 0.06961735], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 150, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.4092362, 0.2786882, 0.0, 0.37006152, 0.2268404, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, -0.08231439, 4.0, 6.0, -0.062807836, 0.003281711, -0.0037787196, 0.06961735], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.4224, 47.020508, 5.401896, 34.4702, 12.550308, 9.477091, 24.993107, 5.891116, 6.659192], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.040327307, -0.06548289, 0.17135437, -0.08881755, 0.027259974, 0.117884725, 0.03151067, 0.03279348, -0.0123836715, -0.04517276, 0.066644214], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 151, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.579018, 0.5168762, 0.5232537, 0.0, 0.10511011, 0.0, 0.48495516, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 4.0, -0.08881755, 4.0, 0.117884725, 8.0, 0.03279348, -0.0123836715, -0.04517276, 0.066644214], "split_indices": [0, 2, 2, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.191143, 21.536495, 17.654646, 5.858127, 15.678369, 5.8537936, 11.800853, 7.529061, 8.149307, 5.874966, 5.9258866], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.068134725, 0.06892957, -0.13030867, -0.05059337, -0.08901729, -0.08790715, 0.0019460671], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 152, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [1.0274439, 0.0, 0.5593839, 0.4824686, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.06892957, 7.0, 4.0, -0.08901729, -0.08790715, 0.0019460671], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.393158, 8.476238, 44.91692, 31.657484, 13.259434, 5.579345, 26.078138], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.021604346, -0.0680187, 0.05544327, 0.08461089, 0.025205744, -0.02189106, 0.02831016], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 153, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.5322563, 0.0, 0.35530013, 0.0, 0.3628547, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, -0.0680187, 2.0, 0.08461089, 6.0, -0.02189106, 0.02831016], "split_indices": [2, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.881123, 6.502905, 56.37822, 5.267828, 51.11039, 20.503845, 30.606546], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.03756634, 0.05940915, -0.07125225, -0.14391583, 0.036454745, -0.054928664, -0.0, 0.02708135, -0.00580356], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 154, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.5985413, 0.0, 0.5398329, 0.2305314, 0.0905426, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.05940915, 6.0, 8.0, 6.0, -0.054928664, -0.0, 0.02708135, -0.00580356], "split_indices": [2, 0, 1, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [74.23778, 8.305109, 65.93267, 39.724884, 26.207787, 31.037243, 8.687643, 13.938811, 12.268974], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.038169887, -0.075320125, 0.04440085, -0.068541795, -0.01904346, 0.005241744, -0.056787763], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 155, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.38041466, 0.3716369, 0.0, 0.0, 0.2416199, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 3.0, 0.04440085, -0.068541795, 9.0, 0.005241744, -0.056787763], "split_indices": [0, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.441257, 45.495354, 7.9459047, 10.925558, 34.569794, 28.71981, 5.8499837], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0091520725, -0.104380235, 0.11772187, 0.013627676, -0.14779615, 0.08722336, -0.0045931246, -0.055021066, -0.0, 0.022126336, -0.039447427], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 156, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.65874815, 0.22252989, 0.5168812, 0.0, 0.1151557, 0.0, 0.1559069, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 7.0, 0.013627676, 4.0, 0.08722336, 6.0, -0.055021066, -0.0, 0.022126336, -0.039447427], "split_indices": [0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.61661, 30.608315, 22.008299, 6.3495874, 24.258726, 8.883106, 13.125194, 18.826458, 5.432268, 7.88668, 5.238513], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.084758565, -0.01686479, 0.1656583, -0.12523146, 0.049608, 0.1088669, 0.10846549, 0.053115625, -0.11569595, -0.004879409, 0.07394895], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 157, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.5141875, 0.5523947, 0.30002713, 1.500192, 0.0, 0.0, 0.5156803, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 6.0, 3.0, 0.049608, 0.1088669, 6.0, 0.053115625, -0.11569595, -0.004879409, 0.07394895], "split_indices": [0, 2, 0, 2, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.393635, 26.128004, 33.26563, 16.924665, 9.203339, 5.883317, 27.382313, 7.8032327, 9.121433, 14.4670315, 12.915281], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.041028727, 0.03543148, -0.10075434, -0.052423485, 0.19062862, 0.0115850195, -0.078793734, 0.113767914, 0.014691228], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 158, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1.2631801, 0.6190379, 0.0, 0.5731725, 0.3769384, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, -0.10075434, 3.0, 3.0, 0.0115850195, -0.078793734, 0.113767914, 0.014691228], "split_indices": [1, 1, 0, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.176334, 42.654644, 10.521691, 27.247871, 15.406773, 19.251368, 7.996503, 5.5863457, 9.820427], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.06668603, -0.06511195, 0.13668996, 0.17145747, -0.036229536, 0.021975458, 0.10411604], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 159, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [1.23595, 0.0, 0.47274625, 0.7042297, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.06511195, 9.0, 7.0, -0.036229536, 0.021975458, 0.10411604], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.137966, 11.203066, 48.9349, 43.749306, 5.1855927, 29.331604, 14.417704], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0005890793, -0.081223816, 0.056568284, 0.11529154, -0.09208925, 0.01890195, 0.08538086, -0.004862992, -0.043242943], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 160, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.80019194, 0.0, 0.40697354, 0.24193853, 0.032403745, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.081223816, 6.0, 5.0, 5.0, 0.01890195, 0.08538086, -0.004862992, -0.043242943], "split_indices": [1, 0, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.083645, 7.945983, 44.13766, 32.28246, 11.855203, 26.165155, 6.1173024, 6.138934, 5.716269], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0190341, 0.08805141, -0.084391065, 0.24854068, -0.037854586, -0.09481351, 0.025675116, 0.0048046205, 0.1213015, -0.033218242, 0.00030552002], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 161, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.304734, 0.5562278, 0.702935, 0.38330692, 0.05385591, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 8.0, 2.0, 2.0, -0.09481351, 0.025675116, 0.0048046205, 0.1213015, -0.033218242, 0.00030552002], "split_indices": [1, 2, 1, 2, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [40.59876, 25.018818, 15.57994, 10.919819, 14.098999, 6.4789352, 9.101005, 5.081068, 5.8387513, 5.156483, 8.942515], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.044676565, -0.044922486, 0.0700137, 0.13008246, -0.014178642, 0.0147626875, 0.119024105, -0.034281727, 0.033120707], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 162, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.28358796, 0.0, 0.2744814, 0.6026179, 0.2836157, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.044922486, 5.0, 4.0, 8.0, 0.0147626875, 0.119024105, -0.034281727, 0.033120707], "split_indices": [0, 0, 1, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.05985, 5.442377, 50.61747, 29.915987, 20.701483, 24.151043, 5.7649436, 11.969835, 8.731648], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.051376693, -0.04163516, 0.107064545, 0.034180257, -0.11784243, 0.059129335, 0.06778407, -0.09712683, 0.0053127427, -0.0073115528, 0.066855796], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 163, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.34238607, 0.2984374, 0.1050483, 0.0, 0.51187027, 0.0, 0.46575826, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [3.0, 3.0, 4.0, 0.034180257, 6.0, 0.059129335, 7.0, -0.09712683, 0.0053127427, -0.0073115528, 0.066855796], "split_indices": [1, 2, 1, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.9786, 23.007412, 39.971184, 6.970527, 16.036884, 10.147956, 29.823229, 6.1078563, 9.9290285, 18.830425, 10.992804], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.045490604, -0.10593225, 0.12360433, -0.2870278, -0.029120823, 0.12914295, -0.07596047, -0.026823966, -0.11869723, 0.01794487, -0.03524788, -0.106634684, 0.05828649], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 164, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.7716945, 0.73595345, 1.2694616, 0.26199794, 0.3324334, 0.0, 1.0322515, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 3.0, 4.0, 1.0, 6.0, 0.12914295, 7.0, -0.026823966, -0.11869723, 0.01794487, -0.03524788, -0.106634684, 0.05828649], "split_indices": [0, 2, 2, 2, 2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [73.39726, 54.781105, 18.616156, 15.112419, 39.668686, 7.077322, 11.538835, 6.3338313, 8.778587, 19.368212, 20.300476, 5.6993694, 5.8394647], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.042605318, 0.035105325, -0.13374893, 0.0611559, -0.017299244, -0.07623416, -0.057332102, -0.055377785, 0.046801526, -0.04278328, 0.0069168685], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 165, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.38854632, 0.27598998, 0.185161, 0.0, 0.6802116, 0.0, 0.13283348, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 7.0, 0.0611559, 8.0, -0.07623416, 5.0, -0.055377785, 0.046801526, -0.04278328, 0.0069168685], "split_indices": [0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.80187, 27.701096, 24.10077, 6.331455, 21.36964, 7.9479313, 16.15284, 11.133848, 10.235792, 8.066037, 8.086803], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.08959286, -0.012136292, -0.17396264, -0.07866633, 0.036201004, 0.028737385, -0.09034464, -0.05501621, 0.020478], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 166, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.32072023, 0.26093337, 0.88360983, 0.33765602, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 3.0, 6.0, 0.036201004, 0.028737385, -0.09034464, -0.05501621, 0.020478], "split_indices": [1, 1, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.24817, 27.933046, 23.315125, 19.383442, 8.549603, 7.297714, 16.017408, 11.569672, 7.8137717], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0054496177, -0.08095077, 0.083966345, 0.004898712, -0.11271197, 0.17739691, -0.0, -0.015015639, 0.015978035, -0.0, 0.11199642, -0.033002105, 0.047998406], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 167, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.44349915, 0.9552565, 0.22633508, 0.07943892, 0.0, 0.50308883, 0.3132514, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 4.0, 4.0, -0.11271197, 2.0, 8.0, -0.015015639, 0.015978035, -0.0, 0.11199642, -0.033002105, 0.047998406], "split_indices": [2, 2, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.731606, 35.212162, 28.519444, 27.768522, 7.44364, 12.8055935, 15.71385, 12.014393, 15.754128, 7.0785084, 5.727085, 9.387162, 6.3266873], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0026402401, 0.029306583, -0.061210632, -0.061809912, 0.13749602, -0.04257707, 0.021980986, 0.078967914, -0.029824829], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 168, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.37126538, 0.48040846, 0.0, 0.29600513, 0.7081783, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, -0.061210632, 5.0, 8.0, -0.04257707, 0.021980986, 0.078967914, -0.029824829], "split_indices": [0, 2, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.059116, 46.103737, 6.9553766, 24.757504, 21.346235, 15.973514, 8.78399, 14.148558, 7.197678], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.028270828, 0.05691356, -0.052134667, -0.19452915, 0.010716851, -0.09454655, -0.030380918, 0.014987989, -0.033283822], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 169, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.32400936, 0.0, 0.5396261, 0.13234335, 0.19378975, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.05691356, 5.0, 4.0, 8.0, -0.09454655, -0.030380918, 0.014987989, -0.033283822], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.0301, 5.021484, 57.008617, 17.303143, 39.705475, 6.0396137, 11.263528, 31.088873, 8.616603], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.06800456, -0.04278332, 0.03819547, -0.08608691, -0.09285881, -0.009593135], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 170, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.5101874, 0.0, 0.34729594, 0.0, 0.41425782, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.06800456, 2.0, 0.03819547, 4.0, -0.09285881, -0.009593135], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.502583, 7.282338, 45.220245, 8.31369, 36.906555, 5.9145136, 30.99204], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.08465314, -0.23003498, 0.07349282, -0.13615799, -0.08377209, 0.078203775, -0.03929787, -0.042284653, -0.0, 0.045315742, -0.08502137], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 171, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.96566135, 0.6191778, 0.45183903, 0.0, 0.08290563, 0.0, 0.6569818, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 4.0, -0.13615799, 8.0, 0.078203775, 8.0, -0.042284653, -0.0, 0.045315742, -0.08502137], "split_indices": [0, 2, 2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.58453, 20.717194, 18.867334, 7.086316, 13.6308775, 6.9344325, 11.932902, 8.341405, 5.2894726, 6.6887064, 5.244196], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.022268422, -0.13696948, 0.087436184, -0.03700493, 0.15892117, 0.017543428, -0.029322013, 0.06922937, -0.043806225], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 172, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [1.7405081, 0.0, 0.46315813, 0.11725105, 0.75370044, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.13696948, 4.0, 4.0, 8.0, 0.017543428, -0.029322013, 0.06922937, -0.043806225], "split_indices": [1, 0, 2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.681095, 5.6471353, 49.03396, 17.555965, 31.477993, 6.1997957, 11.356171, 25.938213, 5.539781], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0232931, 0.032229967, -0.20131369, 0.041996352, -0.009416562, -0.021738246, -0.082034044, -0.13882414, 0.025321392], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 173, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.6548917, 0.2348749, 0.092033386, 0.0, 1.5955125, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 5.0, 0.041996352, 5.0, -0.021738246, -0.082034044, -0.13882414, 0.025321392], "split_indices": [0, 2, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.889027, 48.13375, 14.755276, 13.428402, 34.70535, 6.5125833, 8.242693, 5.459494, 29.245855], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.018736819, 0.13390437, -0.048387237, -0.021875914, 0.20589934, 0.050836787, -0.18930747, 0.038503923, 0.080008104, -0.006764208, 0.052947603, -0.02772765, -0.08687034], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 174, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5636006, 0.42951238, 0.65294087, 0.0, 0.023422658, 0.26639834, 0.12181479, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 1.0, 6.0, -0.021875914, 5.0, 8.0, 8.0, 0.038503923, 0.080008104, -0.006764208, 0.052947603, -0.02772765, -0.08687034], "split_indices": [0, 0, 2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [69.9616, 25.94374, 44.01786, 6.337065, 19.606676, 25.789045, 18.228817, 10.70578, 8.900897, 16.296453, 9.492591, 10.755241, 7.4735756], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.014280156, -0.029694654, 0.036585826, 0.1854814, -0.015895583, 0.00956621, 0.09696113, -0.052269023, 0.018136894], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 175, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.12981877, 0.0, 0.37361136, 0.21589163, 0.43163735, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.029694654, 4.0, 3.0, 6.0, 0.00956621, 0.09696113, -0.052269023, 0.018136894], "split_indices": [2, 0, 2, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.421413, 7.1573863, 44.264027, 11.371898, 32.892128, 6.301652, 5.070246, 10.71003, 22.182098], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.029588431, 0.12292664, -0.1120658, 0.011711996, 0.053064004, -0.17099275, 0.025668833, -0.0, -0.063754246], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 176, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.6665128, 0.05430171, 0.4270789, 0.0, 0.0, 0.1842395, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 2.0, 9.0, 0.011711996, 0.053064004, 2.0, 0.025668833, -0.0, -0.063754246], "split_indices": [2, 2, 2, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.977173, 17.294233, 33.68294, 8.169436, 9.124796, 26.440077, 7.242865, 5.595584, 20.844492], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.01286022, 0.044580422, -0.042305, -0.15288332, 0.025649438, -0.07422196, 0.01355876, 0.052658956, -0.029673574], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 177, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.28230143, 0.0, 0.40960985, 0.4172661, 0.6352628, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.044580422, 4.0, 7.0, 7.0, -0.07422196, 0.01355876, 0.052658956, -0.029673574], "split_indices": [1, 0, 1, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.292877, 7.99716, 51.295715, 19.608965, 31.686752, 13.447602, 6.161362, 14.560379, 17.126373], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.034233406, -0.10132579, 0.061935343, 0.032339107, -0.16634603, 0.16873652, -0.025546204, -0.06284454, -0.0, 0.023900118, 0.06303553], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 178, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.36692396, 0.48007414, 0.37976128, 0.0, 0.17457539, 0.005501747, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 1.0, 8.0, 0.032339107, 7.0, 7.0, -0.025546204, -0.06284454, -0.0, 0.023900118, 0.06303553], "split_indices": [1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.284992, 32.55743, 21.727562, 7.201041, 25.35639, 12.89629, 8.831272, 19.597752, 5.758639, 5.7854667, 7.1108236], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.01232918, -0.07994863, 0.09916277, 0.08487943, -0.17455713, -0.0012186293, 0.07229916, -0.071133316, -0.016206834, -0.020012602, 0.011880317], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 179, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.48080286, 1.4226036, 0.3611908, 0.0, 0.20549405, 0.043952398, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [7.0, 1.0, 6.0, 0.08487943, 7.0, 2.0, 0.07229916, -0.071133316, -0.016206834, -0.020012602, 0.011880317], "split_indices": [2, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.978493, 39.31434, 22.664152, 7.499965, 31.814373, 13.439141, 9.225011, 19.76177, 12.052603, 5.857343, 7.581798], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.019831741, 0.0701291, -0.13837057, 0.023120856, 0.07704457, -0.087487794, 0.01183124, 0.03657799, -0.03287153], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 180, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.39737907, 0.3062293, 0.35872412, 0.44058052, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 8.0, 7.0, 0.07704457, -0.087487794, 0.01183124, 0.03657799, -0.03287153], "split_indices": [1, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.577927, 37.761276, 10.81665, 31.491188, 6.270088, 5.7676845, 5.0489664, 18.528915, 12.962273], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.035051797, 0.03043606, -0.1542117, -0.04750213, 0.11821905, -0.009015655, -0.06988216, 0.011535435, -0.044618778, -0.0, 0.0589724], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 181, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.3175685, 0.18471465, 0.10710508, 0.13358888, 0.10435133, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 6.0, 6.0, 3.0, 3.0, -0.009015655, -0.06988216, 0.011535435, -0.044618778, -0.0, 0.0589724], "split_indices": [1, 2, 2, 2, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.50789, 24.144135, 13.363755, 12.428859, 11.715276, 6.2147183, 7.149037, 6.522645, 5.906214, 5.4159713, 6.2993045], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.078769416, -0.0, 0.22497922, -0.069182135, 0.056017973, 0.31764394, 0.008134594, 0.010149408, -0.05968519, 0.037213776, 0.12932856], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 182, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.6597136, 0.47392, 0.32038057, 0.39473325, 0.0, 0.17793465, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 8.0, 6.0, 5.0, 0.056017973, 8.0, 0.008134594, 0.010149408, -0.05968519, 0.037213776, 0.12932856], "split_indices": [1, 2, 2, 2, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.39114, 35.567333, 18.823805, 26.789738, 8.7775955, 12.06343, 6.7603745, 14.916966, 11.872772, 5.5564647, 6.5069647], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0817599, 0.07209382, -0.15760878, 0.1193298, -0.010988498, -0.11204404, -0.08783317, 0.012612869, 0.051437337, -0.07936108, -0.017495126], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 183, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.774449, 0.12357589, 0.1980561, 0.035639435, 0.0, 0.2400485, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [3.0, 7.0, 8.0, 4.0, -0.010988498, 4.0, -0.08783317, 0.012612869, 0.051437337, -0.07936108, -0.017495126], "split_indices": [0, 2, 0, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.657974, 20.601164, 43.05681, 14.934747, 5.666417, 34.122868, 8.933943, 7.5160522, 7.4186945, 7.382936, 26.739931], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.042862426, 0.101638295, -0.086936526, 0.19517197, -0.025753248, -0.06820726, 0.056842417, -0.0, 0.08818123, 0.020015031, -0.024871884], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 184, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5633258, 0.62829983, 0.9241508, 0.5589626, 0.12328512, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 7.0, 2.0, 2.0, -0.06820726, 0.056842417, -0.0, 0.08818123, 0.020015031, -0.024871884], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [71.488686, 49.88639, 21.602295, 28.911703, 20.974688, 14.682577, 6.9197187, 10.254554, 18.657148, 7.279648, 13.69504], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.09827494, -0.09319121, -0.03658103, -0.121974185, 0.06280882, 0.0077542174, -0.07154962, 0.042077955, -0.009736284], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 185, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.6442367, 0.0, 0.37281963, 0.43476063, 0.1605328, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.09319121, 6.0, 3.0, 8.0, 0.0077542174, -0.07154962, 0.042077955, -0.009736284], "split_indices": [0, 0, 2, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.69043, 10.362983, 41.327446, 22.647884, 18.679562, 9.947986, 12.6999, 10.62146, 8.058102], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.01699925, -0.08077854, 0.082736, 0.02772002, -0.1376721, 0.11657697, -0.0, -0.08442507, 0.0022348755, 0.012932201, 0.05303169], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 186, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.3419459, 0.34518653, 0.063850015, 0.0, 0.56956226, 0.03672947, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [7.0, 1.0, 9.0, 0.02772002, 5.0, 5.0, -0.0, -0.08442507, 0.0022348755, 0.012932201, 0.05303169], "split_indices": [1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.736374, 32.283962, 19.452415, 7.394388, 24.889572, 13.999925, 5.45249, 12.378457, 12.511115, 7.825782, 6.174143], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.08461016, 0.037205413, 0.2331984, -0.022874447, 0.101125084, 0.015918847, 0.14339112, 0.0230188, -0.05338602], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 187, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.4294806, 0.9718481, 0.5949927, 0.70013946, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [8.0, 9.0, 5.0, 5.0, 0.101125084, 0.015918847, 0.14339112, 0.0230188, -0.05338602], "split_indices": [0, 1, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.04362, 50.76973, 14.27389, 42.787594, 7.982137, 9.158893, 5.114997, 25.9688, 16.818792], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.07289656, 0.008309327, -0.23518157, -0.07274068, 0.12099726, -0.13153507, -0.11428106, 0.006983858, -0.041340515, 0.07799738, -0.020871786, -0.06213029, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 188, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.7310915, 0.34501624, 0.34491026, 0.14717937, 0.46281266, 0.0, 0.16017997, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 6.0, 7.0, 2.0, 8.0, -0.13153507, 5.0, 0.006983858, -0.041340515, 0.07799738, -0.020871786, -0.06213029, -0.0], "split_indices": [2, 1, 2, 2, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.606266, 35.28339, 17.322878, 20.212662, 15.070728, 5.1725926, 12.150285, 7.802511, 12.410151, 8.807414, 6.2633142, 6.7388325, 5.411452], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.06156832, 0.05054273, -0.113173954, 0.057198763, -0.04318738, -0.10357946, -0.062514834, -0.035193145, 0.048101455], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 189, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.3670593, 0.5615414, 0.44258583, 0.0, 0.0, 0.0, 0.46421456, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [2.0, 6.0, 2.0, 0.057198763, -0.04318738, -0.10357946, 9.0, -0.035193145, 0.048101455], "split_indices": [1, 0, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.372776, 18.425968, 41.946808, 11.020549, 7.40542, 6.119832, 35.826977, 29.496603, 6.3303733], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04334418, 0.054017257, 0.014319088, 0.0850515, -0.043287627, 0.0001746777, 0.06624437, -0.061360296, 0.0070711276], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 190, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.192144, 0.0, 0.19614144, 0.2283916, 0.29621342, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.054017257, 3.0, 2.0, 5.0, 0.0001746777, 0.06624437, -0.061360296, 0.0070711276], "split_indices": [0, 0, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.970158, 7.7618175, 45.20834, 20.857702, 24.35064, 13.930976, 6.9267263, 6.9720607, 17.378578], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.04444833, 0.0047473684, -0.13562813, 0.045824166, -0.07589958, -0.08074562, -0.0035182028, -0.042839117, -0.005537193], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 191, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.18097864, 0.31136778, 0.19727308, 0.0, 0.04299555, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 8.0, 0.045824166, 2.0, -0.08074562, -0.0035182028, -0.042839117, -0.005537193], "split_indices": [1, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.284794, 23.47185, 12.8129425, 8.400873, 15.070976, 5.214272, 7.5986705, 5.4717526, 9.599224], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.05549624, 0.117922194, -0.09352283, -0.08413261, 0.20122392, 0.009214863, -0.089696005, -0.09078444, 0.04681001, 0.1442075, 0.034974463], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 192, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5315215, 0.7046378, 0.45786422, 0.69568986, 0.57220054, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 5.0, 4.0, 5.0, 0.009214863, -0.089696005, -0.09078444, 0.04681001, 0.1442075, 0.034974463], "split_indices": [1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.68872, 39.162704, 15.526015, 11.079089, 28.083618, 9.902196, 5.6238203, 5.8833146, 5.195774, 5.1660614, 22.917557], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0023282582, 0.051646765, -0.03718206, -0.059311267, 0.008235569, 0.030200737, -0.03458044], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 193, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.43735966, 0.0, 0.39106187, 0.0, 0.46774676, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.051646765, 2.0, -0.059311267, 6.0, 0.030200737, -0.03458044], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.215305, 11.397793, 49.817513, 10.732781, 39.084732, 22.923063, 16.161667], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.005345191, -0.06579512, 0.014722116, 0.032866817, -0.086525634, -0.11035498, 0.059169974, 0.019733846, -0.09975223, 0.0474178, -0.025700374], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 194, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.09545218, 0.8219372, 0.30656907, 0.0, 0.0, 0.6022507, 0.61861086, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 4.0, 2.0, 0.032866817, -0.086525634, 1.0, 7.0, 0.019733846, -0.09975223, 0.0474178, -0.025700374], "split_indices": [2, 1, 1, 0, 0, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [72.67789, 18.78755, 53.89034, 10.542427, 8.245123, 13.150884, 40.739456, 7.476706, 5.674178, 24.565966, 16.173489], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.033924025, -0.20807454, 0.027285228, -0.0, -0.101601444, 0.0053667068, 0.036693733, -0.08975766, 0.024789117], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 195, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.58031726, 0.36273372, 0.067999996, 0.0, 0.0, 0.7952888, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 1.0, 9.0, -0.0, -0.101601444, 3.0, 0.036693733, -0.08975766, 0.024789117], "split_indices": [1, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.212074, 13.119436, 38.09264, 5.6272945, 7.4921417, 32.6667, 5.4259386, 5.767132, 26.899569], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.032875348, 0.06396178, -0.108035594, -0.012929886, 0.05598497, -0.19313356, -0.04041533, -0.0922649, -0.009414386, 0.023060335, -0.064617254], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 196, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.3925649, 0.32358855, 0.14402935, 0.0, 0.0, 0.18891975, 0.41793904, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [4.0, 5.0, 6.0, -0.012929886, 0.05598497, 6.0, 8.0, -0.0922649, -0.009414386, 0.023060335, -0.064617254], "split_indices": [0, 2, 0, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.38136, 21.936579, 29.444778, 11.623828, 10.312751, 11.560836, 17.883944, 5.881173, 5.679662, 10.700659, 7.1832848], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0062063485, -0.073003605, 0.09740662, 0.0024416784, -0.23348537, -0.012139671, 0.2120924, 0.029059952, -0.015008557, -0.10910372, -0.014079268, 0.02951104, -0.0356045, 0.07987765, 0.030755937], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 197, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.46267256, 0.5248592, 0.346465, 0.15183716, 0.273924, 0.17484115, 0.013546586, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 7.0, 6.0, 3.0, 3.0, 8.0, 8.0, 0.029059952, -0.015008557, -0.10910372, -0.014079268, 0.02951104, -0.0356045, 0.07987765, 0.030755937], "split_indices": [1, 2, 2, 2, 1, 1, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.17508, 40.39425, 24.780832, 27.722895, 12.671355, 12.685764, 12.095068, 10.281013, 17.441883, 6.5881157, 6.08324, 5.8038607, 6.881903, 6.52625, 5.568818], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.014049455, 0.12682937, -0.09958844, -0.0, 0.09750847, -0.012741036, -0.13220428, 0.034425087, -0.028261548, 0.029956298, -0.02859787], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 198, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.6719773, 0.5241225, 0.99136806, 0.15803131, 0.0, 0.28160158, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [3.0, 2.0, 7.0, 4.0, 0.09750847, 3.0, -0.13220428, 0.034425087, -0.028261548, 0.029956298, -0.02859787], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.94074, 19.754389, 34.186348, 12.632997, 7.121393, 28.308975, 5.877373, 5.561628, 7.0713687, 11.377328, 16.931648], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.053835835, 0.12769337, -0.022537256, 0.049526818, 0.084986486, -0.052163, 0.012566653, -0.0096237715, 0.07871849, 0.057963334, -0.016392179], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 199, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.3473726, 0.32869375, 0.17393753, 0.41739705, 0.0, 0.0, 0.31789908, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 1.0, 8.0, 0.084986486, -0.052163, 4.0, -0.0096237715, 0.07871849, 0.057963334, -0.016392179], "split_indices": [2, 2, 1, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.208508, 29.915627, 28.292881, 21.21753, 8.698097, 5.119078, 23.173803, 15.611979, 5.6055503, 6.1788707, 16.994932], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.050602254, 0.07079896, -0.009813967, 0.008057305, 0.16755877, 0.023911247, -0.03176046, -0.016424702, 0.043442924, 0.08186345, -0.008300548], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 200, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.07358487, 0.23840863, 0.12586068, 0.252189, 0.3609591, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, 4.0, 5.0, 5.0, 0.023911247, -0.03176046, -0.016424702, 0.043442924, 0.08186345, -0.008300548], "split_indices": [0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.737793, 41.386063, 12.351734, 26.390505, 14.995555, 5.942622, 6.4091115, 17.994547, 8.395958, 9.8150835, 5.180472], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019235814, 0.06650476, -0.04229648, -0.0089438185, 0.13110217, -0.040909424, 0.030301454, 0.09179342, -0.022120174], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 201, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.39544517, 0.14431745, 0.0, 0.19401896, 0.5920768, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [6.0, 6.0, -0.04229648, 3.0, 3.0, -0.040909424, 0.030301454, 0.09179342, -0.022120174], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.787918, 25.86395, 12.923967, 11.613313, 14.250638, 5.689238, 5.9240746, 7.6845164, 6.5661216], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.026240038, 0.049045045, -0.12207012, -0.055069856, 0.08103909, -0.05076047, -0.09108048, 0.021338318, -0.06313698, -0.04491311, 0.0038580373], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 202, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.43530655, 0.79340994, 0.2935783, 0.47760397, 0.0, 0.14591673, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 8.0, 7.0, 2.0, 0.08103909, 3.0, -0.09108048, 0.021338318, -0.06313698, -0.04491311, 0.0038580373], "split_indices": [1, 2, 2, 1, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.49507, 31.88813, 25.606943, 21.88194, 10.006188, 19.699162, 5.9077816, 12.019215, 9.862726, 7.7946663, 11.904495], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.026829727, 0.09664627, -0.08669366, 0.07821991, -0.014918826, -0.06808067, -0.013734192, 0.03141303, -0.01668817], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 203, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.45725948, 0.50962806, 0.40319243, 0.0, 0.0, 0.0, 0.14581345, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 4.0, 3.0, 0.07821991, -0.014918826, -0.06808067, 4.0, 0.03141303, -0.01668817], "split_indices": [2, 1, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.86185, 18.799961, 41.06189, 8.817492, 9.98247, 12.860506, 28.201384, 6.35301, 21.848373], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.031928528, -0.11707919, 0.08636226, -0.15369669, -0.007053649, 0.15201907, -0.0, -0.009082969, -0.06684418, 0.065025054, 0.008487062, -0.04495238, 0.020575946], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 204, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.61544156, 0.047198534, 0.30554965, 0.08248976, 0.0, 0.22002572, 0.2680677, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 7.0, 6.0, 1.0, -0.007053649, 7.0, 5.0, -0.009082969, -0.06684418, 0.065025054, 0.008487062, -0.04495238, 0.020575946], "split_indices": [2, 0, 0, 2, 0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [74.00908, 19.002737, 55.00634, 12.427745, 6.574992, 30.297096, 24.709242, 5.4969354, 6.93081, 18.866629, 11.4304695, 7.0246882, 17.684553], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.05972837, -0.17167689, 0.04849144, -0.0024425082, -0.2594619, 0.051411156, -0.0, -0.030796869, -0.13683994, -0.047749173, 0.03481537], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 205, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.6352356, 0.33877844, 0.15175328, 0.0, 0.39794087, 0.0, 0.3843546, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 7.0, -0.0024425082, 4.0, 0.051411156, 5.0, -0.030796869, -0.13683994, -0.047749173, 0.03481537], "split_indices": [1, 2, 1, 0, 1, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.72966, 24.609701, 25.119957, 9.375648, 15.234055, 6.258695, 18.861261, 9.605798, 5.6282563, 7.714299, 11.146962], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.023310939, -0.01576595, 0.08016743, 0.03548003, -0.091991164, -0.040424623, 0.025237797], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 206, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.5259898, 0.71622866, 0.0, 0.33162484, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, 0.08016743, 2.0, -0.091991164, -0.040424623, 0.025237797], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.10474, 44.500755, 6.603985, 38.28916, 6.2115955, 7.6596785, 30.629482], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.05077297, 0.18131378, -0.10280493, 0.08707221, 0.24668442, -0.18669334, 0.027784988, 0.04540814, -0.0034342776, 0.086431675, 0.041180663, -0.025890723, -0.07882063, -0.0029091693, 0.022622496], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 207, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.2534901, 0.15335155, 0.3322678, 0.117148966, 0.003039956, 0.079004526, 0.027856361, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 5.0, 9.0, 3.0, 3.0, 7.0, 7.0, 0.04540814, -0.0034342776, 0.086431675, 0.041180663, -0.025890723, -0.07882063, -0.0029091693, 0.022622496], "split_indices": [1, 2, 1, 2, 1, 1, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.202522, 32.796295, 27.406227, 15.013624, 17.782671, 16.89156, 10.514667, 9.374834, 5.638789, 11.051757, 6.730914, 8.767145, 8.124414, 5.1544175, 5.3602495], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-0.035128806, 0.022428788, -0.059845574, -0.15644945, 0.015967727, -0.07522825, 0.0080571445, 0.06519995, -0.020985331], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 208, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.14770877, 0.0, 0.34463903, 0.38128948, 0.4698594, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.022428788, 5.0, 6.0, 6.0, -0.07522825, 0.0080571445, 0.06519995, -0.020985331], "split_indices": [2, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.283386, 8.485593, 43.797794, 19.370571, 24.427223, 12.907303, 6.4632688, 7.1961617, 17.23106], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.036305193, -0.16475846, 0.059626006, 0.0026287234, -0.27385473, 0.05612365, 0.021879978, -0.043131933, -0.10780202, 0.041430242, -0.029488292], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 209, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.7394305, 0.5146662, 0.13711312, 0.0, 0.0774982, 0.0, 0.40490964, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 1.0, 5.0, 0.0026287234, 2.0, 0.05612365, 6.0, -0.043131933, -0.10780202, 0.041430242, -0.029488292], "split_indices": [1, 1, 1, 0, 1, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.35529, 24.660059, 32.69523, 9.520493, 15.139566, 5.9611926, 26.734037, 7.531159, 7.608408, 13.937756, 12.796281], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.019327609, -0.0654417, 0.10223804, -0.24795835, 0.072024934, -0.05043825, -0.10545294, 0.044161573, -0.023873055], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 210, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.89552313, 1.2690898, 0.0, 0.06799817, 0.34339455, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 6.0, 0.10223804, 4.0, 6.0, -0.05043825, -0.10545294, 0.044161573, -0.023873055], "split_indices": [2, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.240685, 48.168583, 5.072102, 20.660707, 27.507874, 13.693388, 6.967319, 18.849104, 8.65877], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.06624688, -0.12651226, 0.0134691885, 0.11587148, -0.08311558, -0.0, -0.034095906, 0.03151606, 0.05153467, 0.012137755], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 211, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.333728, 0.055792958, 0.27845943, 0.18703535, 0.025472686, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 5.0, 6.0, 6.0, -0.08311558, -0.0, -0.034095906, 0.03151606, 0.05153467, 0.012137755], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.329266, 25.836885, 12.49238, 14.113344, 11.723541, 5.441169, 7.051212, 5.3518867, 8.761457, 5.2369275, 6.4866138], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.028373642, -0.0, 0.057690736, 0.026026595, -0.05085211, 0.0235869, -0.025922194], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 212, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.26107085, 0.22116978, 0.0, 0.2701121, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, 0.057690736, 7.0, -0.05085211, 0.0235869, -0.025922194], "split_indices": [0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.61785, 49.30376, 7.314088, 43.659107, 5.644655, 30.555447, 13.103659], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.050335683, 0.08061606, -0.013519999, 0.114392415, -0.0052972212, -0.049664825, 0.049332004, 0.050499592, 0.02348662, 0.076137215, -0.036429606], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 213, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.13654584, 0.16561857, 0.22247769, 0.03110215, 0.0, 0.0, 0.57868433, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 8.0, 2.0, 2.0, -0.0052972212, -0.049664825, 4.0, 0.050499592, 0.02348662, 0.076137215, -0.036429606], "split_indices": [0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.58462, 45.372517, 20.212097, 34.40213, 10.970386, 5.8994093, 14.312689, 11.214787, 23.187346, 6.5589457, 7.7537427], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.055307638, -0.046531335, 0.07501447, 0.06415414, 0.051119354, 0.036407664, 0.00467558], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 214, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.3148717, 0.0, 0.19393441, 0.0, 0.13773575, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, -0.046531335, 1.0, 0.06415414, 4.0, 0.036407664, 0.00467558], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [74.06122, 5.319488, 68.74173, 8.312288, 60.42944, 18.544134, 41.885307], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.07558506, -0.094602816, -0.031624153, 0.07020068, -0.07511826, -0.028617011, 0.08050273, -0.04498955, 0.004999861], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 215, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.48357347, 0.0, 0.19981843, 0.46756047, 0.23479216, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.094602816, 4.0, 3.0, 6.0, -0.028617011, 0.08050273, -0.04498955, 0.004999861], "split_indices": [2, 0, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.118065, 6.301642, 42.81642, 11.943079, 30.873343, 6.513508, 5.429571, 17.256731, 13.6166115], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.050392035, -0.021692447, 0.12709688, 0.037445378, -0.11380091, 0.18588585, -0.0023853169, -0.06404663, -0.006977851, 0.0074917874, 0.07827449], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 216, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.30597898, 0.38381618, 0.22936818, 0.0, 0.13210154, 0.19421059, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 7.0, 0.037445378, 5.0, 3.0, -0.0023853169, -0.06404663, -0.006977851, 0.0074917874, 0.07827449], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.933517, 26.470747, 25.46277, 9.661729, 16.80902, 17.993769, 7.469002, 6.828419, 9.980599, 6.569647, 11.424122], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.012336463, 0.06144055, -0.019591639, 0.016682735, -0.09109564, 0.03538685, -0.021930309, -0.06523425, 0.040861685], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 217, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.41295266, 0.0, 0.15151668, 0.34390485, 0.6000931, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.06144055, 7.0, 5.0, 8.0, 0.03538685, -0.021930309, -0.06523425, 0.040861685], "split_indices": [1, 0, 2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.5339, 8.572955, 53.96095, 35.38043, 18.58052, 17.002321, 18.378107, 12.252767, 6.327753], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.081421316, 0.2188246, 0.022940727, 0.12715909, 0.00429267, -0.028672019, 0.04545849, -0.034604892, 0.043984357], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 218, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.3950826, 0.58482605, 0.27324098, 0.0, 0.0, 0.4373268, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [2.0, 4.0, 7.0, 0.12715909, 0.00429267, 7.0, 0.04545849, -0.034604892, 0.043984357], "split_indices": [2, 1, 1, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.80108, 14.135997, 37.665085, 6.2792225, 7.8567743, 26.876629, 10.788455, 18.59433, 8.282298], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.038150016, 0.050975233, -0.13675073, -0.019035771, 0.04024055, -0.08897458, -0.051213417, 0.027053637, -0.04940561, 0.019619621, -0.06339757], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 219, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.57623434, 0.21031104, 0.37515253, 0.31695318, 0.0, 0.0, 0.43677637, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 6.0, 7.0, 3.0, 0.04024055, -0.08897458, 5.0, 0.027053637, -0.04940561, 0.019619621, -0.06339757], "split_indices": [0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.80267, 32.70732, 30.095348, 17.523315, 15.184006, 9.223803, 20.871548, 9.839494, 7.6838207, 12.057121, 8.814425], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0026467273, -0.0296339, 0.07090648, -0.18371789, 0.06481148, -0.019184262, -0.09819957, 0.044722825, -0.04114466], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 220, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.4201431, 0.6964328, 0.0, 0.25347406, 0.51504797, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, 0.07090648, 3.0, 6.0, -0.019184262, -0.09819957, 0.044722825, -0.04114466], "split_indices": [2, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.022007, 45.221207, 5.8008, 17.22799, 27.993216, 10.56153, 6.6664596, 20.286158, 7.707059], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.06739484, -0.19306153, 0.049915824, -0.08289277, -0.023447193, 0.09664874, -0.08544326, 0.034791235, -0.09405031], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 221, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.59932256, 0.1291076, 0.8091786, 0.0, 0.0, 0.0, 0.7121974, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [6.0, 6.0, 3.0, -0.08289277, -0.023447193, 0.09664874, 8.0, 0.034791235, -0.09405031], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.029266, 18.460516, 19.56875, 9.385658, 9.074857, 6.285497, 13.283253, 7.0818057, 6.2014465], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.00397341, 0.077021785, -0.15380892, -0.043005716, 0.1936715, -0.010799459, -0.081844814, 0.052140597, -0.08765431, 0.10069039, 0.03283667], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 222, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6789565, 0.5991001, 0.21766278, 1.1788193, 0.17528176, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, 7.0, 5.0, 5.0, -0.010799459, -0.081844814, 0.052140597, -0.08765431, 0.10069039, 0.03283667], "split_indices": [2, 2, 0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.54621, 40.118305, 17.42791, 19.661514, 20.456789, 9.834775, 7.593134, 10.460762, 9.200753, 6.063201, 14.393587], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.07272725, -0.07741948, 0.10719039, -0.009720157, -0.12076572, -0.0019554745, 0.005931137, 0.10865971, -0.0046711285, -0.046564396, 0.036571737, -0.049555097], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 223, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.3728281, 0.13535082, 0.09184867, 0.5522471, 0.0, 0.050827444, 0.29735142, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 8.0, 6.0, -0.009720157, 3.0, 9.0, 0.005931137, 0.10865971, -0.0046711285, -0.046564396, 0.036571737, -0.049555097], "split_indices": [1, 2, 1, 2, 0, 2, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.23843, 33.335537, 30.902891, 25.807037, 7.5285, 18.405434, 12.497457, 20.313131, 5.4939075, 5.614746, 12.790688, 6.9011927, 5.5962644], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.039534733, 0.06509454, 0.011437353, -0.05026863, 0.049324784, 0.02680486, -0.027699752], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 224, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.33621794, 0.0, 0.43145445, 0.0, 0.3150983, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.06509454, 2.0, -0.05026863, 8.0, 0.02680486, -0.027699752], "split_indices": [1, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [71.51429, 8.370355, 63.143936, 9.971497, 53.17244, 42.296814, 10.875626], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.06438676, 0.11051278, -0.06529646, 0.14304256, -0.0099587375, -0.049275186, 0.004194956, 0.0031267165, 0.060604163], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 225, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.3096367, 0.19185805, 0.12029624, 0.22282606, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [8.0, 8.0, 5.0, 3.0, -0.0099587375, -0.049275186, 0.004194956, 0.0031267165, 0.060604163], "split_indices": [0, 2, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.801144, 36.67532, 12.125824, 30.477041, 6.198277, 5.477974, 6.6478505, 10.264885, 20.212156], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.059062745, -0.012707344, -0.18579118, -0.110247515, 0.05585814, -0.09638887, 0.0012071606, -0.0017275034, -0.08445893, 0.07169856, -0.017917769], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 226, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.2759887, 0.2712045, 0.36594453, 0.2732123, 0.51601124, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 8.0, 3.0, 7.0, -0.09638887, 0.0012071606, -0.0017275034, -0.08445893, 0.07169856, -0.017917769], "split_indices": [0, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.761265, 37.81753, 11.943736, 15.961498, 21.856031, 6.8720617, 5.071674, 10.918324, 5.0431743, 8.378374, 13.477657], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.020703504, -0.047682688, 0.00041203242, 0.071383424, -0.054250505, -0.0016087937, 0.08076237, -0.038582522, 0.006674878], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 227, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.2025008, 0.0, 0.22213921, 0.41520056, 0.19157478, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.047682688, 6.0, 5.0, 4.0, -0.0016087937, 0.08076237, -0.038582522, 0.006674878], "split_indices": [0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.92024, 8.099986, 54.82025, 24.51482, 30.305431, 17.999355, 6.5154634, 15.685348, 14.620084], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.00308374, 0.081509285, -0.17869803, -0.008427591, 0.110116936, -0.0018320546, -0.086565904, 0.030021695, -0.042917088], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 228, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.8199832, 0.9750703, 0.31214488, 0.43392318, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 4.0, 2.0, 0.110116936, -0.0018320546, -0.086565904, 0.030021695, -0.042917088], "split_indices": [2, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.561695, 35.45131, 17.110388, 27.431633, 8.019676, 7.4195485, 9.690839, 14.920707, 12.510927], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.029866694, 0.10780908, -0.05448588, 0.03543218, 0.08167158, -0.08905143, 0.017883841, 0.044753946, -0.06841356, -0.03605854, -0.008254173], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 229, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.42506006, 0.360272, 0.12766731, 0.7738842, 0.0, 0.028516471, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 8.0, 9.0, 3.0, 0.08167158, 5.0, 0.017883841, 0.044753946, -0.06841356, -0.03605854, -0.008254173], "split_indices": [1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.94696, 32.668476, 29.278486, 23.950039, 8.718436, 23.312157, 5.9663296, 17.296286, 6.6537547, 13.766701, 9.545455], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.05272842, 0.013344602, -0.10630511, 0.027783008, -0.037228983, -0.15444393, -0.030429276, -0.0463153, 0.0360489, -0.07138115, -0.0, -0.03805595, 0.019565837], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 230, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.18987338, 0.10004394, 0.0823552, 0.0, 0.27662593, 0.22812465, 0.13680525, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 5.0, 7.0, 0.027783008, 2.0, 7.0, 5.0, -0.0463153, 0.0360489, -0.07138115, -0.0, -0.03805595, 0.019565837], "split_indices": [1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.610184, 21.7149, 27.895283, 8.904595, 12.810304, 15.627264, 12.268019, 7.719058, 5.091246, 10.034948, 5.592315, 6.467429, 5.8005896], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.037443496, 0.074381895, -0.116225734, -0.02048252, 0.06020848, -0.24440293, 0.007807311, -0.030867925, -0.09588479], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 231, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.33601016, 0.30169415, 0.43728945, 0.0, 0.0, 0.055437088, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [5.0, 2.0, 5.0, -0.02048252, 0.06020848, 8.0, 0.007807311, -0.030867925, -0.09588479], "split_indices": [2, 2, 1, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.683994, 14.247411, 21.436583, 6.5348244, 7.712587, 11.2681055, 10.168478, 5.193796, 6.0743093], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.030756753, 0.037370123, -0.20863236, -0.08292897, 0.08726569, -0.10704355, -0.008006284, -0.03719301, -0.005288069, -0.019459024, 0.040817842], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 232, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.69355315, 0.24979182, 0.37914503, 0.015152402, 0.23556525, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 6.0, 4.0, 2.0, -0.10704355, -0.008006284, -0.03719301, -0.005288069, -0.019459024, 0.040817842], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.17612, 39.33747, 14.838648, 10.75082, 28.586653, 7.3455596, 7.4930887, 5.1093297, 5.6414905, 6.364537, 22.222116], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.029303467, -0.0029027562, 0.12751754, -0.07296632, 0.050661746, 0.016978461, 0.052384935, -0.0, -0.08516132, 0.046292167, -0.0018904151], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 233, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.21448448, 0.18689147, 0.02221939, 0.36755723, 0.17582232, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 6.0, 4.0, 2.0, 0.016978461, 0.052384935, -0.0, -0.08516132, 0.046292167, -0.0018904151], "split_indices": [1, 2, 2, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.75156, 47.219624, 15.531933, 21.110853, 26.108772, 7.9242353, 7.607698, 16.088757, 5.0220966, 9.276547, 16.832226], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0040008905, -0.13756573, 0.045313124, 0.019844238, -0.08929183, -0.062996104, 0.20982492, 0.016319789, -0.07134635, 0.08859685, 0.02014234], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 234, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.5080445, 0.72635317, 1.0047798, 0.0, 0.0, 0.7180448, 0.21673292, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 4.0, 6.0, 0.019844238, -0.08929183, 3.0, 8.0, 0.016319789, -0.07134635, 0.08859685, 0.02014234], "split_indices": [0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [73.78582, 19.960882, 53.82494, 8.766502, 11.194381, 32.462135, 21.362803, 19.490833, 12.971302, 12.2511425, 9.11166], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-5.888111e-05, 0.05581779, -0.14407171, -0.055268675, 0.1177429, -0.0038844359, -0.07241866, 0.061748914, 0.017769275], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 235, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.4238003, 0.5599036, 0.1617859, 0.0, 0.11633229, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [8.0, 3.0, 4.0, -0.055268675, 5.0, -0.0038844359, -0.07241866, 0.061748914, 0.017769275], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.569817, 35.65412, 13.915695, 6.5753875, 29.078733, 6.848843, 7.0668516, 9.919159, 19.159575], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.04255323, -0.021497913, -0.04935359, -0.059824742, 0.039164513, -0.057254065, -0.007191882], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 236, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.10886693, 0.2650431, 0.0, 0.1497899, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, -0.04935359, 2.0, 0.039164513, -0.057254065, -0.007191882], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.016636, 44.355236, 5.661399, 36.435135, 7.9200993, 6.299787, 30.13535], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.04161734, -0.0, 0.11408653, 0.034530688, -0.03395419, 0.093611926, 0.035388395, -0.034827173, 0.0034435103, -0.029226191, 0.054098092], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 237, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.19708732, 0.16158524, 0.32240754, 0.0, 0.13436453, 0.0, 0.38005668, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 1.0, 7.0, 0.034530688, 4.0, 0.093611926, 6.0, -0.034827173, 0.0034435103, -0.029226191, 0.054098092], "split_indices": [1, 1, 1, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.818935, 40.184322, 22.634611, 8.338191, 31.846132, 5.233881, 17.40073, 11.57173, 20.274403, 8.891364, 8.509367], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.033140495, -0.07551806, 0.0015229757, 0.0417863, -0.033865202, 0.040806517, -0.041741885, -0.032062702, 0.05403948], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 238, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.4374855, 0.0, 0.069946066, 0.42794618, 0.39522856, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.07551806, 4.0, 6.0, 7.0, 0.040806517, -0.041741885, -0.032062702, 0.05403948], "split_indices": [0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.38615, 6.8449645, 46.541183, 23.008263, 23.53292, 15.655162, 7.3531013, 18.268147, 5.2647743], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.034916352, 0.0794145, -0.09720766, -0.0, 0.108532384, 0.08052295, -0.09290143, -0.03854535, 0.027823955, -0.0012342657, 0.045724988], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 239, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.37233326, 0.116851956, 1.2978319, 0.1720298, 0.19083828, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 3.0, 3.0, 5.0, 3.0, 0.08052295, -0.09290143, -0.03854535, 0.027823955, -0.0012342657, 0.045724988], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.319412, 46.73522, 14.5841875, 12.257501, 34.477722, 5.090401, 9.493787, 5.24662, 7.01088, 9.281228, 25.196495], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.041993517, -0.0027759592, 0.102027185, 0.024358144, -0.04279457, 0.059563383, -0.008499762], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 240, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.7274096, 0.18924168, 0.0, 0.3772746, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 9.0, 0.102027185, 2.0, -0.04279457, 0.059563383, -0.008499762], "split_indices": [2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.907104, 44.829628, 6.077476, 37.638916, 7.190713, 8.539274, 29.09964], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.036485273, 0.12823315, -0.08826281, 0.0670605, -0.009189947, -0.043045968, 0.0035584236], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 241, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.45608497, 0.3752725, 0.10582431, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 9.0, 0.0670605, -0.009189947, -0.043045968, 0.0035584236], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.510742, 22.060034, 15.450707, 13.927501, 8.132533, 10.322811, 5.1278963], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.049644507, 0.029743923, -0.079319134, -0.071744926, -0.027554136, 0.035001565, -0.021865636], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 242, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.26595986, 0.0, 0.3822855, 0.0, 0.26264194, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.029743923, 4.0, -0.071744926, 5.0, 0.035001565, -0.021865636], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.903706, 8.67842, 49.225285, 10.684351, 38.54093, 8.312898, 30.228035], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0054191723, 0.028578466, -0.033579372, 0.009713008, -0.0425985, -0.010279362, 0.04450529], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 243, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.17871872, 0.0, 0.25675982, 0.24297217, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.028578466, 7.0, 6.0, -0.0425985, -0.010279362, 0.04450529], "split_indices": [0, 0, 2, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.083244, 12.49087, 50.592373, 36.146454, 14.445918, 27.465597, 8.680857], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.015855936, -0.11357082, 0.0657636, -0.0, -0.068710566, -0.04223566, 0.10349941, 0.005171197, 0.048141558], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 244, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.47383153, 0.27396822, 0.43042272, 0.0, 0.0, 0.0, 0.20799854, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [2.0, 1.0, 2.0, -0.0, -0.068710566, -0.04223566, 6.0, 0.005171197, 0.048141558], "split_indices": [1, 1, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [71.93933, 19.10766, 52.831673, 9.940815, 9.166845, 7.3299484, 45.501724, 19.314856, 26.18687], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.069666974, -0.21677324, 0.028941644, -0.0046850615, -0.08679856, 0.119093865, -0.03381606, -0.04835277, 0.07727496], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 245, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.7416981, 0.26200825, 0.39786378, 0.0, 0.0, 0.7996006, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [5.0, 4.0, 8.0, -0.0046850615, -0.08679856, 4.0, -0.03381606, -0.04835277, 0.07727496], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.403267, 19.350943, 29.052324, 5.817017, 13.533925, 18.282642, 10.769682, 5.7533426, 12.5293], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0034349435, 0.065634854, -0.0408223, 0.05990042, -0.023136359, -0.1477678, 0.09140158, -0.04555708, 0.020072196, -0.0938774, -0.021992838], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 246, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.14738424, 0.2935712, 1.1327648, 0.0, 0.18282379, 0.22987098, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [4.0, 4.0, 9.0, 0.05990042, 2.0, 5.0, 0.09140158, -0.04555708, 0.020072196, -0.0938774, -0.021992838], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.665142, 21.801466, 28.863676, 8.637377, 13.164089, 22.699068, 6.164609, 5.592915, 7.5711746, 5.640997, 17.058071], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.056864128, -0.023927959, 0.18786639, -0.12776774, 0.1272756, 0.2613971, -0.009941705, -0.0, -0.09961688, 0.12899086, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 247, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.6635497, 1.7917572, 0.41740632, 0.8544952, 0.0, 0.822597, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [7.0, 9.0, 7.0, 5.0, 0.1272756, 9.0, -0.009941705, -0.0, -0.09961688, 0.12899086, -0.0], "split_indices": [0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.72033, 36.972527, 22.7478, 30.779257, 6.193269, 17.27121, 5.476591, 19.324495, 11.4547615, 10.2024555, 7.0687537], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0018287589, -0.056236666, 0.06189595, -0.0, -0.05891982, -0.010419047, 0.061467744, -0.06027164, 0.028767651, 0.016258365, -0.034625586], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 248, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.1873372, 0.20046145, 0.30236614, 0.40935785, 0.0, 0.13802814, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 5.0, 3.0, -0.05891982, 3.0, 0.061467744, -0.06027164, 0.028767651, 0.016258365, -0.034625586], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.55147, 25.398426, 26.153044, 19.037054, 6.361372, 17.520529, 8.632515, 5.9275465, 13.109508, 10.556097, 6.9644322], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.040623374, -0.07952552, 0.06679813, 0.03454043, -0.13965175, -0.019160673, 0.07365565, -0.004636571, -0.05805495], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 249, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.63851154, 0.39319038, 0.0, 0.433407, 0.22262889, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 4.0, 0.06679813, 3.0, 2.0, -0.019160673, 0.07365565, -0.004636571, -0.05805495], "split_indices": [2, 2, 0, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.009262, 54.094204, 6.915058, 18.244596, 35.849606, 12.593664, 5.6509323, 11.79566, 24.053947], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.09641404, -0.08174759, -0.034956794, 0.1936742, -0.0, -0.16331217, 0.03585984, 0.08851409, 0.021931756, -0.037566412, -0.09473726, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 250, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.42203766, 0.53229153, 0.19003971, 0.0, 0.053581297, 0.15702373, 0.33965853, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 3.0, 5.0, -0.034956794, 2.0, 6.0, 8.0, 0.03585984, 0.08851409, 0.021931756, -0.037566412, -0.09473726, -0.0], "split_indices": [1, 2, 2, 0, 1, 1, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.676094, 23.239634, 28.43646, 6.902159, 16.337475, 14.935473, 13.500987, 11.264506, 5.072967, 9.500379, 5.435095, 6.284238, 7.2167487], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.027285911, -0.14640059, 0.118678756, -0.11015675, 0.02780218, 0.06720447, -0.0016040175, -0.019704878, 0.044414174], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 251, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.6788407, 0.8782458, 0.2477583, 0.0, 0.16182959, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [5.0, 6.0, 6.0, -0.11015675, 8.0, 0.06720447, -0.0016040175, -0.019704878, 0.044414174], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.885292, 20.68631, 16.198982, 8.887856, 11.798453, 8.759519, 7.439464, 6.425241, 5.3732123], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.033006873, -0.11368037, 0.04449268, -0.14575218, 0.047543366, 0.101491384, -0.13157216, 0.04318705, -0.031792838, -0.0546672, -0.016545195], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 252, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.35733914, 1.7456822, 1.5270813, 0.0, 0.31870884, 0.0, 0.036578298, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 2.0, -0.14575218, 8.0, 0.101491384, 5.0, 0.04318705, -0.031792838, -0.0546672, -0.016545195], "split_indices": [0, 1, 1, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.238903, 26.97874, 27.26016, 7.733635, 19.245106, 10.082039, 17.178122, 12.259535, 6.985572, 8.711311, 8.466809], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.008346721, 0.008832255, -0.053089585, 0.10526803, -0.030247081, 0.010788336, 0.06011191, 0.018143704, -0.021199148], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 253, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.21474926, 0.24410944, 0.0, 0.09322913, 0.16705756, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 3.0, -0.053089585, 5.0, 2.0, 0.010788336, 0.06011191, 0.018143704, -0.021199148], "split_indices": [1, 2, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.7921, 60.938232, 5.853864, 17.82264, 43.115593, 11.715016, 6.107623, 12.3722315, 30.743362], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.017953202, 0.045835625, -0.08533582, 0.0032857945, 0.0950116, -0.018354855, 0.009304247], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 254, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.61681616, 0.76587355, 0.0, 0.10834727, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, -0.08533582, 3.0, 0.0950116, -0.018354855, 0.009304247], "split_indices": [2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [73.43713, 68.31726, 5.119874, 60.236294, 8.080965, 16.668646, 43.567646], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.026468024, -0.05793201, 0.039533623, -0.0, -0.076021284, -0.02616693, 0.05160225], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 255, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.23596069, 0.4400195, 0.0, 0.49903542, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 7.0, 0.039533623, 8.0, -0.076021284, -0.02616693, 0.05160225], "split_indices": [0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.34036, 39.710594, 6.629766, 31.730442, 7.980152, 21.70074, 10.0297], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.015331904, -0.074281074, 0.031457987, -0.10276907, 0.08982022, -0.051228743, -0.0023835343, 0.087373525, 0.008355742], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 256, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.55857396, 0.0, 0.32955873, 0.060647443, 0.330279, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.074281074, 3.0, 4.0, 5.0, -0.051228743, -0.0023835343, 0.087373525, 0.008355742], "split_indices": [0, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.690838, 7.6350017, 40.055836, 11.388699, 28.66714, 5.5521293, 5.8365693, 5.494311, 23.172827], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011829849, 0.119655214, -0.0983234, -0.0039034747, 0.22740965, 0.012454073, -0.18206812, 0.03225889, -0.046826355, 0.12884557, -0.0003296163, -0.03194195, 0.057209034, -0.029976843, -0.08340937], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 257, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.747197, 0.39594865, 0.3517209, 0.2521283, 0.75453377, 0.36474952, 0.099874675, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 4.0, 2.0, 7.0, 2.0, 8.0, 0.03225889, -0.046826355, 0.12884557, -0.0003296163, -0.03194195, 0.057209034, -0.029976843, -0.08340937], "split_indices": [2, 1, 1, 1, 1, 1, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.834225, 26.993876, 34.84035, 12.59142, 14.402455, 14.851706, 19.988646, 7.058986, 5.5324335, 7.408787, 6.993669, 8.813065, 6.038642, 12.430958, 7.5576873], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.06835446, 0.1082359, -0.020137377, 0.044027872, 0.09438517, 0.019778462, -0.047426496, -0.013103478, 0.0469334], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 258, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.19816995, 0.44716352, 0.21603085, 0.3151939, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 5.0, 5.0, 0.09438517, 0.019778462, -0.047426496, -0.013103478, 0.0469334], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.03792, 36.49659, 15.54133, 29.131315, 7.3652754, 9.44557, 6.0957603, 16.207977, 12.923338], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.025953082, 0.0015821434, -0.08450245, -0.049487263, 0.050698582, 0.05045842, -0.008460517], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 259, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.44222784, 0.43613133, 0.0, 0.0, 0.41452083, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 3.0, -0.08450245, -0.049487263, 6.0, 0.05045842, -0.008460517], "split_indices": [0, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.763245, 52.692287, 5.0709586, 10.999995, 41.69229, 16.80397, 24.888319], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.013257454, 0.05300734, -0.066617094, 0.10765857, 0.0036497328, 0.00588167, 0.07355965, 0.025330104, -0.008688273], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 260, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.48681027, 0.11300139, 0.0, 0.22195773, 0.07715468, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 3.0, -0.066617094, 2.0, 4.0, 0.00588167, 0.07355965, 0.025330104, -0.008688273], "split_indices": [0, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.59346, 45.163956, 6.4295044, 19.910654, 25.253304, 13.279041, 6.6316123, 7.6873465, 17.565958], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.049796954, 0.05046988, -0.07671097, -0.031381607, 0.15892166, 0.06731824, 0.012094799], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 261, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.81012803, 0.45611653, 0.0, 0.0, 0.0884493, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [6.0, 6.0, -0.07671097, -0.031381607, 3.0, 0.06731824, 0.012094799], "split_indices": [1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.620636, 24.809021, 11.811615, 9.845021, 14.964, 8.523699, 6.4403], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.077517845, -0.053713128, 0.11410929, 0.084990576, 0.074344225, -0.021387823, 0.03511963], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 262, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.5431857, 0.0, 0.28152472, 0.0, 0.2776872, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, -0.053713128, 2.0, 0.084990576, 4.0, -0.021387823, 0.03511963], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.375244, 6.084904, 49.29034, 7.7360015, 41.554337, 8.74228, 32.812057], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.034271885, 0.02744151, -0.1148144, 0.10850236, -0.04510721, -0.20788257, -0.0, 0.049428727, 0.010208899, 0.00096510403, -0.11335651, -0.09045738, 0.076525636], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 263, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.32064837, 0.52051747, 0.28741282, 0.080078274, 0.0, 0.58207077, 1.1328737, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 4.0, 4.0, -0.04510721, 7.0, 8.0, 0.049428727, 0.010208899, 0.00096510403, -0.11335651, -0.09045738, 0.076525636], "split_indices": [1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.117935, 34.274097, 26.843838, 24.195316, 10.078783, 14.144219, 12.699617, 12.327443, 11.867872, 6.484031, 7.6601887, 5.82401, 6.875607], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.036619637, -0.03165609, 0.1269204, -0.07668974, -0.08725299, 0.006764446, -0.00419368, 0.11762576, -0.13552351, 0.037458505, -0.01666254, 0.029141335], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 264, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.09354872, 0.39058077, 0.47052345, 0.83940816, 1.3304926, 0.0, 0.22991762, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 5.0, 4.0, 8.0, -0.08725299, 6.0, -0.00419368, 0.11762576, -0.13552351, 0.037458505, -0.01666254, 0.029141335], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [78.93051, 35.730553, 43.199963, 20.308397, 15.422154, 5.0648646, 38.135098, 13.633963, 6.674435, 5.130297, 10.291857, 22.145905, 15.989192], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.02400573, -0.09164592, 0.08062785, 0.13092396, -0.035529047, 0.10802839, 0.019423839], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 265, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.89821976, 0.0, 0.43762082, 0.45642394, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.09164592, 8.0, 2.0, -0.035529047, 0.10802839, 0.019423839], "split_indices": [2, 0, 2, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.030067, 5.9932466, 41.03682, 33.433743, 7.6030774, 6.157661, 27.276081], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.044511653, -0.028150247, -0.0671563, 0.03618937, -0.0846489, 0.034009628], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 266, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.20331143, 0.0, 0.560823, 0.0, 0.81090856, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.044511653, 3.0, -0.0671563, 3.0, -0.0846489, 0.034009628], "split_indices": [1, 0, 2, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.1149, 6.7089634, 41.405937, 10.01573, 31.390205, 5.3538127, 26.036392], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.032721102, -0.01403979, 0.086865336, 0.02891831, -0.059103496, 0.021809692, 0.061474103, 0.021232806, -0.026305323, 0.047506426, -0.05582494], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 267, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.17360343, 0.29651585, 0.2136138, 0.14440487, 0.0, 0.6476964, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 8.0, 8.0, -0.059103496, 5.0, 0.061474103, 0.021232806, -0.026305323, 0.047506426, -0.05582494], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.2102, 33.860683, 30.349516, 27.661694, 6.1989875, 20.876488, 9.473028, 21.275063, 6.3866315, 13.034599, 7.841888], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.047546532, -0.15204766, -0.009655135, -0.013447567, -0.064342886, 0.037822556, -0.06206086, -0.04027484, 0.010998817], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 268, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.20241256, 0.058289498, 0.30411574, 0.0, 0.0, 0.0, 0.24204129, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [2.0, 4.0, 4.0, -0.013447567, -0.064342886, 0.037822556, 5.0, -0.04027484, 0.010998817], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.5727, 13.018096, 41.554604, 6.0120792, 7.0060163, 10.652024, 30.90258, 18.227417, 12.675163], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.035352577, 0.10602392, -0.03628497, -0.0135389855, 0.15991144, -0.080988444, 0.046877448, 0.0060215793, 0.06763208, 0.07390842, -0.016113821], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 269, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.33234227, 0.286896, 0.64867145, 0.0, 0.19983941, 0.0, 0.51063025, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 7.0, -0.0135389855, 3.0, -0.080988444, 3.0, 0.0060215793, 0.06763208, 0.07390842, -0.016113821], "split_indices": [2, 0, 2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.5076, 31.927061, 30.580538, 7.9458475, 23.981213, 7.7526846, 22.827854, 8.555657, 15.425556, 7.49977, 15.328083], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.03654121, 0.03754775, 0.014827371, -0.007033651, 0.04060401, 0.028351735, -0.018849568], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 270, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.084961526, 0.0, 0.1300313, 0.2157794, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, 0.03754775, 9.0, 4.0, 0.04060401, 0.028351735, -0.018849568], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.392727, 8.270401, 43.122326, 36.596058, 6.526269, 12.096838, 24.49922], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0068842387, 0.10293851, -0.07444469, 0.056869455, -0.00862198, -0.041498818, 0.028495725], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 271, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.0534763, 0.27780938, 0.0, 0.0, 0.18041234, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [6.0, 7.0, -0.07444469, 0.056869455, 2.0, -0.041498818, 0.028495725], "split_indices": [1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.247196, 25.705162, 11.542033, 14.580567, 11.124596, 5.2198606, 5.904735], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.04295212, 0.10160218, 0.003821091, 0.060205918, -0.07629883, -0.043827776, 0.04081287], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 272, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.5935943, 0.0, 0.71385396, 0.6698417, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, 0.10160218, 8.0, 4.0, -0.07629883, -0.043827776, 0.04081287], "split_indices": [2, 0, 1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.367737, 5.043385, 48.324352, 40.608795, 7.7155576, 10.31204, 30.296755], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.025241777, 0.058043662, -0.23790325, 0.107262015, -0.018310612, -0.11709057, -0.008498873, -0.019395895, 0.045860205], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 273, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [1.1199322, 0.27264023, 0.5163392, 0.27369136, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 7.0, 8.0, 1.0, -0.018310612, -0.11709057, -0.008498873, -0.019395895, 0.045860205], "split_indices": [0, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.436413, 43.612938, 16.823475, 31.522793, 12.090145, 8.964854, 7.85862, 6.0617776, 25.461014], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.028401902, 0.056284834, -0.03929349, 0.08172149, -0.0074172546, -0.0399101, 0.020640554, -0.017940808, 0.03618085, 0.051838648, -0.09992135], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 274, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.14796911, 0.097988024, 0.23654863, 0.2382231, 0.99121857, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 8.0, 6.0, 1.0, 4.0, -0.0399101, 0.020640554, -0.017940808, 0.03618085, 0.051838648, -0.09992135], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [75.32044, 54.51296, 20.807482, 39.87682, 14.636138, 11.515582, 9.2919, 7.9240503, 31.952768, 9.52657, 5.109568], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.02010725, -0.0507277, 0.014966712, 0.08407515, -0.02123168, 0.034985308, 0.0011869409, -0.030446395, 0.010786205], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 275, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.27121305, 0.0, 0.107151985, 0.025980003, 0.1288021, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.0507277, 6.0, 6.0, 5.0, 0.034985308, 0.0011869409, -0.030446395, 0.010786205], "split_indices": [1, 0, 1, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.468067, 8.860607, 38.60746, 13.765975, 24.841484, 8.578042, 5.1879325, 10.741077, 14.100407], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04044186, 0.12866928, -0.0, 0.068119206, -0.0, 0.054313593, -0.02819966, 0.00013042141, 0.037762385], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 276, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.19240353, 0.20390174, 0.1912278, 0.0, 0.0, 0.076079465, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 4.0, 6.0, 0.068119206, -0.0, 4.0, -0.02819966, 0.00013042141, 0.037762385], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.708088, 15.729768, 34.97832, 8.326885, 7.4028826, 22.022236, 12.956085, 13.934401, 8.087835], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.042529974, 0.012562844, -0.059616428, 0.07953105, -0.10338155, -0.017588131, 0.06895975, -0.06390646, -0.0053958213], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 277, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.096334696, 0.0, 0.34242165, 0.30176163, 0.37034297, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.012562844, 3.0, 2.0, 4.0, -0.017588131, 0.06895975, -0.06390646, -0.0053958213], "split_indices": [1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.835857, 9.348982, 53.486874, 12.05545, 41.431423, 6.253398, 5.8020525, 16.97348, 24.457945], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.007305986, 0.061666686, -0.061482538, -0.20653988, 0.058923267, -0.0, -0.10645286, -0.009632159, 0.054311816], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 278, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.6143158, 0.0, 0.79069436, 0.6224341, 0.29079074, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.061666686, 5.0, 4.0, 5.0, -0.0, -0.10645286, -0.009632159, 0.054311816], "split_indices": [0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.42345, 9.694281, 42.72917, 19.432852, 23.29632, 8.566976, 10.865877, 13.300072, 9.996247], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.06454584, -0.011176379, 0.12283765, 0.015451305, -0.027047114, 0.09423198, 0.062476534, 0.015716301, -0.009134955, 0.039055277, -0.03580977], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 279, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.28608721, 0.06591305, 0.35657096, 0.03802612, 0.0, 0.0, 0.37235856, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 5.0, 6.0, -0.027047114, 0.09423198, 7.0, 0.015716301, -0.009134955, 0.039055277, -0.03580977], "split_indices": [1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.973064, 26.12671, 34.846355, 19.193884, 6.932827, 6.953141, 27.893213, 11.764415, 7.4294686, 20.932978, 6.9602346], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.03758097, -0.006378485, 0.103919424, -0.043465577, 0.09408118, 0.02023444, -0.068147056, -0.019278493, 0.07756327], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 280, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.76553386, 0.17684937, 0.0, 0.7702476, 0.360466, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 8.0, 0.103919424, 5.0, 4.0, 0.02023444, -0.068147056, -0.019278493, 0.07756327], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.723705, 46.72765, 5.9960527, 35.26974, 11.4579115, 22.046425, 13.223315, 5.838198, 5.619714], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.03993008, -0.06872203, 0.13062793, 0.043085292, 0.076598436, -0.014293886, 0.05694821], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 281, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.8703734, 0.0, 0.25366768, 0.25659052, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [4.0, -0.06872203, 5.0, 8.0, 0.076598436, -0.014293886, 0.05694821], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [33.85094, 7.8727307, 25.978212, 16.524817, 9.453396, 10.207371, 6.3174453], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0038246324, -0.06737508, 0.08362536, -0.100392126, -0.0129140355, 0.019704029, -0.044444066], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 282, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.99006844, 0.6335599, 0.0, 0.0, 0.44035468, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 2.0, 0.08362536, -0.100392126, 7.0, 0.019704029, -0.044444066], "split_indices": [1, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.264233, 45.21171, 9.052521, 6.471641, 38.74007, 24.358398, 14.381671], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.022915287, -0.012182949, 0.17311855, 0.033412285, -0.052601077, -0.0, 0.08830858, -0.01447283, 0.030663775], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 283, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.3567076, 0.41017073, 0.26855934, 0.24262007, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [8.0, 7.0, 4.0, 3.0, -0.052601077, -0.0, 0.08830858, -0.01447283, 0.030663775], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.209663, 51.442207, 11.767458, 40.348618, 11.09359, 5.2520695, 6.515388, 17.922623, 22.425995], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.050906315, 0.060915284, -0.07704546, -0.015183313, 0.047488395, -0.09415357, 0.0031304539, -0.008642484, -0.05979684], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 284, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.22389834, 0.17015427, 0.10140422, 0.0, 0.0, 0.3322431, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [2.0, 5.0, 9.0, -0.015183313, 0.047488395, 7.0, 0.0031304539, -0.008642484, -0.05979684], "split_indices": [2, 0, 0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [73.44335, 12.93825, 60.505104, 5.7762275, 7.1620216, 51.500275, 9.004831, 33.136307, 18.363968], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0057366155, 0.09443942, -0.0449433, -0.0, 0.043818794, 0.025927525, -0.09403318, -0.053128418, -0.0007972278], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 285, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.19100122, 0.05065567, 0.24411389, 0.0, 0.0, 0.0, 0.19092877, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 5.0, 3.0, -0.0, 0.043818794, 0.025927525, 7.0, -0.053128418, -0.0007972278], "split_indices": [1, 2, 2, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.810432, 12.3347845, 35.475647, 5.3857985, 6.948986, 8.889369, 26.586279, 12.862058, 13.724221], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.051279753, 0.091054015, -0.1171794, 0.08233917, -0.006958083, -0.22236502, 0.0002862727, -0.10680412, -0.027884224, -0.04382083, 0.029317252], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 286, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.47899413, 0.3624739, 0.4529332, 0.0, 0.0, 0.24786681, 0.2520987, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 4.0, 6.0, 0.08233917, -0.006958083, 6.0, 4.0, -0.10680412, -0.027884224, -0.04382083, 0.029317252], "split_indices": [0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.650806, 14.795495, 33.85531, 5.4765425, 9.318953, 17.839273, 16.016037, 7.5346785, 10.304596, 5.7915277, 10.224508], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.039438374, -0.04005452, 0.07377122, 0.15830773, -0.072016336, 0.005685298, 0.06697073, -0.07376715, 0.01600095], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 287, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.3770728, 0.0, 0.6801974, 0.2850908, 0.4664006, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.04005452, 7.0, 4.0, 9.0, 0.005685298, 0.06697073, -0.07376715, 0.01600095], "split_indices": [1, 0, 1, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.849213, 9.283188, 52.566025, 33.652378, 18.913649, 11.595879, 22.0565, 7.854712, 11.058936], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.034856416, -0.033371918, 0.06693777, 0.06661881, 0.0154586965, -0.026360229, 0.034642663], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 288, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.24998677, 0.0, 0.32730913, 0.0, 0.37346426, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, -0.033371918, 2.0, 0.06661881, 5.0, -0.026360229, 0.034642663], "split_indices": [2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.69317, 8.301992, 43.39118, 9.502012, 33.889164, 16.236788, 17.652376], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.027515989, -0.043686617, 0.07628283, 0.10865341, -0.13622656, 0.086543925, 0.005377362, 0.0566277, -0.015626207, -0.030866943, 0.024485186], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 289, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.22315021, 1.656529, 0.54400986, 0.26781029, 0.0, 0.0, 0.25401673, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 5.0, 7.0, -0.13622656, 0.086543925, 5.0, 0.0566277, -0.015626207, -0.030866943, 0.024485186], "split_indices": [1, 1, 1, 2, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.36685, 24.154049, 37.212807, 17.97381, 6.180239, 8.198508, 29.014297, 12.294584, 5.679225, 11.302534, 17.711763], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.05449508, 0.07489707, -0.028949814, 0.1051666, -0.009670498, 0.011099952, 0.07752004], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 290, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.16571954, 0.16338426, 0.0, 0.35445702, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, -0.028949814, 5.0, -0.009670498, 0.011099952, 0.07752004], "split_indices": [1, 2, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.149868, 46.088726, 5.061142, 36.67749, 9.411234, 26.695787, 9.981705], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.07562953, 0.16389969, -0.0374859, 0.08220577, 0.081082985, 0.0037060939, 0.04367096], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 291, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.66128486, 0.17718261, 0.0, 0.0, 0.056161933, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [6.0, 4.0, -0.0374859, 0.08220577, 8.0, 0.0037060939, 0.04367096], "split_indices": [1, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [34.972565, 24.758533, 10.214033, 9.118128, 15.640405, 8.898627, 6.741778], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0243291, -0.1681918, 0.019813009, -0.0, -0.07948063, -0.048910514, 0.17535181, 0.04393786, -0.07797377, 0.026098344, 0.072876826], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 292, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.38294518, 0.23228753, 0.49493462, 0.0, 0.0, 1.3303283, 0.030921519, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 6.0, -0.0, -0.07948063, 2.0, 6.0, 0.04393786, -0.07797377, 0.026098344, 0.072876826], "split_indices": [0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.44122, 13.124051, 43.31717, 5.1223626, 8.001689, 30.0725, 13.244668, 15.527399, 14.545101, 7.3721075, 5.8725605], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.010486469, -0.027526569, 0.047818106, -0.0975801, 0.012750882, 0.06451035, -0.013777756], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 293, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.35921812, 0.6158227, 0.0, 0.0, 0.5407194, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 1.0, 0.047818106, -0.0975801, 3.0, 0.06451035, -0.013777756], "split_indices": [1, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.356678, 47.39999, 11.956687, 5.100799, 42.29919, 9.294978, 33.00421], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.056593005, -0.0074507906, 0.104238614, 0.04569716, -0.094089, 0.13775307, -9.82278e-05, -0.017968887, 0.052421708, 0.016027337, -0.06708048, 0.022904929, 0.11026974], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 294, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.2320013, 0.1519303, 0.16305795, 0.28326455, 0.27413017, 0.39296895, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 6.0, 8.0, 3.0, 2.0, 7.0, -9.82278e-05, -0.017968887, 0.052421708, 0.016027337, -0.06708048, 0.022904929, 0.11026974], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [71.60452, 30.01033, 41.594196, 18.177639, 11.832691, 31.982454, 9.61174, 9.865617, 8.312023, 5.4455914, 6.3870997, 26.633621, 5.348834], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.084742814, -0.01659235, 0.120176375, 0.15636966, 0.06447711, -0.0, 0.062324855, -0.00038282393, 0.029964939], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 295, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.244073, 0.0, 0.04448986, 0.18010473, 0.05135017, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.01659235, 6.0, 4.0, 7.0, -0.0, 0.062324855, -0.00038282393, 0.029964939], "split_indices": [0, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.537, 8.51167, 37.02533, 20.258587, 16.766743, 5.041935, 15.216651, 5.2172556, 11.549486], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.02928093, -0.008757896, -0.04616122, -0.068795, 0.09502764, 0.005510143, -0.02948892, 0.0037011344, 0.049076054], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 296, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.11328091, 0.28866976, 0.0, 0.08730687, 0.07425697, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 7.0, -0.04616122, 1.0, 6.0, 0.005510143, -0.02948892, 0.0037011344, 0.049076054], "split_indices": [1, 1, 0, 1, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.19021, 44.699944, 5.490263, 29.14248, 15.557465, 6.6077027, 22.534779, 8.2479725, 7.3094916], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.015174394, 0.1828098, -0.07977897, 0.012606894, 0.09615592, -0.006688418, -0.13216875, 0.010203209, -0.01439828, -0.0074461186, -0.065685846], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 297, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.8018902, 0.2462312, 0.16938996, 0.0, 0.0, 0.03989738, 0.22324759, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 2.0, 5.0, 0.012606894, 0.09615592, 6.0, 7.0, 0.010203209, -0.01439828, -0.0074461186, -0.065685846], "split_indices": [0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.358578, 14.26504, 47.093536, 8.058157, 6.2068834, 20.92996, 26.163574, 9.411929, 11.518031, 12.786044, 13.37753], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.038754225, 0.044866443, 0.01870774, -0.016704896, 0.043608956, -0.0010642997, 0.029211655], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 298, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.10118432, 0.0, 0.09007059, 0.0, 0.10480959, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.044866443, 2.0, -0.016704896, 5.0, -0.0010642997, 0.029211655], "split_indices": [0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.38972, 6.425578, 46.96414, 10.460895, 36.503246, 18.95314, 17.550108], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.031020176, -0.02079747, 0.14062072, -0.0, -0.034075867, 0.007148039, 0.060902767, 0.009013926, -0.034804095], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 299, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.37487143, 0.07636133, 0.12619069, 0.13760203, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 9.0, 5.0, 5.0, -0.034075867, 0.007148039, 0.060902767, 0.009013926, -0.034804095], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.382492, 42.287415, 20.095076, 35.91756, 6.3698564, 8.036038, 12.059037, 28.685045, 7.2325134], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.093697704, 0.042825967, -0.1396169, -0.27066144, -0.07108401, -0.10875548, -0.043774452, 0.002701729, -0.098341934], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 300, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.61150825, 0.0, 0.3642717, 0.0694406, 0.70090336, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.042825967, 3.0, 4.0, 6.0, -0.10875548, -0.043774452, 0.002701729, -0.098341934], "split_indices": [1, 0, 2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.51381, 8.005371, 45.50844, 14.113303, 31.395134, 6.568755, 7.544548, 24.368332, 7.026801], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.08342978, -0.034429006, 0.2547986, 0.03624333, -0.04565542, 0.11079536, 0.017765073, 0.055564348, -0.029820964], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 301, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.7784357, 0.20408556, 0.2835337, 0.3163157, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 6.0, 7.0, -0.04565542, 0.11079536, 0.017765073, 0.055564348, -0.029820964], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.016117, 21.467451, 14.548669, 13.317695, 8.1497555, 8.293161, 6.2555075, 6.5243, 6.7933946], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.10902986, 0.07339384, 0.07909266, 0.11658563, 0.015774118, 0.06303784, 0.01237358, -0.028330836, 0.054461144], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 302, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.17631626, 0.0, 0.09909713, 0.17097923, 0.3918627, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.07339384, 5.0, 6.0, 8.0, 0.06303784, 0.01237358, -0.028330836, 0.054461144], "split_indices": [0, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.16784, 8.075108, 47.09273, 28.058065, 19.034666, 11.126892, 16.931173, 11.329252, 7.705414], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.032704644, -0.10044836, 0.0936137, -0.12471043, 0.11183565, 0.06281297, 0.056972086, 0.059500173, -0.0, -0.005526373, 0.024233442], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 303, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.5093915, 1.3713701, 0.14548734, 0.0, 0.1213513, 0.0, 0.070521384, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [3.0, 4.0, 2.0, -0.12471043, 7.0, 0.06281297, 4.0, 0.059500173, -0.0, -0.005526373, 0.024233442], "split_indices": [0, 2, 2, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.7334, 18.34297, 42.39043, 7.185281, 11.157689, 8.402107, 33.988323, 5.950574, 5.2071157, 7.202791, 26.78553], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.02064897, -0.14254604, 0.017626174, -0.004165913, -0.07449078, 0.07755521, -0.024305481, -0.000937393, 0.06128506, 0.05622311, -0.022976482], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 304, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.3753222, 0.23143011, 0.15557082, 0.0, 0.0, 0.28172448, 0.38532013, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 2.0, 5.0, -0.004165913, -0.07449078, 3.0, 4.0, -0.000937393, 0.06128506, 0.05622311, -0.022976482], "split_indices": [0, 0, 2, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [76.22812, 18.188993, 58.039127, 9.142917, 9.046076, 24.52421, 33.514915, 15.098323, 9.425888, 5.746719, 27.768198], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.02202808, 0.106234826, -0.10386479, 0.06745759, -0.045742948, -0.0, -0.2077567, 0.019725002, -0.013684577, -0.08679306, -0.018019924], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 305, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.5037055, 0.60342985, 0.33523217, 0.0, 0.0, 0.050885886, 0.13048714, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [4.0, 8.0, 6.0, 0.06745759, -0.045742948, 3.0, 8.0, 0.019725002, -0.013684577, -0.08679306, -0.018019924], "split_indices": [2, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.958805, 17.3095, 28.649305, 12.2226, 5.0868993, 14.472231, 14.1770735, 6.4081, 8.064131, 8.079593, 6.097481], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.016745014, -0.07499043, 0.029564438, 0.11017319, -0.048520252, -0.03228097, 0.09026423, -0.06320153, 0.022346338], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 306, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.57448566, 0.0, 0.2799126, 0.96198326, 0.46262053, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.07499043, 6.0, 3.0, 8.0, -0.03228097, 0.09026423, -0.06320153, 0.022346338], "split_indices": [0, 0, 1, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.46605, 7.824088, 41.64196, 20.917759, 20.724201, 9.7244425, 11.1933155, 8.982969, 11.741232], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.02080559, 0.062992975, -0.025639407, 0.019234646, 0.060686108, -0.121839754, 0.06711713, 0.023974665, -0.012608993, -0.0, -0.07533692, -0.00071622815, 0.0407086], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 307, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.13604544, 0.19564497, 0.29322544, 0.11617529, 0.0, 0.24210617, 0.0902368, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 4.0, 3.0, 0.060686108, 2.0, 8.0, 0.023974665, -0.012608993, -0.0, -0.07533692, -0.00071622815, 0.0407086], "split_indices": [0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.70176, 35.37048, 30.33128, 28.327555, 7.0429273, 15.264547, 15.066733, 14.898669, 13.4288845, 8.596757, 6.6677904, 7.2379804, 7.8287525], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015343182, 0.0552797, -0.22091396, 0.09878637, 0.0063451296, -0.0, -0.1299704, 0.028686512, -0.02889257], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 308, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.691103, 0.53617364, 0.57027423, 0.0, 0.3529386, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 1.0, 4.0, 0.09878637, 4.0, -0.0, -0.1299704, 0.028686512, -0.02889257], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.13993, 41.646645, 10.493286, 5.132821, 36.513824, 5.4665008, 5.026785, 20.087038, 16.426785], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0048882724, -0.08639018, 0.15207931, -0.21142519, 0.104031205, 0.02001095, 0.06460193, -0.09214302, -0.027278643, 0.10180325, -0.04473642], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 309, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.82523334, 1.0607905, 0.07524586, 0.24034214, 1.1101737, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 7.0, 6.0, 4.0, 3.0, 0.02001095, 0.06460193, -0.09214302, -0.027278643, 0.10180325, -0.04473642], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.99234, 42.1787, 20.81364, 25.711073, 16.467625, 10.405539, 10.408103, 13.003027, 12.708046, 8.562796, 7.904829], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.041879524, 0.006962805, 0.07108575, 0.068265505, -0.08823333, 0.0014610691, 0.052125547, -0.087370895, 0.0036753754], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 310, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.34697822, 0.28365093, 0.0, 0.18424644, 0.41019586, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, 0.07108575, 5.0, 2.0, 0.0014610691, 0.052125547, -0.087370895, 0.0036753754], "split_indices": [2, 2, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.76475, 46.915516, 6.8492327, 29.349142, 17.566376, 19.57756, 9.771583, 5.517189, 12.049186], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.017112311, -0.05276394, 0.03541928, 0.008593684, -0.08574139, 0.05254459, -0.04284792, -0.0, -0.03848938], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 311, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.19564193, 0.07613366, 0.0, 0.3193348, 0.086679816, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 2.0, 0.03541928, 7.0, 5.0, 0.05254459, -0.04284792, -0.0, -0.03848938], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.65816, 32.449368, 7.208794, 10.54201, 21.907358, 5.2599835, 5.2820263, 7.410414, 14.4969425], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.05864564, 0.061876826, -0.116546415, -0.006714193, 0.11048291, -0.1406059, -0.0151134925, -0.0012325649, 0.07541223, 0.03912244, -0.048830673], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 312, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.41058576, 0.08744392, 1.352695, 0.0, 0.23103273, 0.0, 0.70906436, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [2.0, 1.0, 4.0, -0.006714193, 8.0, -0.1406059, 7.0, -0.0012325649, 0.07541223, 0.03912244, -0.048830673], "split_indices": [1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.02031, 17.596603, 38.423702, 5.882181, 11.714423, 7.562568, 30.861135, 6.6041336, 5.1102896, 15.2606125, 15.600523], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.022760948, -0.059224587, 0.05366285, 0.13695228, -0.02225421, 0.07032244, -0.054959353, -0.04697633, 0.060071528], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 313, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.40859056, 0.0, 0.35807332, 0.88038886, 0.88349307, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.059224587, 4.0, 7.0, 7.0, 0.07032244, -0.054959353, -0.04697633, 0.060071528], "split_indices": [1, 0, 1, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.60848, 6.274948, 53.33353, 25.676653, 27.656878, 20.129324, 5.5473285, 17.709902, 9.946977], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.09733104, -0.0053388765, -0.1265795, 0.008714045, -0.017856745, -0.17088272, -0.0, -0.06736592, 0.045271106, 0.04399445, -0.052466508], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 314, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.1839757, 0.041308157, 0.29277962, 0.0, 0.0, 0.73486936, 0.4323768, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 4.0, 8.0, 0.008714045, -0.017856745, 8.0, 5.0, -0.06736592, 0.045271106, 0.04399445, -0.052466508], "split_indices": [2, 1, 2, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [72.5305, 18.607565, 53.92294, 10.538208, 8.069356, 39.162292, 14.760649, 34.08948, 5.072809, 7.839325, 6.921324], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.03813869, -0.0411019, 0.06654557, 0.08670295, -0.0031602536, 0.061321493, 0.015126168], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 315, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.2429299, 0.0, 0.075059265, 0.11401805, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.0411019, 8.0, 2.0, -0.0031602536, 0.061321493, 0.015126168], "split_indices": [2, 0, 2, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.410698, 5.5916467, 41.819054, 34.079357, 7.7396975, 6.2886763, 27.79068], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0772779, 0.15944996, -0.0012616484, 0.029941749, 0.081200615, 0.029261433, -0.051468913, 0.04809772, -0.019710522, -0.037559126, -0.0014677305], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 316, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.33148998, 0.31499237, 0.1262263, 0.18046007, 0.0, 0.0, 0.048681036, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 3.0, 1.0, 0.081200615, 0.029261433, 6.0, 0.04809772, -0.019710522, -0.037559126, -0.0014677305], "split_indices": [1, 1, 2, 1, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.881615, 23.518461, 24.363153, 11.867614, 11.650848, 7.220307, 17.142845, 5.1780334, 6.68958, 5.152193, 11.990652], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.048210327, 0.07113297, -0.03258132, 0.03578204, 0.075197555, 0.02337435, -0.0027607118], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 317, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.2333362, 0.32304785, 0.0, 0.100669906, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 8.0, -0.03258132, 5.0, 0.075197555, 0.02337435, -0.0027607118], "split_indices": [0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.80611, 55.829258, 6.9768505, 48.11176, 7.7174997, 25.613283, 22.498476], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.030246891, 0.08226312, -0.05661367, -0.02761089, 0.1155336, 0.024631357, 0.073645346], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 318, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.58924204, 0.25958067, 0.0, 0.0, 0.10622525, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 1.0, -0.05661367, -0.02761089, 7.0, 0.024631357, 0.073645346], "split_indices": [1, 2, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.307484, 41.52399, 8.783492, 5.8908205, 35.63317, 30.252287, 5.3808846], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.015741015, 0.070141956, -0.05621023, 0.01565101, -0.14581479, 0.03497366, -0.063367724, -0.0674432, 0.02735219], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 319, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.60411805, 0.0, 0.3566783, 0.69535244, 0.48900217, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, 0.070141956, 5.0, 8.0, 9.0, 0.03497366, -0.063367724, -0.0674432, 0.02735219], "split_indices": [2, 0, 1, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.27684, 7.260098, 52.016743, 28.705091, 23.31165, 20.508606, 8.196485, 17.83877, 5.47288], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.024876598, 0.062147655, -0.029154947, 0.014116815, 0.10209534, -0.03539978, 0.041111622, 0.067423955, -0.02273438], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 320, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.23654102, 0.06491962, 0.0, 0.3550767, 0.48770964, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, -0.029154947, 4.0, 4.0, -0.03539978, 0.041111622, 0.067423955, -0.02273438], "split_indices": [2, 2, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.4376, 39.642757, 10.794839, 19.751421, 19.891338, 9.1468935, 10.604528, 11.950089, 7.9412475], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.006859781, -0.08159913, 0.09290314, -0.0, -0.060409244, 0.07141976, -0.014802264, -0.039982162, 0.039303835, -0.039923057, 0.02543025], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 321, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.30233636, 0.18473782, 0.34787154, 0.23237513, 0.0, 0.0, 0.16178407, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 7.0, 4.0, 4.0, -0.060409244, 0.07141976, 8.0, -0.039982162, 0.039303835, -0.039923057, 0.02543025], "split_indices": [0, 2, 2, 2, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.577522, 17.981085, 19.596436, 11.308068, 6.673018, 8.226745, 11.369691, 5.611368, 5.6966996, 5.5019574, 5.867733], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.12763461, 0.017125411, -0.15245783, -0.23187613, -0.04413315, -0.03725985, -0.087547734, 0.0038974492, -0.033130262], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 322, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.28112507, 0.0, 0.40340877, 0.108227015, 0.10004736, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.017125411, 6.0, 5.0, 8.0, -0.03725985, -0.087547734, 0.0038974492, -0.033130262], "split_indices": [2, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.331745, 6.0420837, 50.28966, 27.825594, 22.464067, 11.646644, 16.178951, 11.688821, 10.775247], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.027315065, 0.046188015, -0.03954186, 0.012793704, 0.056071922, 0.015800454, -0.03002366], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 323, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.17924424, 0.23795894, 0.0, 0.20945245, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 7.0, -0.03954186, 8.0, 0.056071922, 0.015800454, -0.03002366], "split_indices": [0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.510452, 54.39678, 5.1136756, 45.419655, 8.977122, 34.611717, 10.807937], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0011658687, -0.05680754, 0.085807376, -0.019323442, -0.05351299, 0.12236265, -0.009377012, -0.031621683, 0.015810287, -0.003477997, 0.055131655], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 324, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.37361604, 0.17796627, 0.14733396, 0.2308928, 0.0, 0.22325492, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 8.0, 5.0, -0.05351299, 3.0, -0.009377012, -0.031621683, 0.015810287, -0.003477997, 0.055131655], "split_indices": [1, 1, 2, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [73.55569, 43.149048, 30.406635, 34.494644, 8.654405, 23.787539, 6.619097, 16.13516, 18.359484, 7.2203, 16.567238], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.015098515, 0.100461245, -0.15155618, -0.01316196, 0.058462348, -0.05221566, -0.08598111, -0.039644767, 0.012104825], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 325, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.8028481, 0.39258623, 0.2670884, 0.0, 0.0, 0.12711145, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [5.0, 6.0, 6.0, -0.01316196, 0.058462348, 4.0, -0.08598111, -0.039644767, 0.012104825], "split_indices": [0, 1, 1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.623463, 26.073755, 22.549707, 10.103753, 15.970001, 14.218067, 8.331639, 7.9798455, 6.2382216], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.035323836, 0.18386015, -0.030372184, 0.13931, -6.69506e-05, -0.09578512, 0.053077836, -0.022655405, 0.04429982], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 326, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.48240963, 0.8267821, 0.84353244, 0.0, 0.0, 0.0, 0.33867314, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 3.0, 3.0, 0.13931, -6.69506e-05, -0.09578512, 5.0, -0.022655405, 0.04429982], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.29094, 14.110691, 32.18025, 5.202997, 8.907694, 6.8471317, 25.333117, 10.373879, 14.959237], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.028962098, -0.0, 0.07830208, 0.052148838, -0.07739996, -0.0019154189, 0.0379028], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 327, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.39230853, 0.7550032, 0.0, 0.22219157, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 9.0, 0.07830208, 4.0, -0.07739996, -0.0019154189, 0.0379028], "split_indices": [2, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.111053, 55.5876, 5.5234504, 47.214916, 8.372684, 26.150322, 21.064594], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.039443087, 0.07852248, -0.045041956, -0.0103271175, 0.10978922, -0.0400683, 0.022624226, 0.0058052344, 0.061543684], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 328, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.1842847, 0.14519884, 0.19522825, 0.0, 0.23767883, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 1.0, 8.0, -0.0103271175, 6.0, -0.0400683, 0.022624226, 0.0058052344, 0.061543684], "split_indices": [0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.73107, 36.911503, 15.819566, 7.2874556, 29.624048, 9.534426, 6.2851396, 16.35971, 13.264339], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.055780467, 0.015388861, -0.10776855, -0.05287067, 0.19074059, -0.0691148, -0.067120515, 0.07550036, 0.033627607, 0.034096062, -0.03898322], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 329, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.24341138, 0.92855614, 0.14045098, 0.0, 0.0055552125, 0.0, 0.35711297, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 3.0, -0.05287067, 5.0, -0.0691148, 5.0, 0.07550036, 0.033627607, 0.034096062, -0.03898322], "split_indices": [0, 0, 2, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.84262, 25.590652, 36.25197, 11.953042, 13.63761, 7.251141, 29.000828, 5.728493, 7.9091167, 6.8601036, 22.140724], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0031428824, 0.02707059, -0.10468184, 0.13010885, -0.022323137, -0.05002789, -0.009593015, 0.064786635, -0.0010362539, -0.039137833, 0.01821767], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 330, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.18200327, 0.23301461, 0.03697543, 0.19131364, 0.2782815, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 2.0, 9.0, 5.0, 5.0, -0.05002789, -0.009593015, 0.064786635, -0.0010362539, -0.039137833, 0.01821767], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.872562, 42.0954, 12.7771635, 13.767015, 28.328384, 5.4525514, 7.3246117, 8.460759, 5.3062572, 12.580614, 15.74777], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0055708545, -0.040092103, 0.03684699, 0.08421501, -0.012872379, 0.06192564, -0.027244866, -0.038155966, 0.052981436], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 331, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.16305314, 0.0, 0.08556299, 0.4149084, 0.36105388, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.040092103, 7.0, 7.0, 8.0, 0.06192564, -0.027244866, -0.038155966, 0.052981436], "split_indices": [0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.541267, 5.7000227, 31.841246, 16.980898, 14.860348, 10.2302885, 6.7506094, 9.802739, 5.057609], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.013581049, 0.04969192, -0.05495218, -0.05123782, 0.097123705, 0.08280408, 0.004731479], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 332, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.5903726, 0.41605628, 0.0, 0.0, 0.44709, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 2.0, -0.05495218, -0.05123782, 5.0, 0.08280408, 0.004731479], "split_indices": [1, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.92526, 37.901886, 14.023371, 5.876434, 32.02545, 8.878954, 23.146498], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.061872408, -0.050162844, -0.0031107618, -0.030518262, 0.059825346, -0.07239939, 0.010047624, 0.06734494, -0.032940116], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 333, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.36144245, 0.0, 0.06949968, 0.43752027, 0.36146054, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.050162844, 6.0, 4.0, 5.0, -0.07239939, 0.010047624, 0.06734494, -0.032940116], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.22909, 20.297005, 39.932087, 29.28362, 10.648466, 6.5392303, 22.74439, 5.5384016, 5.1100645], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.027878799, -0.13471837, 0.007659882, -0.0032099492, -0.07925268, 0.114268824, -0.077437244, 0.062681325, 0.017814308, -0.0074005118, -0.057153355], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 334, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.28574252, 0.26891303, 0.5024298, 0.0, 0.0, 0.090125114, 0.15340891, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 4.0, 6.0, -0.0032099492, -0.07925268, 3.0, 7.0, 0.062681325, 0.017814308, -0.0074005118, -0.057153355], "split_indices": [2, 1, 2, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [70.63793, 17.63752, 53.000416, 9.955435, 7.682085, 23.896017, 29.104399, 7.0712304, 16.824785, 21.277325, 7.827075], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.021712283, 0.13584988, -0.114440866, -0.012767937, 0.09387114, -0.20612618, 0.024107631, 0.013505416, -0.096604906], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 335, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.697561, 0.5901755, 0.5724532, 0.0, 0.0, 0.6625493, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 3.0, 7.0, -0.012767937, 0.09387114, 3.0, 0.024107631, 0.013505416, -0.096604906], "split_indices": [0, 0, 2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.82621, 16.406033, 29.420176, 8.26877, 8.137263, 20.314201, 9.105974, 6.334796, 13.979405], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.053978395, 0.017188918, -0.07449576, 0.06661621, -0.025443409, -0.05979526, 0.007917869], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 336, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.7125344, 0.34688127, 0.0, 0.0, 0.30168298, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [8.0, 1.0, -0.07449576, 0.06661621, 3.0, -0.05979526, 0.007917869], "split_indices": [1, 1, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.538525, 35.818745, 12.719779, 5.849009, 29.969736, 6.6724396, 23.297297], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0027111005, -0.020514172, 0.061722238, 0.037129227, -0.13002314, 0.027952924, -0.0012771038, 0.027640456, -0.06691972], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 337, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.32200184, 0.37655735, 0.0, 0.096958995, 0.45988032, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 6.0, 0.061722238, 3.0, 2.0, 0.027952924, -0.0012771038, 0.027640456, -0.06691972], "split_indices": [0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.46383, 56.416565, 6.047266, 36.795765, 19.620798, 16.124325, 20.67144, 5.4773936, 14.143405], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.047084395, 0.10897902, -0.06916891, 0.0921075, 0.0072072498, 0.009498173, -0.04979366, -0.050147057, 0.032227386], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 338, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.38431948, 0.6669569, 0.1948894, 0.0, 0.43692482, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 4.0, 0.0921075, 5.0, 0.009498173, -0.04979366, -0.050147057, 0.032227386], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.810555, 33.75941, 17.051144, 10.44135, 23.318062, 8.175847, 8.875297, 7.9027004, 15.415361], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.042564053, -0.1782354, 0.04286286, 0.042238217, -0.2901351, -0.049767043, 0.093446866, -0.00469111, -0.11812557, 0.07050586, 0.0071579735], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 339, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.69018453, 0.8750416, 0.38601595, 0.0, 0.45202875, 0.0, 0.26229477, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 3.0, 0.042238217, 1.0, -0.049767043, 6.0, -0.00469111, -0.11812557, 0.07050586, 0.0071579735], "split_indices": [0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.72291, 21.94886, 34.77405, 5.400368, 16.54849, 5.952505, 28.821548, 5.1147304, 11.433761, 8.241023, 20.580526], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0643441, -0.11542724, 0.02361006, -0.0016588117, -0.04508144, 0.054499064, -0.08867904, -0.0010476769, -0.043239422], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 340, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.24844971, 0.114525884, 0.37066942, 0.0, 0.0, 0.0, 0.03988768, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [6.0, 2.0, 8.0, -0.0016588117, -0.04508144, 0.054499064, 4.0, -0.0010476769, -0.043239422], "split_indices": [2, 2, 2, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.647247, 33.193375, 18.453875, 8.960564, 24.232811, 7.778259, 10.675616, 5.339429, 5.336187], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.015124702, -0.07892072, 0.064822726, -0.07150051, 0.01912182, 0.065091126, -0.020591196, 0.013969691, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 341, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.2032034, 0.37960884, 0.3742675, 0.0, 0.010501057, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [5.0, 6.0, 5.0, -0.07150051, 2.0, 0.065091126, -0.020591196, 0.013969691, -0.0], "split_indices": [1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.56179, 21.593996, 15.967796, 8.066742, 13.527254, 7.5192375, 8.448558, 6.2925506, 7.234703], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.05767153, -0.053337947, 0.09089411, 0.057694715, 0.05905014, -0.001853693, 0.042406943], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 342, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.45688125, 0.0, 0.14390007, 0.23484124, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, -0.053337947, 8.0, 6.0, 0.05905014, -0.001853693, 0.042406943], "split_indices": [1, 0, 1, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.44926, 6.0695834, 50.37968, 40.253548, 10.12613, 22.673637, 17.579912], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.052401893, -0.038084548, 0.10440414, 0.053761497, -0.054397047, 0.20226087, 0.052591544, -0.01116209, 0.051193986, 0.07668029, 0.028422264, 0.043891307, -0.02652673], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 343, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.30500013, 0.32329974, 0.1702328, 0.17149898, 0.0, 0.015707254, 0.39706075, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 3.0, 1.0, -0.054397047, 7.0, 8.0, -0.01116209, 0.051193986, 0.07668029, 0.028422264, 0.043891307, -0.02652673], "split_indices": [2, 2, 0, 2, 0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.563404, 21.86386, 39.699543, 13.277042, 8.586817, 12.069861, 27.629683, 7.368489, 5.9085536, 6.525362, 5.5444994, 17.00224, 10.627442], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.098361, -0.1056908, 0.05608934, 0.07146363, -0.13617559, -0.0, 0.03899665, -0.031152241, -0.02006233, -0.07975631], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 344, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.7821871, 0.19030258, 0.12127733, 0.39590126, 0.0, 0.20192212, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 9.0, 8.0, 6.0, 0.07146363, 8.0, -0.0, 0.03899665, -0.031152241, -0.02006233, -0.07975631], "split_indices": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [73.30156, 38.429005, 34.87256, 31.134377, 7.294626, 27.149221, 7.7233357, 21.850382, 9.283996, 19.184, 7.965223], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.025663678, -0.008218564, 0.062379498, -0.04734433, 0.03239164, 0.0019237818, 0.024116328], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 345, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.3244865, 0.27187163, 0.0, 0.0, 0.03222041, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 3.0, 0.062379498, -0.04734433, 6.0, 0.0019237818, 0.024116328], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.03232, 40.84568, 7.186637, 8.630362, 32.215317, 22.989056, 9.226263], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0025858919, -0.087750845, 0.112612404, 0.026832197, -0.17521353, 0.09016642, -0.0, -0.094274454, 0.0024322115, -0.04032135, 0.030314961], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 346, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.48168656, 0.47144967, 0.40780997, 0.0, 0.5438271, 0.0, 0.20217401, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [7.0, 2.0, 3.0, 0.026832197, 7.0, 0.09016642, 7.0, -0.094274454, 0.0024322115, -0.04032135, 0.030314961], "split_indices": [1, 1, 2, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.343464, 27.856075, 19.487389, 8.820526, 19.03555, 6.3906646, 13.0967245, 10.729416, 8.306134, 5.114506, 7.9822187], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0092622535, -0.1152546, 0.070549116, 0.008569892, -0.1748341, -0.020728357, 0.11258425, -0.09061175, -0.012788349, 0.049993306, 0.0039075757], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 347, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.5782515, 0.27360114, 0.23651388, 0.0, 0.3157459, 0.0, 0.14301002, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 3.0, 0.008569892, 7.0, -0.020728357, 7.0, -0.09061175, -0.012788349, 0.049993306, 0.0039075757], "split_indices": [0, 2, 2, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [65.86646, 28.640165, 37.226295, 8.050034, 20.590132, 7.9137073, 29.312588, 9.453372, 11.13676, 17.909533, 11.403055], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.02361378, -0.08546594, 0.06050773, 0.092586055, -0.014929161, -0.042981893, 0.048766732], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 348, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [1.2016035, 0.0, 0.15224278, 0.5606655, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.08546594, 8.0, 2.0, -0.014929161, -0.042981893, 0.048766732], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.866207, 12.34039, 39.525818, 31.445473, 8.080344, 6.627656, 24.817818], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.012385391, -0.0106101185, 0.0601831, 0.044772618, -0.047360092, -0.06651635, 0.0077872453], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 349, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.27680784, 0.30891564, 0.0, 0.0, 0.59363693, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 1.0, 0.0601831, 0.044772618, 4.0, -0.06651635, 0.0077872453], "split_indices": [2, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.071148, 52.116417, 5.9547305, 8.66107, 43.45535, 12.668179, 30.78717], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.05930583, 0.119824804, -0.009134898, 0.009433037, 0.070929244, -0.071154, 0.037064116, -0.010256797, 0.026968492, 0.0168349, -0.088923395], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 350, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.22648326, 0.33756438, 0.20463276, 0.06488445, 0.0, 0.5509805, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 9.0, 5.0, 0.070929244, 4.0, 0.037064116, -0.010256797, 0.026968492, 0.0168349, -0.088923395], "split_indices": [2, 2, 2, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.743504, 27.307278, 23.436228, 15.023193, 12.284083, 16.800589, 6.6356387, 9.308736, 5.714458, 10.931331, 5.8692575], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.016793182, -0.049034324, 0.0719351, 0.11067437, -0.0, 0.060133126, -0.0, -0.032872893, 0.029068455], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 351, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.39418402, 0.0, 0.088532224, 0.20850375, 0.1365056, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.049034324, 7.0, 7.0, 8.0, 0.060133126, -0.0, -0.032872893, 0.029068455], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.398766, 8.110515, 30.288254, 19.445915, 10.842338, 10.422807, 9.0231085, 5.085458, 5.7568803], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.010675662, 0.04942132, -0.05157785, -0.0841409, 0.009669744, -0.013081736, 0.08271878], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 352, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.38606736, 0.0, 0.6631297, 0.0, 0.5492323, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.04942132, 4.0, -0.0841409, 8.0, -0.013081736, 0.08271878], "split_indices": [0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.201786, 9.024935, 44.176853, 8.914694, 35.262157, 29.762741, 5.4994164], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.016815446, 0.08467062, -0.015205413, 0.040990006, 0.03992481, -0.12156492, 0.06598637, 0.03881507, -0.0032596572, -6.478863e-05, -0.08046891, -0.0, 0.07764053], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 353, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.15145724, 0.024267077, 0.39898396, 0.07856115, 0.0, 0.3327092, 0.33681625, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 6.0, 5.0, 1.0, 0.03992481, 6.0, 7.0, 0.03881507, -0.0032596572, -6.478863e-05, -0.08046891, -0.0, 0.07764053], "split_indices": [0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.91704, 21.286884, 43.630154, 13.246919, 8.039965, 19.20561, 24.424541, 5.058129, 8.18879, 11.431009, 7.7746005, 18.742983, 5.6815586], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.0073197708, 0.11566545, -0.08835833, 0.066744246, 0.06519819, -0.18236038, 0.07846547, 0.061906982, -0.05637293, -0.0029114534, -0.11183585, 0.04689124, -0.015145569], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 354, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.70978355, 0.107298166, 0.7073789, 0.0, 0.7916181, 0.8960816, 0.17912331, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 3.0, 7.0, 0.066744246, 2.0, 4.0, 9.0, 0.061906982, -0.05637293, -0.0029114534, -0.11183585, 0.04689124, -0.015145569], "split_indices": [1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [69.465164, 26.93181, 42.53335, 6.912152, 20.019659, 27.528095, 15.005257, 13.224274, 6.795386, 15.26432, 12.263775, 9.708357, 5.2969], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.029327834, -0.09648285, 0.013064825, 0.03892989, -0.20320554, 0.11840786, -0.10890676, -0.1029086, 0.0013679652, -0.017171368, 0.09227943, 0.007906268, -0.08775378], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 355, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.14389268, 0.49939913, 0.3849815, 0.0, 0.43615562, 0.58821607, 0.3687704, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 8.0, 0.03892989, 8.0, 6.0, 4.0, -0.1029086, 0.0013679652, -0.017171368, 0.09227943, 0.007906268, -0.08775378], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.171066, 18.286324, 27.884745, 5.4915214, 12.794802, 15.417883, 12.466862, 7.546435, 5.2483664, 8.082714, 7.3351684, 7.3580565, 5.108805], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.013294541, 0.081660695, -0.08903031, -0.021945192, 0.13126339, -0.0, -0.050619688, 0.011621868, 0.061492253], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 356, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.34566912, 0.24708432, 0.15116905, 0.0, 0.12929118, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 2.0, 5.0, -0.021945192, 5.0, -0.0, -0.050619688, 0.011621868, 0.061492253], "split_indices": [2, 2, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.47875, 29.169199, 18.309547, 6.473738, 22.695461, 8.623987, 9.68556, 11.341505, 11.353956], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.02948893, -0.05538165, 0.053821366, 0.0021064274, -0.10993696, -0.017274465, 0.068369836, 0.02410568, -0.05163673], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 357, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.3626564, 0.2031135, 0.0, 0.43328705, 0.4056726, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, 0.053821366, 4.0, 3.0, -0.017274465, 0.068369836, 0.02410568, -0.05163673], "split_indices": [2, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.50511, 60.250546, 6.254569, 28.912338, 31.338207, 23.106321, 5.8060174, 7.2506833, 24.087524], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.040426705, -0.011907504, -0.059139717, -0.07019125, 0.039783478, 0.0010693156, -0.09011438, 0.036417052, -0.024560494], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 358, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.21880251, 0.14871657, 0.0, 0.43079454, 0.26072663, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, -0.059139717, 3.0, 7.0, 0.0010693156, -0.09011438, 0.036417052, -0.024560494], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.16141, 46.426315, 6.7350993, 22.562653, 23.86366, 17.50419, 5.0584607, 14.77586, 9.087802], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0014253862, -0.046649802, 0.042150415, 0.069039114, 0.009831192, -0.03826838, 0.019603316], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 359, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.3688963, 0.0, 0.2636351, 0.0, 0.324088, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.046649802, 1.0, 0.069039114, 4.0, -0.03826838, 0.019603316], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.471386, 10.755135, 46.71625, 5.5017085, 41.214542, 10.968591, 30.245953], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.025903437, 0.055561393, -0.06629723, -0.093047656, 0.015800081, -0.03825393, 0.024063746], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 360, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.4629155, 0.0, 0.15743421, 0.2487045, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.055561393, 9.0, 9.0, 0.015800081, -0.03825393, 0.024063746], "split_indices": [0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.239826, 7.5774693, 45.662357, 38.143745, 7.518609, 32.51664, 5.6271048], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.030919854, -0.020157361, 0.047282554, -0.05562303, 0.02352178, -0.03345366, 0.016071906], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 361, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.26660275, 0.09933988, 0.0, 0.14475876, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, 0.047282554, 6.0, 0.02352178, -0.03345366, 0.016071906], "split_indices": [2, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.585125, 26.822586, 10.76254, 20.791496, 6.03109, 14.344308, 6.447188], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.017030196, 0.054375447, -0.15056278, -0.065887734, 0.123712264, -0.07687591, -0.0015382152, 0.009442851, -0.052809514, 0.051062346, 0.006838135], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 362, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.53960365, 0.31332415, 0.27721423, 0.16214508, 0.08778629, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, 6.0, 5.0, 5.0, -0.07687591, -0.0015382152, 0.009442851, -0.052809514, 0.051062346, 0.006838135], "split_indices": [0, 0, 2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.7246, 34.903896, 18.820707, 12.255137, 22.648758, 10.060221, 8.760488, 6.402154, 5.852984, 14.356767, 8.291991], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.03006694, 0.05195858, -0.07035853, -0.01426889, 0.0649249, 0.04352572, -0.14871295, -0.07676513, -0.0012040286], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 363, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.21281433, 0.3614124, 0.7530361, 0.0, 0.0, 0.0, 0.47841877, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 5.0, 2.0, -0.01426889, 0.0649249, 0.04352572, 6.0, -0.07676513, -0.0012040286], "split_indices": [2, 1, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.729942, 19.421082, 42.30886, 12.163235, 7.2578464, 10.716028, 31.592834, 17.31162, 14.281214], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.020364964, -0.11285093, 0.01709575, -0.05061928, -0.054963946, 0.05191493, -0.019202543, -0.0, -0.02546716, -0.041273475, 0.008879809], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 364, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.2616748, 0.062435895, 0.31018966, 0.015814438, 0.0, 0.0, 0.25836292, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [2.0, 6.0, 3.0, 1.0, -0.054963946, 0.05191493, 4.0, -0.0, -0.02546716, -0.041273475, 0.008879809], "split_indices": [1, 2, 1, 1, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [71.207664, 20.716957, 50.490707, 12.660258, 8.0567, 9.341761, 41.14895, 6.608712, 6.0515466, 12.079193, 29.069754], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0064576934, -0.13895951, 0.06029619, -0.10231477, 0.016841602, 0.20337802, -0.014043842, 0.11822528, -0.0, -0.074561425, 0.028464066], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 365, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.38318056, 0.5604874, 0.4139707, 0.0, 0.0, 0.4721483, 0.67084193, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 4.0, 4.0, -0.10231477, 0.016841602, 6.0, 6.0, 0.11822528, -0.0, -0.074561425, 0.028464066], "split_indices": [2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.878017, 12.007483, 35.870533, 5.782369, 6.225113, 12.131157, 23.739376, 5.537487, 6.59367, 7.4142094, 16.325167], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.11725784, -0.09962469, -0.07799897, 0.052839056, -0.13549022, -0.0054531395, 0.036659334, -0.014365198, -0.09333207], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 366, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.35310107, 0.0, 0.34494114, 0.07942097, 0.4299193, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.09962469, 3.0, 2.0, 8.0, -0.0054531395, 0.036659334, -0.014365198, -0.09333207], "split_indices": [1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.80115, 5.99431, 42.80684, 12.561357, 30.245485, 5.858748, 6.7026086, 21.408136, 8.837349], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.08600545, 0.024273595, -0.11509701, -0.104018085, -0.07487221, 0.017190449, -0.030421898], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 367, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.33031815, 0.0, 0.45940888, 0.0, 0.18517059, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.024273595, 2.0, -0.104018085, 3.0, 0.017190449, -0.030421898], "split_indices": [1, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.40244, 8.784021, 55.618423, 6.73425, 48.884174, 7.3489165, 41.535255], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.02989982, -0.07551862, 0.01645937, -0.025618035, 0.068518735, 0.041519765, -0.026849668], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 368, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.5913997, 0.0, 0.4355171, 0.41411123, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, -0.07551862, 8.0, 2.0, 0.068518735, 0.041519765, -0.026849668], "split_indices": [2, 0, 1, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.81719, 8.909984, 44.907204, 37.80814, 7.0990644, 9.832017, 27.976124], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.028960804, -0.028590702, 0.060153935, 0.036903314, 0.02931881, -0.009363028, 0.02505605], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 369, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.21916252, 0.0, 0.070307955, 0.0, 0.11688215, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.028590702, 4.0, 0.036903314, 5.0, -0.009363028, 0.02505605], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.96899, 9.97416, 44.994827, 12.757145, 32.237686, 14.558647, 17.679037], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.025900299, -0.06429672, 0.05197802, -0.22727269, 0.033169914, -0.041112863, -0.095087044, 0.020488339, -0.020895468], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 370, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.4036628, 0.74777186, 0.0, 0.052723765, 0.10894057, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 5.0, 0.05197802, 5.0, 9.0, -0.041112863, -0.095087044, 0.020488339, -0.020895468], "split_indices": [2, 0, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.754894, 44.378387, 7.376507, 16.493484, 27.884901, 10.037745, 6.4557395, 21.759916, 6.124986], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.013269113, -0.080100805, 0.11086061, 0.06013395, -0.20384184, -0.06182229, 0.12981953, -0.033977166, -0.09568162], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 371, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.33825445, 0.98384416, 1.53194, 0.0, 0.124860644, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [8.0, 2.0, 5.0, 0.06013395, 5.0, -0.06182229, 0.12981953, -0.033977166, -0.09568162], "split_indices": [2, 1, 1, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [39.074547, 26.153667, 12.920878, 7.5678077, 18.58586, 6.5350895, 6.385789, 11.996843, 6.5890164], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.018711276, 0.084840804, -0.012804012, -0.065240294, 0.05727569, -0.0028486364, 0.12370032], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 372, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.49618408, 0.0, 0.75410813, 0.0, 0.94781953, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [1.0, 0.084840804, 4.0, -0.065240294, 9.0, -0.0028486364, 0.12370032], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [54.93751, 5.345225, 49.592285, 12.47719, 37.115097, 31.822311, 5.292784], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0022352298, 0.01896406, -0.025618201, 0.046700813, -0.046634976, 0.041817907, 0.0081559485, -0.02931024, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 373, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.09112404, 0.10562432, 0.0, 0.05466795, 0.038461972, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 7.0, -0.025618201, 1.0, 4.0, 0.041817907, 0.0081559485, -0.02931024, -0.0], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.58794, 55.850563, 8.737373, 40.527233, 15.32333, 5.0886755, 35.438557, 6.748438, 8.574892], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.032962956, -0.0, 0.06333742, 0.045507457, -0.14176483, 0.033165485, -0.01301974, -0.0923368, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 374, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.4056303, 0.38407716, 0.0, 0.2685427, 0.3262913, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 7.0, 0.06333742, 6.0, 4.0, 0.033165485, -0.01301974, -0.0923368, -0.0], "split_indices": [0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.72896, 56.977726, 9.751231, 43.44685, 13.530876, 25.599527, 17.847324, 5.5269127, 8.003963], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.021749882, -0.038602415, 0.076295264, 0.13375686, -0.03556839, 0.008424812, 0.047972996], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 375, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.40552637, 0.0, 0.431412, 0.05802679, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [3.0, -0.038602415, 8.0, 3.0, -0.03556839, 0.008424812, 0.047972996], "split_indices": [2, 0, 2, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.866375, 11.888377, 35.978, 28.42304, 7.5549593, 6.8088117, 21.614227], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.07764999, 0.12225597, -0.0076358784, 0.16692387, 0.06813905, -0.020715257, 0.02231203, 0.015082691, 0.077211484, -0.0034392755, 0.05132686], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 376, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.20282078, 0.046569884, 0.09204336, 0.13309011, 0.1649294, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 8.0, 2.0, 7.0, -0.020715257, 0.02231203, 0.015082691, 0.077211484, -0.0034392755, 0.05132686], "split_indices": [0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.055782, 32.69485, 16.360931, 15.756853, 16.937998, 10.142683, 6.2182484, 8.044074, 7.71278, 9.50548, 7.432518], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.026219426, -0.016791636, 0.05138037, 0.03986516, -0.04830358, -0.030057115, 0.024628872], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 377, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.4004205, 0.4076203, 0.0, 0.2104195, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 5.0, 0.05138037, 3.0, -0.04830358, -0.030057115, 0.024628872], "split_indices": [1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.24636, 46.61768, 13.628679, 33.490982, 13.126699, 6.8924475, 26.598534], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.017238438, -0.064938456, 0.08769393, 0.01733419, -0.12766665, -0.0, 0.057336684, -0.102627195, -0.012775396], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 378, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.26454654, 0.29992872, 0.13783282, 0.0, 0.41033444, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 5.0, 0.01733419, 3.0, -0.0, 0.057336684, -0.102627195, -0.012775396], "split_indices": [0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.170425, 36.096058, 15.074366, 11.697061, 24.399, 8.916835, 6.157531, 5.7391405, 18.659859], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04306264, 0.05496384, 0.023006178, 0.059353102, -0.090036325, -0.054946642, 0.031544194, -0.05837027, 0.0011874256], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 379, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.14952184, 0.0, 0.23249471, 0.49644417, 0.15110314, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.05496384, 8.0, 3.0, 9.0, -0.054946642, 0.031544194, -0.05837027, 0.0011874256], "split_indices": [0, 0, 2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.029167, 5.9977093, 55.031456, 42.67091, 12.360546, 5.95457, 36.716343, 5.849365, 6.5111804], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.009450689, 0.041374806, -0.016112328, -0.060894992, 0.021693954, 0.028537119, -0.02032507], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 380, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.18108991, 0.0, 0.3233574, 0.0, 0.2447953, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, 0.041374806, 4.0, -0.060894992, 4.0, 0.028537119, -0.02032507], "split_indices": [0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.758026, 8.177795, 41.58023, 6.706584, 34.87365, 19.730383, 15.143266], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.04886579, -0.10211548, 0.06610379, -0.013945559, -0.09266716, -0.04132863, 0.083189875, -0.045902163, 0.01244632], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 381, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.23631345, 0.44130862, 0.5557413, 0.16984864, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [8.0, 6.0, 5.0, 2.0, -0.09266716, -0.04132863, 0.083189875, -0.045902163, 0.01244632], "split_indices": [2, 1, 1, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.90928, 25.213099, 10.69618, 18.763365, 6.4497333, 5.3785305, 5.31765, 5.360248, 13.403116], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.043602284, 0.0842842, -0.089057565, -0.015131678, 0.14476624, -0.0, -0.053174466, -0.032470256, 0.029234888, 0.07911138, 0.024589764], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 382, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.3128944, 0.2825124, 0.11981061, 0.19084838, 0.15569097, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 4.0, 2.0, 6.0, 6.0, -0.0, -0.053174466, -0.032470256, 0.029234888, 0.07911138, 0.024589764], "split_indices": [0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.674767, 43.46432, 12.210447, 16.098778, 27.365541, 6.061882, 6.148566, 9.319006, 6.7797728, 7.800549, 19.564993], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.013463664, 0.0299331, -0.03229242, -0.012204392, -0.03827272, -0.020938156, 0.012065376], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 383, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.12649567, 0.0, 0.08744036, 0.14585929, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [1.0, 0.0299331, 8.0, 4.0, -0.03827272, -0.020938156, 0.012065376], "split_indices": [1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.9808, 7.18119, 52.799614, 45.404774, 7.394838, 22.387165, 23.01761], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.043237306, -0.016187182, 0.058354, -0.0026151584, 0.14451864, -0.015665848, 0.030010233, 0.106167, 0.020280827], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 384, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.110838056, 0.0, 0.35363024, 0.19443908, 0.37585413, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.016187182, 6.0, 7.0, 4.0, -0.015665848, 0.030010233, 0.106167, 0.020280827], "split_indices": [2, 0, 1, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [71.99576, 8.454158, 63.5416, 37.099487, 26.442112, 26.00047, 11.09902, 5.78735, 20.65476], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.07268866, 0.019812893, 0.13683118, 0.108102575, -0.040347993, 0.19309926, 0.0023035074, 0.06816254, -0.050710436, 0.017518768, 0.096223526], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 385, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.14972028, 0.40958995, 0.1337494, 0.6890154, 0.0, 0.18932551, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 8.0, 8.0, -0.040347993, 7.0, 0.0023035074, 0.06816254, -0.050710436, 0.017518768, 0.096223526], "split_indices": [2, 2, 2, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.562565, 28.191156, 20.37141, 18.534008, 9.657148, 13.30529, 7.0661206, 13.341288, 5.19272, 7.563989, 5.7413006], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.08659686, -0.014080762, 0.126072, 0.041351896, -0.055088427, 0.0081014, 0.15702473, 0.06352775, 0.022989476], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 386, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.20766512, 0.39008197, 0.087759554, 0.0, 0.0, 0.0, 0.071347415, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 1.0, 3.0, 0.041351896, -0.055088427, 0.0081014, 6.0, 0.06352775, 0.022989476], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.98789, 12.983853, 35.004036, 6.635457, 6.348397, 9.54797, 25.456066, 13.432692, 12.023375], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.017480327, -0.09462268, 0.039714463, -0.0, -0.13987274, 0.08409551, -0.047725167, 0.015288772, -0.07163539, -0.0047735353, 0.055596042, -0.0, -0.022550901], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 387, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.29239458, 0.13465655, 0.15050423, 0.0, 0.40587527, 0.27842277, 0.012116179, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 7.0, -0.0, 2.0, 4.0, 8.0, 0.015288772, -0.07163539, -0.0047735353, 0.055596042, -0.0, -0.022550901], "split_indices": [0, 2, 2, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.174267, 27.362583, 35.811684, 8.499985, 18.862597, 24.538673, 11.273011, 6.2595396, 12.603058, 12.270574, 12.268101, 5.181521, 6.0914907], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.045589034, -0.089638665, 0.04525794, -0.026411163, -0.10551418, -0.0, 0.019964546, 0.026805377, -0.017968759], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 388, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.22805746, 0.583017, 0.020309307, 0.12654126, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 3.0, 1.0, -0.10551418, -0.0, 0.019964546, 0.026805377, -0.017968759], "split_indices": [1, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.866272, 37.026848, 16.839424, 31.024351, 6.0024967, 5.002821, 11.836603, 5.9161916, 25.10816], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.008055033, 0.09016175, -0.073443666, -0.00091510324, 0.13580994, 0.012920027, -0.2110427, 0.11304075, 0.008074883, -0.014106356, 0.04634973, -0.015098632, -0.10236197], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 389, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.43273923, 0.15264511, 0.40011036, 0.0, 0.54423976, 0.1878798, 0.21052325, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 5.0, -0.00091510324, 3.0, 4.0, 8.0, 0.11304075, 0.008074883, -0.014106356, 0.04634973, -0.015098632, -0.10236197], "split_indices": [0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.429707, 31.657124, 30.772585, 9.993415, 21.66371, 19.016365, 11.75622, 5.6907983, 15.972911, 13.333035, 5.683329, 6.2250457, 5.5311737], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.050573055, -0.12220683, 0.09775808, -0.22308993, -0.0155791305, 0.06695062, -0.0, -0.01663346, -0.10301409, 0.017859498, -0.052389737], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 390, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5571916, 0.35288733, 0.2056864, 0.29612255, 0.2458028, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 5.0, 6.0, 2.0, 3.0, 0.06695062, -0.0, -0.01663346, -0.10301409, 0.017859498, -0.052389737], "split_indices": [1, 2, 1, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.084564, 34.34741, 15.737157, 16.605991, 17.741415, 6.3414845, 9.395672, 7.895088, 8.710904, 12.048614, 5.6928024], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.039125226, -0.071916744, 0.01989025, 0.060074802, -0.013704627, 0.048050147, -0.007684628, -0.03732234, 0.05065748], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 391, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.47941107, 0.0, 0.046072114, 0.14826323, 0.3367692, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [4.0, -0.071916744, 7.0, 6.0, 8.0, 0.048050147, -0.007684628, -0.03732234, 0.05065748], "split_indices": [0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.23675, 8.153829, 29.082922, 14.239162, 14.84376, 6.7050433, 7.5341187, 9.779771, 5.063989], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.051828977, 0.08622439, -0.020537965, 0.13601029, 0.036408905, 0.052869163, 0.0072279745, -0.006940766, 0.050538477], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 392, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.24505481, 0.091159225, 0.0, 0.07245383, 0.21669851, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 5.0, -0.020537965, 5.0, 6.0, 0.052869163, 0.0072279745, -0.006940766, 0.050538477], "split_indices": [0, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.373302, 44.7049, 11.668404, 20.489689, 24.21521, 13.992836, 6.496854, 16.73613, 7.47908], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.08766573, 0.012408223, -0.22656322, 0.04999455, -0.060757164, -0.2752531, -0.012793568, 0.021669637, -0.05323902, -0.042798687, -0.10242429], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 393, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.8439827, 0.41352174, 0.18184435, 0.0, 0.39431417, 0.07206988, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 8.0, 0.04999455, 2.0, 3.0, -0.012793568, 0.021669637, -0.05323902, -0.042798687, -0.10242429], "split_indices": [1, 2, 2, 0, 1, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.975163, 33.807777, 24.167383, 10.905956, 22.901823, 18.28606, 5.881323, 10.481805, 12.420018, 7.6409354, 10.645124], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.021126367, -0.10098216, 0.0061283526, 0.051854108, -0.10811735, -0.07980279, 0.11943157, 0.04401314, -0.01294162, 0.0375043, -0.045730613, 0.07212882, 0.015252511], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 394, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.16538227, 0.808577, 0.53146297, 0.13092948, 0.0, 0.47332674, 0.15656862, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [2.0, 6.0, 6.0, 4.0, -0.10811735, 3.0, 7.0, 0.04401314, -0.01294162, 0.0375043, -0.045730613, 0.07212882, 0.015252511], "split_indices": [1, 2, 2, 2, 0, 1, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [70.34473, 18.144794, 52.199932, 11.678673, 6.466121, 29.351969, 22.847961, 6.097854, 5.580819, 7.1347265, 22.217241, 6.8321905, 16.015772], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.021060197, -0.11327879, 0.044764914, -0.08401299, -0.0, -0.0038493595, 0.061653715, 0.06192321, -0.06001923, 0.01822057, -0.050065625], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 395, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.2985558, 0.40491265, 0.2369249, 0.0, 0.5806366, 0.24948667, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 8.0, -0.08401299, 3.0, 5.0, 0.061653715, 0.06192321, -0.06001923, 0.01822057, -0.050065625], "split_indices": [2, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.21901, 19.582668, 26.63634, 7.530923, 12.051746, 20.72309, 5.9132495, 6.057505, 5.994241, 14.853273, 5.869817], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.10034235, -0.17949243, -0.008454235, -0.08494214, -0.102548204, 0.03949263, -0.06240343, -0.057249792, 0.000635775, 0.036570173, -0.0475916], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 396, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.32868943, 0.13293713, 0.18147825, 0.0, 0.17224003, 0.0, 0.34946913, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 7.0, -0.08494214, 3.0, 0.03949263, 4.0, -0.057249792, 0.000635775, 0.036570173, -0.0475916], "split_indices": [2, 1, 2, 0, 2, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.986237, 24.145906, 22.84033, 8.592239, 15.553668, 5.434418, 17.40591, 8.508845, 7.0448227, 5.522923, 11.882988], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.056567505, -0.0017041933, 0.15621534, -0.04888255, 0.041260943, 0.076946214, 0.09879799, 0.035014585, -0.012180676, -0.017559826, 0.056211296], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 397, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.38949662, 0.30274448, 0.079591155, 0.0, 0.21409714, 0.0, 0.2715379, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [7.0, 2.0, 2.0, -0.04888255, 4.0, 0.076946214, 8.0, 0.035014585, -0.012180676, -0.017559826, 0.056211296], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.370926, 39.974277, 23.396648, 8.312932, 31.661346, 6.6225357, 16.774113, 16.889708, 14.771639, 5.75743, 11.016683], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.023673479, -0.06055507, 0.12239218, 0.058317553, -0.1360942, 0.07505579, 0.030789841, 0.010831301, -0.11554494, 0.04017304, -0.038618945], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 398, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.45692432, 0.57705075, 0.2618267, 0.0, 1.0429014, 0.0, 0.28762257, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 6.0, 0.058317553, 2.0, 0.07505579, 5.0, 0.010831301, -0.11554494, 0.04017304, -0.038618945], "split_indices": [2, 0, 2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.317226, 27.892477, 24.424747, 5.713164, 22.179314, 9.019212, 15.405536, 13.381075, 8.798239, 9.835119, 5.570416], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.005796928, 0.06018298, -0.037618317, 0.038952947, -0.020705475, -0.13561547, 0.04958128, -0.021206807, -0.059988838, 0.056189343, -0.008188128], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 399, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.13037914, 0.19113848, 0.38729382, 0.0, 0.0, 0.047800124, 0.26700097, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 7.0, 0.038952947, -0.020705475, 6.0, 3.0, -0.021206807, -0.059988838, 0.056189343, -0.008188128], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.094368, 18.565664, 42.528706, 12.561839, 6.0038257, 20.30311, 22.225595, 11.924237, 8.378872, 7.9135017, 14.312093], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.09098187, 0.14230944, -0.06572473, 0.23719111, 0.053577114, 0.09216458, 0.04426448, -0.0, 0.053474393], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 400, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.80535614, 0.32051253, 0.0, 0.034483433, 0.17462388, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 3.0, -0.06572473, 5.0, 7.0, 0.09216458, 0.04426448, -0.0, 0.053474393], "split_indices": [0, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.127453, 42.032932, 6.0945225, 18.959455, 23.073477, 8.65556, 10.303896, 16.402433, 6.6710424], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.05967783, -0.039085343, 0.11095705, 0.05796011, 0.066047035, 0.03387947, -0.0046526175], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 401, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.39965498, 0.0, 0.080427766, 0.0, 0.100068256, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [2.0, -0.039085343, 5.0, 0.05796011, 7.0, 0.03387947, -0.0046526175], "split_indices": [2, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.742023, 7.493136, 31.248886, 9.0842905, 22.164597, 14.608148, 7.556449], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0051322677, 0.08030411, -0.14216964, 0.1708562, 0.0066661914, -0.06278746, -0.0026136003, 0.08455175, -0.008600043, 0.017597578, -0.0025913029], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 402, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.63303995, 0.23868628, 0.14719555, 0.40264064, 0.023772582, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 5.0, 9.0, 5.0, 2.0, -0.06278746, -0.0026136003, 0.08455175, -0.008600043, 0.017597578, -0.0025913029], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.69757, 37.628513, 18.06906, 15.6749325, 21.953583, 11.145803, 6.9232545, 10.129252, 5.5456796, 5.8342013, 16.11938], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.060995728, -0.10587502, -0.015743915, 0.10903757, -0.12594883, 0.015516686, 0.05702123, -0.061116148, -0.0065191756], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 403, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.73702544, 0.0, 0.73640174, 0.08070168, 0.20589218, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.10587502, 4.0, 6.0, 8.0, 0.015516686, 0.05702123, -0.061116148, -0.0065191756], "split_indices": [1, 0, 1, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.004585, 6.575701, 51.428883, 23.698746, 27.730137, 15.563168, 8.135578, 14.783699, 12.946437], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.049461287, -0.02568367, 0.14264353, 0.069692194, -0.07692831, 0.2109116, 0.07656449, 0.003274859, -0.032196846, 0.020955393, 0.11376645, -0.049848493, 0.05869286], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 404, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5268851, 0.5403281, 0.105588794, 0.0, 0.10432768, 0.28694886, 0.5922936, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 4.0, 0.069692194, 1.0, 3.0, 6.0, 0.003274859, -0.032196846, 0.020955393, 0.11376645, -0.049848493, 0.05869286], "split_indices": [1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [72.05237, 39.693523, 32.358845, 5.668874, 34.02465, 14.09341, 18.265434, 8.065363, 25.959286, 8.760448, 5.332962, 5.605057, 12.660377], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.09253824, -0.13590685, 0.027013881, -0.098378435, -0.081704974, -0.0058996934, -0.051042378], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 405, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.3986215, 0.34650856, 0.0, 0.0, 0.15751596, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 3.0, 0.027013881, -0.098378435, 8.0, -0.0058996934, -0.051042378], "split_indices": [0, 1, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.216286, 38.802917, 8.413368, 7.023972, 31.778944, 20.059818, 11.719126], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.038695607, -0.016920105, 0.12514122, 0.030471263, -0.051331118, -0.0027675054, 0.05410749, -0.028395858, 0.015032415], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 406, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.25250846, 0.12454591, 0.16961893, 0.0, 0.118855394, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 1.0, 3.0, 0.030471263, 7.0, -0.0027675054, 0.05410749, -0.028395858, 0.015032415], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.8299, 29.498922, 19.330976, 5.5455647, 23.953358, 5.345364, 13.985612, 17.522652, 6.430706], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.008350222, 0.06209696, -0.14377955, 0.02045833, 0.05860917, -0.023392636, -0.0631085, 0.027585622, -0.03990188], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 407, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.5277479, 0.2452694, 0.027677447, 0.4346925, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [8.0, 8.0, 7.0, 6.0, 0.05860917, -0.023392636, -0.0631085, 0.027585622, -0.03990188], "split_indices": [0, 2, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.451694, 47.809845, 15.641847, 37.878708, 9.931138, 9.71419, 5.927657, 26.558487, 11.320221], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.044987757, 0.06298404, -0.13279963, 0.052129712, 0.0018073245, -0.09492336, -0.07496941, -0.017977586, 0.034734417, 0.0069316053, -0.076830715], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 408, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, -1, -1, -1], "loss_changes": [0.4877997, 0.13473406, 0.2377797, 0.0, 0.12579134, 0.0, 0.4423566, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 1.0, 2.0, 0.052129712, 6.0, -0.09492336, 8.0, -0.017977586, 0.034734417, 0.0069316053, -0.076830715], "split_indices": [1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.830574, 21.517277, 27.313295, 6.418806, 15.098472, 5.056923, 22.256372, 9.532268, 5.5662045, 14.646408, 7.6099644], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.03599154, -0.0023529953, 0.11923162, -0.12197875, 0.049721204, 0.072959915, -0.029939093, -0.012745966, -0.057238996, 0.029931283, -0.011711679], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 409, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.20579684, 0.2732658, 0.58148503, 0.042576537, 0.13970597, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 2.0, 5.0, 4.0, 8.0, 0.072959915, -0.029939093, -0.012745966, -0.057238996, 0.029931283, -0.011711679], "split_indices": [0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.726955, 40.733475, 18.99348, 12.529173, 28.2043, 12.349812, 6.6436696, 7.2384534, 5.2907195, 18.675953, 9.528348], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0024696528, -0.0977491, 0.096431464, -0.025074959, -0.06729819, 0.029034771, 0.06638092, 0.048585996, -0.041768756, 0.058637053, -0.019245965], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 410, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.49968562, 0.22243342, 0.18317549, 0.42804983, 0.0, 0.308504, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [3.0, 2.0, 7.0, 4.0, -0.06729819, 3.0, 0.06638092, 0.048585996, -0.041768756, 0.058637053, -0.019245965], "split_indices": [1, 1, 2, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.012638, 26.502956, 24.509682, 18.09941, 8.403547, 17.237339, 7.2723427, 6.3693104, 11.730099, 6.178667, 11.058672], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.018001001, -0.019693397, 0.04731246, 0.011187188, -0.024202725, 0.032557238, -0.037045803], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 411, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.2228734, 0.06646707, 0.0, 0.28000864, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 7.0, 0.04731246, 4.0, -0.024202725, 0.032557238, -0.037045803], "split_indices": [1, 2, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.11111, 30.126057, 7.985056, 19.452381, 10.673675, 11.835101, 7.61728], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.018387865, -0.14332993, 0.07956948, -0.21823801, 0.03781766, 0.06232402, 0.032579184, -0.08456229, -0.014373496, -0.026189977, 0.025543103], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 412, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.72251904, 0.5636297, 0.16477218, 0.18207169, 0.0, 0.0, 0.1680092, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [4.0, 9.0, 2.0, 2.0, 0.03781766, 0.06232402, 4.0, -0.08456229, -0.014373496, -0.026189977, 0.025543103], "split_indices": [1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.581398, 25.11084, 31.47056, 20.08653, 5.024309, 6.9536552, 24.516905, 13.718934, 6.367596, 6.6919456, 17.824959], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.033382412, -0.085105, 0.10262153, -0.15684876, 0.0028184736, 0.15276723, -0.047181632, -0.0015321313, -0.07515281, 0.08656646, 0.029133225], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 413, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.487253, 0.16163333, 0.5144603, 0.15578777, 0.0, 0.17830193, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [3.0, 5.0, 8.0, 3.0, 0.0028184736, 2.0, -0.047181632, -0.0015321313, -0.07515281, 0.08656646, 0.029133225], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.11432, 20.44825, 36.66607, 11.850129, 8.598121, 31.40377, 5.262297, 5.3327055, 6.517424, 7.390798, 24.012972], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0286251, 0.023817059, -0.1163194, 0.07055361, -0.063676424, -0.24463473, 0.016046071, -0.026755698, 0.039723575, -0.14031212, -0.015599108], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 414, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.3576253, 0.5209482, 0.6642095, 0.4158933, 0.0, 0.6558026, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [7.0, 8.0, 9.0, 2.0, -0.063676424, 6.0, 0.016046071, -0.026755698, 0.039723575, -0.14031212, -0.015599108], "split_indices": [0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [74.14977, 46.143517, 28.00625, 39.45676, 6.686756, 16.03644, 11.969811, 10.439651, 29.01711, 6.5289583, 9.507482], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.009529531, -0.052793052, 0.072616875, 0.02782692, -0.12900299, 0.09526976, -0.0, -0.07135236, 0.009575506, 0.0073026237, 0.049555432], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 415, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.20139013, 0.290728, 0.050867215, 0.0, 0.32854298, 0.079545885, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 9.0, 0.02782692, 6.0, 6.0, -0.0, -0.07135236, 0.009575506, 0.0073026237, 0.049555432], "split_indices": [1, 1, 1, 0, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.742043, 23.804337, 24.937704, 7.655056, 16.149282, 19.673801, 5.2639027, 9.719189, 6.430093, 11.145421, 8.52838], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.03549613, -0.02980542, 0.06184429, 0.1292826, 0.004446965, 0.014823444, 0.04747032, -0.058909897, 0.025604773], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 416, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.17947608, 0.0, 0.15222678, 0.015126139, 0.41227692, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.02980542, 4.0, 2.0, 5.0, 0.014823444, 0.04747032, -0.058909897, 0.025604773], "split_indices": [2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.840492, 6.909948, 41.930542, 17.902065, 24.028479, 6.4017773, 11.500288, 6.135602, 17.892876], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.02650549, -0.086801514, 0.0367298, -0.0010184237, -0.13565546, 0.1616229, -0.07351728, 0.02272145, -0.010838738, 0.015509716, 0.07551079, 0.0021190497, -0.04824597], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 417, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.24887583, 1.001716, 0.44345975, 0.07501448, 0.0, 0.10649994, 0.13308227, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 5.0, 4.0, 2.0, -0.13565546, 2.0, 8.0, 0.02272145, -0.010838738, 0.015509716, 0.07551079, 0.0021190497, -0.04824597], "split_indices": [2, 2, 1, 1, 0, 1, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.123734, 32.419685, 29.704048, 27.289297, 5.1303883, 14.133734, 15.570314, 7.3079348, 19.981361, 7.5745735, 6.55916, 7.9791584, 7.5911555], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.04969691, 0.06886758, -0.0127793085, 0.09911986, -0.03648354, -0.011876105, 0.044366483], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 418, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.10123813, 0.27518845, 0.0, 0.29231232, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [8.0, 8.0, -0.0127793085, 2.0, -0.03648354, -0.011876105, 0.044366483], "split_indices": [1, 2, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.074997, 45.054058, 8.020939, 39.746128, 5.30793, 9.854831, 29.891296], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.024456132, 0.07808617, -0.08114267, 0.04444562, 0.029949963, -0.13994034, 0.068823546, -0.0031382346, 0.023463901, 0.028296856, -0.057999495], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 419, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.35965538, 0.05027297, 0.7577263, 0.0, 0.03654638, 0.4623562, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [3.0, 5.0, 9.0, 0.04444562, 7.0, 2.0, 0.068823546, -0.0031382346, 0.023463901, 0.028296856, -0.057999495], "split_indices": [1, 2, 2, 0, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.730724, 20.50212, 39.228607, 6.657059, 13.845059, 33.716423, 5.512182, 6.834115, 7.0109444, 5.768142, 27.94828], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.010464297, 0.12128147, -0.09831987, 0.14578445, 0.008154519, -0.047614407, -0.08220053, 0.009819834, 0.064754486, 0.018294858, -0.068662494], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 420, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.58129585, 0.024282634, 0.22771776, 0.0837104, 0.0, 0.5312813, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 6.0, 6.0, 2.0, 0.008154519, 8.0, -0.08220053, 0.009819834, 0.064754486, 0.018294858, -0.068662494], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.389847, 18.75167, 29.638176, 13.598697, 5.152973, 24.427984, 5.210191, 6.310625, 7.288071, 15.345827, 9.082158], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.07091958, -0.04081683, -0.032523613, 0.06180892, 0.03749106, -0.1303521, -0.07889998, -0.014991853], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 421, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.11316339, 0.36811674, 0.40362132, 0.0, 0.0, 0.0, 0.14535585, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [5.0, 3.0, 3.0, -0.032523613, 0.06180892, 0.03749106, 6.0, -0.07889998, -0.014991853], "split_indices": [0, 0, 2, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.92573, 12.86816, 25.05757, 5.2961063, 7.572054, 8.256628, 16.800941, 5.012633, 11.788308], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.06523742, -0.11445109, 0.031791445, -0.04954241, 0.08098307, 0.051999725, -9.645499e-05], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 422, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [1.7914624, 0.0, 0.43294668, 0.0, 0.29086646, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.11445109, 2.0, -0.04954241, 6.0, 0.051999725, -9.645499e-05], "split_indices": [0, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.84379, 12.664039, 43.17975, 7.7527146, 35.427036, 16.63281, 18.794226], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.04750672, 0.15031119, -0.0, 0.18874715, 0.013183905, -0.068252206, 0.14442989, 0.07521325, 0.022120262, -0.047677934, 0.006212152, 0.0646791, 0.013787421], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 423, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.3020386, 0.047515154, 0.4136176, 0.043245018, 0.0, 0.25225118, 0.05727139, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 7.0, 7.0, 2.0, 0.013183905, 6.0, 5.0, 0.07521325, 0.022120262, -0.047677934, 0.006212152, 0.0646791, 0.013787421], "split_indices": [0, 2, 0, 0, 0, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.812225, 18.343132, 40.469097, 12.214587, 6.1285443, 28.150614, 12.318482, 6.60617, 5.608417, 14.108127, 14.042486, 5.8706946, 6.447788], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.004871388, 0.054934002, -0.095187336, -0.0065416456, 0.08048202, -0.1544819, 0.00848672, 0.036988523, -0.0, -0.021428062, -0.08029657], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 424, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.3835278, 0.08928992, 0.2266525, 0.0, 0.122738004, 0.1354202, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 3.0, 7.0, -0.0065416456, 7.0, 5.0, 0.00848672, 0.036988523, -0.0, -0.021428062, -0.08029657], "split_indices": [0, 2, 2, 0, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.29218, 40.655483, 27.636703, 9.213339, 31.442142, 19.048292, 8.588409, 20.89741, 10.544734, 12.43632, 6.611973], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.047536496, 0.13178077, 0.008396408, 0.05787012, 0.0023383442, -0.052316986, 0.077987, 0.004469076, 0.039930377], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 425, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.13865702, 0.083882794, 0.4329117, 0.0, 0.0, 0.0, 0.07192241, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [4.0, 6.0, 6.0, 0.05787012, 0.0023383442, -0.052316986, 8.0, 0.004469076, 0.039930377], "split_indices": [1, 2, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.63813, 12.916059, 32.72207, 7.7388134, 5.1772456, 8.211324, 24.510746, 12.866726, 11.64402], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.011876215, 0.058271043, -0.16456126, 0.048451196, -0.0, -0.07424224, -0.016726144, -0.0432176, 0.04888161], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 426, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.5629976, 0.21541043, 0.10621005, 0.0, 0.5674233, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 4.0, 5.0, 0.048451196, 8.0, -0.07424224, -0.016726144, -0.0432176, 0.04888161], "split_indices": [0, 1, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.677616, 34.01411, 15.663503, 11.816564, 22.197548, 7.6446724, 8.01883, 11.939311, 10.258237], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0012400347, -0.016742134, 0.045989573, 0.047590755, -0.1116465, 0.0011615163, 0.03504805, -0.09224978, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 427, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.18954863, 0.3597227, 0.0, 0.09052669, 0.5484947, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 6.0, 0.045989573, 4.0, 7.0, 0.0011615163, 0.03504805, -0.09224978, -0.0], "split_indices": [2, 2, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.388718, 55.998672, 6.390047, 33.043976, 22.954695, 21.786911, 11.257065, 7.9586105, 14.996084], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.021672036, 0.06940083, -0.07977202, 0.13135466, -0.039599854, 0.06648975, -0.0, -0.036726713, 0.0156060215], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 428, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [1.206402, 0.27607432, 0.0, 0.30991346, 0.12136858, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [7.0, 4.0, -0.07977202, 5.0, 6.0, 0.06648975, -0.0, -0.036726713, 0.0156060215], "split_indices": [1, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.5239, 37.752674, 13.771224, 24.525316, 13.227358, 14.275121, 10.2501955, 7.3438954, 5.8834624], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.03093262, -0.068926014, 0.09457551, -0.03344731, -0.010173042, 0.031863697, 0.20746106, -0.03264064, 0.019113086, 0.03209516, -0.0, 0.10298326, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 429, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.3894211, 0.044872895, 0.23552394, 0.0, 0.09786514, 0.062192015, 0.34541756, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 5.0, 7.0, -0.03344731, 3.0, 6.0, 8.0, -0.03264064, 0.019113086, 0.03209516, -0.0, 0.10298326, -0.0], "split_indices": [2, 0, 0, 0, 2, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.84578, 22.261765, 36.584015, 11.4017935, 10.859971, 24.912424, 11.671592, 5.0283456, 5.8316255, 6.732333, 18.18009, 6.559471, 5.1121206], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.02427139, -0.056954324, 0.048931714, -0.1566981, -0.0, -0.09293076, -0.011972158, 0.05344343, -0.0144221], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 430, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.30584767, 0.23457482, 0.0, 0.22714865, 0.2493246, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 2.0, 0.048931714, 4.0, 2.0, -0.09293076, -0.011972158, 0.05344343, -0.0144221], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.15865, 42.90478, 6.2538667, 14.353443, 28.551338, 5.1262255, 9.227218, 5.0737886, 23.47755], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.021496298, -0.10203967, 0.05264101, -0.05393741, -0.0010485345, 0.049916428, -0.00714664], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 431, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.24062641, 0.13024816, 0.19301441, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [6.0, 6.0, 4.0, -0.05393741, -0.0010485345, 0.049916428, -0.00714664], "split_indices": [0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.570534, 18.473385, 19.097149, 9.298013, 9.175372, 7.729885, 11.367264], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.008509672, 0.07860317, -0.04721684, 0.07894616, 0.020437423, -0.036498472, 0.048459966], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 432, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.6410924, 0.39108765, 0.0, 0.0, 0.6569592, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 3.0, -0.04721684, 0.07894616, 3.0, -0.036498472, 0.048459966], "split_indices": [1, 2, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.74502, 38.57556, 15.169459, 7.987874, 30.587688, 14.955177, 15.632511], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.04953322, 0.032069016, -0.091086484, -0.018587917, 0.05075996, 0.040078808, -0.17155889, 0.07770854, -0.0864431, -0.076268904, 0.02286117], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 433, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.20507066, 0.27206802, 0.43611622, 0.0, 0.0, 1.1732098, 0.5511567, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 6.0, 3.0, -0.018587917, 0.05075996, 7.0, 8.0, 0.07770854, -0.0864431, -0.076268904, 0.02286117], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.875885, 18.45523, 38.420654, 10.84079, 7.6144414, 14.327296, 24.093359, 8.903477, 5.4238195, 18.348532, 5.744826], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.011411382, -0.022762774, 0.06987849, 0.03310203, -0.11691796, -0.007876191, 0.1304957, 0.056300677, -0.021959808, -0.11089599, 0.0031441443, 0.07352024, -0.010347034], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 434, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.14748904, 0.24511272, 0.1734674, 0.48356202, 0.59916127, 0.0, 0.36227742, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 3.0, 3.0, 7.0, -0.007876191, 9.0, 0.056300677, -0.021959808, -0.11089599, 0.0031441443, 0.07352024, -0.010347034], "split_indices": [0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [69.66182, 43.270454, 26.391367, 26.897688, 16.372766, 9.78716, 16.604206, 11.031981, 15.865707, 5.1129637, 11.259803, 9.870513, 6.733694], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.027618803, -0.0392406, 0.052224774, 0.18114258, 0.0003795285, 0.089565754, -0.0, -0.09310678, 0.024641324], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 435, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.1934148, 0.0, 0.27878037, 0.23845094, 0.84730554, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.0392406, 4.0, 5.0, 3.0, 0.089565754, -0.0, -0.09310678, 0.024641324], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.88111, 5.4449472, 43.436165, 11.189572, 32.246593, 6.1625385, 5.027034, 5.879907, 26.366686], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04007707, -0.022910064, 0.18011571, -0.031030511, 0.01563619, 0.03112164, 0.07063154, 0.020457868, -0.02512795], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 436, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.45686275, 0.11769552, 0.009163916, 0.0, 0.12642983, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [8.0, 3.0, 9.0, -0.031030511, 6.0, 0.03112164, 0.07063154, 0.020457868, -0.02512795], "split_indices": [2, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.544712, 33.568466, 14.976245, 11.161806, 22.406662, 8.258096, 6.718149, 15.520197, 6.8864646], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0017158851, -0.075245544, 0.046088677, -0.0, -0.05491457, 0.13306549, -0.019935938, -0.021058848, 0.028523082, 0.007111443, 0.06327532, 0.007452947, -0.030392596], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 437, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.21633182, 0.17687021, 0.25241446, 0.10286769, 0.0, 0.13076767, 0.0969606, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 7.0, 4.0, -0.05491457, 7.0, 7.0, -0.021058848, 0.028523082, 0.007111443, 0.06327532, 0.007452947, -0.030392596], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.185932, 21.663225, 40.522705, 13.555009, 8.108216, 17.702156, 22.82055, 8.178648, 5.376362, 8.438037, 9.264119, 14.375534, 8.445017], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.03964265, 0.07985242, -0.035533447, -0.0021133511, 0.14093436, 0.018787168, -0.036263566, 0.025012536, 0.06939199], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 438, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.35457054, 0.23881796, 0.0, 0.16289903, 0.08669317, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [7.0, 3.0, -0.035533447, 2.0, 7.0, 0.018787168, -0.036263566, 0.025012536, 0.06939199], "split_indices": [0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.89859, 43.86812, 10.03047, 18.445024, 25.423094, 11.698854, 6.7461696, 17.381626, 8.041469], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.013992159, 0.036928363, -0.039223596, -0.03408879, 0.0673049, -0.057534743, 0.058994904, 0.038244534, -0.02534339], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 439, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.1937376, 0.11981373, 0.0, 0.60255015, 0.36580652, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 3.0, -0.039223596, 7.0, 7.0, -0.057534743, 0.058994904, 0.038244534, -0.02534339], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.73112, 51.936615, 6.794506, 14.536824, 37.39979, 8.992974, 5.5438495, 27.368162, 10.0316305], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.045597155, -0.1775566, 0.016779067, -0.0069656707, -0.07359063, -0.066751845, 0.08632369, 0.08057474, 0.00794554], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 440, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.42747885, 0.14025605, 0.5729128, 0.0, 0.0, 0.0, 0.26285183, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [2.0, 4.0, 2.0, -0.0069656707, -0.07359063, -0.066751845, 3.0, 0.08057474, 0.00794554], "split_indices": [1, 2, 2, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.619164, 15.527254, 33.09191, 5.596398, 9.930857, 6.632788, 26.459122, 5.264543, 21.194578], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.061046854, -0.21683554, 0.024950556, -0.13054131, -0.0013973548, 0.030440183, -0.023324955, -0.033256836, 0.030207504], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 441, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.5544053, 0.6294205, 0.10608634, 0.0, 0.0, 0.0, 0.1851173, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [5.0, 5.0, 7.0, -0.13054131, -0.0013973548, 0.030440183, 9.0, -0.033256836, 0.030207504], "split_indices": [0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [38.545414, 13.582119, 24.963293, 5.944211, 7.637908, 10.032067, 14.931227, 9.288118, 5.6431084], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.029829303, 0.049347192, -0.12906551, 0.011730078, 0.051259678, -0.078518555, -0.019879546, -0.016873017, 0.028868614, 0.019521754, -0.026973307], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 442, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, -1, -1, -1], "loss_changes": [0.46150768, 0.12518302, 0.3446006, 0.15874064, 0.0, 0.0, 0.10238725, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, -1, -1, -1], "split_conditions": [5.0, 9.0, 7.0, 2.0, 0.051259678, -0.078518555, 4.0, -0.016873017, 0.028868614, 0.019521754, -0.026973307], "split_indices": [1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.91191, 30.818293, 25.093618, 24.999058, 5.8192344, 10.284636, 14.808983, 13.383704, 11.615354, 6.0784125, 8.730571], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.011893896, -0.026577499, 0.030361343, -0.0025381518, 0.12012456, 0.02478297, -0.009622252, 0.0029658482, 0.07236242], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 443, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.1118439, 0.0, 0.17131263, 0.09725214, 0.17672034, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.026577499, 7.0, 3.0, 5.0, 0.02478297, -0.009622252, 0.0029658482, 0.07236242], "split_indices": [0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.958527, 8.033628, 52.9249, 38.62571, 14.29919, 8.613017, 30.012693, 8.483268, 5.815922], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.022806192, 0.104378365, -0.090354055, 0.041112516, 0.0809053, -0.16522369, -0.0037232684, 0.030647488, -0.04369086, -0.018393023, -0.06477043, -0.015757473, 0.006304749], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 444, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6807592, 0.40912262, 0.17982033, 0.38474452, 0.0, 0.04160142, 0.023265548, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 8.0, 8.0, 6.0, 0.0809053, 4.0, 5.0, 0.030647488, -0.04369086, -0.018393023, -0.06477043, -0.015757473, 0.006304749], "split_indices": [2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [71.575615, 42.15167, 29.423946, 31.825188, 10.326481, 14.677527, 14.746419, 24.72978, 7.0954075, 6.1828275, 8.4947, 5.9426556, 8.803763], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.065634824, 0.12348387, -0.06861156, -0.031951476, 0.17047863, -0.059939858, 0.030690156, 0.09408525, -0.00382055], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 445, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.3900302, 0.39981455, 0.359907, 0.0, 0.80967456, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [7.0, 2.0, 6.0, -0.031951476, 5.0, -0.059939858, 0.030690156, 0.09408525, -0.00382055], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.425102, 33.719757, 13.705346, 5.1452217, 28.574535, 7.9891477, 5.7161984, 15.948532, 12.626004], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.030701471, 0.052652553, -0.07283175, -0.012954223, 0.047547936, -0.0036014197, -0.12636507, -0.016009925, 0.011296567, -0.0671679, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 446, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.18383506, 0.18877456, 0.115827784, 0.0, 0.0, 0.038597118, 0.21047732, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [4.0, 7.0, 5.0, -0.012954223, 0.047547936, 3.0, 8.0, -0.016009925, 0.011296567, -0.0671679, -0.0], "split_indices": [2, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.682266, 15.799629, 33.882637, 8.10666, 7.692969, 16.055115, 17.827522, 8.242769, 7.812346, 9.196234, 8.631288], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.02227913, 0.062629245, -0.025696645, 0.0206814, 0.058084678, -0.088954054, 0.028496685, 0.039812114, -0.021581277, -0.0, -0.08783169], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 447, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.124457166, 0.16608725, 0.21928413, 0.30717263, 0.0, 0.37317556, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 9.0, 6.0, 4.0, 0.058084678, 9.0, 0.028496685, 0.039812114, -0.021581277, -0.0, -0.08783169], "split_indices": [0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.55224, 33.856293, 26.695946, 27.099209, 6.7570834, 18.23145, 8.464497, 12.557443, 14.541767, 13.102453, 5.1289964], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.076550774, 0.12484029, -0.030289203, -0.041775897, 0.19880332, 0.049178563, -0.042245038, 0.07985207, 0.02360704], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 448, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, -1], "loss_changes": [0.28772262, 0.7657677, 0.37972018, 0.0, 0.18723774, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 2.0, -0.041775897, 3.0, 0.049178563, -0.042245038, 0.07985207, 0.02360704], "split_indices": [2, 1, 1, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.202717, 36.481373, 15.721347, 7.4927835, 28.988588, 5.1985755, 10.522771, 17.2594, 11.729188], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.029762985, -0.10768988, 0.08224121, -0.05612338, -0.044510312, -0.0073568695, -0.0127227595, 0.039448515, -0.057667542, 0.060223382], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 449, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.20741586, 0.23020883, 0.026794583, 0.20995042, 0.68923295, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 7.0, 6.0, 3.0, 9.0, -0.044510312, -0.0073568695, -0.0127227595, 0.039448515, -0.057667542, 0.060223382], "split_indices": [1, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.57887, 48.472664, 13.106207, 30.837269, 17.635393, 7.4674344, 5.6387725, 8.198181, 22.639088, 11.946495, 5.688899], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.004305385, 0.07508167, -0.042196393, 9.973334e-06, 0.036097288, -0.09639283, 0.024208464, -0.0, -0.071326755], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 450, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.15768668, 0.04383727, 0.25580972, 0.0, 0.0, 0.35236824, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 4.0, 8.0, 9.973334e-06, 0.036097288, 6.0, 0.024208464, -0.0, -0.071326755], "split_indices": [0, 2, 0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.461475, 15.456741, 36.004734, 7.021133, 8.435608, 25.700068, 10.304667, 16.077799, 9.62227], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.030457737, 0.13923827, -0.026529375, 0.073191784, -0.0, -0.07307801, 0.030226687, -0.0, -0.035415977], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 451, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.2509687, 0.21799815, 0.15401992, 0.0, 0.0, 0.07393042, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [5.0, 6.0, 7.0, 0.073191784, -0.0, 2.0, 0.030226687, -0.0, -0.035415977], "split_indices": [0, 1, 1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.07074, 12.851141, 24.2196, 7.1739373, 5.6772037, 18.621246, 5.598355, 6.7977457, 11.823501], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.033548143, 0.078947775, -0.07891597, 0.016852923, 0.15614212, -0.06626454, 0.04842033, 0.04870481, -0.04522551, 0.0035829865, 0.07101243], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 452, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.28512162, 0.17202638, 0.57297176, 0.61169314, 0.16996479, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [7.0, 6.0, 9.0, 2.0, 3.0, -0.06626454, 0.04842033, 0.04870481, -0.04522551, 0.0035829865, 0.07101243], "split_indices": [2, 1, 2, 1, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.621525, 39.046417, 14.57511, 23.032722, 16.013693, 9.474875, 5.1002345, 12.665707, 10.367016, 6.596532, 9.417161], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.030253068, 0.12638961, -0.046422556, 0.20701832, 0.017386867, -0.05165692, 0.026060788, 0.027848957, 0.0813858, 0.05065352, -0.034349207, 0.026634155, -0.018139107], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 453, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.46304634, 0.21902886, 0.32951942, 0.051487982, 0.29393107, 0.0, 0.12740584, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 6.0, 4.0, 2.0, 2.0, -0.05165692, 8.0, 0.027848957, 0.0813858, 0.05065352, -0.034349207, 0.026634155, -0.018139107], "split_indices": [0, 2, 2, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.936398, 26.884373, 33.052025, 14.409292, 12.475081, 12.085794, 20.96623, 6.6299167, 7.7793756, 6.0291204, 6.4459605, 12.810121, 8.156111], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.02940944, -0.09809799, 0.03405956, -0.035565607, -0.06832735, 0.07581519, -0.016661035, 0.03083395, -0.027506815, -0.022587301, 0.040256303], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 454, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.31512186, 0.2465454, 0.14238788, 0.2030839, 0.0, 0.24539226, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 7.0, 2.0, -0.06832735, 3.0, -0.016661035, 0.03083395, -0.027506815, -0.022587301, 0.040256303], "split_indices": [1, 1, 2, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [69.04709, 33.654686, 35.392403, 24.084675, 9.570013, 25.007702, 10.3847, 6.1966963, 17.887978, 6.4104047, 18.597298], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.00231264, 0.07168459, -0.10358675, 0.1991959, -0.07754904, -0.23692653, 0.044312384, -0.0, 0.09180285, -0.0, -0.14055938], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 455, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.3642012, 1.194231, 0.74420387, 0.4571833, 0.0, 0.7756082, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 7.0, 7.0, 3.0, -0.07754904, 4.0, 0.044312384, -0.0, 0.09180285, -0.0, -0.14055938], "split_indices": [0, 2, 2, 2, 0, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [46.18244, 26.283104, 19.899336, 19.465412, 6.817692, 13.29825, 6.6010857, 6.911311, 12.554102, 7.1088476, 6.1894026], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.058257896, 0.07578035, 0.024268182, -0.12799908, 0.11047473, -0.0746663, 0.0065451367, 0.042971108, -0.0013332211], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 456, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.28503013, 0.0, 0.5719373, 0.30801663, 0.12059161, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.07578035, 5.0, 7.0, 9.0, -0.0746663, 0.0065451367, 0.042971108, -0.0013332211], "split_indices": [1, 0, 1, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.268703, 5.641053, 41.62765, 14.45351, 27.17414, 8.0024395, 6.4510713, 21.63108, 5.5430613], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.029756498, 0.05499238, -0.099291444, -0.054321326, 0.1925774, -0.24886265, -0.025939612, 0.03151007, -0.059251677, -0.015417841, 0.12600182, -0.09487043, -0.03776758, 0.041479476, -0.041319348], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 457, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.38693887, 0.45791706, 0.36268237, 0.40514272, 0.8014182, 0.0098442435, 0.49296373, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 2.0, 6.0, 6.0, 6.0, 5.0, 8.0, 0.03151007, -0.059251677, -0.015417841, 0.12600182, -0.09487043, -0.03776758, 0.041479476, -0.041319348], "split_indices": [1, 1, 1, 2, 2, 2, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [62.982372, 27.831944, 35.15043, 15.441644, 12.390299, 10.330192, 24.820238, 7.1037116, 8.337932, 6.139058, 6.251241, 5.1330905, 5.197101, 9.595134, 15.225104], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [0.016211808, 0.048258994, -0.004733092, 0.111445054, -0.12659812, -0.0, 0.04819564, -0.06823229, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 458, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.18125995, 0.0, 0.6938925, 0.13139322, 0.3152787, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.048258994, 4.0, 3.0, 6.0, -0.0, 0.04819564, -0.06823229, -0.0], "split_indices": [1, 0, 2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.422855, 6.51595, 46.906902, 23.63744, 23.269464, 7.6903114, 15.947128, 12.614068, 10.655396], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.017008416, 0.052764814, -0.08911161, -0.041137516, 0.099015966, -0.0, 0.04907503], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 459, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.6849985, 0.50687224, 0.0, 0.0, 0.29565462, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 1.0, -0.08911161, -0.041137516, 4.0, -0.0, 0.04907503], "split_indices": [0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.78117, 55.601025, 5.180146, 10.081847, 45.519176, 18.426907, 27.09227], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.009358844, 0.10053698, -0.044354655, 0.05542562, 0.029313693, -0.08651921, 0.0256221, 0.03769505, -0.013412702, -0.053454492, -0.014168594], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 460, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.26646903, 0.09556116, 0.187769, 0.0, 0.10560667, 0.062486663, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 9.0, 0.05542562, 2.0, 2.0, 0.0256221, 0.03769505, -0.013412702, -0.053454492, -0.014168594], "split_indices": [0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.351547, 19.403795, 31.94775, 7.5172772, 11.8865185, 24.945791, 7.00196, 5.448751, 6.4377675, 5.660376, 19.285414], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.041526444, -0.076613314, 0.038863696, -0.019667746, -0.05833212, -0.03259434, 0.057977576], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 461, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.2334552, 0.19449133, 0.0, 0.46273834, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [9.0, 7.0, 0.038863696, 6.0, -0.05833212, -0.03259434, 0.057977576], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.12211, 31.747421, 5.3746862, 22.762148, 8.985273, 16.73015, 6.0319977], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.001313761, 0.1705723, -0.062140282, 0.01462387, 0.09974839, -0.118328705, 0.0683057, -0.05379638, 0.00626978], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 462, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.6001525, 0.24923828, 0.65915793, 0.0, 0.0, 0.30854747, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [3.0, 2.0, 9.0, 0.01462387, 0.09974839, 8.0, 0.0683057, -0.05379638, 0.00626978], "split_indices": [2, 2, 0, 0, 0, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.882908, 14.389064, 38.493847, 9.306058, 5.083005, 33.027565, 5.4662795, 23.265686, 9.76188], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.05165689, 0.0026977335, 0.052073125, 0.17283544, -0.08800225, 0.102046624, -0.020695237, -0.008634457, -0.089402266], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 463, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.34601238, 0.72000515, 0.0, 0.7145968, 0.32825828, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [7.0, 2.0, 0.052073125, 5.0, 7.0, 0.102046624, -0.020695237, -0.008634457, -0.089402266], "split_indices": [1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.99874, 44.122803, 15.875937, 15.411124, 28.711678, 9.112724, 6.2984004, 23.654442, 5.057236], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.031929184, 0.0566099, -0.08908618, 0.050480124, 0.0059912577, -0.15469411, -0.001608433, -0.019950256, 0.0545303, -0.065825894, -0.027227264, 0.041776776, -0.032225307], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 464, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.3578021, 0.13427803, 0.23116851, 0.0, 0.28157684, 0.048113763, 0.31153458, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 2.0, 7.0, 0.050480124, 2.0, 5.0, 5.0, -0.019950256, 0.0545303, -0.065825894, -0.027227264, 0.041776776, -0.032225307], "split_indices": [1, 2, 1, 0, 1, 1, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.06481, 26.019419, 42.045395, 6.775004, 19.244415, 22.933695, 19.1117, 13.662585, 5.5818305, 9.41084, 13.522854, 7.656872, 11.454828], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.06020526, -0.117553025, 0.06894568, -0.08231388, -0.062872276, 0.0070879627, -0.029298123], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 465, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.7939285, 0.29213148, 0.0, 0.0, 0.10430839, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 3.0, 0.06894568, -0.08231388, 3.0, 0.0070879627, -0.029298123], "split_indices": [0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [45.784992, 39.01071, 6.7742825, 8.573869, 30.436842, 7.997582, 22.439259], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.032697853, -0.081747636, 0.06763478, -0.15100162, 0.02197775, 0.03721656, -0.0, -0.0030192889, -0.084482454], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 466, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, -1], "loss_changes": [0.25592077, 0.39565724, 0.07143655, 0.4288289, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, -1], "split_conditions": [6.0, 8.0, 6.0, 4.0, 0.02197775, 0.03721656, -0.0, -0.0030192889, -0.084482454], "split_indices": [0, 1, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.59248, 34.102604, 15.489878, 24.035147, 10.067456, 8.54333, 6.946548, 12.446674, 11.588471], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.05379851, -0.03331363, 0.07089847, 0.15611547, -0.017268654, -0.003944568, 0.063692294, -0.028361699, 0.05504093], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 467, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.18862484, 0.0, 0.4714896, 0.32197994, 0.47505733, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.03331363, 6.0, 2.0, 8.0, -0.003944568, 0.063692294, -0.028361699, 0.05504093], "split_indices": [2, 0, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [64.54738, 5.0005093, 59.54687, 30.464891, 29.081982, 7.3403697, 23.124521, 21.740576, 7.3414063], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.04081067, 0.016078714, 0.035779867, -0.047423664, 0.06594902, 0.036407884, -0.00033181562], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 468, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.08395905, 0.35799283, 0.0, 0.0, 0.13509369, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [7.0, 2.0, 0.035779867, -0.047423664, 3.0, 0.036407884, -0.00033181562], "split_indices": [2, 1, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.233395, 40.078716, 10.154678, 7.9924383, 32.086277, 17.941708, 14.14457], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.0012003091, -0.021358969, 0.04378218, -0.06495174, 0.036439825, 0.093071654, -0.018262377, -0.044687226, -0.0027503823, 0.019780554, -0.0, 0.010722319, 0.048366494], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 469, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.057772096, 0.09664933, 0.120025024, 0.08606614, 0.01444814, 0.035851017, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 7.0, 9.0, 1.0, 2.0, 8.0, -0.018262377, -0.044687226, -0.0027503823, 0.019780554, -0.0, 0.010722319, 0.048366494], "split_indices": [1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [55.832928, 35.287884, 20.545044, 21.03177, 14.256113, 14.6919, 5.853143, 7.010961, 14.020811, 6.9831057, 7.2730074, 9.604226, 5.0876746], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-0.038607705, -0.0747988, 0.024730535, 0.06868561, -0.0156505, -0.043876957, 0.017855376], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 470, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.6786385, 0.0, 0.33821535, 0.0, 0.33395776, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.0747988, 1.0, 0.06868561, 3.0, -0.043876957, 0.017855376], "split_indices": [0, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.70199, 10.710685, 36.991302, 5.744732, 31.246569, 11.554726, 19.691845], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.05995435, 0.04399624, 0.12915793, -0.004501543, 0.07450174, -0.00078053266, -0.035644524, 0.029437544], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 471, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.35233885, 0.0, 0.14631076, 0.22134516, 0.25747085, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [2.0, -0.05995435, 5.0, 7.0, 7.0, 0.07450174, -0.00078053266, -0.035644524, 0.029437544], "split_indices": [2, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [37.524147, 6.258103, 31.266045, 11.4894495, 19.776596, 5.966613, 5.5228367, 9.741938, 10.0346575], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.045647673, 0.035938602, -0.122591004, 0.15269826, -0.07380799, -0.18852982, 0.007906351, -0.0, 0.068817675, -0.08408112, 0.010652325], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 472, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, -1, -1], "loss_changes": [0.35817564, 0.9113658, 0.3074755, 0.21215224, 0.0, 0.46179432, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 8.0, 5.0, 4.0, -0.07380799, 9.0, 0.007906351, -0.0, 0.068817675, -0.08408112, 0.010652325], "split_indices": [0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.974686, 25.82481, 28.149878, 18.837463, 6.9873466, 19.793037, 8.35684, 7.0155816, 11.821881, 14.18132, 5.6117163], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.12679192, 0.05982319, -0.0030864489, -0.06852946, 0.12498949, -0.022781506, -0.0055772485, 0.07071572, 0.024011197, -0.057449043], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 473, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.46070176, 0.20628846, 0.23122771, 0.0, 0.0, 0.39948565, 0.33943895, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 5.0, 4.0, -0.0030864489, -0.06852946, 5.0, 6.0, -0.0055772485, 0.07071572, 0.024011197, -0.057449043], "split_indices": [0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.16258, 18.635405, 39.527172, 9.654305, 8.981099, 22.485775, 17.041399, 9.759663, 12.726112, 10.546824, 6.494574], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.06550082, -0.023373464, 0.094461784, 0.028722662, -0.06622061, -0.0, 0.13196062, -0.01061175, 0.013254502, 0.056445714, 0.0007065368], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 474, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.18801248, 0.43366727, 0.20221028, 0.0, 0.0, 0.0248074, 0.26439166, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 2.0, 3.0, 0.028722662, -0.06622061, 8.0, 8.0, -0.01061175, 0.013254502, 0.056445714, 0.0007065368], "split_indices": [0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [68.426476, 16.016294, 52.41018, 9.993208, 6.023087, 14.429692, 37.980488, 9.36476, 5.064932, 25.640493, 12.339994], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.022686752, 0.121561214, -0.052971203, -0.0012041194, 0.20915924, -0.008953251, -0.04343352, 0.11759085, -0.005998584, -0.02658014, 0.02197882], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 475, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.37645385, 0.26248395, 0.094969384, 0.0, 0.60642093, 0.14209928, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [5.0, 4.0, 8.0, -0.0012041194, 3.0, 7.0, -0.04343352, 0.11759085, -0.005998584, -0.02658014, 0.02197882], "split_indices": [2, 0, 0, 0, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.546066, 20.920668, 26.625397, 8.519812, 12.400857, 19.47477, 7.150627, 6.7454734, 5.6553836, 10.47028, 9.004491], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.012699461, 0.052969053, -0.054111443, -0.018310178, 0.037559997, -0.11861303, 0.013816215, -0.088054396, -0.0013274845, 0.050281532, -0.028939601], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 476, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.13636108, 0.16672331, 0.15016475, 0.0, 0.0, 0.30883592, 0.2859346, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [5.0, 2.0, 6.0, -0.018310178, 0.037559997, 7.0, 8.0, -0.088054396, -0.0013274845, 0.050281532, -0.028939601], "split_indices": [2, 2, 1, 0, 0, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [48.046665, 17.539614, 30.507051, 6.325915, 11.213699, 16.020535, 14.486518, 5.361753, 10.658781, 6.2114944, 8.275023], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.0056965286, 0.09635969, -0.020643951, 0.0038823925, 0.043931074, -0.10466933, 0.05330651, -0.0693355, -0.0, 0.06382828, -0.0069610467], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 477, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.16280957, 0.04682325, 0.31949887, 0.0, 0.0, 0.3081219, 0.34671372, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 5.0, 7.0, 0.0038823925, 0.043931074, 4.0, 2.0, -0.0693355, -0.0, 0.06382828, -0.0069610467], "split_indices": [0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [63.171528, 14.477748, 48.69378, 6.652084, 7.8256636, 23.232721, 25.46106, 9.738563, 13.494159, 8.101961, 17.359098], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.05557787, 0.024182627, -0.08070482, 0.038974334, -0.15208578, 0.04342535, -0.01819868, -0.0690431, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 478, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.1932518, 0.0, 0.42037112, 0.20587279, 0.35312152, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, 0.024182627, 2.0, 6.0, 7.0, 0.04342535, -0.01819868, -0.0690431, -0.0], "split_indices": [1, 0, 2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [53.57016, 7.3804903, 46.18967, 16.902897, 29.286774, 8.466013, 8.436885, 18.893051, 10.3937235], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.066456094, -0.102496244, 0.04912713, 0.04198116, -0.1517603, -0.08695589, -0.021494595], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 479, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.51561344, 0.6627337, 0.0, 0.0, 0.44512522, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [9.0, 2.0, 0.04912713, 0.04198116, 5.0, -0.08695589, -0.021494595], "split_indices": [2, 2, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.860558, 52.613075, 7.247483, 8.232021, 44.381054, 14.874583, 29.50647], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.043237295, -0.0079276655, -0.047432244, -0.11531095, 0.046306908, -0.046373494, -0.007066889, -0.0081007965, 0.07541518], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 480, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.1881873, 0.24854113, 0.0, 0.030863702, 0.4359514, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 3.0, -0.047432244, 7.0, 8.0, -0.046373494, -0.007066889, -0.0081007965, 0.07541518], "split_indices": [2, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.799576, 39.574455, 10.225121, 13.520952, 26.053503, 8.225997, 5.2949557, 19.473099, 6.580405], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.011737069, 0.03060337, -0.061357845, -0.16131599, 0.02441415, -0.08732964, -0.018909104], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 481, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.21605109, 0.0, 0.41516405, 0.16296208, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, 0.03060337, 7.0, 5.0, 0.02441415, -0.08732964, -0.018909104], "split_indices": [1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [36.88921, 10.271716, 26.617493, 15.990174, 10.627319, 5.607051, 10.383123], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.016722018, -0.052313887, 0.02594644, -0.040228926, 0.06639598, 0.025478391, 0.0023436714], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 482, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.37215814, 0.0, 0.2717282, 0.0, 0.027421147, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, -0.052313887, 2.0, -0.040228926, 8.0, 0.025478391, 0.0023436714], "split_indices": [0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [51.443874, 10.798993, 40.644882, 7.234288, 33.41059, 23.743189, 9.667404], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.11640203, -0.05515977, 0.0684053, 0.031306922, -0.10446489, 0.049668513, -0.0010695858, 0.022936461, -0.0733766, 0.01400621], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 483, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.39214984, 0.15563855, 0.4509266, 0.0, 0.027936086, 0.75159883, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [3.0, 3.0, 9.0, 0.0684053, 7.0, 4.0, 0.049668513, -0.0010695858, 0.022936461, -0.0733766, 0.01400621], "split_indices": [2, 1, 2, 0, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [58.275806, 18.868465, 39.40734, 6.9273877, 11.941078, 33.012688, 6.3946557, 6.007209, 5.933869, 17.153852, 15.858835], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.06271864, 0.09271441, -0.028590027, 0.12300753, -0.0, 0.007187628, 0.07070854, 0.066898294, -0.040766012], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 484, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.35247698, 0.1771788, 0.0, 0.4923404, 0.5152918, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [8.0, 8.0, -0.028590027, 4.0, 2.0, 0.007187628, 0.07070854, 0.066898294, -0.040766012], "split_indices": [1, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [71.55435, 61.017807, 10.536547, 45.942585, 15.075221, 25.568617, 20.37397, 5.397518, 9.677703], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.06722849, -0.08000412, 0.11818608, 0.17446385, -0.013797961, 0.035117038, 0.09809943], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 485, "left_children": [1, -1, 3, 5, -1, -1, -1], "loss_changes": [0.7852869, 0.0, 0.39178985, 0.18490422, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3], "right_children": [2, -1, 4, 6, -1, -1, -1], "split_conditions": [2.0, -0.08000412, 8.0, 6.0, -0.013797961, 0.035117038, 0.09809943], "split_indices": [2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [44.172485, 5.028239, 39.14425, 29.558546, 9.585702, 23.236406, 6.3221393], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.052708607, -0.08319442, 0.1019585, -2.1430451e-05, -0.04485706, -0.005543662, 0.15281793, 0.06089199, 0.006293556], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 486, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.3538376, 0.06034836, 0.25304466, 0.0, 0.0, 0.0, 0.15764147, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 3.0, 3.0, -2.1430451e-05, -0.04485706, -0.005543662, 7.0, 0.06089199, 0.006293556], "split_indices": [1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [50.31751, 12.660816, 37.656693, 6.732466, 5.9283504, 10.785833, 26.870857, 18.52528, 8.345577], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.015710142, 0.030642942, -0.16535051, 0.10352296, -0.0005948159, -0.14219353, 0.017383955, 0.051439524, -0.0, -0.019041587, 0.032691114], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 487, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, -1, -1], "loss_changes": [0.44709274, 0.1185062, 1.1220171, 0.090173766, 0.23168367, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, -1, -1], "split_conditions": [8.0, 3.0, 6.0, 6.0, 7.0, -0.14219353, 0.017383955, 0.051439524, -0.0, -0.019041587, 0.032691114], "split_indices": [0, 0, 2, 2, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [60.78447, 46.505676, 14.278793, 14.218974, 32.2867, 5.7108264, 8.567966, 7.614736, 6.6042376, 21.3613, 10.925404], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.031497754, -0.10844478, 0.022412477, 0.059721645, -0.080160044, 0.00010568473, 0.023076462, -0.042678118, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 488, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [1.0017005, 0.0, 0.18280768, 0.028792828, 0.054795705, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.10844478, 6.0, 3.0, 6.0, 0.00010568473, 0.023076462, -0.042678118, -0.0], "split_indices": [1, 0, 2, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.94691, 6.8946123, 46.052296, 34.790478, 11.261818, 9.350288, 25.440191, 5.600726, 5.6610913], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.041429676, -0.018025117, 0.06754664, 0.12241167, -0.07450386, 0.05771666, -0.01622319, -0.04669605, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 489, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.1570048, 0.0, 0.3795439, 0.4531486, 0.08254431, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [3.0, -0.018025117, 7.0, 8.0, 7.0, 0.05771666, -0.01622319, -0.04669605, -0.0], "split_indices": [2, 0, 1, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.407505, 10.493007, 45.914497, 33.729267, 12.18523, 24.494005, 9.235261, 5.427818, 6.757413], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.025306119, 0.041632496, -0.0800085, -0.06600914, -0.018854046, -0.01221176, 0.00081835134], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 490, "left_children": [1, -1, 3, -1, 5, -1, -1], "loss_changes": [0.4408521, 0.0, 0.2929941, 0.0, 0.016965121, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4], "right_children": [2, -1, 4, -1, 6, -1, -1], "split_conditions": [3.0, 0.041632496, 6.0, -0.06600914, 8.0, -0.01221176, 0.00081835134], "split_indices": [0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.534275, 11.043638, 36.490635, 9.805767, 26.68487, 15.121571, 11.563299], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.05035056, 0.12829086, -0.0090490915, -0.0, 0.057696648, -0.12460871, 0.045575153, 0.013946147, -0.09218088], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 491, "left_children": [1, 3, 5, -1, -1, 7, -1, -1, -1], "loss_changes": [0.18410136, 0.13425356, 0.40997085, 0.0, 0.0, 0.45394588, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5], "right_children": [2, 4, 6, -1, -1, 8, -1, -1, -1], "split_conditions": [4.0, 6.0, 8.0, -0.0, 0.057696648, 6.0, 0.045575153, 0.013946147, -0.09218088], "split_indices": [1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [35.93953, 15.770389, 20.169144, 5.6783605, 10.092028, 12.233607, 7.9355354, 6.4190297, 5.8145776], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.08874375, 0.1512727, 0.024223566, 0.24626125, -0.0, -0.033946633, 0.09511682, 0.037448026, 0.10990761, 0.047959138, -0.048308924, -0.020045383, 0.06319898], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 492, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.20891511, 0.41624266, 0.30164057, 0.15928149, 0.32352167, 0.0, 0.4185093, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 6.0, 7.0, 3.0, 3.0, -0.033946633, 5.0, 0.037448026, 0.10990761, 0.047959138, -0.048308924, -0.020045383, 0.06319898], "split_indices": [0, 2, 0, 2, 0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [56.042015, 26.990253, 29.051762, 16.425386, 10.564866, 9.205795, 19.845968, 9.595299, 6.8300877, 5.127518, 5.437348, 8.079321, 11.766646], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.007925121, -0.09790741, 0.06420755, -0.0032038759, -0.093332365, -0.013945505, 0.15537693, -0.047852885, 0.038513914, 0.038031764, -0.039335057, -0.0, 0.081217445], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 493, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.36160713, 0.3852306, 0.30511215, 0.3456408, 0.0, 0.38229036, 0.3421984, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 7.0, 6.0, 4.0, -0.093332365, 5.0, 5.0, -0.047852885, 0.038513914, 0.038031764, -0.039335057, -0.0, 0.081217445], "split_indices": [0, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [59.218246, 19.685549, 39.5327, 14.658375, 5.027173, 21.151335, 18.381365, 6.9501553, 7.7082195, 9.18506, 11.966276, 8.328261, 10.0531025], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [0.019691613, -0.03381047, 0.04726958, 0.09033654, 0.014793048, -0.0, 0.04130169, -0.025618559, 0.01647442], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 494, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, -1], "loss_changes": [0.24570888, 0.0, 0.06829466, 0.09558654, 0.14308007, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, -1], "split_conditions": [1.0, -0.03381047, 4.0, 2.0, 6.0, -0.0, 0.04130169, -0.025618559, 0.01647442], "split_indices": [1, 0, 1, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [66.62959, 10.329268, 56.30032, 22.114136, 34.186188, 8.118939, 13.995195, 8.740179, 25.446007], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.10263818, -0.29278743, -0.032261573, -0.042575315, -0.110786356, 0.041547697, -0.0754009, 0.022686804, -0.036366865], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 495, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, -1], "loss_changes": [0.6077835, 0.04198742, 0.27627522, 0.0, 0.0, 0.0, 0.22554365, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, -1], "split_conditions": [3.0, 5.0, 4.0, -0.042575315, -0.110786356, 0.041547697, 3.0, 0.022686804, -0.036366865], "split_indices": [2, 1, 2, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [47.55264, 11.607994, 35.944645, 5.3060384, 6.3019557, 6.3362207, 29.608423, 6.232886, 23.375536], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [0.0037394231, 0.12717897, -0.056742657, 0.1029971, -0.0012930986, -0.17273696, 0.025497086, -0.015372154, -0.08981823, 0.083391346, -0.055614594], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 496, "left_children": [1, 3, 5, -1, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.3931858, 0.5339553, 0.3454853, 0.0, 0.0, 0.17774683, 1.1461437, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6], "right_children": [2, 4, 6, -1, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.0, 3.0, 5.0, 0.1029971, -0.0012930986, 7.0, 8.0, -0.015372154, -0.08981823, 0.083391346, -0.055614594], "split_indices": [0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [49.754322, 16.542633, 33.21169, 5.9353952, 10.607238, 13.8262, 19.385489, 8.176728, 5.6494713, 8.893653, 10.491837], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-0.008777758, -0.03413277, 0.052005455, -0.104976065, 0.085732564, -0.085992895, -0.018989034, 0.06267126, -0.041460432], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 497, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, -1], "loss_changes": [0.2809239, 0.4884854, 0.0, 0.22187969, 0.6128907, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, -1], "split_conditions": [9.0, 7.0, 0.052005455, 2.0, 7.0, -0.085992895, -0.018989034, 0.06267126, -0.041460432], "split_indices": [0, 2, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [61.567276, 55.23888, 6.3283954, 35.321507, 19.917374, 5.046925, 30.274584, 13.181022, 6.7363515], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-0.0026426183, 0.04304979, -0.04597793, -0.013479963, 0.10094848, 0.014803523, -0.05683832, 0.009300725, 0.043730825, -0.012275804, 0.0385362], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 498, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.10789932, 0.13932016, 0.27305382, 0.0, 0.028867215, 0.14572373, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [4.0, 3.0, 7.0, -0.013479963, 2.0, 4.0, -0.05683832, 0.009300725, 0.043730825, -0.012275804, 0.0385362], "split_indices": [0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [52.3445, 24.359276, 27.985224, 9.071294, 15.287982, 19.798277, 8.186946, 7.502548, 7.7854342, 13.123757, 6.6745205], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [0.03635738, 0.08449954, -0.027861847, -0.0131443925, 0.13565846, -0.0714033, 0.01764274, 0.0011385859, 0.048942417, -0.0022635055, -0.030152993], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 499, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.18864024, 0.24179007, 0.09940179, 0.0, 0.07505429, 0.021500863, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [6.0, 2.0, 7.0, -0.0131443925, 2.0, 4.0, 0.01764274, 0.0011385859, 0.048942417, -0.0022635055, -0.030152993], "split_indices": [0, 0, 2, 0, 2, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [57.422535, 33.528873, 23.89366, 9.0411625, 24.48771, 16.789139, 7.1045213, 5.047328, 19.440382, 6.726488, 10.062651], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "11", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "5E-1", "boost_from_average": "1", "num_class": "10", "num_feature": "3", "num_target": "1"}, "objective": {"name": "multi:softprob", "softmax_multiclass_param": {"num_class": "10"}}}, "version": [3, 0, 2]}