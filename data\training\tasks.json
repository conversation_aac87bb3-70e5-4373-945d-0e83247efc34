{"tasks": {"ccbf2018-7dcd-4f17-a8d9-afef24e3dfad": {"task_id": "ccbf2018-7dcd-4f17-a8d9-afef24e3dfad", "position": "hundreds", "model_type": "xgb", "issue": "2025217", "train_until": null, "priority": 2, "status": "failed", "created_at": "2025-08-16T14:29:41.927899", "started_at": "2025-08-16T15:09:55.291671", "completed_at": "2025-08-16T15:10:05.351397", "progress": 0.0, "message": "任务失败", "result": null, "error": "'utf-8' codec can't decode byte 0xbf in position 37: invalid start byte"}, "d7d431ff-c137-4884-8ffd-52803982e495": {"task_id": "d7d431ff-c137-4884-8ffd-52803982e495", "position": "hundreds", "model_type": "xgb", "issue": "2025217", "train_until": null, "priority": 2, "status": "failed", "created_at": "2025-08-16T15:09:55.281671", "started_at": "2025-08-16T15:10:05.353398", "completed_at": "2025-08-16T15:10:15.376453", "progress": 0.0, "message": "任务失败", "result": null, "error": "'utf-8' codec can't decode byte 0xbf in position 37: invalid start byte"}, "1c6c36ba-fbe6-4002-a864-73521d52326e": {"task_id": "1c6c36ba-fbe6-4002-a864-73521d52326e", "position": "hundreds", "model_type": "xgb", "issue": "2025218", "train_until": null, "priority": 2, "status": "failed", "created_at": "2025-08-16T15:11:31.676907", "started_at": "2025-08-16T15:11:35.386592", "completed_at": "2025-08-16T15:11:45.415988", "progress": 0.0, "message": "任务失败", "result": null, "error": "'utf-8' codec can't decode byte 0xbf in position 37: invalid start byte"}, "9f8bb35f-8287-49aa-b6f8-cc24af071c51": {"task_id": "9f8bb35f-8287-49aa-b6f8-cc24af071c51", "position": "tens", "model_type": "lgb", "issue": "2025219", "train_until": null, "priority": 2, "status": "failed", "created_at": "2025-08-16T15:15:55.079448", "started_at": "2025-08-16T15:15:55.087449", "completed_at": "2025-08-16T15:16:05.143215", "progress": 0.0, "message": "任务失败", "result": null, "error": "INFO:src.data.feature_importance:SHAP�⵼��ɹ�\n2025-08-16 15:15:58.139549: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n2025-08-16 15:15:59.536389: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\nTraceback (most recent call last):\n  File \"D:\\github\\fucai3d\\scripts\\train_tens_predictor.py\", line 369, in <module>\n    main()\n  File \"D:\\github\\fucai3d\\scripts\\train_tens_predictor.py\", line 275, in main\n    args = parse_arguments()\n           ^^^^^^^^^^^^^^^^^\n  File \"D:\\github\\fucai3d\\scripts\\train_tens_predictor.py\", line 110, in parse_arguments\n    parser.add_argument(\n  File \"C:\\Program Files\\Python311\\Lib\\argparse.py\", line 1473, in add_argument\n    return self._add_action(action)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\argparse.py\", line 1855, in _add_action\n    self._optionals._add_action(action)\n  File \"C:\\Program Files\\Python311\\Lib\\argparse.py\", line 1675, in _add_action\n    action = super(_ArgumentGroup, self)._add_action(action)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\argparse.py\", line 1487, in _add_action\n    self._check_conflict(action)\n  File \"C:\\Program Files\\Python311\\Lib\\argparse.py\", line 1624, in _check_conflict\n    conflict_handler(action, confl_optionals)\n  File \"C:\\Program Files\\Python311\\Lib\\argparse.py\", line 1633, in _handle_conflict_error\n    raise ArgumentError(action, message % conflict_string)\nargparse.ArgumentError: argument --model/-m: conflicting option strings: --model, -m"}, "1cd37adc-239c-47a2-8f3f-45713b78074b": {"task_id": "1cd37adc-239c-47a2-8f3f-45713b78074b", "position": "units", "model_type": "xgb", "issue": "2025220", "train_until": null, "priority": 2, "status": "failed", "created_at": "2025-08-16T15:20:58.249039", "started_at": "2025-08-16T15:20:58.257041", "completed_at": "2025-08-16T15:21:08.344488", "progress": 0.0, "message": "任务失败", "result": null, "error": "INFO:src.data.feature_importance:SHAP�⵼��ɹ�\n2025-08-16 15:21:01.385358: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n2025-08-16 15:21:02.959005: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\nTraceback (most recent call last):\n  File \"D:\\github\\fucai3d\\scripts\\train_units_predictor.py\", line 362, in <module>\n    main()\n  File \"D:\\github\\fucai3d\\scripts\\train_units_predictor.py\", line 277, in main\n    print_banner()\n  File \"D:\\github\\fucai3d\\scripts\\train_units_predictor.py\", line 116, in print_banner\n    print(\"\\U0001f680 P5-��λԤ����ѵ��ϵͳ\")\nUnicodeEncodeError: 'gbk' codec can't encode character '\\U0001f680' in position 0: illegal multibyte sequence"}}, "task_queue": [], "running_tasks": []}