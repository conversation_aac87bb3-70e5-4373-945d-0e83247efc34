{"tasks": {"ccbf2018-7dcd-4f17-a8d9-afef24e3dfad": {"task_id": "ccbf2018-7dcd-4f17-a8d9-afef24e3dfad", "position": "hundreds", "model_type": "xgb", "issue": "2025217", "train_until": null, "priority": 2, "status": "failed", "created_at": "2025-08-16T14:29:41.927899", "started_at": "2025-08-16T15:09:55.291671", "completed_at": "2025-08-16T15:10:05.351397", "progress": 0.0, "message": "任务失败", "result": null, "error": "'utf-8' codec can't decode byte 0xbf in position 37: invalid start byte", "training_details": null, "data_range": null, "model_params": null, "training_results": null}, "d7d431ff-c137-4884-8ffd-52803982e495": {"task_id": "d7d431ff-c137-4884-8ffd-52803982e495", "position": "hundreds", "model_type": "xgb", "issue": "2025217", "train_until": null, "priority": 2, "status": "failed", "created_at": "2025-08-16T15:09:55.281671", "started_at": "2025-08-16T15:10:05.353398", "completed_at": "2025-08-16T15:10:15.376453", "progress": 0.0, "message": "任务失败", "result": null, "error": "'utf-8' codec can't decode byte 0xbf in position 37: invalid start byte", "training_details": null, "data_range": null, "model_params": null, "training_results": null}, "1c6c36ba-fbe6-4002-a864-73521d52326e": {"task_id": "1c6c36ba-fbe6-4002-a864-73521d52326e", "position": "hundreds", "model_type": "xgb", "issue": "2025218", "train_until": null, "priority": 2, "status": "failed", "created_at": "2025-08-16T15:11:31.676907", "started_at": "2025-08-16T15:11:35.386592", "completed_at": "2025-08-16T15:11:45.415988", "progress": 0.0, "message": "任务失败", "result": null, "error": "'utf-8' codec can't decode byte 0xbf in position 37: invalid start byte", "training_details": null, "data_range": null, "model_params": null, "training_results": null}, "9f8bb35f-8287-49aa-b6f8-cc24af071c51": {"task_id": "9f8bb35f-8287-49aa-b6f8-cc24af071c51", "position": "tens", "model_type": "lgb", "issue": "2025219", "train_until": null, "priority": 2, "status": "failed", "created_at": "2025-08-16T15:15:55.079448", "started_at": "2025-08-16T15:15:55.087449", "completed_at": "2025-08-16T15:16:05.143215", "progress": 0.0, "message": "任务失败", "result": null, "error": "INFO:src.data.feature_importance:SHAP�⵼��ɹ�\n2025-08-16 15:15:58.139549: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n2025-08-16 15:15:59.536389: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\nTraceback (most recent call last):\n  File \"D:\\github\\fucai3d\\scripts\\train_tens_predictor.py\", line 369, in <module>\n    main()\n  File \"D:\\github\\fucai3d\\scripts\\train_tens_predictor.py\", line 275, in main\n    args = parse_arguments()\n           ^^^^^^^^^^^^^^^^^\n  File \"D:\\github\\fucai3d\\scripts\\train_tens_predictor.py\", line 110, in parse_arguments\n    parser.add_argument(\n  File \"C:\\Program Files\\Python311\\Lib\\argparse.py\", line 1473, in add_argument\n    return self._add_action(action)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\argparse.py\", line 1855, in _add_action\n    self._optionals._add_action(action)\n  File \"C:\\Program Files\\Python311\\Lib\\argparse.py\", line 1675, in _add_action\n    action = super(_ArgumentGroup, self)._add_action(action)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\argparse.py\", line 1487, in _add_action\n    self._check_conflict(action)\n  File \"C:\\Program Files\\Python311\\Lib\\argparse.py\", line 1624, in _check_conflict\n    conflict_handler(action, confl_optionals)\n  File \"C:\\Program Files\\Python311\\Lib\\argparse.py\", line 1633, in _handle_conflict_error\n    raise ArgumentError(action, message % conflict_string)\nargparse.ArgumentError: argument --model/-m: conflicting option strings: --model, -m", "training_details": null, "data_range": null, "model_params": null, "training_results": null}, "1cd37adc-239c-47a2-8f3f-45713b78074b": {"task_id": "1cd37adc-239c-47a2-8f3f-45713b78074b", "position": "units", "model_type": "xgb", "issue": "2025220", "train_until": null, "priority": 2, "status": "failed", "created_at": "2025-08-16T15:20:58.249039", "started_at": "2025-08-16T15:20:58.257041", "completed_at": "2025-08-16T15:21:08.344488", "progress": 0.0, "message": "任务失败", "result": null, "error": "INFO:src.data.feature_importance:SHAP�⵼��ɹ�\n2025-08-16 15:21:01.385358: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n2025-08-16 15:21:02.959005: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\nTraceback (most recent call last):\n  File \"D:\\github\\fucai3d\\scripts\\train_units_predictor.py\", line 362, in <module>\n    main()\n  File \"D:\\github\\fucai3d\\scripts\\train_units_predictor.py\", line 277, in main\n    print_banner()\n  File \"D:\\github\\fucai3d\\scripts\\train_units_predictor.py\", line 116, in print_banner\n    print(\"\\U0001f680 P5-��λԤ����ѵ��ϵͳ\")\nUnicodeEncodeError: 'gbk' codec can't encode character '\\U0001f680' in position 0: illegal multibyte sequence", "training_details": null, "data_range": null, "model_params": null, "training_results": null}, "5e2d94a9-06a2-49b7-ab41-b7b964710876": {"task_id": "5e2d94a9-06a2-49b7-ab41-b7b964710876", "position": "hundreds", "model_type": "xgb", "issue": "2025220", "train_until": null, "priority": 2, "status": "failed", "created_at": "2025-08-16T16:05:59.793595", "started_at": "2025-08-16T16:05:59.809598", "completed_at": "2025-08-16T16:06:05.589246", "progress": 0.0, "message": "任务失败", "result": null, "error": "Traceback (most recent call last):\nFile \"D:\\github\\fucai3d\\scripts\\ultra_light_train.py\", line 178, in <module>\n    success = main()\n              ^^^^^^\n  File \"D:\\github\\fucai3d\\scripts\\ultra_light_train.py\", line 158, in main\n    print(\"\\U0001f6a8 ��������ѵ��ģʽ����\")\nUnicodeEncodeError: 'gbk' codec can't encode character '\\U0001f6a8' in position 0: illegal multibyte sequence", "training_details": null, "data_range": null, "model_params": null, "training_results": null}, "1d7e4e02-24a4-42c9-8075-04b0153b4e59": {"task_id": "1d7e4e02-24a4-42c9-8075-04b0153b4e59", "position": "tens", "model_type": "xgb", "issue": "2025221", "train_until": null, "priority": 2, "status": "completed", "created_at": "2025-08-16T16:11:23.366148", "started_at": "2025-08-16T16:11:23.379150", "completed_at": "2025-08-16T16:11:28.807055", "progress": 100.0, "message": "任务已完成", "result": {"duration_seconds": 2.8527047634124756, "memory_stats": {"duration_seconds": 5.013359, "memory_peak_mb": 534.0234375, "memory_avg_mb": 534.0234375, "memory_current_mb": 534.0234375, "cpu_avg_percent": 0.0, "samples_count": 1}, "output": "[ULTRA_LIGHT] ��������ѵ��ģʽ����\n[POSITION] λ��: tens\n[ISSUE] �ں�: 2025221\n[LIMIT] ��������: 500��\n[START] ������������tensλXGBoostѵ��\n[DATA] ��������: 500��\n[FEATURE] ����ά��: (499, 3)\n[TARGET] Ŀ��ά��: (499,)\n[TRAIN] ��ʼѵ��...\n[RESULT] ѵ����ɣ�����׼ȷ��: 0.100\n[SAVE] ģ���ѱ���: models\\tens\\ultra_light_xgb_2025221.json\n[SUCCESS] ��������ѵ���ɹ���ɣ�\n[INFO] ��ʾ: ����Ӧ��ģʽ��ģ�;��ȿ��ܽϵ�"}, "error": null, "training_details": null, "data_range": null, "model_params": null, "training_results": null}, "d6c111d7-4adf-4fe8-8086-de76d9a78709": {"task_id": "d6c111d7-4adf-4fe8-8086-de76d9a78709", "position": "tens", "model_type": "xgb", "issue": "2025222", "train_until": null, "priority": 2, "status": "completed", "created_at": "2025-08-16T16:44:23.476029", "started_at": "2025-08-16T16:44:23.487033", "completed_at": "2025-08-16T16:44:29.022391", "progress": 100.0, "message": "任务已完成", "result": {"duration_seconds": 2.7041335105895996, "memory_stats": {"duration_seconds": 5.012404, "memory_peak_mb": 425.39453125, "memory_avg_mb": 425.39453125, "memory_current_mb": 425.39453125, "cpu_avg_percent": 0.0, "samples_count": 1}, "output": "[ULTRA_LIGHT] ��������ѵ��ģʽ����\n[POSITION] λ��: tens\n[ISSUE] �ں�: 2025222\n[LIMIT] ��������: 500��\n[START] ������������tensλXGBoostѵ��\n[DATA] ��������: 500��\n[RANGE] ���ݷ�Χ: 2024070 �� 2025217\n[FEATURE] ����ά��: (499, 3)\n[TARGET] Ŀ��ά��: (499,)\n[TRAIN] ��ʼѵ��...\n[PARAMS] ģ�Ͳ���: ���3, ѧϰ��0.3, �ִ�50\n[RESULT] ѵ����ɣ�����׼ȷ��: 0.100\n[METRICS] ��ȷԤ��: 10/100\n[SAVE] ģ���ѱ���: models\\tens\\ultra_light_xgb_2025222.json\n[TRAINING_INFO] {\"training_mode\": \"ultra_light\", \"position\": \"tens\", \"target_issue\": \"2025222\", \"data_limit\": 500, \"model_type\": \"XGBoost\", \"start_time\": \"2025-08-16T16:44:24.115097\", \"end_time\": \"2025-08-16T16:44:26.215661\", \"data_range\": {\"total_records\": 500, \"latest_issue\": \"2025217\", \"earliest_issue\": \"2024070\", \"data_source\": \"lottery_data table\"}, \"model_params\": {\"algorithm\": \"XGBoost\", \"objective\": \"multi:softprob\", \"num_class\": 10, \"max_depth\": 3, \"learning_rate\": 0.3, \"num_boost_round\": 50, \"train_test_split\": \"80/20\", \"feature_count\": 3, \"training_samples\": 399, \"test_samples\": 100}, \"training_results\": {\"accuracy\": 0.1, \"test_samples\": 100, \"correct_predictions\": 10, \"model_file\": \"ultra_light_xgb_2025222.json\", \"training_duration_seconds\": 2.100564}}\n[SUCCESS] ��������ѵ���ɹ���ɣ�\n[INFO] ��ʾ: ����Ӧ��ģʽ��ģ�;��ȿ��ܽϵ�"}, "error": null, "training_details": null, "data_range": null, "model_params": null, "training_results": null}, "5fa5af52-53c5-40b7-965f-d9fd0c1111f9": {"task_id": "5fa5af52-53c5-40b7-965f-d9fd0c1111f9", "position": "tens", "model_type": "xgb", "issue": "2025223", "train_until": null, "priority": 2, "status": "completed", "created_at": "2025-08-16T16:49:42.025944", "started_at": "2025-08-16T16:49:42.034945", "completed_at": "2025-08-16T16:49:47.398507", "progress": 100.0, "message": "任务已完成", "result": {"duration_seconds": 2.6172218322753906, "memory_stats": {"duration_seconds": 5.011024, "memory_peak_mb": 548.28515625, "memory_avg_mb": 548.28515625, "memory_current_mb": 548.28515625, "cpu_avg_percent": 0.0, "samples_count": 1}, "output": "[ULTRA_LIGHT] ��������ѵ��ģʽ����\n[POSITION] λ��: tens\n[ISSUE] �ں�: 2025223\n[LIMIT] ��������: 500��\n[START] ������������tensλXGBoostѵ��\n[DATA] ��������: 500��\n[RANGE] ���ݷ�Χ: 2024070 �� 2025217\n[FEATURE] ����ά��: (499, 3)\n[TARGET] Ŀ��ά��: (499,)\n[TRAIN] ��ʼѵ��...\n[PARAMS] ģ�Ͳ���: ���3, ѧϰ��0.3, �ִ�50\n[RESULT] ѵ����ɣ�����׼ȷ��: 0.100\n[METRICS] ��ȷԤ��: 10/100\n[SAVE] ģ���ѱ���: models\\tens\\ultra_light_xgb_2025223.json\n[TRAINING_INFO] {\"training_mode\": \"ultra_light\", \"position\": \"tens\", \"target_issue\": \"2025223\", \"data_limit\": 500, \"model_type\": \"XGBoost\", \"start_time\": \"2025-08-16T16:49:42.458632\", \"end_time\": \"2025-08-16T16:49:44.523667\", \"data_range\": {\"total_records\": 500, \"latest_issue\": \"2025217\", \"earliest_issue\": \"2024070\", \"data_source\": \"lottery_data table\"}, \"model_params\": {\"algorithm\": \"XGBoost\", \"objective\": \"multi:softprob\", \"num_class\": 10, \"max_depth\": 3, \"learning_rate\": 0.3, \"num_boost_round\": 50, \"train_test_split\": \"80/20\", \"feature_count\": 3, \"training_samples\": 399, \"test_samples\": 100}, \"training_results\": {\"accuracy\": 0.1, \"test_samples\": 100, \"correct_predictions\": 10, \"model_file\": \"ultra_light_xgb_2025223.json\", \"training_duration_seconds\": 2.065035}}\n[SUCCESS] ��������ѵ���ɹ���ɣ�\n[INFO] ��ʾ: ����Ӧ��ģʽ��ģ�;��ȿ��ܽϵ�"}, "error": null, "training_details": null, "data_range": null, "model_params": null, "training_results": null}, "81106482-8e6c-4754-904d-1aa864caa476": {"task_id": "81106482-8e6c-4754-904d-1aa864caa476", "position": "tens", "model_type": "xgb", "issue": "2025225", "train_until": null, "priority": 2, "status": "completed", "created_at": "2025-08-16T16:57:33.843198", "started_at": "2025-08-16T16:57:33.853197", "completed_at": "2025-08-16T16:57:39.211670", "progress": 100.0, "message": "任务已完成", "result": {"duration_seconds": 2.639989137649536, "memory_stats": {"duration_seconds": 5.01143, "memory_peak_mb": 562.80078125, "memory_avg_mb": 562.80078125, "memory_current_mb": 562.80078125, "cpu_avg_percent": 0.0, "samples_count": 1}, "output": "[ULTRA_LIGHT] ��������ѵ��ģʽ����\n[POSITION] λ��: tens\n[ISSUE] �ں�: 2025225\n[LIMIT] ��������: 500��\n[START] ������������tensλXGBoostѵ��\n[DATA] ��������: 500��\n[RANGE] ���ݷ�Χ: 2024070 �� 2025217\n[FEATURE] ����ά��: (499, 3)\n[TARGET] Ŀ��ά��: (499,)\n[TRAIN] ��ʼѵ��...\n[PARAMS] ģ�Ͳ���: ���3, ѧϰ��0.3, �ִ�50\n[RESULT] ѵ����ɣ�����׼ȷ��: 0.100\n[METRICS] ��ȷԤ��: 10/100\n[SAVE] ģ���ѱ���: models\\tens\\ultra_light_xgb_2025225.json\n[TRAINING_INFO] {\"training_mode\": \"ultra_light\", \"position\": \"tens\", \"target_issue\": \"2025225\", \"data_limit\": 500, \"model_type\": \"XGBoost\", \"start_time\": \"2025-08-16T16:57:34.254237\", \"end_time\": \"2025-08-16T16:57:36.343855\", \"data_range\": {\"total_records\": 500, \"latest_issue\": \"2025217\", \"earliest_issue\": \"2024070\", \"data_source\": \"lottery_data table\"}, \"model_params\": {\"algorithm\": \"XGBoost\", \"objective\": \"multi:softprob\", \"num_class\": 10, \"max_depth\": 3, \"learning_rate\": 0.3, \"num_boost_round\": 50, \"train_test_split\": \"80/20\", \"feature_count\": 3, \"training_samples\": 399, \"test_samples\": 100}, \"training_results\": {\"accuracy\": 0.1, \"test_samples\": 100, \"correct_predictions\": 10, \"model_file\": \"ultra_light_xgb_2025225.json\", \"training_duration_seconds\": 2.089618}}\n[SUCCESS] ��������ѵ���ɹ���ɣ�\n[INFO] ��ʾ: ����Ӧ��ģʽ��ģ�;��ȿ��ܽϵ�"}, "error": null, "training_details": null, "data_range": null, "model_params": null, "training_results": null}, "5037a0fa-2e48-4eef-aab3-7bb393b197f0": {"task_id": "5037a0fa-2e48-4eef-aab3-7bb393b197f0", "position": "hundreds", "model_type": "xgb", "issue": "2025227", "train_until": null, "priority": 2, "status": "completed", "created_at": "2025-08-16T17:03:48.422291", "started_at": "2025-08-16T17:03:48.433877", "completed_at": "2025-08-16T17:03:53.864867", "progress": 100.0, "message": "任务已完成", "result": {"duration_seconds": 2.5681703090667725, "memory_stats": {"duration_seconds": 5.011307, "memory_peak_mb": 523.09375, "memory_avg_mb": 523.09375, "memory_current_mb": 523.09375, "cpu_avg_percent": 0.0, "samples_count": 1}, "output": "[ULTRA_LIGHT] ��������ѵ��ģʽ����\n[POSITION] λ��: hundreds\n[ISSUE] �ں�: 2025227\n[LIMIT] ��������: 500��\n[START] ������������hundredsλXGBoostѵ��\n[DATA] ��������: 500��\n[RANGE] ���ݷ�Χ: 2024070 �� 2025217\n[FEATURE] ����ά��: (499, 3)\n[TARGET] Ŀ��ά��: (499,)\n[TRAIN] ��ʼѵ��...\n[PARAMS] ģ�Ͳ���: ���3, ѧϰ��0.3, �ִ�50\n[RESULT] ѵ����ɣ�����׼ȷ��: 0.110\n[METRICS] ��ȷԤ��: 11/100\n[SAVE] ģ���ѱ���: models\\hundreds\\ultra_light_xgb_2025227.json\n[TRAINING_INFO] {\"training_mode\": \"ultra_light\", \"position\": \"hundreds\", \"target_issue\": \"2025227\", \"data_limit\": 500, \"model_type\": \"XGBoost\", \"start_time\": \"2025-08-16T17:03:48.938205\", \"end_time\": \"2025-08-16T17:03:50.917483\", \"data_range\": {\"total_records\": 500, \"latest_issue\": \"2025217\", \"earliest_issue\": \"2024070\", \"data_source\": \"lottery_data table\"}, \"model_params\": {\"algorithm\": \"XGBoost\", \"objective\": \"multi:softprob\", \"num_class\": 10, \"max_depth\": 3, \"learning_rate\": 0.3, \"num_boost_round\": 50, \"train_test_split\": \"80/20\", \"feature_count\": 3, \"training_samples\": 399, \"test_samples\": 100}, \"training_results\": {\"accuracy\": 0.11, \"test_samples\": 100, \"correct_predictions\": 11, \"model_file\": \"ultra_light_xgb_2025227.json\", \"training_duration_seconds\": 1.979278}}\n[SUCCESS] ��������ѵ���ɹ���ɣ�\n[INFO] ��ʾ: ����Ӧ��ģʽ��ģ�;��ȿ��ܽϵ�"}, "error": null, "training_details": null, "data_range": null, "model_params": null, "training_results": null}, "51bed6c3-1ea2-4b45-a0cf-d4ccd794c80f": {"task_id": "51bed6c3-1ea2-4b45-a0cf-d4ccd794c80f", "position": "units", "model_type": "xgb", "issue": "2025229", "train_until": null, "priority": 2, "status": "completed", "created_at": "2025-08-16T17:08:30.520185", "started_at": "2025-08-16T17:08:33.909957", "completed_at": "2025-08-16T17:08:39.286068", "progress": 100.0, "message": "任务已完成", "result": {"duration_seconds": 1.9059793949127197, "memory_stats": {"duration_seconds": 5.012351, "memory_peak_mb": 307.6640625, "memory_avg_mb": 307.6640625, "memory_current_mb": 307.6640625, "cpu_avg_percent": 0.0, "samples_count": 1}, "output": "[ULTRA_LIGHT] ��������ѵ��ģʽ����\n[POSITION] λ��: units\n[ISSUE] �ں�: 2025229\n[LIMIT] ��������: 500��\n[START] ������������unitsλXGBoostѵ��\n[DATA] ��������: 500��\n[RANGE] ���ݷ�Χ: 2024070 �� 2025217\n[FEATURE] ����ά��: (499, 3)\n[TARGET] Ŀ��ά��: (499,)\n[TRAIN] ��ʼѵ��...\n[PARAMS] ģ�Ͳ���: ���3, ѧϰ��0.3, �ִ�50\n[RESULT] ѵ����ɣ�����׼ȷ��: 0.050\n[METRICS] ��ȷԤ��: 5/100\n[SAVE] ģ���ѱ���: models\\units\\ultra_light_xgb_2025229.json\n[TRAINING_INFO] {\"training_mode\": \"ultra_light\", \"position\": \"units\", \"target_issue\": \"2025229\", \"data_limit\": 500, \"model_type\": \"XGBoost\", \"start_time\": \"2025-08-16T17:08:34.301469\", \"end_time\": \"2025-08-16T17:08:35.743966\", \"data_range\": {\"total_records\": 500, \"latest_issue\": \"2025217\", \"earliest_issue\": \"2024070\", \"data_source\": \"lottery_data table\"}, \"model_params\": {\"algorithm\": \"XGBoost\", \"objective\": \"multi:softprob\", \"num_class\": 10, \"max_depth\": 3, \"learning_rate\": 0.3, \"num_boost_round\": 50, \"train_test_split\": \"80/20\", \"feature_count\": 3, \"training_samples\": 399, \"test_samples\": 100}, \"training_results\": {\"accuracy\": 0.05, \"test_samples\": 100, \"correct_predictions\": 5, \"model_file\": \"ultra_light_xgb_2025229.json\", \"training_duration_seconds\": 1.442497}}\n[SUCCESS] ��������ѵ���ɹ���ɣ�\n[INFO] ��ʾ: ����Ӧ��ģʽ��ģ�;��ȿ��ܽϵ�"}, "error": null, "training_details": null, "data_range": null, "model_params": null, "training_results": null}}, "task_queue": [], "running_tasks": []}