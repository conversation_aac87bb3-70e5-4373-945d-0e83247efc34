# 福彩3D项目模型文件格式详细分析

## 模型训练完成后生成的文件格式

### 1. XGBoost模型 🌳

#### 文件格式
- **主文件**: `.pkl` (Python Pickle格式)
- **保存路径**: `models/{position}/xgb_{position}_model.pkl`
- **保存方式**: `pickle.dump()`

#### 文件内容
```python
model_data = {
    'model': self.xgb_model,              # XGBoost模型对象
    'label_encoder': self.label_encoder,   # 标签编码器
    'feature_names': self.feature_names,   # 特征名称列表
    'feature_importance': self.feature_importance_, # 特征重要性
    'training_metrics': self.training_metrics,      # 训练指标
    'config': self.xgb_config             # 模型配置
}
```

#### 具体文件示例
- `models/hundreds/xgb_hundreds_model.pkl`
- `models/tens/xgb_tens_model.pkl`
- `models/units/xgb_units_model.pkl`

### 2. LightGBM模型 🚀

#### 文件格式
- **主文件**: `.pkl` (Python Pickle格式)
- **保存路径**: `models/{position}/lgb_{position}_model.pkl`
- **保存方式**: `pickle.dump()`

#### 文件内容
```python
model_data = {
    'model': self.lgb_model,              # LightGBM模型对象
    'label_encoder': self.label_encoder,   # 标签编码器
    'feature_names': self.feature_names,   # 特征名称列表
    'feature_importance': self.feature_importance_, # 特征重要性
    'training_metrics': self.training_metrics,      # 训练指标
    'config': self.lgb_config             # 模型配置
}
```

#### 具体文件示例
- `models/hundreds/lgb_hundreds_model.pkl`
- `models/tens/lgb_tens_model.pkl`
- `models/units/lgb_units_model.pkl`

### 3. LSTM模型 🧠

#### 文件格式
- **主模型文件**: `.h5` (Keras HDF5格式)
- **辅助数据文件**: `_components.pkl` 或 `_metadata.pkl`
- **保存路径**: `models/{position}/lstm_{position}_model`

#### 文件结构
```
lstm_hundreds_model.h5              # Keras模型文件
lstm_hundreds_model_components.pkl  # 辅助组件文件
```

#### 主模型文件 (.h5)
- **格式**: HDF5 (Hierarchical Data Format)
- **保存方式**: `self.lstm_model.save(f"{filepath}.h5")`
- **内容**: 完整的Keras/TensorFlow模型

#### 辅助数据文件 (.pkl)
```python
model_data = {
    'label_encoder': self.label_encoder,    # 标签编码器
    'feature_scaler': self.feature_scaler,  # 特征缩放器
    'feature_names': self.feature_names,    # 特征名称
    'training_metrics': self.training_metrics, # 训练指标
    'config': self.lstm_config,             # LSTM配置
    'sequence_length': self.sequence_length # 序列长度
}
```

#### 具体文件示例
- `models/hundreds/lstm_hundreds_model.h5`
- `models/hundreds/lstm_hundreds_model_components.pkl`

#### 特殊情况 (LSTM跨度/和值模型)
某些LSTM模型使用不同的命名方式：
```
lstm_span_model_keras_model/        # TensorFlow SavedModel格式目录
lstm_span_model_metadata.pkl       # 元数据文件
```

### 4. 集成模型 🎯

#### 文件格式
- **集成配置文件**: `_ensemble.pkl`
- **基础模型文件**: 各自的格式 (`.pkl`, `.h5`)
- **保存路径**: `models/{position}/ensemble_{position}_model`

#### 文件结构
```
ensemble_hundreds_model_ensemble.pkl    # 集成模型配置
ensemble_hundreds_model_xgb.pkl        # XGBoost基础模型
ensemble_hundreds_model_lgb.pkl        # LightGBM基础模型
ensemble_hundreds_model_lstm.h5        # LSTM基础模型
ensemble_hundreds_model_lstm_components.pkl # LSTM辅助文件
```

#### 集成配置文件内容
```python
ensemble_data = {
    'ensemble_config': self.ensemble_config,    # 集成配置
    'model_weights': self.model_weights,        # 模型权重
    'label_encoder': self.label_encoder,        # 标签编码器
    'training_metrics': self.training_metrics,  # 训练指标
    'base_model_paths': base_model_paths,       # 基础模型路径
    'meta_model': self.meta_model               # 元模型(如果有)
}
```

## 模型文件格式总结

### 按文件后缀分类

| 后缀 | 用途 | 模型类型 | 保存方式 |
|------|------|----------|----------|
| **`.pkl`** | 主模型文件 | XGBoost, LightGBM | Python Pickle |
| **`.pkl`** | 辅助数据 | LSTM, 集成模型 | Python Pickle |
| **`.h5`** | 神经网络模型 | LSTM | Keras HDF5 |
| **`/`** | 模型目录 | LSTM (部分) | TensorFlow SavedModel |

### 按模型类型分类

| 模型类型 | 主要格式 | 辅助格式 | 特点 |
|----------|----------|----------|------|
| **XGBoost** | `.pkl` | 无 | 单文件包含所有信息 |
| **LightGBM** | `.pkl` | 无 | 单文件包含所有信息 |
| **LSTM** | `.h5` | `_components.pkl` | 双文件结构 |
| **集成模型** | `_ensemble.pkl` | 基础模型文件 | 多文件结构 |

## 模型保存路径规范

### 目录结构
```
models/
├── hundreds/                    # 百位模型
│   ├── xgb_hundreds_model.pkl
│   ├── lgb_hundreds_model.pkl
│   ├── lstm_hundreds_model.h5
│   ├── lstm_hundreds_model_components.pkl
│   ├── ensemble_hundreds_model_ensemble.pkl
│   └── ensemble_hundreds_model_*
├── tens/                        # 十位模型
│   ├── xgb_tens_model.pkl
│   ├── lgb_tens_model.pkl
│   └── ...
├── units/                       # 个位模型
│   ├── xgb_units_model.pkl
│   ├── lgb_units_model.pkl
│   └── ...
├── sum/                         # 和值模型
│   └── ...
└── span/                        # 跨度模型
    └── ...
```

### 命名规则
- **格式**: `{algorithm}_{position}_model.{extension}`
- **算法**: `xgb`, `lgb`, `lstm`, `ensemble`
- **位置**: `hundreds`, `tens`, `units`, `sum`, `span`
- **扩展**: `.pkl`, `.h5`, `_components.pkl`, `_ensemble.pkl`

## 技术细节

### Pickle格式 (.pkl)
- **优点**: 
  - 保存完整的Python对象
  - 包含模型、编码器、配置等所有信息
  - 加载速度快
- **缺点**: 
  - 仅限Python环境
  - 文件较大
  - 版本兼容性问题

### HDF5格式 (.h5)
- **优点**: 
  - 跨平台兼容
  - 高效存储大型数组
  - 支持压缩
- **缺点**: 
  - 需要额外的辅助文件存储元数据
  - 加载需要重建预处理管道

### TensorFlow SavedModel
- **优点**: 
  - 完整的模型图和权重
  - 支持多种部署环境
  - 版本兼容性好
- **缺点**: 
  - 目录结构复杂
  - 文件较多

## 模型加载机制

### 加载顺序
1. **检查模型文件存在性**
2. **加载主模型文件** (`.pkl` 或 `.h5`)
3. **加载辅助数据文件** (如果存在)
4. **重建预处理管道**
5. **验证模型完整性**

### 错误处理
- **文件不存在**: 返回加载失败
- **版本不兼容**: 尝试兼容性转换
- **数据损坏**: 记录错误并返回失败

## 最佳实践

### 模型保存
1. **统一命名规范**: 遵循项目命名约定
2. **完整性检查**: 保存后验证文件完整性
3. **版本管理**: 记录模型版本和训练时间
4. **备份策略**: 定期备份重要模型文件

### 模型加载
1. **异常处理**: 完善的错误处理机制
2. **兼容性检查**: 验证模型版本兼容性
3. **性能监控**: 记录加载时间和成功率
4. **缓存机制**: 避免重复加载相同模型

---

**分析时间**: 2025-08-16 04:00
**模型文件状态**: 当前项目中暂无已训练的模型文件
**建议**: 完成模型训练后验证文件格式是否符合预期