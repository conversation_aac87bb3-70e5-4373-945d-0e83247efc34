{"learner": {"attributes": {}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "500"}, "iteration_indptr": [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130, 140, 150, 160, 170, 180, 190, 200, 210, 220, 230, 240, 250, 260, 270, 280, 290, 300, 310, 320, 330, 340, 350, 360, 370, 380, 390, 400, 410, 420, 430, 440, 450, 460, 470, 480, 490, 500], "tree_info": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "trees": [{"base_weights": [-0.116459645, 0.031065084, -0.09691012], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 0, "left_children": [1, -1, -1], "loss_changes": [0.6407733, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.031065084, -0.09691012], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.879999, 5.7599998, 6.12], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-1.8657548e-08, 0.0049668825, -0.008645539], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 1, "left_children": [1, -1, -1], "loss_changes": [0.0074193217, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.0049668825, -0.008645539], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.98, 5.04, 5.9399996], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.097244754, 0.042187497, -0.09510087], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 2, "left_children": [1, -1, -1], "loss_changes": [0.70727515, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.042187497, -0.09510087], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.339999, 5.3999996, 5.9399996], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.07159906, 0.10430464], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 3, "left_children": [1, -1, -1], "loss_changes": [1.2074593, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, -0.07159906, 0.10430464], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.419999, 7.3799996, 5.04], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.016207475, -0.13757397, 0.12765957], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 4, "left_children": [1, -1, -1], "loss_changes": [2.6098454, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.13757397, 0.12765957], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.339999, 5.7599998, 5.58], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.008223703, -0.13593751, 0.11982249], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 5, "left_children": [1, -1, -1], "loss_changes": [2.3916426, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, -0.13593751, 0.11982249], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.16, 5.3999996, 5.7599998], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.2917342, 0.11239193, 0.042187497], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 6, "left_children": [1, -1, -1], "loss_changes": [0.050382733, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.11239193, 0.042187497], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.339999, 5.9399996, 5.3999996], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.18356642], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 7, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.055069927], "split_indices": [0], "split_type": [0], "sum_hessian": [10.44], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.039936084, 0.06320225, -0.042187504], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 8, "left_children": [1, -1, -1], "loss_changes": [0.42260566, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.06320225, -0.042187504], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.5199995, 6.12, 5.3999996], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.083472475, -0.084437095, 0.025936596], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 9, "left_children": [1, -1, -1], "loss_changes": [0.44687754, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.084437095, 0.025936596], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.98, 5.04, 5.9399996], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.09679414, 0.0006276645, -0.054589216], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 10, "left_children": [1, -1, -1], "loss_changes": [0.122719, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.0006276645, -0.054589216], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.390093, 5.1786923, 6.211401], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.1075817, -0.0, -0.056718156], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 11, "left_children": [1, -1, -1], "loss_changes": [0.11305544, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.0, -0.056718156], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.634063, 5.380236, 6.2538266], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.17699024, 0.004976818, -0.09799233], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 12, "left_children": [1, -1, -1], "loss_changes": [0.39491045, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.004976818, -0.09799233], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.374679, 5.0557623, 6.3189163], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.07455963, -0.044512384, 0.09269794], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 13, "left_children": [1, -1, -1], "loss_changes": [0.6807294, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, -0.044512384, 0.09269794], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.840456, 5.526024, 5.3144317], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.039561372, -0.09611472, 0.13634945], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 14, "left_children": [1, -1, -1], "loss_changes": [2.0272083, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, -0.09611472, 0.13634945], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.508435, 6.155565, 5.35287], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.14310291, -0.0, -0.06938465], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 15, "left_children": [1, -1, -1], "loss_changes": [0.15508595, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.0, -0.06938465], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.69742, 5.554288, 7.143132], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.16387787, 0.09638736, -0.004816377], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 16, "left_children": [1, -1, -1], "loss_changes": [0.40574238, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.09638736, -0.004816377], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.065372, 6.3127913, 5.752581], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.127433, 0.079305306, -0.009330274], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 17, "left_children": [1, -1, -1], "loss_changes": [0.33077425, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.079305306, -0.009330274], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.78539, 6.8407097, 5.94468], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.027354157, -0.05371265, 0.07599151], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 18, "left_children": [1, -1, -1], "loss_changes": [0.64858353, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.05371265, 0.07599151], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.775804, 6.0314064, 5.7443967], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.014354811], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 19, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.0043064435], "split_indices": [0], "split_type": [0], "sum_hessian": [10.355069], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.24569824], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 20, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.07370947], "split_indices": [0], "split_type": [0], "sum_hessian": [10.932765], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.22802043, -0.0864037, -0.033660874], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 21, "left_children": [1, -1, -1], "loss_changes": [0.005724728, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.0864037, -0.033660874], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.235004, 5.19425, 5.040755], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.078887224], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 22, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.023666168], "split_indices": [0], "split_type": [0], "sum_hessian": [8.865858], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.081416585, -0.13351533, 0.07090997], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 23, "left_children": [1, -1, -1], "loss_changes": [1.4982361, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, -0.13351533, 0.07090997], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.87443, 5.031338, 5.843092], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.01289424, -0.13150562, 0.11206518], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 24, "left_children": [1, -1, -1], "loss_changes": [2.279984, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.13150562, 0.11206518], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.043225, 5.1308737, 6.9123516], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.04590902, 0.055100717], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 25, "left_children": [1, -1, -1], "loss_changes": [0.3589627, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.04590902, 0.055100717], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.662664, 5.611365, 5.051299], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.013669687, 0.04147949, -0.045006238], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 26, "left_children": [1, -1, -1], "loss_changes": [0.3182137, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, 0.04147949, -0.045006238], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.643086, 8.1817045, 5.4613824], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.003185008, 0.09177567, -0.09464411], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 27, "left_children": [1, -1, -1], "loss_changes": [1.3706313, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.09177567, -0.09464411], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.217222, 6.446664, 5.770559], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.099406466, -0.003937847, 0.061877917], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 28, "left_children": [1, -1, -1], "loss_changes": [0.17690957, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.003937847, 0.061877917], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.881127, 5.758184, 6.1229434], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.07334872, -0.0, -0.03796557], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 29, "left_children": [1, -1, -1], "loss_changes": [0.036316216, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, -0.0, -0.03796557], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.756054, 5.539268, 5.216786], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.036083706], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 30, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.010825112], "split_indices": [0], "split_type": [0], "sum_hessian": [10.03148], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.15767542, -0.1337688, 0.03600469], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 31, "left_children": [1, -1, -1], "loss_changes": [1.0334117, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.1337688, 0.03600469], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.8359995, 5.1966686, 5.639331], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.009840544, -0.03991981, 0.05131964], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 32, "left_children": [1, -1, -1], "loss_changes": [0.29186141, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.03991981, 0.05131964], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.514639, 5.337759, 5.1768794], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.1684364, -0.0, 0.096251], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 33, "left_children": [1, -1, -1], "loss_changes": [0.3041699, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.0, 0.096251], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.630618, 5.4701004, 5.1605177], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.032051884, 0.024330383, -0.048182517], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 34, "left_children": [1, -1, -1], "loss_changes": [0.20302212, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.024330383, -0.048182517], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.51469, 5.9068537, 5.607837], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.10782312, -0.055875856, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 35, "left_children": [1, -1, -1], "loss_changes": [0.103612766, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.055875856, -0.0], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.646802, 6.2251763, 5.4216256], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.093001895, 0.04973204, -0.00082435587], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 36, "left_children": [1, -1, -1], "loss_changes": [0.11671036, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.04973204, -0.00082435587], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.34476, 7.760049, 5.584711], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.17086886, 0.10323239, -0.012055019], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 37, "left_children": [1, -1, -1], "loss_changes": [0.5656781, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.10323239, -0.012055019], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.262104, 7.1975107, 6.064593], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.09551848, -0.015813274, 0.07685423], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 38, "left_children": [1, -1, -1], "loss_changes": [0.3411445, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.015813274, 0.07685423], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.903449, 6.217023, 5.6864257], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.4067167], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 39, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.122015014], "split_indices": [0], "split_type": [0], "sum_hessian": [10.208472], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.03228393, -0.03769301], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 40, "left_children": [1, -1, -1], "loss_changes": [0.17765605, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.03228393, -0.03769301], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.044759, 5.7223034, 5.322456], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.13489875], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 41, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.040469628], "split_indices": [0], "split_type": [0], "sum_hessian": [10.241028], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.044117566], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 42, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.01323527], "split_indices": [0], "split_type": [0], "sum_hessian": [10.164611], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.07381925, -0.10295384, 0.07005317], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 43, "left_children": [1, -1, -1], "loss_changes": [1.218571, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.10295384, 0.07005317], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.619656, 6.836509, 5.7831464], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.13455696, 0.021972965, -0.09907122], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 44, "left_children": [1, -1, -1], "loss_changes": [0.5859059, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.021972965, -0.09907122], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.182165, 5.963728, 6.218437], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.13387163, 0.12844498], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 45, "left_children": [1, -1, -1], "loss_changes": [2.4833698, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, -0.13387163, 0.12844498], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.991378, 5.4419045, 5.5494733], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.24168748, 0.016915472, 0.13188292], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 46, "left_children": [1, -1, -1], "loss_changes": [0.44438493, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.016915472, 0.13188292], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.144914, 7.71343, 5.4314833], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.077697195, 0.11994394, -0.0625399], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 47, "left_children": [1, -1, -1], "loss_changes": [1.3140644, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.11994394, -0.0625399], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.143966, 5.690658, 6.453308], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.17570676, 0.11458806, -0.01075398], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 48, "left_children": [1, -1, -1], "loss_changes": [0.60876745, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.11458806, -0.01075398], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.844781, 5.8290014, 6.0157795], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.009491896], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 49, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.002847569], "split_indices": [0], "split_type": [0], "sum_hessian": [9.509019], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.07632695, -0.09317358, 0.0557882], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 50, "left_children": [1, -1, -1], "loss_changes": [0.803967, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.09317358, 0.0557882], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.942288, 5.8841987, 5.0580893], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.008475721], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 51, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0025427165], "split_indices": [0], "split_type": [0], "sum_hessian": [9.516846], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.04215063], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 52, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.01264519], "split_indices": [0], "split_type": [0], "sum_hessian": [10.125492], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.07839435, -0.032915927, 0.1008582], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 53, "left_children": [1, -1, -1], "loss_changes": [0.7019972, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, -0.032915927, 0.1008582], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.247812, 7.188748, 5.0590644], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.016436204, -0.08845838, 0.07487448], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 54, "left_children": [1, -1, -1], "loss_changes": [0.9830586, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, -0.08845838, 0.07487448], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.218, 5.611873, 5.6061273], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.032847013, 0.028379142], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 55, "left_children": [1, -1, -1], "loss_changes": [0.13292862, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.032847013, 0.028379142], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.816023, 5.002051, 5.813972], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.06848823, 0.1363985, -0.05505231], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 56, "left_children": [1, -1, -1], "loss_changes": [1.5479281, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.1363985, -0.05505231], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.770505, 5.276727, 8.493778], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.12333129, 0.09005072, -0.0031533244], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 57, "left_children": [1, -1, -1], "loss_changes": [0.35518956, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.09005072, -0.0031533244], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.649796, 5.2360992, 7.4136963], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.100276716, 0.03573159, -0.07910147], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 58, "left_children": [1, -1, -1], "loss_changes": [0.55878484, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.03573159, -0.07910147], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.30913, 5.535687, 7.773443], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.023255909], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 59, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.006976773], "split_indices": [0], "split_type": [0], "sum_hessian": [9.786542], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.11316288, 0.05366635, 0.004277342], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 60, "left_children": [1, -1, -1], "loss_changes": [0.051395863, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.05366635, 0.004277342], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.2699995, 5.07667, 5.1933293], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0539123], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 61, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.01617369], "split_indices": [0], "split_type": [0], "sum_hessian": [9.762224], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.17048593], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 62, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.05114578], "split_indices": [0], "split_type": [0], "sum_hessian": [9.3514805], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.03241102, -0.01956194, 0.049114693], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 63, "left_children": [1, -1, -1], "loss_changes": [0.181303, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, -0.01956194, 0.049114693], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.54341, 6.472949, 5.0704613], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.028471606, 0.025390135, -0.04778759], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 64, "left_children": [1, -1, -1], "loss_changes": [0.20387803, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.025390135, -0.04778759], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.363118, 5.873565, 5.4895535], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.031066444], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 65, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.009319933], "split_indices": [0], "split_type": [0], "sum_hessian": [9.772659], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.14167967, 0.06794464, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 66, "left_children": [1, -1, -1], "loss_changes": [0.17984685, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.06794464, -0.0], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.556893, 8.202789, 5.3541036], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.09282838, 0.087870486, -0.04847089], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 67, "left_children": [1, -1, -1], "loss_changes": [0.7853594, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.087870486, -0.04847089], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.242188, 7.547854, 5.6943345], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.13634706, 0.08036675, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 68, "left_children": [1, -1, -1], "loss_changes": [0.21882671, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.08036675, -0.0], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.501237, 5.5467052, 6.9545317], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.029981352], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 69, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.008994406], "split_indices": [0], "split_type": [0], "sum_hessian": [9.927257], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.024820762], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 70, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.007446229], "split_indices": [0], "split_type": [0], "sum_hessian": [10.215624], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.015898908], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 71, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.0047696726], "split_indices": [0], "split_type": [0], "sum_hessian": [10.478627], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.08943926, 0.094554596], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 72, "left_children": [1, -1, -1], "loss_changes": [1.209513, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.08943926, 0.094554596], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.868684, 5.584428, 5.2842555], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.036084086, 0.015474734, -0.044275038], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 73, "left_children": [1, -1, -1], "loss_changes": [0.14070117, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.015474734, -0.044275038], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.633385, 6.311482, 5.3219028], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.068437144, 0.0010164896, 0.030736458], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 74, "left_children": [1, -1, -1], "loss_changes": [0.015127193, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.0010164896, 0.030736458], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.7762575, 5.087384, 5.688874], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.012494278, -0.040283125, 0.026388103], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 75, "left_children": [1, -1, -1], "loss_changes": [0.16649398, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.040283125, 0.026388103], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.265459, 5.389934, 5.8755255], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.035408527, 0.065860525, -0.0631125], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 76, "left_children": [1, -1, -1], "loss_changes": [0.7573964, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.065860525, -0.0631125], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.964636, 5.754089, 9.210547], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.048928395, 0.064961575, -0.05122407], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 77, "left_children": [1, -1, -1], "loss_changes": [0.6013647, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.064961575, -0.05122407], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.204102, 8.323129, 5.8809724], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.08539212, -0.037154123, -0.0027773348], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 78, "left_children": [1, -1, -1], "loss_changes": [0.026209146, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.037154123, -0.0027773348], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.889948, 7.275155, 5.614793], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.053121813], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 79, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.015936544], "split_indices": [0], "split_type": [0], "sum_hessian": [9.775901], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.1133199], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 80, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.03399597], "split_indices": [0], "split_type": [0], "sum_hessian": [9.672282], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.2647247], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 81, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.079417415], "split_indices": [0], "split_type": [0], "sum_hessian": [8.40874], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.025748238], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 82, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.007724472], "split_indices": [0], "split_type": [0], "sum_hessian": [9.768411], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.085971266, -0.0066629667, 0.05808495], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 83, "left_children": [1, -1, -1], "loss_changes": [0.1726811, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.0066629667, 0.05808495], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.987362, 5.9113145, 6.0760474], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 84, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0], "split_indices": [0], "split_type": [0], "sum_hessian": [10.511476], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0851406, -0.050561745, 0.002502893], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 85, "left_children": [1, -1, -1], "loss_changes": [0.111323215, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, -0.050561745, 0.002502893], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.043707, 5.9777083, 5.065999], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.18261775, 0.0010149042, 0.11854582], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 86, "left_children": [1, -1, -1], "loss_changes": [0.5331429, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.0010149042, 0.11854582], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.076877, 8.443089, 5.633788], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.19658901, 0.120101996, -0.011773319], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 87, "left_children": [1, -1, -1], "loss_changes": [0.70273906, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.120101996, -0.011773319], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.546327, 6.584257, 5.96207], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.055276137, 0.049193803], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 88, "left_children": [1, -1, -1], "loss_changes": [0.43846112, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.055276137, 0.049193803], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.486835, 5.9291496, 6.557685], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.055914447], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 89, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.016774334], "split_indices": [0], "split_type": [0], "sum_hessian": [9.717017], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.02118254], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 90, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0063547627], "split_indices": [0], "split_type": [0], "sum_hessian": [9.730701], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.031465363], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 91, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.009439609], "split_indices": [0], "split_type": [0], "sum_hessian": [10.241961], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.12002849], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 92, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.036008548], "split_indices": [0], "split_type": [0], "sum_hessian": [10.1106205], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.12765338, 0.07297234, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 93, "left_children": [1, -1, -1], "loss_changes": [0.20297632, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.07297234, -0.0], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.225497, 5.797718, 5.4277797], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0360526, 0.04644368, -0.06070033], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 94, "left_children": [1, -1, -1], "loss_changes": [0.43689874, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.04644368, -0.06070033], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.629364, 5.1665645, 6.462799], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.029293008], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 95, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.008787903], "split_indices": [0], "split_type": [0], "sum_hessian": [9.7979555], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.008417584, -0.03226773, 0.05048008], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 96, "left_children": [1, -1, -1], "loss_changes": [0.3197422, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.03226773, 0.05048008], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.943471, 8.487213, 6.456258], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.25924826, -0.11474954, -0.02361284], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 97, "left_children": [1, -1, -1], "loss_changes": [0.245933, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.11474954, -0.02361284], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.478161, 7.0159583, 6.462203], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.004236734, 0.043103788, -0.044084806], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 98, "left_children": [1, -1, -1], "loss_changes": [0.29849762, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.043103788, -0.044084806], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.17304, 6.7023716, 5.4706693], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.024469892], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 99, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0073409677], "split_indices": [0], "split_type": [0], "sum_hessian": [9.82208], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.06759018, 0.0012084874, -0.042380825], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 100, "left_children": [1, -1, -1], "loss_changes": [0.07641559, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.0012084874, -0.042380825], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.7727585, 5.253899, 5.5188594], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.049441073], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 101, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.0148323225], "split_indices": [0], "split_type": [0], "sum_hessian": [8.203067], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.106421724], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 102, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.03192652], "split_indices": [0], "split_type": [0], "sum_hessian": [10.305719], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.04474789, -0.023473347, 0.054973844], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 103, "left_children": [1, -1, -1], "loss_changes": [0.26019922, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, -0.023473347, 0.054973844], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.816948, 6.636485, 6.1804633], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.0041333945, 0.0021794464], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 104, "left_children": [1, -1, -1], "loss_changes": [0.0015879774, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.0041333945, 0.0021794464], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.713963, 5.69048, 5.0234823], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.040340252], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 105, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.012102076], "split_indices": [0], "split_type": [0], "sum_hessian": [10.013997], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.0106963, 0.06061933, -0.03304898], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 106, "left_children": [1, -1, -1], "loss_changes": [0.39919144, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.06061933, -0.03304898], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.899801, 5.8277297, 9.072071], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.023505852, -0.044966135, 0.044914477], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 107, "left_children": [1, -1, -1], "loss_changes": [0.33225244, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, -0.044966135, 0.044914477], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.157954, 5.130056, 8.027899], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.09648485, 0.079397924, -0.04155543], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 108, "left_children": [1, -1, -1], "loss_changes": [0.6154959, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.079397924, -0.04155543], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.326895, 7.9424076, 5.3844867], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.04628641], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 109, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.013885924], "split_indices": [0], "split_type": [0], "sum_hessian": [9.876074], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.1208164], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 110, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.03624492], "split_indices": [0], "split_type": [0], "sum_hessian": [10.171676], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.09114371], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 111, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.027343115], "split_indices": [0], "split_type": [0], "sum_hessian": [9.167018], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.08658631, 0.03930005, -0.09057127], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 112, "left_children": [1, -1, -1], "loss_changes": [0.6238873, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.03930005, -0.09057127], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.107667, 5.4885044, 5.6191626], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.04079699, -0.08269477, 0.07983599], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 113, "left_children": [1, -1, -1], "loss_changes": [1.061823, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, -0.08269477, 0.07983599], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.876354, 5.0778303, 7.798524], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.04619298, 0.033174664, -0.002504317], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 114, "left_children": [1, -1, -1], "loss_changes": [0.053944938, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.033174664, -0.002504317], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.186903, 5.499878, 5.6870255], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.16315457], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 115, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.048946373], "split_indices": [0], "split_type": [0], "sum_hessian": [8.4166975], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.19880304, 0.036803734, -0.11866863], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 116, "left_children": [1, -1, -1], "loss_changes": [1.0063831, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.036803734, -0.11866863], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.711477, 5.155779, 8.555697], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.036690447, 0.020271271, -0.04662691], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 117, "left_children": [1, -1, -1], "loss_changes": [0.17239572, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.020271271, -0.04662691], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.36818, 5.833948, 5.5342317], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.033691697, -0.03816156, 0.07299901], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 118, "left_children": [1, -1, -1], "loss_changes": [0.5193416, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.03816156, 0.07299901], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.135016, 7.3840103, 5.751006], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.1168831], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 119, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.035064932], "split_indices": [0], "split_type": [0], "sum_hessian": [9.822693], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.038190175], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 120, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.011457053], "split_indices": [0], "split_type": [0], "sum_hessian": [9.99943], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.018916667], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 121, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.005675], "split_indices": [0], "split_type": [0], "sum_hessian": [9.74571], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.010176915, -0.034440268, 0.043501206], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 122, "left_children": [1, -1, -1], "loss_changes": [0.21273115, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.034440268, 0.043501206], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.457551, 5.119178, 5.3383727], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.07605147, -0.13445581, 0.06643546], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 123, "left_children": [1, -1, -1], "loss_changes": [1.6012003, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.13445581, 0.06643546], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.361576, 5.416248, 6.945328], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.08281804], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 124, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.024845412], "split_indices": [0], "split_type": [0], "sum_hessian": [10.71143], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.036103874], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 125, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.010831162], "split_indices": [0], "split_type": [0], "sum_hessian": [10.0043335], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.10777041, 0.06487045, 0.0030719342], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 126, "left_children": [1, -1, -1], "loss_changes": [0.13534611, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.06487045, 0.0030719342], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.144513, 5.6351795, 8.509334], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0705365, -0.027823178, 0.06630586], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 127, "left_children": [1, -1, -1], "loss_changes": [0.39429152, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, -0.027823178, 0.06630586], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.690764, 6.4314904, 7.2592745], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.04786679, 0.031018088, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 128, "left_children": [1, -1, -1], "loss_changes": [0.039938584, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.031018088, -0.0], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.774929, 5.6883535, 7.086576], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.01683623, -0.0, 0.0070277606], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 129, "left_children": [1, -1, -1], "loss_changes": [6.19099e-05, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.0, 0.0070277606], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.472917, 5.4339585, 5.038958], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.019631794], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 130, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.005889538], "split_indices": [0], "split_type": [0], "sum_hessian": [9.704515], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.023472236], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 131, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0070416713], "split_indices": [0], "split_type": [0], "sum_hessian": [9.844415], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.020825038, 0.082346916, -0.057573434], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 132, "left_children": [1, -1, -1], "loss_changes": [0.75964653, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.082346916, -0.057573434], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.94779, 5.5316706, 6.416119], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.08485793, -0.09594541], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 133, "left_children": [1, -1, -1], "loss_changes": [1.2736113, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.08485793, -0.09594541], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.091211, 6.528452, 5.5627594], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.118734464], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 134, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.03562034], "split_indices": [0], "split_type": [0], "sum_hessian": [9.47834], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 135, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0], "split_indices": [0], "split_type": [0], "sum_hessian": [9.271819], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.036591917, -0.065725036, 0.06353024, -0.0, -0.035707556], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 136, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.33023936, 0.037705876, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [6.0, 5.0, 0.06353024, -0.0, -0.035707556], "split_indices": [1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [15.83365, 10.029528, 5.8041224, 5.0048895, 5.024638], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.06179326, 0.06677504, -0.047068805], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 137, "left_children": [1, -1, -1], "loss_changes": [0.5621057, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.06677504, -0.047068805], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.805711, 8.209315, 5.596395], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.01945669, 0.037506547, -0.014334244], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 138, "left_children": [1, -1, -1], "loss_changes": [0.11618883, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.037506547, -0.014334244], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.439564, 5.4736342, 7.9659295], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.09380864], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 139, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.028142594], "split_indices": [0], "split_type": [0], "sum_hessian": [9.289824], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.02775501], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 140, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.008326503], "split_indices": [0], "split_type": [0], "sum_hessian": [9.870495], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.085328795], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 141, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.02559864], "split_indices": [0], "split_type": [0], "sum_hessian": [9.136469], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.11138227, 0.043242585, -0.09913189], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 142, "left_children": [1, -1, -1], "loss_changes": [0.7783885, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.043242585, -0.09913189], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.722384, 5.357962, 6.364423], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.14390102, -0.01968787, -0.058544386], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 143, "left_children": [1, -1, -1], "loss_changes": [0.013490289, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.01968787, -0.058544386], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.05661, 6.444768, 5.6118426], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.11594537, 0.023648594, -0.09301405], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 144, "left_children": [1, -1, -1], "loss_changes": [0.5254361, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.023648594, -0.09301405], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.640184, 5.8493004, 5.790884], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00566344], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 145, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0016990321], "split_indices": [0], "split_type": [0], "sum_hessian": [9.418107], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.07490103, 0.011172523, -0.0514258], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 146, "left_children": [1, -1, -1], "loss_changes": [0.18326229, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.011172523, -0.0514258], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.10192, 6.327821, 7.7740993], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.09230844, -0.0210314, 0.070594154], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 147, "left_children": [1, -1, -1], "loss_changes": [0.36067817, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, -0.0210314, 0.070594154], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.107523, 6.048577, 7.0589457], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.03126649, 0.029663838, -0.00420654], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 148, "left_children": [1, -1, -1], "loss_changes": [0.053190287, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.029663838, -0.00420654], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.146629, 5.6845884, 7.4620404], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0814686], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 149, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.024440581], "split_indices": [0], "split_type": [0], "sum_hessian": [9.307568], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 150, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0], "split_indices": [0], "split_type": [0], "sum_hessian": [9.15838], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.07496419], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 151, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.022489257], "split_indices": [0], "split_type": [0], "sum_hessian": [9.430667], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.010723389, 0.045836713, -0.050612714], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 152, "left_children": [1, -1, -1], "loss_changes": [0.3436855, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.045836713, -0.050612714], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.244853, 5.230893, 6.01396], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.058211103, -0.015104593, 0.052446745], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 153, "left_children": [1, -1, -1], "loss_changes": [0.18973623, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.015104593, 0.052446745], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.309326, 6.224911, 6.0844145], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.010264327, 0.035643253, -0.044851463], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 154, "left_children": [1, -1, -1], "loss_changes": [0.23719122, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.035643253, -0.044851463], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.038339, 5.431713, 5.6066256], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.1209124], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 155, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.03627372], "split_indices": [0], "split_type": [0], "sum_hessian": [9.831515], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.09101485, -0.0058625997, 0.062233295], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 156, "left_children": [1, -1, -1], "loss_changes": [0.22195409, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.0058625997, 0.062233295], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.468365, 7.407634, 7.0607314], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.010726958, -0.0175323, 0.010477672], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 157, "left_children": [1, -1, -1], "loss_changes": [0.03751204, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.0175323, 0.010477672], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.204478, 7.8795, 6.3249784], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.10751957, 0.0027182512, 0.055209413], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 158, "left_children": [1, -1, -1], "loss_changes": [0.08445449, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.0027182512, 0.055209413], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.995436, 6.7432895, 6.2521462], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.002896252], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 159, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.00086887565], "split_indices": [0], "split_type": [0], "sum_hessian": [9.396529], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.11904729, 0.057666343, 0.0037240244], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 160, "left_children": [1, -1, -1], "loss_changes": [0.06447813, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.057666343, 0.0037240244], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.191827, 5.012058, 5.1797686], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0024111574], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 161, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.00072334724], "split_indices": [0], "split_type": [0], "sum_hessian": [9.430133], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.015058962, 0.033075, -0.045165733], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 162, "left_children": [1, -1, -1], "loss_changes": [0.22943997, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.033075, -0.045165733], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.29854, 5.5832376, 5.715303], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.036196847, 0.02623022, -0.051303837], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 163, "left_children": [1, -1, -1], "loss_changes": [0.22827257, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.02623022, -0.051303837], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.297188, 5.68946, 5.607728], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.060153056, -0.0, 0.033753734], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 164, "left_children": [1, -1, -1], "loss_changes": [0.04043859, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.0, 0.033753734], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.96107, 5.3477454, 5.613324], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00547154], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 165, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.0016414621], "split_indices": [0], "split_type": [0], "sum_hessian": [8.8237705], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.028331688, -0.041699845, 0.02279915], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 166, "left_children": [1, -1, -1], "loss_changes": [0.19625336, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.041699845, 0.02279915], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.537156, 7.3581347, 7.179022], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.09613022, 0.026615065, -0.100709446], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 167, "left_children": [1, -1, -1], "loss_changes": [0.7547572, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.026615065, -0.100709446], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.813521, 8.481545, 6.331976], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.012787205, -0.061101485, 0.04969051], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 168, "left_children": [1, -1, -1], "loss_changes": [0.5061812, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.061101485, 0.04969051], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.760117, 6.3671727, 6.392944], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 169, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0], "split_indices": [0], "split_type": [0], "sum_hessian": [9.2887745], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.08882942, -0.086194724, 0.027411245], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 170, "left_children": [1, -1, -1], "loss_changes": [0.48506755, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.086194724, 0.027411245], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.279962, 5.3488464, 5.931115], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.03100034], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 171, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0093001025], "split_indices": [0], "split_type": [0], "sum_hessian": [10.013434], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0907891], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 172, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.027236732], "split_indices": [0], "split_type": [0], "sum_hessian": [9.058824], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.16493912], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 173, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.04948174], "split_indices": [0], "split_type": [0], "sum_hessian": [10.511894], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.06869622, 0.00043043523, 0.031470444], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 174, "left_children": [1, -1, -1], "loss_changes": [0.017835274, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.00043043523, 0.031470444], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.752306, 5.0927505, 5.659556], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.092202894], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 175, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.027660869], "split_indices": [0], "split_type": [0], "sum_hessian": [9.057724], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.099818386, 0.063564874, -0.014232446], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 176, "left_children": [1, -1, -1], "loss_changes": [0.27791464, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.063564874, -0.014232446], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.235072, 8.219943, 6.0151286], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.108822085, 0.07896842, -0.008886644], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 177, "left_children": [1, -1, -1], "loss_changes": [0.35521215, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.07896842, -0.008886644], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.159283, 6.609137, 7.550145], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.035188697, 0.048589416, -0.039662994], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 178, "left_children": [1, -1, -1], "loss_changes": [0.32031277, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.048589416, -0.039662994], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.944116, 7.7241917, 5.219924], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.024414403], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 179, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.007324321], "split_indices": [0], "split_type": [0], "sum_hessian": [9.827759], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.049609922], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 180, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.014882977], "split_indices": [0], "split_type": [0], "sum_hessian": [8.13761], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.0014248398], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 181, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.00042745197], "split_indices": [0], "split_type": [0], "sum_hessian": [9.001502], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.122758426], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 182, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.03682753], "split_indices": [0], "split_type": [0], "sum_hessian": [9.790041], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.13311678, 0.06254325, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 183, "left_children": [1, -1, -1], "loss_changes": [0.11932422, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.06254325, -0.0], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.304424, 7.169725, 5.1347], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.09334975], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 184, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.028004926], "split_indices": [0], "split_type": [0], "sum_hessian": [10.303614], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 185, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0], "split_indices": [0], "split_type": [0], "sum_hessian": [9.080337], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.042310335, 0.046041936, -0.057158425], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 186, "left_children": [1, -1, -1], "loss_changes": [0.4934219, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.046041936, -0.057158425], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.868298, 6.100241, 8.768057], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.06932212, 0.076691136, -0.012481937], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 187, "left_children": [1, -1, -1], "loss_changes": [0.36461556, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.076691136, -0.012481937], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [15.091633, 5.4816484, 9.609984], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.051893897, 0.05051602, -0.0074572307], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 188, "left_children": [1, -1, -1], "loss_changes": [0.14013, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.05051602, -0.0074572307], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.599338, 5.0474, 7.551938], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.19731557], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 189, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.059194673], "split_indices": [0], "split_type": [0], "sum_hessian": [9.491879], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.057558924], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 190, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.017267678], "split_indices": [0], "split_type": [0], "sum_hessian": [9.659106], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.014694137], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 191, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.0044082412], "split_indices": [0], "split_type": [0], "sum_hessian": [8.76028], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.16824134], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 192, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.050472405], "split_indices": [0], "split_type": [0], "sum_hessian": [9.349954], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.04402373, 0.04086219, -0.051837932], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 193, "left_children": [1, -1, -1], "loss_changes": [0.3550136, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.04086219, -0.051837932], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.125128, 5.121924, 8.003204], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.06585027], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 194, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.019755082], "split_indices": [0], "split_type": [0], "sum_hessian": [10.260529], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.045217074], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 195, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.013565123], "split_indices": [0], "split_type": [0], "sum_hessian": [9.854596], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.046190023, 0.0533641, -0.060510647], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 196, "left_children": [1, -1, -1], "loss_changes": [0.59424466, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.0533641, -0.060510647], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.9556055, 5.804825, 9.150781], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.051019296, -0.035433173, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 197, "left_children": [1, -1, -1], "loss_changes": [0.0652062, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, -0.035433173, -0.0], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [15.26382, 6.708931, 8.554888], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.11636138, -0.06579135, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 198, "left_children": [1, -1, -1], "loss_changes": [0.15978302, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.06579135, -0.0], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.664213, 6.450671, 7.213542], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 199, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0], "split_indices": [0], "split_type": [0], "sum_hessian": [9.227883], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.033314712, -0.03554141], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 200, "left_children": [1, -1, -1], "loss_changes": [0.1695663, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.033314712, -0.03554141], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.897575, 5.7253714, 5.1722035], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.043931786], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 201, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.013179536], "split_indices": [0], "split_type": [0], "sum_hessian": [8.264615], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.03455219], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 202, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.0103656575], "split_indices": [0], "split_type": [0], "sum_hessian": [9.9641905], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.026777701, 0.024359148, -0.003512216], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 203, "left_children": [1, -1, -1], "loss_changes": [0.0366973, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.024359148, -0.003512216], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.750276, 5.898349, 6.8519263], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.06978886, 0.036946088, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 204, "left_children": [1, -1, -1], "loss_changes": [0.0417066, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.036946088, -0.0], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.729206, 5.516432, 5.2127743], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.03904104], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 205, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.011712312], "split_indices": [0], "split_type": [0], "sum_hessian": [9.920059], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.059496988, -0.016038055, 0.068143554], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 206, "left_children": [1, -1, -1], "loss_changes": [0.33173883, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.016038055, 0.068143554], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.959554, 8.988175, 5.9713783], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.04015377, 0.04245744, -0.06728243], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 207, "left_children": [1, -1, -1], "loss_changes": [0.51010764, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.04245744, -0.06728243], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.0532255, 6.4132643, 6.6399612], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.051805418, -0.08466855], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 208, "left_children": [1, -1, -1], "loss_changes": [0.7770033, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, 0.051805418, -0.08466855], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.989399, 8.96528, 5.0241194], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.12246112], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 209, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.036738336], "split_indices": [0], "split_type": [0], "sum_hessian": [8.686289], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.1837823], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 210, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.05513469], "split_indices": [0], "split_type": [0], "sum_hessian": [9.032833], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.0102936765], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 211, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.0030881031], "split_indices": [0], "split_type": [0], "sum_hessian": [8.829255], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.056860227], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 212, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.017058069], "split_indices": [0], "split_type": [0], "sum_hessian": [9.568028], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.007061701, -0.022366377, 0.01582626], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 213, "left_children": [1, -1, -1], "loss_changes": [0.05997241, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, -0.022366377, 0.01582626], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.340928, 6.468093, 5.872835], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.030785808, 0.019991862, -0.04331367], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 214, "left_children": [1, -1, -1], "loss_changes": [0.1542416, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.019991862, -0.04331367], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.365432, 5.8667297, 5.498702], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.004908404], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 215, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0014725212], "split_indices": [0], "split_type": [0], "sum_hessian": [9.335379], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.05919498, 0.049996305, -0.037299242], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 216, "left_children": [1, -1, -1], "loss_changes": [0.3404632, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, 0.049996305, -0.037299242], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.998254, 9.861114, 5.13714], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.02961927, 0.028726421], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 217, "left_children": [1, -1, -1], "loss_changes": [0.1477707, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, -0.02961927, 0.028726421], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.645856, 6.453665, 7.1921916], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.09846645, 0.08446468, -0.010729554], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 218, "left_children": [1, -1, -1], "loss_changes": [0.38122505, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.08446468, -0.010729554], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.102565, 5.393587, 7.7089777], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.020007316], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 219, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.006002195], "split_indices": [0], "split_type": [0], "sum_hessian": [9.783395], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.025681864], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 220, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0077045597], "split_indices": [0], "split_type": [0], "sum_hessian": [9.850355], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.072389245], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 221, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.021716774], "split_indices": [0], "split_type": [0], "sum_hessian": [8.852131], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.056776896, -0.03252854, 0.060905818], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 222, "left_children": [1, -1, -1], "loss_changes": [0.32583138, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.03252854, 0.060905818], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.183722, 5.0517263, 6.131995], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.047823537, -0.035899006, 0.05371644], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 223, "left_children": [1, -1, -1], "loss_changes": [0.31963775, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.035899006, 0.05371644], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.297487, 5.106428, 7.191059], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 224, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0], "split_indices": [0], "split_type": [0], "sum_hessian": [10.513718], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.11405437], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 225, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.03421631], "split_indices": [0], "split_type": [0], "sum_hessian": [9.66896], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.019214159, 0.07574838, -0.04739297], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 226, "left_children": [1, -1, -1], "loss_changes": [0.6826085, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.07574838, -0.04739297], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.320346, 6.2419214, 8.078424], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.06077954, 0.0669453, -0.047114197], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 227, "left_children": [1, -1, -1], "loss_changes": [0.5636826, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.0669453, -0.047114197], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.759509, 8.132248, 5.627262], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.113523856, 0.002318215, -0.08746764], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 228, "left_children": [1, -1, -1], "loss_changes": [0.34238327, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.002318215, -0.08746764], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.441457, 8.230773, 5.210684], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.040382277], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 229, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.012114684], "split_indices": [0], "split_type": [0], "sum_hessian": [10.040568], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.094395354], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 230, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.028318608], "split_indices": [0], "split_type": [0], "sum_hessian": [9.311216], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.0026329858], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 231, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.00078989577], "split_indices": [0], "split_type": [0], "sum_hessian": [8.97623], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.05528374, 0.027680438, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 232, "left_children": [1, -1, -1], "loss_changes": [0.021156631, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.027680438, -0.0], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.201919, 5.8655543, 5.336364], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.018831033, 0.05960327, -0.036990482], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 233, "left_children": [1, -1, -1], "loss_changes": [0.38645536, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.05960327, -0.036990482], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.8651, 5.8140583, 7.051041], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.039168805], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 234, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.011750642], "split_indices": [0], "split_type": [0], "sum_hessian": [11.252021], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 235, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0], "split_indices": [0], "split_type": [0], "sum_hessian": [9.201984], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.042312637, 0.013311425, -0.038931232], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 236, "left_children": [1, -1, -1], "loss_changes": [0.13131933, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.013311425, -0.038931232], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.435671, 6.9230986, 7.512572], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.07341977, -0.01974035, 0.05754089], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 237, "left_children": [1, -1, -1], "loss_changes": [0.26274166, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, -0.01974035, 0.05754089], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.411419, 5.9792075, 7.4322114], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.027765384, 0.077072084, -0.042730335], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 238, "left_children": [1, -1, -1], "loss_changes": [0.59825045, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.077072084, -0.042730335], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.108024, 5.6199446, 7.488079], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.08143846], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 239, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.02443154], "split_indices": [0], "split_type": [0], "sum_hessian": [9.035925], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 240, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0], "split_indices": [0], "split_type": [0], "sum_hessian": [9.180795], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 241, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0], "split_indices": [0], "split_type": [0], "sum_hessian": [9.391109], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.025140645], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 242, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.007542194], "split_indices": [0], "split_type": [0], "sum_hessian": [10.058551], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.046610188, 0.029866781, -0.04806242], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 243, "left_children": [1, -1, -1], "loss_changes": [0.25475195, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.029866781, -0.04806242], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.016793, 5.3614893, 7.6553035], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.063754946], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 244, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.019126484], "split_indices": [0], "split_type": [0], "sum_hessian": [10.10727], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.015674008], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 245, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.0047022025], "split_indices": [0], "split_type": [0], "sum_hessian": [8.65925], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.034779172, -0.04466724, 0.037806474], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 246, "left_children": [1, -1, -1], "loss_changes": [0.30235258, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.04466724, 0.037806474], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.33046, 8.789196, 5.5412636], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.029739149, 0.049945097, -0.09002211], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 247, "left_children": [1, -1, -1], "loss_changes": [0.796528, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.049945097, -0.09002211], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.822238, 7.4377666, 5.3844714], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.075357944, -0.07223784, 0.03890781], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 248, "left_children": [1, -1, -1], "loss_changes": [0.5029104, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.07223784, 0.03890781], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.577152, 7.1344523, 5.4427], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.093655], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 249, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0280965], "split_indices": [0], "split_type": [0], "sum_hessian": [9.338589], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.029032925], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 250, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.008709878], "split_indices": [0], "split_type": [0], "sum_hessian": [8.499069], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.04911628], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 251, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.014734885], "split_indices": [0], "split_type": [0], "sum_hessian": [8.3992605], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.048571076, -0.034905277, 0.0028828876], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 252, "left_children": [1, -1, -1], "loss_changes": [0.05565012, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.034905277, 0.0028828876], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.121627, 5.007228, 5.114399], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.008998173, -0.046285447, 0.043704715], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 253, "left_children": [1, -1, -1], "loss_changes": [0.33911046, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.046285447, 0.043704715], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.22579, 5.6344695, 7.5913205], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.08297898, 0.038335606, 0.0015715703], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 254, "left_children": [1, -1, -1], "loss_changes": [0.025945738, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.038335606, 0.0015715703], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.459803, 5.41103, 5.0487723], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.022902356], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 255, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.006870707], "split_indices": [0], "split_type": [0], "sum_hessian": [8.51969], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.061665747, 0.054312706, -0.010057132], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 256, "left_children": [1, -1, -1], "loss_changes": [0.18379036, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.054312706, -0.010057132], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.341595, 5.984762, 7.3568325], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.06756645, -0.045558937, 0.057412345], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 257, "left_children": [1, -1, -1], "loss_changes": [0.46607047, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [2.0, -0.045558937, 0.057412345], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.940452, 5.016038, 9.924413], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.08270381, 0.033579838, 0.0026857273], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 258, "left_children": [1, -1, -1], "loss_changes": [0.018138535, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.033579838, 0.0026857273], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.48692, 8.317097, 5.169823], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 259, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0], "split_indices": [0], "split_type": [0], "sum_hessian": [9.200119], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.092250615], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 260, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.027675185], "split_indices": [0], "split_type": [0], "sum_hessian": [9.120242], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.083501995], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 261, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0250506], "split_indices": [0], "split_type": [0], "sum_hessian": [9.115707], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0928126, 0.00016542811, -0.05291434], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 262, "left_children": [1, -1, -1], "loss_changes": [0.11274297, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.00016542811, -0.05291434], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.183949, 5.186402, 5.997548], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.016954405, -0.02372146, 0.037656613], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 263, "left_children": [1, -1, -1], "loss_changes": [0.15860875, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, -0.02372146, 0.037656613], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.816235, 6.456333, 6.359901], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.24684316], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 264, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.07405295], "split_indices": [0], "split_type": [0], "sum_hessian": [10.41558], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.20220396], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 265, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.06066119], "split_indices": [0], "split_type": [0], "sum_hessian": [9.516931], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.05826317, -0.02895199, 0.073805705], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 266, "left_children": [1, -1, -1], "loss_changes": [0.50388885, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.02895199, 0.073805705], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [15.0086775, 8.201477, 6.8072004], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.027454244, 0.066249214, -0.08764737], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 267, "left_children": [1, -1, -1], "loss_changes": [1.0025775, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, 0.066249214, -0.08764737], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.29303, 9.292619, 5.0004115], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.040782705, 0.036554202, -0.08623792], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 268, "left_children": [1, -1, -1], "loss_changes": [0.6314812, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.036554202, -0.08623792], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.4550295, 8.169603, 5.285426], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.07574392], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 269, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.022723177], "split_indices": [0], "split_type": [0], "sum_hessian": [9.441125], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.19395946], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 270, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.05818784], "split_indices": [0], "split_type": [0], "sum_hessian": [9.063021], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.073351674], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 271, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.022005502], "split_indices": [0], "split_type": [0], "sum_hessian": [7.8487678], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.06548305, -0.0, -0.03474523], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 272, "left_children": [1, -1, -1], "loss_changes": [0.033404898, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, -0.0, -0.03474523], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.544681, 5.3637633, 5.180917], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.20269139, -0.086363144, -0.010191414], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 273, "left_children": [1, -1, -1], "loss_changes": [0.16214728, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, -0.086363144, -0.010191414], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.201502, 7.909235, 5.292267], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0688466], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 274, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.02065398], "split_indices": [0], "split_type": [0], "sum_hessian": [10.331022], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.0011785516], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 275, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.0003535655], "split_indices": [0], "split_type": [0], "sum_hessian": [8.935833], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.011324126, 0.020564841, -0.03178969], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 276, "left_children": [1, -1, -1], "loss_changes": [0.123069674, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.020564841, -0.03178969], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.824563, 7.073712, 6.7508516], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.12861812, -0.09646027, -0.006585124, -0.008496888, 9.712509e-05], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 277, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.3529054, 0.0, 0.0045255884, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [2.0, -0.09646027, 5.0, -0.008496888, 9.712509e-05], "split_indices": [1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [15.474069, 5.0447702, 10.429298, 5.2585664, 5.1707325], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.10042306, -0.0, 0.053437926], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 278, "left_children": [1, -1, -1], "loss_changes": [0.09225373, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.0, 0.053437926], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.010639, 6.649929, 6.36071], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.07652622], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 279, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.022957865], "split_indices": [0], "split_type": [0], "sum_hessian": [8.949587], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.05495689], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 280, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.016487068], "split_indices": [0], "split_type": [0], "sum_hessian": [9.67834], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.018224685], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 281, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0054674055], "split_indices": [0], "split_type": [0], "sum_hessian": [7.854557], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.04969614], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 282, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.014908843], "split_indices": [0], "split_type": [0], "sum_hessian": [9.6449175], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.049445704, -0.0, -0.026720708], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 283, "left_children": [1, -1, -1], "loss_changes": [0.03631849, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.0, -0.026720708], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.05207, 5.1435256, 7.908544], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.043866787, -0.04097033], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 284, "left_children": [1, -1, -1], "loss_changes": [0.24847747, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.043866787, -0.04097033], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.40568, 5.263859, 5.141821], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.06447335], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 285, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.019342007], "split_indices": [0], "split_type": [0], "sum_hessian": [9.514354], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.16405684, -0.10878967, 0.006448901], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 286, "left_children": [1, -1, -1], "loss_changes": [0.6070087, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.10878967, 0.006448901], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.336681, 6.7246704, 7.6120114], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.10568191, -0.0, 0.05917525], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 287, "left_children": [1, -1, -1], "loss_changes": [0.15423606, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, -0.0, 0.05917525], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.989497, 6.722568, 7.2669287], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.10369834, 0.07817874, -0.013413393], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 288, "left_children": [1, -1, -1], "loss_changes": [0.3249001, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.07817874, -0.013413393], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.525611, 5.56268, 5.962931], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.052315123], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 289, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.015694538], "split_indices": [0], "split_type": [0], "sum_hessian": [8.479228], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0033078715], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 290, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0009923615], "split_indices": [0], "split_type": [0], "sum_hessian": [9.320036], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.036312167], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 291, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.01089365], "split_indices": [0], "split_type": [0], "sum_hessian": [8.394436], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.07105243, 0.0368085, -0.083185785], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 292, "left_children": [1, -1, -1], "loss_changes": [0.51357406, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.0368085, -0.083185785], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.614761, 5.431904, 5.182858], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.06604324, 0.07086163, -0.023611099], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 293, "left_children": [1, -1, -1], "loss_changes": [0.34896192, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.07086163, -0.023611099], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.773683, 5.4388576, 6.3348255], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.103581004], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 294, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.031074302], "split_indices": [0], "split_type": [0], "sum_hessian": [9.174949], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.07695639], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 295, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.023086919], "split_indices": [0], "split_type": [0], "sum_hessian": [8.833716], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.1666037, -0.016336413, -0.06497436], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 296, "left_children": [1, -1, -1], "loss_changes": [0.048314452, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, -0.016336413, -0.06497436], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.559865, 5.749202, 8.810663], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.14650054, -0.05621073, -0.018398022], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 297, "left_children": [1, -1, -1], "loss_changes": [0.015756577, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.05621073, -0.018398022], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.972114, 7.8375454, 6.1345677], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.020240666, -0.045426436, 0.032228358], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 298, "left_children": [1, -1, -1], "loss_changes": [0.28069916, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.045426436, 0.032228358], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.526526, 7.4592714, 7.0672545], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 299, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0], "split_indices": [0], "split_type": [0], "sum_hessian": [9.351977], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.041582454, -0.04395489], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 300, "left_children": [1, -1, -1], "loss_changes": [0.2646301, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.041582454, -0.04395489], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.001709, 5.42093, 5.580779], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.01865865], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 301, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.005597595], "split_indices": [0], "split_type": [0], "sum_hessian": [9.748361], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.062126786, 0.04542191, -0.08622452], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 302, "left_children": [1, -1, -1], "loss_changes": [0.60470086, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.04542191, -0.08622452], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.3827305, 5.2709126, 5.111818], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.04033314, 0.049953185, -0.015599889], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 303, "left_children": [1, -1, -1], "loss_changes": [0.19350673, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.049953185, -0.015599889], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.883472, 5.9847155, 7.8987565], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.04447084], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 304, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.013341253], "split_indices": [0], "split_type": [0], "sum_hessian": [9.859808], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.053141672], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 305, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.015942503], "split_indices": [0], "split_type": [0], "sum_hessian": [9.7131605], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.026538875, 0.03717102], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 306, "left_children": [1, -1, -1], "loss_changes": [0.17004997, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.026538875, 0.03717102], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.422422, 7.8642864, 5.5581365], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.023084294, 0.05135783, -0.051905446], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 307, "left_children": [1, -1, -1], "loss_changes": [0.4748305, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.05135783, -0.051905446], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.334904, 8.52971, 5.8051934], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.03195211, 0.039985865, -0.014236402], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 308, "left_children": [1, -1, -1], "loss_changes": [0.13981998, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.039985865, -0.014236402], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.5235195, 6.635945, 7.8875747], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.012996801], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 309, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0038990404], "split_indices": [0], "split_type": [0], "sum_hessian": [9.658496], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.07269491, -0.08522782, 0.038723018], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 310, "left_children": [1, -1, -1], "loss_changes": [0.5559955, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.08522782, 0.038723018], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.811864, 5.322771, 5.489093], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.056085393], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 311, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.016825618], "split_indices": [0], "split_type": [0], "sum_hessian": [9.741213], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0061396803], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 312, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0018419041], "split_indices": [0], "split_type": [0], "sum_hessian": [9.253197], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.018723072, 0.031670783, -0.01391884], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 313, "left_children": [1, -1, -1], "loss_changes": [0.09687686, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.031670783, -0.01391884], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.217709, 6.4843435, 7.7333655], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.13071413], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 314, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.039214242], "split_indices": [0], "split_type": [0], "sum_hessian": [9.773997], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0025687327], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 315, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.00077061984], "split_indices": [0], "split_type": [0], "sum_hessian": [9.357593], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.041078217, -0.013241472, 0.034492582], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 316, "left_children": [1, -1, -1], "loss_changes": [0.10579681, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.013241472, 0.034492582], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.869722, 6.000059, 7.8696632], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.09478494, 0.0041421563, -0.05413511], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 317, "left_children": [1, -1, -1], "loss_changes": [0.16285835, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.0041421563, -0.05413511], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.447581, 6.226355, 8.221227], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.07215988, -0.058734395, 0.013937553], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 318, "left_children": [1, -1, -1], "loss_changes": [0.21810962, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.058734395, 0.013937553], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.235449, 6.085648, 6.1498003], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.061940715], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 319, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.018582216], "split_indices": [0], "split_type": [0], "sum_hessian": [9.660164], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.048518624], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 320, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.014555587], "split_indices": [0], "split_type": [0], "sum_hessian": [10.228265], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 321, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0], "split_indices": [0], "split_type": [0], "sum_hessian": [9.210269], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.025339425], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 322, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.007601828], "split_indices": [0], "split_type": [0], "sum_hessian": [10.106409], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.02038281, 0.028085707, -0.04029252], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 323, "left_children": [1, -1, -1], "loss_changes": [0.18705903, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.028085707, -0.04029252], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.121333, 5.7089953, 6.412338], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.14326324], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 324, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.042978972], "split_indices": [0], "split_type": [0], "sum_hessian": [9.588966], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.047282454], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 325, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.014184737], "split_indices": [0], "split_type": [0], "sum_hessian": [9.793565], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.036662318, 0.043457076, -0.011500054], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 326, "left_children": [1, -1, -1], "loss_changes": [0.1252757, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.043457076, -0.011500054], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.442019, 5.2581267, 7.1838927], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.011692258, 0.026947593, -0.04910531], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 327, "left_children": [1, -1, -1], "loss_changes": [0.25201052, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.026947593, -0.04910531], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.901073, 8.185171, 5.715902], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.033202965, -0.036764685, 0.028460978], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 328, "left_children": [1, -1, -1], "loss_changes": [0.19707823, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.036764685, 0.028460978], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.964988, 9.300632, 5.664355], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.17217545], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 329, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.051652636], "split_indices": [0], "split_type": [0], "sum_hessian": [8.8331375], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.11169586, 0.055210024, 0.002103391], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 330, "left_children": [1, -1, -1], "loss_changes": [0.064732194, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.055210024, 0.002103391], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.241412, 5.043262, 5.1981497], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 331, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0], "split_indices": [0], "split_type": [0], "sum_hessian": [9.306807], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.038942173], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 332, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.011682652], "split_indices": [0], "split_type": [0], "sum_hessian": [9.831013], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.039939925, -0.039604463, 0.00614134], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 333, "left_children": [1, -1, -1], "loss_changes": [0.090519525, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.039604463, 0.00614134], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.782434, 5.250295, 7.5321393], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.04867183], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 334, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.01460155], "split_indices": [0], "split_type": [0], "sum_hessian": [9.983667], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.27348584], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 335, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.08204576], "split_indices": [0], "split_type": [0], "sum_hessian": [8.6258955], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.04724347, 0.03924154, -0.014613359], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 336, "left_children": [1, -1, -1], "loss_changes": [0.13323, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.03924154, -0.014613359], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.797579, 7.7379675, 6.0596113], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.014211879, 0.014317599, -0.03744803], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 337, "left_children": [1, -1, -1], "loss_changes": [0.11568049, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.014317599, -0.03744803], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.889845, 8.689046, 5.2007985], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.03768341, 0.015562559, -0.052503232], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 338, "left_children": [1, -1, -1], "loss_changes": [0.21918187, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.015562559, -0.052503232], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [15.0819645, 9.064548, 6.017417], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.1104388], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 339, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.03313164], "split_indices": [0], "split_type": [0], "sum_hessian": [8.883436], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.023023456], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 340, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.006907037], "split_indices": [0], "split_type": [0], "sum_hessian": [9.68966], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.074194215], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 341, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.022258265], "split_indices": [0], "split_type": [0], "sum_hessian": [9.407154], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.047731854], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 342, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.014319557], "split_indices": [0], "split_type": [0], "sum_hessian": [10.096759], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.04669977, 0.03665493, -0.08911073], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 343, "left_children": [1, -1, -1], "loss_changes": [0.63189715, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.03665493, -0.08911073], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.674407, 7.6356616, 5.038746], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.14196485], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 344, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.042589456], "split_indices": [0], "split_type": [0], "sum_hessian": [9.574489], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 345, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0], "split_indices": [0], "split_type": [0], "sum_hessian": [9.2656355], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.18517387, -0.0, -0.091721125], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 346, "left_children": [1, -1, -1], "loss_changes": [0.32718423, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, -0.0, -0.091721125], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.558982, 5.0849333, 7.474049], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.07474631, 0.093697876, -0.01756793], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 347, "left_children": [1, -1, -1], "loss_changes": [0.53804696, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.093697876, -0.01756793], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.609957, 5.038141, 9.571816], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.039720964, 0.048924763, -0.011543207], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 348, "left_children": [1, -1, -1], "loss_changes": [0.15118325, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.048924763, -0.011543207], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.729898, 5.0137577, 7.716141], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.07492589], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 349, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.022477768], "split_indices": [0], "split_type": [0], "sum_hessian": [9.42794], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.14892134], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 350, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.044676404], "split_indices": [0], "split_type": [0], "sum_hessian": [9.645025], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.01442066], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 351, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0043261983], "split_indices": [0], "split_type": [0], "sum_hessian": [9.622599], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.024681227], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 352, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0074043684], "split_indices": [0], "split_type": [0], "sum_hessian": [9.664143], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.15082991, -0.0, -0.07762168], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 353, "left_children": [1, -1, -1], "loss_changes": [0.20167315, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.0, -0.07762168], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.503007, 5.241728, 6.261279], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.01881183], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 354, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.005643549], "split_indices": [0], "split_type": [0], "sum_hessian": [9.425147], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.018162705], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 355, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0054488117], "split_indices": [0], "split_type": [0], "sum_hessian": [7.7900124], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.008885115, -0.027877778, 0.034703806], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 356, "left_children": [1, -1, -1], "loss_changes": [0.16815647, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.027877778, 0.034703806], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.267813, 6.379485, 6.8883276], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0114975255, 0.08026585, -0.052506883], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 357, "left_children": [1, -1, -1], "loss_changes": [0.7915671, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.08026585, -0.052506883], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [15.523385, 5.2746687, 10.248716], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.051476467, 0.056982532, -0.022758994], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 358, "left_children": [1, -1, -1], "loss_changes": [0.2640424, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.056982532, -0.022758994], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.510836, 6.134289, 6.3765473], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.016876161], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 359, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0050628483], "split_indices": [0], "split_type": [0], "sum_hessian": [9.699469], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.070597686, -0.036836155, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 360, "left_children": [1, -1, -1], "loss_changes": [0.034182116, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.036836155, -0.0], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.683686, 5.129594, 5.5540924], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.030015647], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 361, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.009004694], "split_indices": [0], "split_type": [0], "sum_hessian": [10.169489], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.040989324], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 362, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.012296798], "split_indices": [0], "split_type": [0], "sum_hessian": [9.7983], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.036792517, 0.012271007, -0.036457136], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 363, "left_children": [1, -1, -1], "loss_changes": [0.102648064, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.012271007, -0.036457136], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.612581, 6.233613, 6.3789687], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.048255146, 0.03918631, -0.006862219], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 364, "left_children": [1, -1, -1], "loss_changes": [0.08474578, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.03918631, -0.006862219], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.116037, 5.415047, 5.7009907], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 365, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0], "split_indices": [0], "split_type": [0], "sum_hessian": [9.065507], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.014723404, 0.01749335], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 366, "left_children": [1, -1, -1], "loss_changes": [0.044695385, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.014723404, 0.01749335], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.744757, 7.91523, 5.8295274], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.1304836, 0.08009446, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 367, "left_children": [1, -1, -1], "loss_changes": [0.28367454, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.08009446, -0.0], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.434898, 6.4277377, 7.00716], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.08442977, -0.0, -0.042853884], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 368, "left_children": [1, -1, -1], "loss_changes": [0.07763665, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, -0.0, -0.042853884], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.586618, 5.0354385, 7.55118], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.051787723], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 369, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.015536318], "split_indices": [0], "split_type": [0], "sum_hessian": [8.446356], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.052835397, -0.04925229], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 370, "left_children": [1, -1, -1], "loss_changes": [0.3723051, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.052835397, -0.04925229], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.89493, 5.088429, 5.806501], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.010036955], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 371, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.0030110865], "split_indices": [0], "split_type": [0], "sum_hessian": [8.789057], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.060566597], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 372, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.01816998], "split_indices": [0], "split_type": [0], "sum_hessian": [10.366304], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.032634575, -0.06007331, 0.037204273], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 373, "left_children": [1, -1, -1], "loss_changes": [0.3887986, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, -0.06007331, 0.037204273], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.571914, 6.247421, 6.3244934], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.010250799, 0.022927364, -0.03604708], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 374, "left_children": [1, -1, -1], "loss_changes": [0.12738323, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.022927364, -0.03604708], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.9489, 5.7830105, 5.1658893], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.028639683], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 375, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.008591905], "split_indices": [0], "split_type": [0], "sum_hessian": [8.438552], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.08089248, -0.0166011, 0.05884093], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 376, "left_children": [1, -1, -1], "loss_changes": [0.24781066, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.0166011, 0.05884093], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.196335, 5.8881154, 7.308219], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.028431317, -0.04482494], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 377, "left_children": [1, -1, -1], "loss_changes": [0.23770116, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.028431317, -0.04482494], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.88949, 9.443966, 5.4455247], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.008096418], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 378, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.0024289256], "split_indices": [0], "split_type": [0], "sum_hessian": [10.075846], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.06276255], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 379, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.018828766], "split_indices": [0], "split_type": [0], "sum_hessian": [9.639492], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.11213198, 0.0514083, 0.005260026], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 380, "left_children": [1, -1, -1], "loss_changes": [0.040752888, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.0514083, 0.005260026], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.180994, 5.1118603, 5.069134], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.005850406], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 381, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.0017551219], "split_indices": [0], "split_type": [0], "sum_hessian": [8.85472], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.05239282, 0.03938799, -0.004715239], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 382, "left_children": [1, -1, -1], "loss_changes": [0.07930211, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.03938799, -0.004715239], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.222131, 5.4496655, 5.772465], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0436599, 0.04574064, -0.010613481], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 383, "left_children": [1, -1, -1], "loss_changes": [0.13001059, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, 0.04574064, -0.010613481], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.128689, 5.244685, 6.8840036], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.12569661], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 384, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.037708987], "split_indices": [0], "split_type": [0], "sum_hessian": [9.52765], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 385, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0], "split_indices": [0], "split_type": [0], "sum_hessian": [9.247489], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.09802936, 0.06288901, 0.0008172528], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 386, "left_children": [1, -1, -1], "loss_changes": [0.13972878, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.06288901, 0.0008172528], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.187424, 5.4991684, 8.688254], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.13173187, 0.022381295, -0.10545294], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 387, "left_children": [1, -1, -1], "loss_changes": [0.69616723, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.022381295, -0.10545294], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.162712, 6.894977, 6.267735], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.008322719, 0.0147390105, -0.03529027], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 388, "left_children": [1, -1, -1], "loss_changes": [0.10836287, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.0147390105, -0.03529027], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.243521, 9.097646, 5.145874], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.009108749], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 389, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0027326248], "split_indices": [0], "split_type": [0], "sum_hessian": [9.555634], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.03848839, 0.04379163, -0.014205423], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 390, "left_children": [1, -1, -1], "loss_changes": [0.13235168, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.04379163, -0.014205423], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.526868, 5.3244004, 6.2024674], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.043880243], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 391, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.013164073], "split_indices": [0], "split_type": [0], "sum_hessian": [9.909316], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.0016888584, 0.0054122154], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 392, "left_children": [1, -1, -1], "loss_changes": [0.0021648712, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.0016888584, 0.0054122154], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.624893, 5.6177, 5.007193], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.054983508, -0.0013231112, 0.040050298], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 393, "left_children": [1, -1, -1], "loss_changes": [0.073749974, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.0013231112, 0.040050298], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.920052, 6.598728, 5.3213234], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.032989785], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 394, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.009896936], "split_indices": [0], "split_type": [0], "sum_hessian": [9.825051], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.119041406], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 395, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.035712425], "split_indices": [0], "split_type": [0], "sum_hessian": [8.647443], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.007576087, 0.044997226, -0.023626184], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 396, "left_children": [1, -1, -1], "loss_changes": [0.19424108, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.044997226, -0.023626184], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.258631, 5.162774, 8.095856], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.056770895, 0.08631981, -0.020777045], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 397, "left_children": [1, -1, -1], "loss_changes": [0.50401145, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.08631981, -0.020777045], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.880272, 5.0803223, 9.799951], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.12302352, 0.056389417, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 398, "left_children": [1, -1, -1], "loss_changes": [0.11933409, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, 0.056389417, -0.0], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.936829, 8.776186, 5.160643], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.14601254], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 399, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.043803766], "split_indices": [0], "split_type": [0], "sum_hessian": [8.165204], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.011605729, 0.043255594, -0.03398106], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 400, "left_children": [1, -1, -1], "loss_changes": [0.20746472, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.043255594, -0.03398106], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.357899, 5.3373985, 5.0204997], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.13893825], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 401, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.041681476], "split_indices": [0], "split_type": [0], "sum_hessian": [10.362389], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.055225335], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 402, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.0165676], "split_indices": [0], "split_type": [0], "sum_hessian": [9.554289], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.069705375, -0.066673756, 0.034027554], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 403, "left_children": [1, -1, -1], "loss_changes": [0.38994655, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, -0.066673756, 0.034027554], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.667209, 6.5466094, 5.1205993], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0029371565], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 404, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.000881147], "split_indices": [0], "split_type": [0], "sum_hessian": [9.084406], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018295669], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 405, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.00054887007], "split_indices": [0], "split_type": [0], "sum_hessian": [9.2797785], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.11673408, 0.061001863, 0.0065986454], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 406, "left_children": [1, -1, -1], "loss_changes": [0.10522361, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.061001863, 0.0065986454], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [15.115977, 6.7466536, 8.369324], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0009907618, 0.061819058, -0.037621442], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 407, "left_children": [1, -1, -1], "loss_changes": [0.44172162, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.061819058, -0.037621442], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.733405, 5.679484, 9.053922], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.03625384, 0.04957], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 408, "left_children": [1, -1, -1], "loss_changes": [0.28204775, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.03625384, 0.04957], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.006244, 6.902767, 5.1034765], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.052597623], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 409, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.015779288], "split_indices": [0], "split_type": [0], "sum_hessian": [8.155422], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.053837247, 0.03535492, -0.0007177273], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 410, "left_children": [1, -1, -1], "loss_changes": [0.055729873, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.03535492, -0.0007177273], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.197085, 5.5553594, 5.641726], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.040906362], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 411, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.012271909], "split_indices": [0], "split_type": [0], "sum_hessian": [9.991544], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.07995458, 0.030614603, -0.083455615], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 412, "left_children": [1, -1, -1], "loss_changes": [0.47386107, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.030614603, -0.083455615], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.859063, 5.651211, 5.2078524], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.04198873, 0.04966258, -0.013119408], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 413, "left_children": [1, -1, -1], "loss_changes": [0.15880863, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.04966258, -0.013119408], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.161018, 5.0776753, 7.083343], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.10525423], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 414, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.031576272], "split_indices": [0], "split_type": [0], "sum_hessian": [10.125853], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.07492939], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 415, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.022478817], "split_indices": [0], "split_type": [0], "sum_hessian": [9.301106], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.05156924, -0.03552379], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 416, "left_children": [1, -1, -1], "loss_changes": [0.34595412, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.05156924, -0.03552379], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.853388, 6.0613875, 8.792], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.08238297, -0.06160224, 0.009134472], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 417, "left_children": [1, -1, -1], "loss_changes": [0.20515282, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, -0.06160224, 0.009134472], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.076122, 5.810508, 6.2656145], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.06204629, 0.04726994, -0.005308963], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 418, "left_children": [1, -1, -1], "loss_changes": [0.12886517, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.04726994, -0.005308963], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.715646, 6.3670073, 7.3486385], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.011917875], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 419, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0035753627], "split_indices": [0], "split_type": [0], "sum_hessian": [9.635145], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.068561524, 0.040594354, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 420, "left_children": [1, -1, -1], "loss_changes": [0.061678424, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.040594354, -0.0], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.9187765, 5.428438, 5.4903383], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.11331426], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 421, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.03399428], "split_indices": [0], "split_type": [0], "sum_hessian": [9.74017], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.04971971, -0.034040216, 0.0013021961], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 422, "left_children": [1, -1, -1], "loss_changes": [0.049865253, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.034040216, 0.0013021961], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.160065, 5.006872, 5.1531925], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.22165257, 0.0018095595, -0.117770545], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 423, "left_children": [1, -1, -1], "loss_changes": [0.52239245, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.0018095595, -0.117770545], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.369836, 5.038053, 6.3317823], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.009772396, 0.05012368, -0.03966081], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 424, "left_children": [1, -1, -1], "loss_changes": [0.27736977, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.05012368, -0.03966081], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.274213, 5.124199, 5.150014], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.04113735], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 425, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.012341205], "split_indices": [0], "split_type": [0], "sum_hessian": [8.203756], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.024009293, 0.052850105, -0.027372831], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 426, "left_children": [1, -1, -1], "loss_changes": [0.29067057, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.052850105, -0.027372831], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.174315, 6.2552094, 7.919106], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00092119764, -0.015816476, 0.024740638], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 427, "left_children": [1, -1, -1], "loss_changes": [0.069940604, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.015816476, 0.024740638], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.314518, 7.5049195, 5.8095984], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.054458756, 0.019435072, -0.046103813], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 428, "left_children": [1, -1, -1], "loss_changes": [0.1914593, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.019435072, -0.046103813], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.589595, 5.8719573, 7.717638], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.14927363], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 429, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.04478209], "split_indices": [0], "split_type": [0], "sum_hessian": [8.3252735], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.051413942, 0.028456775, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 430, "left_children": [1, -1, -1], "loss_changes": [0.028518654, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.028456775, -0.0], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.177023, 5.7470236, 5.4299994], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.01097125], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 431, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0032913752], "split_indices": [0], "split_type": [0], "sum_hessian": [9.573656], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.014371725], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 432, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.004311518], "split_indices": [0], "split_type": [0], "sum_hessian": [10.317061], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.050451588, 0.048849244], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 433, "left_children": [1, -1, -1], "loss_changes": [0.37374273, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, -0.050451588, 0.048849244], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.654684, 5.620273, 6.0344105], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.1346456], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 434, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.04039368], "split_indices": [0], "split_type": [0], "sum_hessian": [9.7074585], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.039304513], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 435, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.011791355], "split_indices": [0], "split_type": [0], "sum_hessian": [9.865869], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.13477217, 0.093607575, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 436, "left_children": [1, -1, -1], "loss_changes": [0.37536976, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.093607575, -0.0], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.703269, 5.78511, 8.918159], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.09342074, 0.04679843, 0.006881527], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 437, "left_children": [1, -1, -1], "loss_changes": [0.04541029, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.04679843, 0.006881527], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.164711, 6.1090593, 8.055651], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.08922764, -0.0, 0.059397817], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 438, "left_children": [1, -1, -1], "loss_changes": [0.15243201, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.0, 0.059397817], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.639628, 8.574828, 6.0648003], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.009189589], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 439, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.0027568769], "split_indices": [0], "split_type": [0], "sum_hessian": [8.833229], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.14958382], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 440, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.044875145], "split_indices": [0], "split_type": [0], "sum_hessian": [10.370489], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.17017113], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 441, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.05105134], "split_indices": [0], "split_type": [0], "sum_hessian": [8.766153], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.12698427], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 442, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.03809528], "split_indices": [0], "split_type": [0], "sum_hessian": [9.883431], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.07377513, 0.04770446, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 443, "left_children": [1, -1, -1], "loss_changes": [0.08662901, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.04770446, -0.0], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.730508, 5.166255, 6.564253], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.09080794, 0.042896286, 0.001791099], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 444, "left_children": [1, -1, -1], "loss_changes": [0.034549423, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.042896286, 0.001791099], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.362364, 5.2618775, 5.1004863], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.09615125], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 445, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.028845375], "split_indices": [0], "split_type": [0], "sum_hessian": [8.910713], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.076149195, 0.029172802, -0.096176535], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 446, "left_children": [1, -1, -1], "loss_changes": [0.6751667, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.029172802, -0.096176535], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.667339, 8.108613, 5.5587263], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.031981513, -0.07299555, 0.04102245], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 447, "left_children": [1, -1, -1], "loss_changes": [0.58959943, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, -0.07299555, 0.04102245], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.30313, 6.416544, 7.886586], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.026161151, 0.032362882], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 448, "left_children": [1, -1, -1], "loss_changes": [0.1322956, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.026161151, 0.032362882], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.9546795, 6.463598, 5.491082], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.039060783], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 449, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.011718235], "split_indices": [0], "split_type": [0], "sum_hessian": [10.038413], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.030216152, -0.017258672, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 450, "left_children": [1, -1, -1], "loss_changes": [0.012548898, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.017258672, -0.0], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.59431, 6.2661, 5.32821], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.027776053], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 451, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.008332816], "split_indices": [0], "split_type": [0], "sum_hessian": [8.545021], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.14900884], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 452, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.044702653], "split_indices": [0], "split_type": [0], "sum_hessian": [9.593552], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.17091347, 0.0020224461, 0.08710177], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 453, "left_children": [1, -1, -1], "loss_changes": [0.18651837, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, 0.0020224461, 0.08710177], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.128211, 5.062581, 5.06563], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.12865981], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 454, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.038597945], "split_indices": [0], "split_type": [0], "sum_hessian": [9.692811], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.113504924], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 455, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.034051478], "split_indices": [0], "split_type": [0], "sum_hessian": [9.525417], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.065110594, -0.09118509, 0.026045537], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 456, "left_children": [1, -1, -1], "loss_changes": [0.5647636, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, -0.09118509, 0.026045537], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.238333, 5.0138755, 8.224457], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.01535201, -0.016775832], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 457, "left_children": [1, -1, -1], "loss_changes": [0.047846675, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.01535201, -0.016775832], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.883974, 8.737403, 6.1465716], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.044937745, -0.041897163], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 458, "left_children": [1, -1, -1], "loss_changes": [0.3304559, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.044937745, -0.041897163], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.829349, 6.4031253, 7.4262238], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.09121767], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 459, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0273653], "split_indices": [0], "split_type": [0], "sum_hessian": [9.232139], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0156631, 0.04567832, -0.053126365], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 460, "left_children": [1, -1, -1], "loss_changes": [0.36148274, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.04567832, -0.053126365], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.24905, 5.237725, 6.011325], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.071876585], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 461, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.021562977], "split_indices": [0], "split_type": [0], "sum_hessian": [8.840922], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.07580707, -0.04207645, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 462, "left_children": [1, -1, -1], "loss_changes": [0.057467774, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.04207645, -0.0], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.681333, 5.3339133, 5.3474197], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.13570973, -0.0, 0.074925], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 463, "left_children": [1, -1, -1], "loss_changes": [0.1848435, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.0, 0.074925], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.755525, 5.3211164, 5.4344087], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.1491505], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 464, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.044745155], "split_indices": [0], "split_type": [0], "sum_hessian": [9.446366], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.017732598], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 465, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.0053197797], "split_indices": [0], "split_type": [0], "sum_hessian": [10.251559], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.07191988, -0.00785177, 0.05412944], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 466, "left_children": [1, -1, -1], "loss_changes": [0.17175, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.00785177, 0.05412944], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.242709, 6.8697896, 6.3729196], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.19652238, -0.099141404, -0.021799939], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 467, "left_children": [1, -1, -1], "loss_changes": [0.17098445, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, -0.099141404, -0.021799939], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.183668, 5.144433, 8.039235], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.14016192, -0.0, -0.07590618], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 468, "left_children": [1, -1, -1], "loss_changes": [0.23647556, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, -0.0, -0.07590618], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.047649, 6.7362337, 7.311416], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.13791059], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 469, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.04137318], "split_indices": [0], "split_type": [0], "sum_hessian": [7.981531], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.15006492, -0.0, -0.08530615], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 470, "left_children": [1, -1, -1], "loss_changes": [0.24066725, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.0, -0.08530615], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.39469, 5.2447076, 5.1499825], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.09949878], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 471, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.029849635], "split_indices": [0], "split_type": [0], "sum_hessian": [9.475095], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.01196057], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 472, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.003588171], "split_indices": [0], "split_type": [0], "sum_hessian": [10.340947], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.03978423], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 473, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.011935269], "split_indices": [0], "split_type": [0], "sum_hessian": [12.553187], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.017883202], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 474, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.005364961], "split_indices": [0], "split_type": [0], "sum_hessian": [9.329982], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.028194329], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 475, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.008458299], "split_indices": [0], "split_type": [0], "sum_hessian": [9.7659645], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.05541956, 0.0021467404, -0.048516694], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 476, "left_children": [1, -1, -1], "loss_changes": [0.1213023, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.0021467404, -0.048516694], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.738044, 9.272021, 5.4660225], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.06736678, 0.061804704, -0.03977198], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 477, "left_children": [1, -1, -1], "loss_changes": [0.43246472, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.061804704, -0.03977198], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.35019, 8.160816, 5.1893744], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.034927424, 0.041009426, -0.009463623], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 478, "left_children": [1, -1, -1], "loss_changes": [0.108366795, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.041009426, -0.009463623], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.826984, 5.2449026, 7.5820813], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 479, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.0], "split_indices": [0], "split_type": [0], "sum_hessian": [7.273542], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.004141103, 0.0027613258], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 480, "left_children": [1, -1, -1], "loss_changes": [0.0017989929, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.004141103, 0.0027613258], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.860682, 5.7040043, 5.1566772], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.042324975], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 481, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.012697493], "split_indices": [0], "split_type": [0], "sum_hessian": [8.325885], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.06402544], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 482, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.019207632], "split_indices": [0], "split_type": [0], "sum_hessian": [10.433692], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.08128276, 0.0072343773, -0.056984484], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 483, "left_children": [1, -1, -1], "loss_changes": [0.16688028, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.0072343773, -0.056984484], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [11.722927, 5.878782, 5.8441453], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.073184505, 0.029832516, 0.0049575507], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 484, "left_children": [1, -1, -1], "loss_changes": [0.004001461, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.029832516, 0.0049575507], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.589201, 5.513914, 5.0752864], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0616538], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 485, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.01849614], "split_indices": [0], "split_type": [0], "sum_hessian": [8.424464], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.09632671, 0.070086345, -0.0058984105], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 486, "left_children": [1, -1, -1], "loss_changes": [0.26242712, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.070086345, -0.0058984105], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.847411, 6.271659, 7.575752], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.12721029, -0.09799645, 0.0034424851], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 487, "left_children": [1, -1, -1], "loss_changes": [0.43032718, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, -0.09799645, 0.0034424851], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.282744, 5.187804, 8.09494], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.06113586, -0.050013598, 0.019716017], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 488, "left_children": [1, -1, -1], "loss_changes": [0.2183778, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.050013598, 0.019716017], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.733288, 7.7540627, 5.9792247], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.142421], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 489, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.042726304], "split_indices": [0], "split_type": [0], "sum_hessian": [8.098142], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.07430013], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 490, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.02229004], "split_indices": [0], "split_type": [0], "sum_hessian": [8.724095], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.03616072], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 491, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.010848216], "split_indices": [0], "split_type": [0], "sum_hessian": [8.177122], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [0.06294047, 0.041940518, -0.0017660317], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 492, "left_children": [1, -1, -1], "loss_changes": [0.07765771, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.041940518, -0.0017660317], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [10.992713, 5.3924894, 5.6002235], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.045558292, -0.008468845, 0.036006074], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 493, "left_children": [1, -1, -1], "loss_changes": [0.08514409, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, -0.008468845, 0.036006074], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.209294, 5.7697268, 6.4395676], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.014404774], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 494, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.004321432], "split_indices": [0], "split_type": [0], "sum_hessian": [10.014232], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.03783918], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 495, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [-0.011351755], "split_indices": [0], "split_type": [0], "sum_hessian": [9.936796], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}, {"base_weights": [-0.0364549, -0.04179991, 0.028466403], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 496, "left_children": [1, -1, -1], "loss_changes": [0.24469277, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.04179991, 0.028466403], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [15.801508, 9.253763, 6.5477448], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.04468964, 0.04530081, -0.070660725], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 497, "left_children": [1, -1, -1], "loss_changes": [0.56330585, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, 0.04530081, -0.070660725], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [12.904313, 6.2156334, 6.6886797], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.07394735, -0.015545911, 0.07304404], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 498, "left_children": [1, -1, -1], "loss_changes": [0.33985174, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.015545911, 0.07304404], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [13.448638, 7.780899, 5.6677394], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.08305462], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0], "id": 499, "left_children": [-1], "loss_changes": [0.0], "parents": [2147483647], "right_children": [-1], "split_conditions": [0.024916386], "split_indices": [0], "split_type": [0], "sum_hessian": [7.712648], "tree_param": {"num_deleted": "0", "num_feature": "3", "num_nodes": "1", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "5E-1", "boost_from_average": "1", "num_class": "10", "num_feature": "3", "num_target": "1"}, "objective": {"name": "multi:softprob", "softmax_multiclass_param": {"num_class": "10"}}}, "version": [3, 0, 2]}