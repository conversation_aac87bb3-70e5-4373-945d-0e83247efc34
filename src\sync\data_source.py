"""
数据源管理器
负责从外部数据源获取最新的福彩3D开奖数据
"""

import requests
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import re
import urllib3

from .utils import retry_on_exception, validate_lottery_data

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

logger = logging.getLogger(__name__)

class DataSourceManager:
    """数据源管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config.get('data_sources', {})
        self.primary_url = self.config.get('primary', 'https://data.17500.cn/3d_asc.txt')
        self.backup_urls = self.config.get('backup', [])
        self.timeout = self.config.get('timeout', 30)
        self.retry_count = self.config.get('retry_count', 3)
        self.retry_delay = self.config.get('retry_delay', 5)
        
        # HTTP请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache'
        }
        
        logger.info(f"数据源管理器初始化完成，主数据源: {self.primary_url}")
    
    @retry_on_exception(max_retries=3, delay=2.0, backoff=2.0)
    def fetch_raw_data(self, url: str) -> Optional[str]:
        """获取原始数据"""
        try:
            logger.info(f"正在获取数据: {url}")
            response = requests.get(
                url, 
                headers=self.headers, 
                timeout=self.timeout,
                verify=False  # 某些数据源可能有SSL问题
            )
            response.raise_for_status()
            
            content = response.text.strip()
            if not content:
                raise ValueError("获取到空内容")
            
            logger.info(f"成功获取数据，内容长度: {len(content)} 字符")
            return content
            
        except requests.exceptions.RequestException as e:
            logger.error(f"网络请求失败: {e}")
            raise
        except Exception as e:
            logger.error(f"获取数据失败: {e}")
            raise
    
    def parse_17500_format(self, content: str) -> List[Dict[str, Any]]:
        """解析17500.cn格式的数据"""
        records = []
        lines = content.strip().split('\n')
        
        logger.info(f"开始解析数据，共 {len(lines)} 行")
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            try:
                # 17500.cn格式: 期号 日期 百位 十位 个位 其他信息...
                parts = line.split()
                if len(parts) < 5:
                    logger.warning(f"第{line_num}行数据格式不正确: {line}")
                    continue
                
                issue = parts[0]
                date_str = parts[1]
                hundreds = int(parts[2])
                tens = int(parts[3])
                units = int(parts[4])
                
                # 验证数据
                record = {
                    'issue': issue,
                    'draw_date': date_str,
                    'hundreds': hundreds,
                    'tens': tens,
                    'units': units,
                    'numbers': f"{hundreds}{tens}{units}",
                    'sum_value': hundreds + tens + units,
                    'span': max(hundreds, tens, units) - min(hundreds, tens, units)
                }
                
                if validate_lottery_data(record):
                    records.append(record)
                else:
                    logger.warning(f"第{line_num}行数据验证失败: {record}")
                    
            except (ValueError, IndexError) as e:
                logger.warning(f"第{line_num}行解析失败: {line}, 错误: {e}")
                continue
        
        logger.info(f"解析完成，有效记录: {len(records)} 条")
        return records
    
    def get_latest_data(self, count: int = 10) -> List[Dict[str, Any]]:
        """获取最新的开奖数据"""
        logger.info(f"开始获取最新 {count} 期数据")
        
        # 尝试主数据源
        urls_to_try = [self.primary_url] + self.backup_urls
        
        for i, url in enumerate(urls_to_try):
            try:
                logger.info(f"尝试数据源 {i+1}/{len(urls_to_try)}: {url}")
                
                raw_data = self.fetch_raw_data(url)
                if not raw_data:
                    continue
                
                records = self.parse_17500_format(raw_data)
                if not records:
                    logger.warning(f"数据源 {url} 解析结果为空")
                    continue
                
                # 返回最新的N条记录
                latest_records = records[-count:] if len(records) >= count else records
                
                logger.info(f"成功获取 {len(latest_records)} 条最新记录")
                if latest_records:
                    latest_issue = latest_records[-1]['issue']
                    latest_date = latest_records[-1]['draw_date']
                    logger.info(f"最新期号: {latest_issue}, 开奖日期: {latest_date}")
                
                return latest_records
                
            except Exception as e:
                logger.error(f"数据源 {url} 获取失败: {e}")
                if i < len(urls_to_try) - 1:
                    logger.info("尝试下一个数据源...")
                    continue
                else:
                    logger.error("所有数据源都无法访问")
                    break
        
        logger.error("获取数据失败，所有数据源都不可用")
        return []
    
    def validate_data_consistency(self, records: List[Dict[str, Any]]) -> bool:
        """验证数据一致性"""
        if not records:
            return False
        
        logger.info("开始验证数据一致性")
        
        # 检查期号连续性
        issues = [record['issue'] for record in records]
        issues.sort()
        
        for i in range(1, len(issues)):
            current_issue = int(issues[i])
            prev_issue = int(issues[i-1])
            
            # 简单的连续性检查（可能跨年）
            if current_issue - prev_issue > 10:  # 允许一定的间隔
                logger.warning(f"期号可能不连续: {prev_issue} -> {current_issue}")
        
        # 检查日期合理性
        for record in records:
            try:
                draw_date = datetime.strptime(record['draw_date'], '%Y-%m-%d')
                now = datetime.now()
                
                # 开奖日期不能是未来
                if draw_date > now:
                    logger.warning(f"期号 {record['issue']} 的开奖日期是未来: {record['draw_date']}")
                    return False
                
                # 开奖日期不能太久远（比如超过1年）
                if (now - draw_date).days > 365:
                    logger.warning(f"期号 {record['issue']} 的开奖日期过于久远: {record['draw_date']}")
                    
            except ValueError:
                logger.error(f"期号 {record['issue']} 的日期格式错误: {record['draw_date']}")
                return False
        
        logger.info("数据一致性验证通过")
        return True
    
    def get_data_source_status(self) -> Dict[str, Any]:
        """获取数据源状态"""
        status = {
            'primary_url': self.primary_url,
            'backup_urls': self.backup_urls,
            'last_check': datetime.now().isoformat(),
            'sources_status': []
        }
        
        urls_to_check = [self.primary_url] + self.backup_urls
        
        for url in urls_to_check:
            source_status = {
                'url': url,
                'available': False,
                'response_time': None,
                'error': None
            }
            
            try:
                start_time = datetime.now()
                response = requests.head(url, headers=self.headers, timeout=10)
                end_time = datetime.now()
                
                source_status['available'] = response.status_code == 200
                source_status['response_time'] = (end_time - start_time).total_seconds()
                
            except Exception as e:
                source_status['error'] = str(e)
            
            status['sources_status'].append(source_status)
        
        return status
