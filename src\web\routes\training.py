#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练相关API路由
提供训练任务的启动、状态查询、进度获取、取消功能
"""

import sys
from pathlib import Path
from typing import Dict, List, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.training.task_manager import task_manager, TaskPriority, TaskStatus
from src.training.training_worker import training_worker
from src.training.progress_monitor import progress_monitor
from src.training.performance_analyzer import performance_analyzer
from src.training.smart_scheduler import smart_scheduler
from src.web.cache_manager import cache_response

# 创建路由器
router = APIRouter(prefix="/api/training", tags=["training"])

# 请求模型
class TrainingRequest(BaseModel):
    """训练请求模型"""
    position: str  # hundreds/tens/units
    model_type: str  # xgb/lgb/lstm/ensemble/all
    issue: Optional[str] = None
    train_until: Optional[str] = None
    priority: str = "normal"  # low/normal/high/urgent

class BatchTrainingRequest(BaseModel):
    """批量训练请求模型"""
    positions: List[str]  # [hundreds, tens, units]
    model_types: List[str]  # [xgb, lgb, lstm, ensemble]
    issue: Optional[str] = None
    train_until: Optional[str] = None
    priority: str = "normal"

# 响应模型
class TaskResponse(BaseModel):
    """任务响应模型"""
    task_id: str
    status: str
    message: str

class TaskDetailResponse(BaseModel):
    """任务详情响应模型"""
    task_id: str
    position: str
    model_type: str
    issue: Optional[str]
    train_until: Optional[str]
    priority: str
    status: str
    progress: float
    message: str
    created_at: str
    started_at: Optional[str]
    completed_at: Optional[str]
    duration_seconds: Optional[float]
    result: Optional[Dict]
    error: Optional[str]

@router.post("/start", response_model=TaskResponse)
async def start_training_task(request: TrainingRequest, background_tasks: BackgroundTasks):
    """
    启动训练任务
    
    Args:
        request: 训练请求
        background_tasks: 后台任务
        
    Returns:
        任务响应
    """
    try:
        # 验证参数
        if request.position not in ["hundreds", "tens", "units"]:
            raise HTTPException(status_code=400, detail="无效的位置参数")
            
        if request.model_type not in ["xgb", "lgb", "lstm", "ensemble", "all"]:
            raise HTTPException(status_code=400, detail="无效的模型类型")
            
        # 转换优先级
        priority_map = {
            "low": TaskPriority.LOW,
            "normal": TaskPriority.NORMAL,
            "high": TaskPriority.HIGH,
            "urgent": TaskPriority.URGENT
        }
        priority = priority_map.get(request.priority, TaskPriority.NORMAL)
        
        # 创建任务
        task_id = task_manager.create_task(
            position=request.position,
            model_type=request.model_type,
            issue=request.issue,
            train_until=request.train_until,
            priority=priority
        )
        
        # 确保训练工作器正在运行
        if not training_worker.is_running:
            background_tasks.add_task(_start_training_worker)
            
        return TaskResponse(
            task_id=task_id,
            status="created",
            message="训练任务已创建并加入队列"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建训练任务失败: {str(e)}")

@router.post("/batch-start")
async def start_batch_training(request: BatchTrainingRequest, background_tasks: BackgroundTasks):
    """
    启动批量训练任务
    
    Args:
        request: 批量训练请求
        background_tasks: 后台任务
        
    Returns:
        批量任务响应
    """
    try:
        # 验证参数
        for position in request.positions:
            if position not in ["hundreds", "tens", "units"]:
                raise HTTPException(status_code=400, detail=f"无效的位置参数: {position}")
                
        for model_type in request.model_types:
            if model_type not in ["xgb", "lgb", "lstm", "ensemble"]:
                raise HTTPException(status_code=400, detail=f"无效的模型类型: {model_type}")
        
        # 转换优先级
        priority_map = {
            "low": TaskPriority.LOW,
            "normal": TaskPriority.NORMAL,
            "high": TaskPriority.HIGH,
            "urgent": TaskPriority.URGENT
        }
        priority = priority_map.get(request.priority, TaskPriority.NORMAL)
        
        # 创建所有任务
        task_ids = []
        for position in request.positions:
            for model_type in request.model_types:
                task_id = task_manager.create_task(
                    position=position,
                    model_type=model_type,
                    issue=request.issue,
                    train_until=request.train_until,
                    priority=priority
                )
                task_ids.append(task_id)
        
        # 确保训练工作器正在运行
        if not training_worker.is_running:
            background_tasks.add_task(_start_training_worker)
            
        return {
            "task_ids": task_ids,
            "total_tasks": len(task_ids),
            "status": "created",
            "message": f"已创建 {len(task_ids)} 个训练任务"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建批量训练任务失败: {str(e)}")

@router.get("/task/{task_id}", response_model=TaskDetailResponse)
async def get_task_status(task_id: str):
    """
    获取任务状态
    
    Args:
        task_id: 任务ID
        
    Returns:
        任务详情
    """
    task = task_manager.get_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
        
    # 计算运行时长
    duration_seconds = None
    if task.started_at:
        start_time = datetime.fromisoformat(task.started_at)
        if task.completed_at:
            end_time = datetime.fromisoformat(task.completed_at)
            duration_seconds = (end_time - start_time).total_seconds()
        else:
            duration_seconds = (datetime.now() - start_time).total_seconds()
    
    return TaskDetailResponse(
        task_id=task.task_id,
        position=task.position,
        model_type=task.model_type,
        issue=task.issue,
        train_until=task.train_until,
        priority=task.priority.name.lower(),
        status=task.status.value,
        progress=task.progress,
        message=task.message,
        created_at=task.created_at,
        started_at=task.started_at,
        completed_at=task.completed_at,
        duration_seconds=duration_seconds,
        result=task.result,
        error=task.error
    )

@router.get("/task/{task_id}/progress")
async def get_task_progress(task_id: str):
    """
    获取任务进度
    
    Args:
        task_id: 任务ID
        
    Returns:
        任务进度信息
    """
    task = task_manager.get_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
        
    return {
        "task_id": task_id,
        "progress": task.progress,
        "status": task.status.value,
        "message": task.message,
        "timestamp": datetime.now().isoformat()
    }

@router.post("/task/{task_id}/cancel")
async def cancel_task(task_id: str):
    """
    取消任务
    
    Args:
        task_id: 任务ID
        
    Returns:
        取消结果
    """
    success = task_manager.cancel_task(task_id)
    if not success:
        raise HTTPException(status_code=400, detail="无法取消任务")
        
    return {
        "task_id": task_id,
        "status": "cancelled",
        "message": "任务已取消"
    }

@router.get("/tasks")
@cache_response(ttl=10)  # 缓存10秒
async def get_all_tasks(status: Optional[str] = None, limit: int = 100):
    """
    获取所有任务
    
    Args:
        status: 过滤状态 (pending/running/completed/failed/cancelled)
        limit: 限制数量
        
    Returns:
        任务列表
    """
    all_tasks = task_manager.get_all_tasks()
    
    # 状态过滤
    if status:
        try:
            filter_status = TaskStatus(status)
            all_tasks = [task for task in all_tasks if task.status == filter_status]
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的状态参数")
    
    # 按创建时间倒序排序
    all_tasks.sort(key=lambda x: x.created_at, reverse=True)
    
    # 限制数量
    all_tasks = all_tasks[:limit]
    
    # 转换为响应格式
    tasks_data = []
    for task in all_tasks:
        duration_seconds = None
        if task.started_at:
            start_time = datetime.fromisoformat(task.started_at)
            if task.completed_at:
                end_time = datetime.fromisoformat(task.completed_at)
                duration_seconds = (end_time - start_time).total_seconds()
            else:
                duration_seconds = (datetime.now() - start_time).total_seconds()
        
        tasks_data.append({
            "task_id": task.task_id,
            "position": task.position,
            "model_type": task.model_type,
            "status": task.status.value,
            "progress": task.progress,
            "created_at": task.created_at,
            "duration_seconds": duration_seconds
        })
    
    return {
        "tasks": tasks_data,
        "total": len(tasks_data),
        "timestamp": datetime.now().isoformat()
    }

@router.get("/queue/status")
@cache_response(ttl=5)  # 缓存5秒
async def get_queue_status():
    """
    获取队列状态
    
    Returns:
        队列状态信息
    """
    queue_status = task_manager.get_queue_status()
    worker_status = training_worker.get_status()
    
    return {
        "queue": queue_status,
        "worker": worker_status,
        "timestamp": datetime.now().isoformat()
    }

@router.get("/monitor/status")
async def get_monitoring_status():
    """
    获取监控状态
    
    Returns:
        监控状态信息
    """
    if not progress_monitor.is_monitoring:
        # 如果监控未启动，启动它
        progress_monitor.start_monitoring()
        
    current_status = progress_monitor.get_current_status()
    return current_status

@router.get("/monitor/history")
async def get_monitoring_history(hours: int = 1):
    """
    获取监控历史数据
    
    Args:
        hours: 历史数据小时数
        
    Returns:
        历史监控数据
    """
    if hours < 1 or hours > 24:
        raise HTTPException(status_code=400, detail="小时数必须在1-24之间")
        
    history_data = progress_monitor.get_historical_data(hours)
    return history_data

@router.get("/monitor/summary")
async def get_performance_summary():
    """
    获取性能摘要

    Returns:
        性能摘要信息
    """
    summary = progress_monitor.get_performance_summary()
    return summary

@router.get("/performance/trends")
async def get_performance_trends(days: int = 7):
    """
    获取性能趋势分析

    Args:
        days: 分析天数

    Returns:
        性能趋势数据
    """
    trends = performance_analyzer.analyze_performance_trends(days)
    return trends

@router.get("/performance/bottlenecks")
async def get_performance_bottlenecks():
    """
    获取性能瓶颈分析

    Returns:
        瓶颈分析结果
    """
    bottlenecks = performance_analyzer.identify_bottlenecks()
    return {
        "bottlenecks": [
            {
                "type": b.bottleneck_type,
                "severity": b.severity,
                "description": b.description,
                "impact_score": b.impact_score,
                "recommendations": b.recommendations
            } for b in bottlenecks
        ],
        "timestamp": datetime.now().isoformat()
    }

@router.get("/performance/suggestions")
async def get_optimization_suggestions():
    """
    获取优化建议

    Returns:
        优化建议列表
    """
    suggestions = performance_analyzer.generate_optimization_suggestions()
    return {
        "suggestions": [
            {
                "category": s.category,
                "priority": s.priority,
                "title": s.title,
                "description": s.description,
                "expected_improvement": s.expected_improvement,
                "implementation_effort": s.implementation_effort
            } for s in suggestions
        ],
        "timestamp": datetime.now().isoformat()
    }

@router.get("/performance/report")
async def export_performance_report():
    """
    导出性能报告

    Returns:
        报告文件路径
    """
    report_path = performance_analyzer.export_performance_report()
    return {
        "report_path": report_path,
        "message": "性能报告已生成",
        "timestamp": datetime.now().isoformat()
    }

@router.get("/scheduler/status")
async def get_scheduler_status():
    """
    获取智能调度器状态

    Returns:
        调度器状态信息
    """
    resource_stats = smart_scheduler.get_resource_statistics()
    recommendations = smart_scheduler.get_scheduling_recommendations()

    return {
        "resource_statistics": resource_stats,
        "scheduling_recommendations": recommendations,
        "timestamp": datetime.now().isoformat()
    }

async def _start_training_worker():
    """启动训练工作器（后台任务）"""
    if not training_worker.is_running:
        training_worker.start()
        
    if not progress_monitor.is_monitoring:
        progress_monitor.start_monitoring()
