#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进度监控器
负责实时监控训练进度、资源使用和状态更新
"""

import sys
import time
import threading
import logging
import psutil
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass, asdict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.training.task_manager import task_manager, TaskStatus

@dataclass
class SystemMetrics:
    """系统指标数据类"""
    timestamp: str
    cpu_percent: float
    memory_percent: float
    memory_used_gb: float
    memory_total_gb: float
    disk_usage_percent: float
    active_processes: int
    
    def to_dict(self) -> Dict:
        return asdict(self)

@dataclass
class TrainingMetrics:
    """训练指标数据类"""
    task_id: str
    position: str
    model_type: str
    progress: float
    status: str
    message: str
    duration_seconds: float
    memory_peak_mb: float
    
    def to_dict(self) -> Dict:
        return asdict(self)

class ProgressMonitor:
    """进度监控器"""
    
    def __init__(self, update_interval: float = 2.0):
        """
        初始化进度监控器
        
        Args:
            update_interval: 更新间隔（秒）
        """
        self.update_interval = update_interval
        self.is_monitoring = False
        self.monitor_thread = None
        
        # 数据存储
        self.system_metrics_history: List[SystemMetrics] = []
        self.training_metrics_history: List[TrainingMetrics] = []
        self.max_history_size = 1000
        
        # 回调函数
        self.update_callbacks: List[Callable] = []
        
        # 设置日志
        self.logger = logging.getLogger('ProgressMonitor')
        self.logger.setLevel(logging.INFO)
        
        # 数据目录
        self.data_dir = Path("data/monitoring")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            return
            
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        self.logger.info("🔍 进度监控已启动")
        
    def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            return
            
        self.is_monitoring = False
        
        if self.monitor_thread:
            self.monitor_thread.join(timeout=10)
            
        # 保存监控数据
        self._save_monitoring_data()
        
        self.logger.info("⏹️ 进度监控已停止")
        
    def add_update_callback(self, callback: Callable):
        """添加更新回调函数"""
        self.update_callbacks.append(callback)
        
    def remove_update_callback(self, callback: Callable):
        """移除更新回调函数"""
        if callback in self.update_callbacks:
            self.update_callbacks.remove(callback)
            
    def _monitoring_loop(self):
        """监控主循环"""
        while self.is_monitoring:
            try:
                # 收集系统指标
                system_metrics = self._collect_system_metrics()
                self.system_metrics_history.append(system_metrics)
                
                # 收集训练指标
                training_metrics = self._collect_training_metrics()
                self.training_metrics_history.extend(training_metrics)
                
                # 限制历史数据大小
                self._trim_history()
                
                # 调用更新回调
                self._notify_callbacks(system_metrics, training_metrics)
                
                time.sleep(self.update_interval)
                
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                time.sleep(5)
                
    def _collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=None)
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used_gb = memory.used / 1024 / 1024 / 1024
        memory_total_gb = memory.total / 1024 / 1024 / 1024
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        disk_usage_percent = disk.percent
        
        # 活跃进程数
        active_processes = len(psutil.pids())
        
        return SystemMetrics(
            timestamp=datetime.now().isoformat(),
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            memory_used_gb=memory_used_gb,
            memory_total_gb=memory_total_gb,
            disk_usage_percent=disk_usage_percent,
            active_processes=active_processes
        )
        
    def _collect_training_metrics(self) -> List[TrainingMetrics]:
        """收集训练指标"""
        metrics = []
        
        # 获取所有运行中的任务
        running_tasks = task_manager.get_running_tasks()
        
        for task in running_tasks:
            # 计算运行时长
            duration = 0
            if task.started_at:
                start_time = datetime.fromisoformat(task.started_at)
                duration = (datetime.now() - start_time).total_seconds()
                
            # 估算内存使用（简化版本）
            memory_peak_mb = 0
            try:
                # 这里可以集成更详细的内存监控
                current_process = psutil.Process()
                memory_peak_mb = current_process.memory_info().rss / 1024 / 1024
            except:
                pass
                
            metrics.append(TrainingMetrics(
                task_id=task.task_id,
                position=task.position,
                model_type=task.model_type,
                progress=task.progress,
                status=task.status.value,
                message=task.message,
                duration_seconds=duration,
                memory_peak_mb=memory_peak_mb
            ))
            
        return metrics
        
    def _trim_history(self):
        """限制历史数据大小"""
        if len(self.system_metrics_history) > self.max_history_size:
            self.system_metrics_history = self.system_metrics_history[-self.max_history_size:]
            
        if len(self.training_metrics_history) > self.max_history_size:
            self.training_metrics_history = self.training_metrics_history[-self.max_history_size:]
            
    def _notify_callbacks(self, system_metrics: SystemMetrics, training_metrics: List[TrainingMetrics]):
        """通知所有回调函数"""
        for callback in self.update_callbacks:
            try:
                callback(system_metrics, training_metrics)
            except Exception as e:
                self.logger.warning(f"回调函数异常: {e}")
                
    def get_current_status(self) -> Dict:
        """获取当前状态"""
        # 最新的系统指标
        latest_system = self.system_metrics_history[-1] if self.system_metrics_history else None
        
        # 当前训练任务
        running_tasks = task_manager.get_running_tasks()
        current_training = [metric.to_dict() for metric in self._collect_training_metrics()]
        
        # 队列状态
        queue_status = task_manager.get_queue_status()
        
        return {
            'timestamp': datetime.now().isoformat(),
            'system_metrics': latest_system.to_dict() if latest_system else None,
            'training_metrics': current_training,
            'queue_status': queue_status,
            'monitoring_active': self.is_monitoring
        }
        
    def get_historical_data(self, hours: int = 1) -> Dict:
        """获取历史数据"""
        cutoff_time = datetime.now().timestamp() - (hours * 3600)
        
        # 过滤系统指标
        filtered_system = [
            metric for metric in self.system_metrics_history
            if datetime.fromisoformat(metric.timestamp).timestamp() > cutoff_time
        ]
        
        # 过滤训练指标
        filtered_training = [
            metric for metric in self.training_metrics_history
            if datetime.fromisoformat(metric.timestamp).timestamp() > cutoff_time
        ]
        
        return {
            'system_metrics': [metric.to_dict() for metric in filtered_system],
            'training_metrics': [metric.to_dict() for metric in filtered_training],
            'hours': hours
        }
        
    def get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        if not self.system_metrics_history:
            return {}
            
        # 计算系统指标统计
        cpu_values = [m.cpu_percent for m in self.system_metrics_history]
        memory_values = [m.memory_percent for m in self.system_metrics_history]
        
        # 计算训练统计
        completed_tasks = [t for t in task_manager.get_all_tasks() 
                          if t.status == TaskStatus.COMPLETED]
        
        avg_duration = 0
        if completed_tasks:
            durations = []
            for task in completed_tasks:
                if task.started_at and task.completed_at:
                    start = datetime.fromisoformat(task.started_at)
                    end = datetime.fromisoformat(task.completed_at)
                    durations.append((end - start).total_seconds())
            avg_duration = sum(durations) / len(durations) if durations else 0
            
        return {
            'system_performance': {
                'cpu_avg': sum(cpu_values) / len(cpu_values),
                'cpu_max': max(cpu_values),
                'memory_avg': sum(memory_values) / len(memory_values),
                'memory_max': max(memory_values),
                'samples_count': len(self.system_metrics_history)
            },
            'training_performance': {
                'total_tasks': len(task_manager.get_all_tasks()),
                'completed_tasks': len(completed_tasks),
                'avg_duration_seconds': avg_duration,
                'success_rate': len(completed_tasks) / len(task_manager.get_all_tasks()) * 100 
                              if task_manager.get_all_tasks() else 0
            }
        }
        
    def _save_monitoring_data(self):
        """保存监控数据"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 保存系统指标
            system_file = self.data_dir / f"system_metrics_{timestamp}.json"
            with open(system_file, 'w', encoding='utf-8') as f:
                json.dump([m.to_dict() for m in self.system_metrics_history], 
                         f, indent=2, ensure_ascii=False)
                         
            # 保存训练指标
            training_file = self.data_dir / f"training_metrics_{timestamp}.json"
            with open(training_file, 'w', encoding='utf-8') as f:
                json.dump([m.to_dict() for m in self.training_metrics_history], 
                         f, indent=2, ensure_ascii=False)
                         
            self.logger.info(f"💾 监控数据已保存: {system_file}, {training_file}")
            
        except Exception as e:
            self.logger.error(f"保存监控数据失败: {e}")

# 全局进度监控器实例
progress_monitor = ProgressMonitor()

if __name__ == "__main__":
    # 测试代码
    def update_callback(system_metrics, training_metrics):
        print(f"系统: CPU {system_metrics.cpu_percent:.1f}%, 内存 {system_metrics.memory_percent:.1f}%")
        for metric in training_metrics:
            print(f"训练: {metric.task_id} - {metric.progress:.1f}%")
            
    # 启动监控
    progress_monitor.add_update_callback(update_callback)
    progress_monitor.start_monitoring()
    
    try:
        # 运行一段时间
        time.sleep(30)
        
        # 显示摘要
        summary = progress_monitor.get_performance_summary()
        print(f"性能摘要: {summary}")
        
    except KeyboardInterrupt:
        print("用户中断")
    finally:
        progress_monitor.stop_monitoring()
