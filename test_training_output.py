#!/usr/bin/env python3
"""
测试训练脚本输出
"""

import subprocess
import sys
import os

def test_training_output():
    """测试训练脚本的输出"""
    print("🔍 测试训练脚本输出")
    
    # 模拟训练工作器的命令
    cmd = [
        sys.executable, "-u", "-W", "ignore",
        "scripts/ultra_light_train.py",
        "--position", "units",
        "--limit", "500", 
        "--issue", "2025231"
    ]
    
    print(f"🚀 执行命令: {' '.join(cmd)}")
    
    try:
        # 启动进程，使用与训练工作器相同的配置
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='replace',
            cwd=os.getcwd(),
            bufsize=1,
            universal_newlines=True
        )
        
        # 监控进程输出
        output_lines = []
        training_info_found = False
        
        print("📝 开始监控输出...")
        
        while process.poll() is None:
            # 读取输出
            if process.stdout:
                try:
                    line = process.stdout.readline()
                    if line:
                        line_stripped = line.strip()
                        output_lines.append(line_stripped)
                        
                        # 显示所有输出行
                        print(f"📝 输出: {line_stripped}")
                        
                        # 检查是否包含训练信息
                        if "[TRAINING_INFO]" in line_stripped:
                            print(f"🔍 发现训练信息: {line_stripped}")
                            training_info_found = True
                            
                except UnicodeDecodeError:
                    print("❌ 编码错误")
        
        # 等待进程完成
        process.wait()
        
        # 读取剩余输出
        remaining_stdout, remaining_stderr = process.communicate()
        if remaining_stdout:
            for line in remaining_stdout.split('\n'):
                if line.strip():
                    output_lines.append(line.strip())
                    print(f"📝 剩余输出: {line.strip()}")
                    
                    if "[TRAINING_INFO]" in line.strip():
                        print(f"🔍 发现剩余训练信息: {line.strip()}")
                        training_info_found = True
        
        if remaining_stderr:
            print(f"❌ 错误输出: {remaining_stderr}")
        
        print(f"\n📊 测试结果:")
        print(f"   - 进程返回码: {process.returncode}")
        print(f"   - 输出行数: {len(output_lines)}")
        print(f"   - 训练信息捕获: {'✅ 成功' if training_info_found else '❌ 失败'}")
        
        if not training_info_found:
            print("\n🔍 搜索包含'training'的行:")
            for i, line in enumerate(output_lines):
                if 'training' in line.lower() or 'train' in line.lower():
                    print(f"   {i+1}: {line}")
        
        return training_info_found
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_training_output()
    sys.exit(0 if success else 1)
