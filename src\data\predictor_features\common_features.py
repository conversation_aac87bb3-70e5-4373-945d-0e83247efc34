"""
通用特征生成器
为所有预测器提供通用特征，包括：
- 组合特征：三位数字的组合模式、重复模式
- 时间特征：时间序列相关特征、周期性
- 形态特征：数字形态、对称性、规律性
- 交互特征：位间交互、复合指标
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from collections import Counter
from datetime import datetime


def generate_common_features(df: pd.DataFrame, window_sizes: List[int] = [5, 10, 20, 50]) -> pd.DataFrame:
    """
    生成通用特征
    
    Args:
        df: 包含历史数据的DataFrame，必须包含'hundreds', 'tens', 'units'列
        window_sizes: 滑动窗口大小列表
        
    Returns:
        pd.DataFrame: 包含通用特征的DataFrame
    """
    required_cols = ['hundreds', 'tens', 'units']
    if not all(col in df.columns for col in required_cols):
        raise ValueError(f"DataFrame必须包含{required_cols}列")
    
    result_df = df.copy()

    # 1. 组合模式特征
    combo_features = _generate_combination_features(df[required_cols], window_sizes)
    result_df = pd.concat([result_df, combo_features], axis=1)

    # 2. 时间序列特征
    temporal_features = _generate_temporal_features(df[required_cols], window_sizes)
    result_df = pd.concat([result_df, temporal_features], axis=1)

    # 3. 形态特征
    morpho_features = _generate_morphological_features(df[required_cols], window_sizes)
    result_df = pd.concat([result_df, morpho_features], axis=1)

    # 4. 交互特征
    interact_features = _generate_interaction_features(df[required_cols], window_sizes)
    result_df = pd.concat([result_df, interact_features], axis=1)

    # 5. 复合指标特征
    composite_features = _generate_composite_features(df[required_cols], window_sizes)
    result_df = pd.concat([result_df, composite_features], axis=1)
    
    return result_df


def _generate_combination_features(df: pd.DataFrame, window_sizes: List[int]) -> pd.DataFrame:
    """生成组合模式特征"""
    features = pd.DataFrame(index=df.index)
    
    hundreds, tens, units = df['hundreds'], df['tens'], df['units']
    
    # 基础组合特征
    features['number_combination'] = hundreds * 100 + tens * 10 + units
    features['digit_sum'] = hundreds + tens + units
    features['digit_product'] = hundreds * tens * units
    features['digit_span'] = df[['hundreds', 'tens', 'units']].max(axis=1) - df[['hundreds', 'tens', 'units']].min(axis=1)
    
    # 重复模式
    features['has_repeat'] = ((hundreds == tens) | (tens == units) | (hundreds == units)).astype(int)
    features['all_same'] = ((hundreds == tens) & (tens == units)).astype(int)
    features['all_different'] = ((hundreds != tens) & (tens != units) & (hundreds != units)).astype(int)
    
    # 数字类型
    features['number_type'] = _classify_number_type(hundreds, tens, units)
    
    for window in window_sizes:
        # 组合频次
        features[f'combination_freq_{window}'] = features['number_combination'].rolling(window=window).apply(
            lambda x: Counter(x)[x.iloc[-1]] if len(x) > 0 else 0
        )
        
        # 重复模式统计
        features[f'repeat_ratio_{window}'] = features['has_repeat'].rolling(window=window).mean()
        features[f'all_same_ratio_{window}'] = features['all_same'].rolling(window=window).mean()
        features[f'all_different_ratio_{window}'] = features['all_different'].rolling(window=window).mean()
        
        # 组合多样性
        features[f'combination_diversity_{window}'] = _calculate_combination_diversity(
            features['number_combination'], window
        )
        
        # 数字类型分布
        features[f'number_type_diversity_{window}'] = _calculate_type_diversity(
            features['number_type'], window
        )
    
    return features


def _generate_temporal_features(df: pd.DataFrame, window_sizes: List[int]) -> pd.DataFrame:
    """生成时间序列特征"""
    features = pd.DataFrame(index=df.index)
    
    hundreds, tens, units = df['hundreds'], df['tens'], df['units']
    
    # 时间序列基础特征
    features['sequence_number'] = range(len(df))
    
    # 如果有日期信息
    if hasattr(df.index, 'dayofweek'):
        features['day_of_week'] = df.index.dayofweek
        features['is_weekend'] = (df.index.dayofweek >= 5).astype(int)
        features['month'] = df.index.month
        features['day_of_month'] = df.index.day
    
    for window in window_sizes:
        # 趋势特征
        features[f'hundreds_trend_{window}'] = hundreds.rolling(window=window).apply(
            lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) > 1 else 0
        )
        features[f'tens_trend_{window}'] = tens.rolling(window=window).apply(
            lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) > 1 else 0
        )
        features[f'units_trend_{window}'] = units.rolling(window=window).apply(
            lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) > 1 else 0
        )
        
        # 周期性特征
        features[f'periodicity_{window}'] = _calculate_periodicity(df, window)
        
        # 自相关特征
        features[f'autocorr_{window}'] = _calculate_autocorrelation(df, window)
    
    return features


def _generate_morphological_features(df: pd.DataFrame, window_sizes: List[int]) -> pd.DataFrame:
    """生成形态特征"""
    features = pd.DataFrame(index=df.index)
    
    hundreds, tens, units = df['hundreds'], df['tens'], df['units']
    
    # 基础形态特征
    features['is_ascending'] = ((hundreds < tens) & (tens < units)).astype(int)
    features['is_descending'] = ((hundreds > tens) & (tens > units)).astype(int)
    features['is_symmetric'] = (hundreds == units).astype(int)
    features['is_palindrome'] = features['is_symmetric']  # 对于3位数，对称即回文
    
    # 数字大小关系
    features['max_position'] = df[['hundreds', 'tens', 'units']].idxmax(axis=1).map(
        {'hundreds': 0, 'tens': 1, 'units': 2}
    )
    features['min_position'] = df[['hundreds', 'tens', 'units']].idxmin(axis=1).map(
        {'hundreds': 0, 'tens': 1, 'units': 2}
    )
    
    # 奇偶性特征
    features['odd_count'] = (hundreds % 2 + tens % 2 + units % 2)
    features['even_count'] = 3 - features['odd_count']
    features['all_odd'] = (features['odd_count'] == 3).astype(int)
    features['all_even'] = (features['even_count'] == 3).astype(int)
    features['mixed_parity'] = ((features['odd_count'] > 0) & (features['even_count'] > 0)).astype(int)
    
    for window in window_sizes:
        # 形态模式统计
        features[f'ascending_ratio_{window}'] = features['is_ascending'].rolling(window=window).mean()
        features[f'descending_ratio_{window}'] = features['is_descending'].rolling(window=window).mean()
        features[f'symmetric_ratio_{window}'] = features['is_symmetric'].rolling(window=window).mean()
        
        # 奇偶性统计
        features[f'all_odd_ratio_{window}'] = features['all_odd'].rolling(window=window).mean()
        features[f'all_even_ratio_{window}'] = features['all_even'].rolling(window=window).mean()
        features[f'mixed_parity_ratio_{window}'] = features['mixed_parity'].rolling(window=window).mean()
        
        # 形态稳定性
        features[f'morphology_stability_{window}'] = _calculate_morphology_stability(df, window)
    
    return features


def _generate_interaction_features(df: pd.DataFrame, window_sizes: List[int]) -> pd.DataFrame:
    """生成交互特征"""
    features = pd.DataFrame(index=df.index)
    
    hundreds, tens, units = df['hundreds'], df['tens'], df['units']
    
    # 位间差值
    features['h_t_diff'] = hundreds - tens
    features['t_u_diff'] = tens - units
    features['h_u_diff'] = hundreds - units
    
    # 位间比值
    features['h_t_ratio'] = hundreds / (tens + 1e-8)
    features['t_u_ratio'] = tens / (units + 1e-8)
    features['h_u_ratio'] = hundreds / (units + 1e-8)
    
    # 位间相关性
    for window in window_sizes:
        features[f'h_t_corr_{window}'] = hundreds.rolling(window=window).corr(tens)
        features[f't_u_corr_{window}'] = tens.rolling(window=window).corr(units)
        features[f'h_u_corr_{window}'] = hundreds.rolling(window=window).corr(units)
        
        # 交互强度
        features[f'interaction_strength_{window}'] = _calculate_interaction_strength(df, window)
        
        # 位间平衡度
        features[f'position_balance_{window}'] = _calculate_position_balance(df, window)
    
    return features


def _generate_composite_features(df: pd.DataFrame, window_sizes: List[int]) -> pd.DataFrame:
    """生成复合指标特征"""
    features = pd.DataFrame(index=df.index)
    
    hundreds, tens, units = df['hundreds'], df['tens'], df['units']
    
    # 复合指标
    features['complexity_index'] = _calculate_complexity_index(df)
    features['randomness_index'] = _calculate_randomness_index(df)
    features['pattern_index'] = _calculate_pattern_index(df)
    
    for window in window_sizes:
        # 复合指标的滑动统计
        features[f'complexity_mean_{window}'] = features['complexity_index'].rolling(window=window).mean()
        features[f'randomness_mean_{window}'] = features['randomness_index'].rolling(window=window).mean()
        features[f'pattern_mean_{window}'] = features['pattern_index'].rolling(window=window).mean()
        
        # 综合评分
        features[f'composite_score_{window}'] = _calculate_composite_score(df, window)
    
    return features


# 辅助函数
def _classify_number_type(hundreds: pd.Series, tens: pd.Series, units: pd.Series) -> pd.Series:
    """分类数字类型"""
    def classify_single(h, t, u):
        if h == t == u:
            return 'triple'
        elif h == t or t == u or h == u:
            return 'double'
        elif abs(h - t) == 1 and abs(t - u) == 1:
            return 'consecutive'
        else:
            return 'random'
    
    return pd.Series([classify_single(h, t, u) for h, t, u in zip(hundreds, tens, units)], index=hundreds.index)


def _calculate_combination_diversity(combinations: pd.Series, window: int) -> pd.Series:
    """计算组合多样性"""
    diversities = []
    for i in range(len(combinations)):
        if i < window:
            diversities.append(0)
        else:
            window_data = combinations.iloc[i-window+1:i+1]
            unique_count = len(window_data.unique())
            diversity = unique_count / window
            diversities.append(diversity)
    return pd.Series(diversities, index=combinations.index)


def _calculate_type_diversity(types: pd.Series, window: int) -> pd.Series:
    """计算类型多样性"""
    diversities = []
    for i in range(len(types)):
        if i < window:
            diversities.append(0)
        else:
            window_data = types.iloc[i-window+1:i+1]
            unique_count = len(window_data.unique())
            diversity = unique_count / 4  # 4种类型：triple, double, consecutive, random
            diversities.append(diversity)
    return pd.Series(diversities, index=types.index)


def _calculate_periodicity(df: pd.DataFrame, window: int) -> pd.Series:
    """计算周期性"""
    periodicities = []
    combinations = df['hundreds'] * 100 + df['tens'] * 10 + df['units']
    
    for i in range(len(df)):
        if i < window:
            periodicities.append(0)
        else:
            window_data = combinations.iloc[i-window+1:i+1]
            # 简化的周期性计算
            autocorr = window_data.autocorr(lag=1)
            periodicity = autocorr if not pd.isna(autocorr) else 0
            periodicities.append(periodicity)
    
    return pd.Series(periodicities, index=df.index)


def _calculate_autocorrelation(df: pd.DataFrame, window: int) -> pd.Series:
    """计算自相关"""
    autocorrs = []
    combinations = df['hundreds'] * 100 + df['tens'] * 10 + df['units']
    
    for i in range(len(df)):
        if i < window:
            autocorrs.append(0)
        else:
            window_data = combinations.iloc[i-window+1:i+1]
            autocorr = window_data.autocorr(lag=1)
            autocorrs.append(autocorr if not pd.isna(autocorr) else 0)
    
    return pd.Series(autocorrs, index=df.index)


def _calculate_morphology_stability(df: pd.DataFrame, window: int) -> pd.Series:
    """计算形态稳定性"""
    stabilities = []
    
    for i in range(len(df)):
        if i < window:
            stabilities.append(0)
        else:
            window_df = df.iloc[i-window+1:i+1]
            
            # 计算形态变化的频率
            ascending = ((window_df['hundreds'] < window_df['tens']) & 
                        (window_df['tens'] < window_df['units'])).sum()
            descending = ((window_df['hundreds'] > window_df['tens']) & 
                         (window_df['tens'] > window_df['units'])).sum()
            symmetric = (window_df['hundreds'] == window_df['units']).sum()
            
            # 稳定性：主要形态的占比
            max_pattern = max(ascending, descending, symmetric)
            stability = max_pattern / window
            stabilities.append(stability)
    
    return pd.Series(stabilities, index=df.index)


def _calculate_interaction_strength(df: pd.DataFrame, window: int) -> pd.Series:
    """计算交互强度"""
    strengths = []
    
    for i in range(len(df)):
        if i < window:
            strengths.append(0)
        else:
            window_df = df.iloc[i-window+1:i+1]
            
            # 计算位间相关性的平均值
            h_t_corr = window_df['hundreds'].corr(window_df['tens'])
            t_u_corr = window_df['tens'].corr(window_df['units'])
            h_u_corr = window_df['hundreds'].corr(window_df['units'])
            
            corrs = [c for c in [h_t_corr, t_u_corr, h_u_corr] if not pd.isna(c)]
            strength = np.mean(np.abs(corrs)) if corrs else 0
            strengths.append(strength)
    
    return pd.Series(strengths, index=df.index)


def _calculate_position_balance(df: pd.DataFrame, window: int) -> pd.Series:
    """计算位间平衡度"""
    balances = []
    
    for i in range(len(df)):
        if i < window:
            balances.append(0)
        else:
            window_df = df.iloc[i-window+1:i+1]
            
            # 计算三个位置的标准差
            h_std = window_df['hundreds'].std()
            t_std = window_df['tens'].std()
            u_std = window_df['units'].std()
            
            # 平衡度：标准差的一致性
            stds = [h_std, t_std, u_std]
            balance = 1 - np.std(stds) / (np.mean(stds) + 1e-8)
            balances.append(balance)
    
    return pd.Series(balances, index=df.index)


def _calculate_complexity_index(df: pd.DataFrame) -> pd.Series:
    """计算复杂度指数"""
    hundreds, tens, units = df['hundreds'], df['tens'], df['units']
    
    # 基于数字间的关系计算复杂度
    same_count = (hundreds == tens).astype(int) + (tens == units).astype(int) + (hundreds == units).astype(int)
    complexity = 1 - same_count / 3  # 相同数字越多，复杂度越低
    
    return complexity


def _calculate_randomness_index(df: pd.DataFrame) -> pd.Series:
    """计算随机性指数"""
    hundreds, tens, units = df['hundreds'], df['tens'], df['units']
    
    # 基于数字分布的随机性
    digit_sum = hundreds + tens + units
    digit_product = hundreds * tens * units + 1  # 避免0
    
    # 随机性：和值与乘积的关系
    randomness = np.abs(digit_sum - np.log(digit_product)) / 10
    
    return randomness


def _calculate_pattern_index(df: pd.DataFrame) -> pd.Series:
    """计算模式指数"""
    hundreds, tens, units = df['hundreds'], df['tens'], df['units']
    
    # 基于数字模式的规律性
    ascending = ((hundreds < tens) & (tens < units)).astype(int)
    descending = ((hundreds > tens) & (tens > units)).astype(int)
    symmetric = (hundreds == units).astype(int)
    
    pattern = ascending + descending + symmetric
    
    return pattern


def _calculate_composite_score(df: pd.DataFrame, window: int) -> pd.Series:
    """计算综合评分"""
    scores = []
    
    for i in range(len(df)):
        if i < window:
            scores.append(0)
        else:
            window_df = df.iloc[i-window+1:i+1]
            
            # 综合多个指标
            diversity = len(pd.unique(window_df['hundreds'] * 100 + window_df['tens'] * 10 + window_df['units'])) / window
            stability = window_df['hundreds'].std() + window_df['tens'].std() + window_df['units'].std()
            balance = 1 / (1 + stability)
            
            score = (diversity + balance) / 2
            scores.append(score)
    
    return pd.Series(scores, index=df.index)
