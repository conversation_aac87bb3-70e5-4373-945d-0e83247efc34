#!/usr/bin/env python3
"""
训练状态查询脚本

功能：
- 查询当前训练进度
- 显示训练状态和预计完成时间
- 支持实时监控模式
- 显示详细的性能指标

Author: Augment Code AI Assistant
Date: 2025-08-15
"""

import sys
import json
import time
import argparse
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional

def load_training_status(log_dir: str = "logs") -> Dict[str, Any]:
    """加载训练状态"""
    status_file = Path(log_dir) / "training_status.json"
    
    if not status_file.exists():
        return {}
    
    try:
        with open(status_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 读取状态文件失败: {e}")
        return {}

def format_time_delta(td: timedelta) -> str:
    """格式化时间差"""
    total_seconds = int(td.total_seconds())
    hours = total_seconds // 3600
    minutes = (total_seconds % 3600) // 60
    seconds = total_seconds % 60
    
    if hours > 0:
        return f"{hours}小时{minutes}分钟{seconds}秒"
    elif minutes > 0:
        return f"{minutes}分钟{seconds}秒"
    else:
        return f"{seconds}秒"

def get_status_emoji(status: str) -> str:
    """获取状态表情符号"""
    status_map = {
        "initializing": "🔧",
        "training": "🚀",
        "completed": "✅",
        "failed": "❌",
        "interrupted": "⚠️"
    }
    return status_map.get(status, "❓")

def display_position_status(position: str, status_data: Dict[str, Any], detailed: bool = False):
    """显示位置训练状态"""
    if not status_data:
        print(f"📍 {position.title()}位预测器: 无训练记录")
        return
    
    status = status_data.get("status", "unknown")
    progress = status_data.get("progress", 0) * 100
    current_model = status_data.get("current_model", "未知")
    
    # 基本状态信息
    emoji = get_status_emoji(status)
    print(f"\n📍 {position.title()}位预测器 {emoji}")
    print("=" * 50)
    
    # 进度条
    bar_length = 30
    filled_length = int(bar_length * progress / 100)
    bar = "█" * filled_length + "░" * (bar_length - filled_length)
    print(f"进度: [{bar}] {progress:.1f}%")
    
    # 状态信息
    print(f"状态: {status}")
    if current_model:
        print(f"当前模型: {current_model.upper()}")
    
    # 时间信息
    if "start_time" in status_data:
        start_time = datetime.fromisoformat(status_data["start_time"])
        elapsed = datetime.now() - start_time
        print(f"已运行时间: {format_time_delta(elapsed)}")
        
        if "estimated_completion" in status_data and status_data["estimated_completion"]:
            try:
                completion_time = datetime.fromisoformat(status_data["estimated_completion"])
                remaining = completion_time - datetime.now()
                if remaining.total_seconds() > 0:
                    print(f"预计剩余时间: {format_time_delta(remaining)}")
                    print(f"预计完成时间: {completion_time.strftime('%H:%M:%S')}")
            except:
                pass
    
    # 模型完成情况
    completed = status_data.get("models_completed", [])
    failed = status_data.get("models_failed", [])
    total = status_data.get("total_models", 4)
    
    print(f"模型进度: {len(completed)}/{total} 完成")
    if completed:
        print(f"已完成: {', '.join([m.upper() for m in completed])}")
    if failed:
        print(f"失败模型: {', '.join([m.upper() for m in failed])}")
    
    # 详细信息
    if detailed:
        print("\n📊 详细信息:")
        
        # 数据统计
        if "data_stats" in status_data:
            data_stats = status_data["data_stats"]
            print(f"数据库: {data_stats.get('database_path', '未知')}")
            print(f"历史数据: {data_stats.get('total_records', 0)} 条")
            print(f"期号范围: {data_stats.get('period_range', '未知')}")
        
        # 性能指标
        if "performance_metrics" in status_data:
            metrics = status_data["performance_metrics"]
            print("\n📈 性能指标:")
            for model_name, model_metrics in metrics.items():
                print(f"  {model_name.upper()}:")
                if "val_accuracy" in model_metrics:
                    print(f"    验证准确率: {model_metrics['val_accuracy']:.4f}")
                if "training_time" in model_metrics:
                    print(f"    训练时间: {model_metrics['training_time']:.2f}秒")

def display_all_status(status_data: Dict[str, Any], detailed: bool = False):
    """显示所有位置的训练状态"""
    if not status_data:
        print("📋 当前无训练任务运行")
        return
    
    print("🎯 福彩3D预测器训练状态总览")
    print("=" * 60)
    
    positions = ["hundreds", "tens", "units", "sum", "span"]
    active_positions = []
    
    for position in positions:
        if position in status_data:
            pos_status = status_data[position].get("status", "unknown")
            if pos_status in ["training", "initializing"]:
                active_positions.append(position)
            display_position_status(position, status_data[position], detailed)
    
    # 总体统计
    print(f"\n📊 总体状态:")
    print(f"活跃训练任务: {len(active_positions)}")
    if active_positions:
        print(f"正在训练: {', '.join([p.title() for p in active_positions])}")

def monitor_training(log_dir: str = "logs", interval: int = 5):
    """实时监控训练进度"""
    print("🔍 启动实时训练监控 (按 Ctrl+C 退出)")
    print(f"刷新间隔: {interval}秒")
    print("=" * 60)
    
    try:
        while True:
            # 清屏（Windows）
            import os
            os.system('cls' if os.name == 'nt' else 'clear')
            
            # 显示当前时间
            print(f"⏰ 监控时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 加载并显示状态
            status_data = load_training_status(log_dir)
            display_all_status(status_data, detailed=False)
            
            # 等待下次刷新
            time.sleep(interval)
            
    except KeyboardInterrupt:
        print("\n⚠️ 监控已停止")

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='训练状态查询脚本')
    
    parser.add_argument(
        '--position', '-p',
        type=str,
        choices=['hundreds', 'tens', 'units', 'sum', 'span', 'all'],
        default='all',
        help='查询的预测器位置 (默认: all)'
    )
    
    parser.add_argument(
        '--log-dir', '-l',
        type=str,
        default='logs',
        help='日志目录 (默认: logs)'
    )
    
    parser.add_argument(
        '--detailed', '-d',
        action='store_true',
        help='显示详细信息'
    )
    
    parser.add_argument(
        '--monitor', '-m',
        action='store_true',
        help='实时监控模式'
    )
    
    parser.add_argument(
        '--interval', '-i',
        type=int,
        default=5,
        help='监控刷新间隔（秒）(默认: 5)'
    )
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_arguments()
    
    if args.monitor:
        monitor_training(args.log_dir, args.interval)
        return
    
    # 加载训练状态
    status_data = load_training_status(args.log_dir)
    
    if args.position == 'all':
        display_all_status(status_data, args.detailed)
    else:
        if args.position in status_data:
            display_position_status(args.position, status_data[args.position], args.detailed)
        else:
            print(f"📍 {args.position.title()}位预测器: 无训练记录")
            print("💡 提示: 使用 --position all 查看所有位置状态")

if __name__ == "__main__":
    main()
