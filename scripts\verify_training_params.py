#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证训练脚本参数功能
"""

import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_hundreds_params():
    """测试百位训练脚本参数"""
    print("🔍 测试百位训练脚本参数...")
    
    # 模拟参数解析
    parser = argparse.ArgumentParser()
    
    # 添加期号相关参数
    parser.add_argument('--issue', '-i', type=str, default=None, 
                       help='训练期号标识 (如: 2025217)，None则自动获取最新期号')
    parser.add_argument('--train-until', '-u', type=str, default=None,
                       help='训练数据截止期号 (如: 2025217)，None则使用所有可用数据')
    parser.add_argument('--auto-issue', action='store_true',
                       help='自动获取最新期号进行训练（等同于--issue=None）')
    parser.add_argument('--db-path', '-d', type=str, default='data/fucai3d.db',
                       help='数据库文件路径 (默认: data/fucai3d.db)')
    
    # 测试不同参数组合
    test_cases = [
        ['--issue', '2025217'],
        ['--auto-issue'],
        ['--train-until', '2025216'],
        ['--issue', '2025217', '--train-until', '2025216'],
        ['--db-path', 'data/fucai3d.db']
    ]
    
    for i, test_args in enumerate(test_cases, 1):
        try:
            args = parser.parse_args(test_args)
            print(f"   ✅ 测试 {i}: {' '.join(test_args)}")
            print(f"      issue: {args.issue}")
            print(f"      train_until: {args.train_until}")
            print(f"      auto_issue: {args.auto_issue}")
            print(f"      db_path: {args.db_path}")
        except Exception as e:
            print(f"   ❌ 测试 {i}: {e}")
            return False
    
    return True

def test_script_imports():
    """测试脚本导入"""
    print("\n🔍 测试脚本导入...")
    
    scripts = [
        'scripts.train_hundreds_predictor',
        'scripts.train_tens_predictor', 
        'scripts.train_units_predictor'
    ]
    
    for script in scripts:
        try:
            # 只测试模块是否可以导入，不执行
            import importlib.util
            spec = importlib.util.find_spec(script)
            if spec is not None:
                print(f"   ✅ {script}: 可导入")
            else:
                print(f"   ❌ {script}: 无法找到")
                return False
        except Exception as e:
            print(f"   ❌ {script}: 导入错误 - {e}")
            return False
    
    return True

def test_dynamic_issue_manager():
    """测试动态期号管理器"""
    print("\n🔍 测试动态期号管理器...")
    
    try:
        from src.utils.dynamic_issue_manager import issue_manager
        
        # 测试期号获取
        latest_issue = issue_manager.get_latest_issue()
        print(f"   ✅ 最新期号: {latest_issue}")
        
        # 测试文件名生成
        filename = issue_manager.generate_model_filename('xgb', 'hundreds', latest_issue)
        print(f"   ✅ 文件名生成: {filename}")
        
        # 测试期号检测
        new_issue = issue_manager.detect_new_issue()
        if new_issue:
            print(f"   ✅ 检测到新期号: {new_issue}")
        else:
            print(f"   ✅ 没有新期号需要训练")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 动态期号管理器测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 训练脚本参数功能验证")
    print("=" * 50)
    
    tests = [
        ("参数解析测试", test_hundreds_params),
        ("脚本导入测试", test_script_imports),
        ("动态期号管理器测试", test_dynamic_issue_manager)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: 通过")
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {e}")
    
    print("\n" + "=" * 50)
    print(f"验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有训练脚本参数功能验证通过！")
        print("\n📋 确认的功能:")
        print("   ✅ --issue: 指定训练期号")
        print("   ✅ --auto-issue: 自动获取最新期号")
        print("   ✅ --train-until: 限制训练数据截止期号")
        print("   ✅ --db-path: 数据库路径 (默认: data/fucai3d.db)")
        print("   ✅ 动态期号管理器: 正常工作")
        return True
    else:
        print("⚠️ 部分功能验证失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
