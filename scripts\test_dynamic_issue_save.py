#!/usr/bin/env python3
"""
测试动态期号保存功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.utils.dynamic_issue_manager import issue_manager
    from src.predictors.models.xgb_hundreds_model import XGBHundredsModel
    from src.predictors.models.lgb_hundreds_model import LGBHundredsModel
except ImportError as e:
    print(f"导入失败: {e}")
    sys.exit(1)

def test_dynamic_issue_manager():
    """测试动态期号管理器"""
    print("=" * 50)
    print("测试动态期号管理器")
    print("=" * 50)
    
    # 测试获取最新期号
    latest_issue = issue_manager.get_latest_issue()
    print(f"最新期号: {latest_issue}")
    
    # 测试下一期期号计算
    next_issue = issue_manager.get_next_issue(latest_issue)
    print(f"下一期期号: {next_issue}")
    
    # 测试文件名生成
    xgb_filename = issue_manager.generate_model_filename('xgb', 'hundreds', latest_issue)
    lgb_filename = issue_manager.generate_model_filename('lgb', 'hundreds', latest_issue)
    print(f"XGBoost文件名: {xgb_filename}")
    print(f"LightGBM文件名: {lgb_filename}")
    
    # 测试文件路径生成
    xgb_filepath = issue_manager.get_model_filepath('xgb', 'hundreds', latest_issue)
    lgb_filepath = issue_manager.get_model_filepath('lgb', 'hundreds', latest_issue)
    print(f"XGBoost文件路径: {xgb_filepath}")
    print(f"LightGBM文件路径: {lgb_filepath}")
    
    # 测试可用模型期号
    available_models = issue_manager.get_available_model_issues('hundreds')
    print(f"可用模型数量: {len(available_models)}")
    if available_models:
        print("最新3个可用模型:")
        for model in available_models[:3]:
            print(f"  - {model['display_name']} ({model['train_date']})")
    
    # 测试期号状态摘要
    summary = issue_manager.get_issue_summary()
    print(f"期号状态摘要: {summary}")
    
    return True

def test_model_save_with_issue():
    """测试模型按期号保存功能"""
    print("\n" + "=" * 50)
    print("测试模型按期号保存功能")
    print("=" * 50)
    
    try:
        # 创建XGBoost模型实例
        xgb_model = XGBHundredsModel(db_path="data/fucai3d.db")
        print("XGBoost模型实例创建成功")
        
        # 模拟训练状态
        xgb_model.is_trained = True
        xgb_model.xgb_model = "模拟模型对象"  # 模拟模型
        xgb_model.label_encoder = "模拟编码器"
        xgb_model.feature_names = ["feature1", "feature2"]
        xgb_model.feature_importance_ = [0.6, 0.4]
        xgb_model.training_metrics = {"accuracy": 0.85}
        xgb_model.xgb_config = {"n_estimators": 100}
        
        # 测试按期号保存
        latest_issue = issue_manager.get_latest_issue()
        print(f"使用期号 {latest_issue} 进行测试保存...")
        
        # 这里只是测试文件名生成，不实际保存
        expected_filename = f"xgb_hundreds_model_{latest_issue}.pkl"
        print(f"期望的文件名: {expected_filename}")
        
        # 测试save_model_with_issue方法的文件名生成逻辑
        model_dir = Path('models/hundreds/')
        model_dir.mkdir(parents=True, exist_ok=True)
        
        filename = issue_manager.generate_model_filename('xgb', 'hundreds', latest_issue)
        filepath = model_dir / filename
        
        print(f"生成的文件路径: {filepath}")
        print("文件名生成测试通过!")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def test_model_filename_patterns():
    """测试各种模型的文件名模式"""
    print("\n" + "=" * 50)
    print("测试模型文件名模式")
    print("=" * 50)
    
    latest_issue = issue_manager.get_latest_issue()
    positions = ['hundreds', 'tens', 'units']
    algorithms = ['xgb', 'lgb', 'lstm', 'ensemble']
    
    print(f"期号: {latest_issue}")
    print()
    
    for position in positions:
        print(f"{position.upper()} 位模型:")
        for algorithm in algorithms:
            if algorithm == 'lstm':
                # LSTM有两个文件
                main_file = issue_manager.generate_model_filename(algorithm, position, latest_issue, 'model')
                comp_file = issue_manager.generate_model_filename(algorithm, position, latest_issue, 'components')
                print(f"  {algorithm.upper()}: {main_file}")
                print(f"  {algorithm.upper()}: {comp_file}")
            else:
                filename = issue_manager.generate_model_filename(algorithm, position, latest_issue)
                print(f"  {algorithm.upper()}: {filename}")
        print()
    
    return True

def main():
    """主函数"""
    print("🔍 动态期号保存功能测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 动态期号管理器
    if test_dynamic_issue_manager():
        success_count += 1
        print("✅ 动态期号管理器测试通过")
    else:
        print("❌ 动态期号管理器测试失败")
    
    # 测试2: 模型按期号保存
    if test_model_save_with_issue():
        success_count += 1
        print("✅ 模型按期号保存测试通过")
    else:
        print("❌ 模型按期号保存测试失败")
    
    # 测试3: 文件名模式
    if test_model_filename_patterns():
        success_count += 1
        print("✅ 文件名模式测试通过")
    else:
        print("❌ 文件名模式测试失败")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！动态期号保存功能正常工作")
        return True
    else:
        print("⚠️ 部分测试失败，需要检查问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
