import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Play,
  Square,
  RefreshCw,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Activity,
  Cpu,
  MemoryStick,
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

// 类型定义
interface TrainingTask {
  task_id: string;
  position: string;
  model_type: string;
  issue?: string;
  train_until?: string;
  priority: string;
  status: string;
  progress: number;
  message: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  duration_seconds?: number;
  result?: any;
  error?: string;
}

interface QueueStatus {
  total_tasks: number;
  pending: number;
  running: number;
  completed: number;
  failed: number;
  queue_length: number;
  max_concurrent: number;
}

interface SystemMetrics {
  cpu_percent: number;
  memory_percent: number;
  memory_used_gb: number;
  memory_total_gb: number;
}

const TrainingManager: React.FC = () => {
  const { toast } = useToast();
  
  // 状态管理
  const [tasks, setTasks] = useState<TrainingTask[]>([]);
  const [queueStatus, setQueueStatus] = useState<QueueStatus | null>(null);
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedTab, setSelectedTab] = useState('create');
  
  // 表单状态
  const [formData, setFormData] = useState({
    position: '',
    model_type: '',
    issue: '',
    train_until: '',
    priority: 'normal'
  });
  
  // 批量训练表单状态
  const [batchFormData, setBatchFormData] = useState({
    positions: [] as string[],
    model_types: [] as string[],
    issue: '',
    train_until: '',
    priority: 'normal'
  });

  // WebSocket连接
  const [ws, setWs] = useState<WebSocket | null>(null);

  // 获取任务列表
  const fetchTasks = useCallback(async () => {
    try {
      const response = await fetch('/api/training/tasks');
      if (response.ok) {
        const data = await response.json();
        setTasks(data.tasks || []);
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
    }
  }, []);

  // 获取队列状态
  const fetchQueueStatus = useCallback(async () => {
    try {
      const response = await fetch('/api/training/queue/status');
      if (response.ok) {
        const data = await response.json();
        setQueueStatus(data.queue);
        setSystemMetrics(data.system_metrics);
      }
    } catch (error) {
      console.error('获取队列状态失败:', error);
    }
  }, []);

  // WebSocket连接管理
  useEffect(() => {
    const connectWebSocket = () => {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/ws`;
      
      const websocket = new WebSocket(wsUrl);
      
      websocket.onopen = () => {
        console.log('WebSocket连接已建立');
        setWs(websocket);
      };
      
      websocket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          handleWebSocketMessage(message);
        } catch (error) {
          console.error('解析WebSocket消息失败:', error);
        }
      };
      
      websocket.onclose = () => {
        console.log('WebSocket连接已断开');
        setWs(null);
        // 5秒后重连
        setTimeout(connectWebSocket, 5000);
      };
      
      websocket.onerror = (error) => {
        console.error('WebSocket错误:', error);
      };
    };

    connectWebSocket();

    return () => {
      if (ws) {
        ws.close();
      }
    };
  }, []);

  // 处理WebSocket消息
  const handleWebSocketMessage = (message: any) => {
    switch (message.type) {
      case 'training_progress':
        updateTaskProgress(message.data);
        break;
      case 'training_started':
        handleTrainingStarted(message.data);
        break;
      case 'training_completed':
        handleTrainingCompleted(message.data);
        break;
      case 'training_failed':
        handleTrainingFailed(message.data);
        break;
      case 'queue_status_update':
        setQueueStatus(message.data);
        break;
      default:
        break;
    }
  };

  // 更新任务进度
  const updateTaskProgress = (data: any) => {
    setTasks(prevTasks => 
      prevTasks.map(task => 
        task.task_id === data.task_id 
          ? { ...task, progress: data.progress, message: data.message, status: data.status }
          : task
      )
    );
  };

  // 处理训练开始
  const handleTrainingStarted = (data: any) => {
    toast({
      title: "训练开始",
      description: `${data.position} ${data.model_type} 模型训练已开始`,
    });
    fetchTasks();
  };

  // 处理训练完成
  const handleTrainingCompleted = (data: any) => {
    toast({
      title: "训练完成",
      description: `${data.position} ${data.model_type} 模型训练已完成`,
    });
    fetchTasks();
  };

  // 处理训练失败
  const handleTrainingFailed = (data: any) => {
    toast({
      title: "训练失败",
      description: `${data.position} ${data.model_type} 模型训练失败`,
      variant: "destructive",
    });
    fetchTasks();
  };

  // 初始化数据
  useEffect(() => {
    fetchTasks();
    fetchQueueStatus();
    
    // 定期刷新数据
    const interval = setInterval(() => {
      fetchTasks();
      fetchQueueStatus();
    }, 10000);

    return () => clearInterval(interval);
  }, [fetchTasks, fetchQueueStatus]);

  // 启动单个训练任务
  const startTraining = async () => {
    if (!formData.position || !formData.model_type) {
      toast({
        title: "参数错误",
        description: "请选择位置和模型类型",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/training/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const result = await response.json();
        toast({
          title: "任务创建成功",
          description: `训练任务已创建: ${result.task_id}`,
        });
        
        // 重置表单
        setFormData({
          position: '',
          model_type: '',
          issue: '',
          train_until: '',
          priority: 'normal'
        });
        
        // 刷新任务列表
        fetchTasks();
      } else {
        const error = await response.json();
        throw new Error(error.detail || '创建任务失败');
      }
    } catch (error) {
      toast({
        title: "创建失败",
        description: error instanceof Error ? error.message : '未知错误',
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 启动批量训练任务
  const startBatchTraining = async () => {
    if (batchFormData.positions.length === 0 || batchFormData.model_types.length === 0) {
      toast({
        title: "参数错误",
        description: "请至少选择一个位置和一个模型类型",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/training/batch-start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(batchFormData),
      });

      if (response.ok) {
        const result = await response.json();
        toast({
          title: "批量任务创建成功",
          description: `已创建 ${result.total_tasks} 个训练任务`,
        });
        
        // 重置表单
        setBatchFormData({
          positions: [],
          model_types: [],
          issue: '',
          train_until: '',
          priority: 'normal'
        });
        
        // 刷新任务列表
        fetchTasks();
      } else {
        const error = await response.json();
        throw new Error(error.detail || '创建批量任务失败');
      }
    } catch (error) {
      toast({
        title: "创建失败",
        description: error instanceof Error ? error.message : '未知错误',
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 取消任务
  const cancelTask = async (taskId: string) => {
    try {
      const response = await fetch(`/api/training/task/${taskId}/cancel`, {
        method: 'POST',
      });

      if (response.ok) {
        toast({
          title: "任务已取消",
          description: `任务 ${taskId} 已取消`,
        });
        fetchTasks();
      } else {
        const error = await response.json();
        throw new Error(error.detail || '取消任务失败');
      }
    } catch (error) {
      toast({
        title: "取消失败",
        description: error instanceof Error ? error.message : '未知错误',
        variant: "destructive",
      });
    }
  };

  // 获取状态徽章
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: 'secondary' as const, icon: Clock, text: '等待中' },
      running: { variant: 'default' as const, icon: Activity, text: '运行中' },
      completed: { variant: 'default' as const, icon: CheckCircle, text: '已完成' },
      failed: { variant: 'destructive' as const, icon: XCircle, text: '失败' },
      cancelled: { variant: 'outline' as const, icon: Square, text: '已取消' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="w-3 h-3" />
        {config.text}
      </Badge>
    );
  };

  // 格式化时长
  const formatDuration = (seconds?: number) => {
    if (!seconds) return '-';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-6">
      {/* 系统状态概览 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">队列状态</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {queueStatus?.running || 0} / {queueStatus?.max_concurrent || 1}
            </div>
            <p className="text-xs text-muted-foreground">
              运行中 / 最大并发
            </p>
            <div className="mt-2 text-sm">
              <div>等待: {queueStatus?.pending || 0}</div>
              <div>已完成: {queueStatus?.completed || 0}</div>
              <div>失败: {queueStatus?.failed || 0}</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">CPU使用率</CardTitle>
            <Cpu className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {systemMetrics?.cpu_percent?.toFixed(1) || 0}%
            </div>
            <Progress 
              value={systemMetrics?.cpu_percent || 0} 
              className="mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">内存使用</CardTitle>
            <MemoryStick className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {systemMetrics?.memory_percent?.toFixed(1) || 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              {systemMetrics?.memory_used_gb?.toFixed(1) || 0}GB / {systemMetrics?.memory_total_gb?.toFixed(1) || 0}GB
            </p>
            <Progress 
              value={systemMetrics?.memory_percent || 0} 
              className="mt-2"
            />
          </CardContent>
        </Card>
      </div>

      {/* 主要内容 */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="create">创建任务</TabsTrigger>
          <TabsTrigger value="batch">批量训练</TabsTrigger>
          <TabsTrigger value="monitor">任务监控</TabsTrigger>
        </TabsList>

        {/* 创建单个任务 */}
        <TabsContent value="create" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>创建训练任务</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="position">位置</Label>
                  <Select 
                    value={formData.position} 
                    onValueChange={(value) => setFormData(prev => ({ ...prev, position: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择位置" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hundreds">百位</SelectItem>
                      <SelectItem value="tens">十位</SelectItem>
                      <SelectItem value="units">个位</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="model_type">模型类型</Label>
                  <Select 
                    value={formData.model_type} 
                    onValueChange={(value) => setFormData(prev => ({ ...prev, model_type: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择模型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="xgb">XGBoost</SelectItem>
                      <SelectItem value="lgb">LightGBM</SelectItem>
                      <SelectItem value="lstm">LSTM</SelectItem>
                      <SelectItem value="ensemble">集成模型</SelectItem>
                      <SelectItem value="all">所有模型</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="issue">训练期号</Label>
                  <Input
                    id="issue"
                    placeholder="如: 2025217 (可选)"
                    value={formData.issue}
                    onChange={(e) => setFormData(prev => ({ ...prev, issue: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="train_until">截止期号</Label>
                  <Input
                    id="train_until"
                    placeholder="如: 2025216 (可选)"
                    value={formData.train_until}
                    onChange={(e) => setFormData(prev => ({ ...prev, train_until: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="priority">优先级</Label>
                  <Select 
                    value={formData.priority} 
                    onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">低</SelectItem>
                      <SelectItem value="normal">普通</SelectItem>
                      <SelectItem value="high">高</SelectItem>
                      <SelectItem value="urgent">紧急</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Button 
                onClick={startTraining} 
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    创建中...
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-4 w-4" />
                    开始训练
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 批量训练 */}
        <TabsContent value="batch" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>批量训练任务</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>位置 (可多选)</Label>
                  <div className="flex gap-2">
                    {['hundreds', 'tens', 'units'].map((pos) => (
                      <Button
                        key={pos}
                        variant={batchFormData.positions.includes(pos) ? "default" : "outline"}
                        size="sm"
                        onClick={() => {
                          setBatchFormData(prev => ({
                            ...prev,
                            positions: prev.positions.includes(pos)
                              ? prev.positions.filter(p => p !== pos)
                              : [...prev.positions, pos]
                          }));
                        }}
                      >
                        {pos === 'hundreds' ? '百位' : pos === 'tens' ? '十位' : '个位'}
                      </Button>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>模型类型 (可多选)</Label>
                  <div className="flex gap-2 flex-wrap">
                    {['xgb', 'lgb', 'lstm', 'ensemble'].map((model) => (
                      <Button
                        key={model}
                        variant={batchFormData.model_types.includes(model) ? "default" : "outline"}
                        size="sm"
                        onClick={() => {
                          setBatchFormData(prev => ({
                            ...prev,
                            model_types: prev.model_types.includes(model)
                              ? prev.model_types.filter(m => m !== model)
                              : [...prev.model_types, model]
                          }));
                        }}
                      >
                        {model === 'xgb' ? 'XGBoost' : 
                         model === 'lgb' ? 'LightGBM' : 
                         model === 'lstm' ? 'LSTM' : '集成模型'}
                      </Button>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="batch_issue">训练期号</Label>
                    <Input
                      id="batch_issue"
                      placeholder="如: 2025217 (可选)"
                      value={batchFormData.issue}
                      onChange={(e) => setBatchFormData(prev => ({ ...prev, issue: e.target.value }))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="batch_train_until">截止期号</Label>
                    <Input
                      id="batch_train_until"
                      placeholder="如: 2025216 (可选)"
                      value={batchFormData.train_until}
                      onChange={(e) => setBatchFormData(prev => ({ ...prev, train_until: e.target.value }))}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="batch_priority">优先级</Label>
                  <Select 
                    value={batchFormData.priority} 
                    onValueChange={(value) => setBatchFormData(prev => ({ ...prev, priority: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">低</SelectItem>
                      <SelectItem value="normal">普通</SelectItem>
                      <SelectItem value="high">高</SelectItem>
                      <SelectItem value="urgent">紧急</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {batchFormData.positions.length > 0 && batchFormData.model_types.length > 0 && (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      将创建 {batchFormData.positions.length * batchFormData.model_types.length} 个训练任务
                    </AlertDescription>
                  </Alert>
                )}
              </div>

              <Button 
                onClick={startBatchTraining} 
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    创建中...
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-4 w-4" />
                    开始批量训练
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 任务监控 */}
        <TabsContent value="monitor" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>训练任务列表</CardTitle>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => {
                  fetchTasks();
                  fetchQueueStatus();
                }}
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>任务ID</TableHead>
                      <TableHead>位置</TableHead>
                      <TableHead>模型</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>进度</TableHead>
                      <TableHead>时长</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {tasks.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center text-muted-foreground">
                          暂无训练任务
                        </TableCell>
                      </TableRow>
                    ) : (
                      tasks.map((task) => (
                        <TableRow key={task.task_id}>
                          <TableCell className="font-mono text-xs">
                            {task.task_id.slice(0, 8)}...
                          </TableCell>
                          <TableCell>
                            {task.position === 'hundreds' ? '百位' : 
                             task.position === 'tens' ? '十位' : '个位'}
                          </TableCell>
                          <TableCell>
                            {task.model_type === 'xgb' ? 'XGBoost' : 
                             task.model_type === 'lgb' ? 'LightGBM' : 
                             task.model_type === 'lstm' ? 'LSTM' : 
                             task.model_type === 'ensemble' ? '集成' : task.model_type}
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(task.status)}
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <Progress value={task.progress} className="w-20" />
                              <div className="text-xs text-muted-foreground">
                                {task.progress.toFixed(1)}%
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            {formatDuration(task.duration_seconds)}
                          </TableCell>
                          <TableCell>
                            {task.status === 'pending' || task.status === 'running' ? (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => cancelTask(task.task_id)}
                              >
                                <Square className="h-3 w-3" />
                              </Button>
                            ) : null}
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TrainingManager;

// 导出类型定义供其他组件使用
export type { TrainingTask, QueueStatus, SystemMetrics };
