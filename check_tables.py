import sqlite3
import os

# 连接数据库
db_path = 'data/fucai3d.db'
if os.path.exists(db_path):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # 检查预测器相关的表
    tables_to_check = [
        'tens_predictions',
        'units_predictions',
        'sum_predictions',
        'span_predictions',
        'final_predictions'
    ]

    for table in tables_to_check:
        cursor.execute('SELECT name FROM sqlite_master WHERE type=? AND name=?', ('table', table))
        result = cursor.fetchone()
        if result:
            cursor.execute(f'SELECT COUNT(*) FROM {table}')
            count = cursor.fetchone()[0]
            print(f'✅ {table}: 存在，包含 {count} 条记录')
        else:
            print(f'❌ {table}: 不存在')
            # 如果是span_predictions表不存在，尝试创建
            if table == 'span_predictions':
                try:
                    from src.data.span_data_access import SpanDataAccess
                    span_access = SpanDataAccess(db_path)
                    print(f'✅ 已创建 {table} 表')
                except Exception as e:
                    print(f'❌ 创建 {table} 表失败: {e}')

    conn.close()
else:
    print('❌ 数据库文件不存在')
