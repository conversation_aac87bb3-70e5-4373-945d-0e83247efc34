# 动态期号训练系统完整设计

## 核心理念

### 动态性原则
- ❌ **固定期号**: 硬编码2025217等特定期号
- ✅ **动态期号**: 系统自动获取和适应期号变化
- ✅ **自动化**: 无需人工指定期号，完全自动化运行

### 工作流程
```
每日开奖流程:
1. 新期号开奖 (如2025218期)
2. 数据库更新开奖数据
3. 系统自动检测新期号
4. 自动训练2025218期模型
5. Web系统可选择2025218期模型预测2025219期
```

## 核心技术架构

### 1. 动态期号获取机制

#### 核心函数设计
```python
def get_latest_issue() -> str:
    """从数据库自动获取最新期号"""
    conn = sqlite3.connect('data/fucai3d.db')
    cursor = conn.cursor()
    cursor.execute('SELECT MAX(issue) FROM lottery_data')
    latest_issue = cursor.fetchone()[0]
    conn.close()
    return latest_issue  # 返回如: "2025217"

def get_available_model_issues() -> List[Dict]:
    """获取所有可用的模型期号"""
    model_dir = Path('models/hundreds/')
    pattern = r'xgb_hundreds_model_(\d{7})\.pkl'
    issues = []
    
    for file in model_dir.glob('xgb_hundreds_model_*.pkl'):
        match = re.search(pattern, file.name)
        if match:
            issue = match.group(1)
            train_date = datetime.fromtimestamp(file.stat().st_mtime)
            issues.append({
                'issue': issue,
                'display_name': f'{issue}期模型',
                'train_date': train_date.strftime('%Y-%m-%d %H:%M'),
                'file_size': file.stat().st_size,
                'status': 'available'
            })
    
    return sorted(issues, key=lambda x: x['issue'], reverse=True)

def get_next_issue(current_issue: str) -> str:
    """计算下一期期号"""
    return str(int(current_issue) + 1)

def check_models_exist_for_issue(issue: str) -> bool:
    """检查指定期号的模型是否已存在"""
    positions = ['hundreds', 'tens', 'units']
    algorithms = ['xgb', 'lgb', 'lstm', 'ensemble']
    
    for position in positions:
        for algorithm in algorithms:
            if algorithm == 'lstm':
                model_file = f'models/{position}/lstm_{position}_model_{issue}.h5'
            else:
                model_file = f'models/{position}/{algorithm}_{position}_model_{issue}.pkl'
            
            if not Path(model_file).exists():
                return False
    
    return True
```

### 2. 动态文件命名系统

#### 命名规则
```python
def generate_model_filename(algorithm: str, position: str, issue: str, extension: str = 'pkl') -> str:
    """动态生成模型文件名"""
    return f"{algorithm}_{position}_model_{issue}.{extension}"

# 示例生成的文件名:
# 今天 (2025217期):
#   - xgb_hundreds_model_2025217.pkl
#   - lgb_hundreds_model_2025217.pkl
#   - lstm_hundreds_model_2025217.h5
#   - ensemble_hundreds_model_2025217_ensemble.pkl

# 明天 (2025218期):
#   - xgb_hundreds_model_2025218.pkl
#   - lgb_hundreds_model_2025218.pkl
#   - lstm_hundreds_model_2025218.h5
#   - ensemble_hundreds_model_2025218_ensemble.pkl
```

#### 目录结构演进
```
models/
├── hundreds/
│   ├── xgb_hundreds_model_2025215.pkl
│   ├── xgb_hundreds_model_2025216.pkl
│   ├── xgb_hundreds_model_2025217.pkl    # 当前最新
│   ├── xgb_hundreds_model_2025218.pkl    # 明天训练后生成
│   ├── lgb_hundreds_model_2025215.pkl
│   ├── lgb_hundreds_model_2025216.pkl
│   ├── lgb_hundreds_model_2025217.pkl
│   └── ...
├── tens/
│   └── (相同结构)
└── units/
    └── (相同结构)
```

### 3. 自动化训练触发机制

#### 数据更新检测
```python
def detect_new_issue() -> Optional[str]:
    """检测是否有新期号数据"""
    latest_db_issue = get_latest_issue()
    latest_model_issue = get_latest_trained_issue()
    
    if latest_db_issue > latest_model_issue:
        return latest_db_issue
    return None

def auto_train_latest_issue():
    """自动训练最新期号的模型"""
    new_issue = detect_new_issue()
    
    if not new_issue:
        logger.info("没有检测到新期号，跳过训练")
        return
    
    logger.info(f"检测到新期号: {new_issue}，开始自动训练...")
    
    # 检查是否已经训练过
    if check_models_exist_for_issue(new_issue):
        logger.info(f"期号 {new_issue} 的模型已存在，跳过训练")
        return
    
    # 自动训练所有位置
    success_count = 0
    for position in ['hundreds', 'tens', 'units']:
        try:
            train_position_with_issue(position, new_issue)
            success_count += 1
            logger.info(f"期号 {new_issue} 的 {position} 位模型训练完成")
        except Exception as e:
            logger.error(f"训练 {position} 位模型失败: {e}")
    
    logger.info(f"期号 {new_issue} 的模型训练完成，成功 {success_count}/3 个位置")

def train_position_with_issue(position: str, issue: str):
    """训练指定位置和期号的模型"""
    script_map = {
        'hundreds': 'scripts/train_hundreds_predictor.py',
        'tens': 'scripts/train_tens_predictor.py',
        'units': 'scripts/train_units_predictor.py'
    }
    
    script_path = script_map[position]
    
    # 自动模式：不指定期号，让脚本自动获取
    cmd = f"python {script_path} --model all --auto-issue"
    
    # 或手动指定期号模式
    # cmd = f"python {script_path} --model all --issue {issue}"
    
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if result.returncode != 0:
        raise RuntimeError(f"训练脚本执行失败: {result.stderr}")
```

### 4. Web系统动态集成

#### API接口设计
```python
@app.route('/api/available-models')
def get_available_models():
    """获取可用的模型期号列表"""
    try:
        models = get_available_model_issues()
        return jsonify({
            'success': True,
            'models': models,
            'latest_issue': get_latest_issue(),
            'total_count': len(models)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/predict/with-model/<issue>')
def predict_with_issue_model(issue):
    """使用指定期号的模型进行预测"""
    try:
        # 验证期号格式
        if not re.match(r'^\d{7}$', issue):
            return jsonify({'error': '期号格式错误'}), 400
        
        # 检查模型是否存在
        if not check_models_exist_for_issue(issue):
            return jsonify({'error': f'期号 {issue} 的模型不存在'}), 404
        
        # 加载指定期号的模型
        if not unified_interface.load_models_by_issue(issue):
            return jsonify({'error': f'无法加载期号 {issue} 的模型'}), 500
        
        # 获取当前期号进行预测
        current_issue = get_latest_issue()
        next_issue = get_next_issue(current_issue)
        
        # 生成预测
        prediction = unified_interface.predict_next_period(current_issue)
        
        return jsonify({
            'success': True,
            'model_issue': issue,
            'predict_issue': next_issue,
            'prediction': prediction,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/train/auto')
def trigger_auto_train():
    """手动触发自动训练"""
    try:
        auto_train_latest_issue()
        return jsonify({'success': True, 'message': '自动训练已触发'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500
```

#### 前端界面设计
```javascript
// 动态加载可用模型期号
async function loadAvailableModels() {
    try {
        const response = await fetch('/api/available-models');
        const data = await response.json();
        
        if (data.success) {
            const select = document.getElementById('model-issue-select');
            select.innerHTML = '<option value="">选择模型期号</option>';
            
            data.models.forEach(model => {
                const option = document.createElement('option');
                option.value = model.issue;
                option.textContent = `${model.display_name} (${model.train_date})`;
                select.appendChild(option);
            });
            
            // 默认选择最新期号
            if (data.models.length > 0) {
                select.value = data.models[0].issue;
            }
        }
    } catch (error) {
        console.error('加载模型列表失败:', error);
    }
}

// 使用选定期号的模型进行预测
async function predictWithSelectedModel() {
    const selectedIssue = document.getElementById('model-issue-select').value;
    
    if (!selectedIssue) {
        alert('请选择模型期号');
        return;
    }
    
    try {
        const response = await fetch(`/api/predict/with-model/${selectedIssue}`);
        const data = await response.json();
        
        if (data.success) {
            displayPrediction(data);
        } else {
            alert(`预测失败: ${data.error}`);
        }
    } catch (error) {
        console.error('预测失败:', error);
    }
}
```

### 5. 自动化数据更新集成

#### 数据更新流程集成
```python
def enhanced_data_update_with_training():
    """增强的数据更新流程，集成自动训练"""
    
    # 1. 记录更新前的最新期号
    old_latest_issue = get_latest_issue()
    
    # 2. 执行数据更新
    update_result = update_lottery_data()  # 现有的数据更新函数
    
    if not update_result:
        logger.error("数据更新失败，跳过模型训练")
        return False
    
    # 3. 检查是否有新期号
    new_latest_issue = get_latest_issue()
    
    if new_latest_issue > old_latest_issue:
        logger.info(f"检测到新期号: {old_latest_issue} → {new_latest_issue}")
        
        # 4. 自动触发模型训练
        try:
            auto_train_latest_issue()
            logger.info(f"期号 {new_latest_issue} 的模型训练已完成")
        except Exception as e:
            logger.error(f"自动训练失败: {e}")
            return False
    else:
        logger.info("没有新期号，跳过模型训练")
    
    return True

# 定时任务配置
def setup_auto_update_schedule():
    """设置自动更新定时任务"""
    import schedule
    
    # 每天21:30检查数据更新和训练
    schedule.every().day.at("21:30").do(enhanced_data_update_with_training)
    
    # 每小时检查一次（防止遗漏）
    schedule.every().hour.do(lambda: auto_train_latest_issue())
    
    logger.info("自动更新定时任务已设置")
```

### 6. 模型文件管理策略

#### 动态清理机制
```python
def cleanup_old_models(keep_latest: int = 10):
    """清理旧的模型文件，保留最新N期"""
    
    for position in ['hundreds', 'tens', 'units']:
        model_dir = Path(f'models/{position}')
        
        # 获取所有期号模型文件
        all_issues = set()
        for pattern in ['xgb_*_model_*.pkl', 'lgb_*_model_*.pkl', 'lstm_*_model_*.h5']:
            for file in model_dir.glob(pattern):
                match = re.search(r'_(\d{7})\.', file.name)
                if match:
                    all_issues.add(match.group(1))
        
        # 按期号排序，保留最新的
        sorted_issues = sorted(all_issues, reverse=True)
        issues_to_delete = sorted_issues[keep_latest:]
        
        # 删除旧期号的所有模型文件
        for issue in issues_to_delete:
            for pattern in [f'*_model_{issue}.*']:
                for file in model_dir.glob(pattern):
                    file.unlink()
                    logger.info(f"删除旧模型文件: {file}")

def compress_old_models(archive_after_days: int = 30):
    """压缩旧的模型文件"""
    import zipfile
    from datetime import datetime, timedelta
    
    cutoff_date = datetime.now() - timedelta(days=archive_after_days)
    
    for position in ['hundreds', 'tens', 'units']:
        model_dir = Path(f'models/{position}')
        archive_dir = model_dir / 'archive'
        archive_dir.mkdir(exist_ok=True)
        
        # 找到需要压缩的文件
        old_files = []
        for file in model_dir.glob("*_model_*.pkl"):
            if file.stat().st_mtime < cutoff_date.timestamp():
                old_files.append(file)
        
        if old_files:
            # 按期号分组压缩
            issues_to_archive = {}
            for file in old_files:
                match = re.search(r'_(\d{7})\.', file.name)
                if match:
                    issue = match.group(1)
                    if issue not in issues_to_archive:
                        issues_to_archive[issue] = []
                    issues_to_archive[issue].append(file)
            
            # 为每个期号创建压缩包
            for issue, files in issues_to_archive.items():
                archive_name = f"{position}_models_{issue}.zip"
                archive_path = archive_dir / archive_name
                
                with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zf:
                    for file in files:
                        zf.write(file, file.name)
                        file.unlink()  # 删除原文件
                
                logger.info(f"压缩归档: {archive_path}")
```

## 用户使用场景

### 场景1: 日常自动运行
```
1. 每天21:30，2025218期开奖
2. 系统自动检测到新期号
3. 自动训练2025218期的所有模型
4. 用户第二天可以选择2025218期模型预测2025219期
```

### 场景2: 手动选择模型
```
1. 用户打开Web界面
2. 期号选择下拉框显示: [2025218期模型, 2025217期模型, 2025216期模型...]
3. 用户选择2025217期模型
4. 系统使用2025217期训练的模型预测2025218期
```

### 场景3: 模型对比分析
```
1. 用户可以选择不同期号的模型进行预测
2. 比较2025217期模型 vs 2025216期模型的预测结果
3. 分析模型性能随时间的变化趋势
```

## 技术优势

### 1. 完全自动化
- 无需手动指定期号
- 自动检测新数据
- 自动触发训练
- 自动文件管理

### 2. 灵活性
- 支持任意期号模型选择
- 支持模型性能对比
- 支持历史模型回退

### 3. 可维护性
- 统一的命名规则
- 自动清理机制
- 完整的日志记录
- 错误恢复机制

### 4. 扩展性
- 易于添加新的模型类型
- 支持分布式训练
- 支持云端存储
- 支持API扩展

---

**设计完成时间**: 2025-08-16 05:00
**核心特点**: 完全动态化、自动化、无人工干预
**适用场景**: 生产环境长期运行
**维护成本**: 极低，自动化程度高