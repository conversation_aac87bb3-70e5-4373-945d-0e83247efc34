#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件编码修复
"""

import ast
import sys
from pathlib import Path

def test_file_encoding(filename):
    """测试文件编码"""
    try:
        # 尝试用UTF-8编码读取
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试解析语法
        ast.parse(content)
        
        print(f"✅ {filename}: UTF-8编码，语法正确")
        return True
        
    except UnicodeDecodeError as e:
        print(f"❌ {filename}: UTF-8编码错误 - {e}")
        return False
    except SyntaxError as e:
        print(f"❌ {filename}: 语法错误 - {e}")
        return False
    except Exception as e:
        print(f"❌ {filename}: 其他错误 - {e}")
        return False

def main():
    """主函数"""
    print("🔍 测试文件编码修复")
    print("=" * 40)
    
    files_to_test = [
        'scripts/train_hundreds_predictor.py',
        'scripts/train_tens_predictor.py',
        'scripts/train_units_predictor.py'
    ]
    
    all_passed = True
    
    for filename in files_to_test:
        if not test_file_encoding(filename):
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有文件编码正确！")
    else:
        print("\n⚠️ 部分文件编码有问题")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
