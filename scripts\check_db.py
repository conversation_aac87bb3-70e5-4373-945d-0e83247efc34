#!/usr/bin/env python3
import sqlite3
from pathlib import Path

db_path = Path("data/fucai3d.db")
if not db_path.exists():
    print("数据库文件不存在")
    exit(1)

conn = sqlite3.connect(str(db_path))
cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()
print("数据库表:")
for table in tables:
    print(f"  - {table[0]}")
    
# 检查第一个表的结构
if tables:
    table_name = tables[0][0]
    cursor = conn.execute(f"PRAGMA table_info({table_name})")
    columns = cursor.fetchall()
    print(f"\n表 {table_name} 的结构:")
    for col in columns:
        print(f"  - {col[1]} ({col[2]})")
        
conn.close()
