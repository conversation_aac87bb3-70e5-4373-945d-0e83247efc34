#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量训练管理器
支持后台运行、进度输出和日志记录
"""

import sys
import subprocess
import time
import argparse
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional
import json
import gc
import psutil

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.memory_monitor import MemoryMonitor

class BatchTrainer:
    """批量训练管理器"""
    
    def __init__(self, log_dir: str = "logs/training"):
        """
        初始化批量训练管理器
        
        Args:
            log_dir: 日志目录
        """
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self.logger = self._setup_logger()
        
        # 内存监控器
        self.memory_monitor = None
        
        # 训练结果
        self.training_results = []
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('BatchTrainer')
        logger.setLevel(logging.INFO)
        
        # 清除现有处理器
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
            
        # 文件处理器
        log_file = self.log_dir / f"batch_train_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
        
    def train_single_model(self, position: str, model_type: str, issue: str = None, 
                          train_until: str = None) -> Dict:
        """
        训练单个模型
        
        Args:
            position: 位置 (hundreds/tens/units)
            model_type: 模型类型 (xgb/lgb/lstm/ensemble)
            issue: 训练期号
            train_until: 训练数据截止期号
            
        Returns:
            训练结果字典
        """
        start_time = time.time()
        
        # 构建命令
        script_name = f"train_{position}_predictor.py"
        cmd = [sys.executable, f"scripts/{script_name}", "--model", model_type]
        
        if issue:
            cmd.extend(["--issue", issue])
        if train_until:
            cmd.extend(["--train-until", train_until])
            
        self.logger.info(f"🚀 开始训练: {position} - {model_type}")
        self.logger.info(f"📝 命令: {' '.join(cmd)}")
        
        # 启动内存监控
        memory_log = self.log_dir / f"memory_{position}_{model_type}_{datetime.now().strftime('%H%M%S')}.log"
        self.memory_monitor = MemoryMonitor(str(memory_log), interval=2.0)
        self.memory_monitor.start_monitoring()
        
        try:
            # 执行训练
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                cwd=project_root
            )
            
            duration = time.time() - start_time
            
            # 停止内存监控
            self.memory_monitor.stop_monitoring()
            memory_stats = self.memory_monitor.get_memory_stats()
            
            # 强制垃圾回收
            gc.collect()
            
            training_result = {
                'position': position,
                'model_type': model_type,
                'issue': issue,
                'train_until': train_until,
                'success': result.returncode == 0,
                'duration_seconds': duration,
                'memory_peak_mb': memory_stats.get('memory_peak_mb', 0),
                'memory_avg_mb': memory_stats.get('memory_avg_mb', 0),
                'stdout': result.stdout,
                'stderr': result.stderr,
                'timestamp': datetime.now().isoformat()
            }
            
            if result.returncode == 0:
                self.logger.info(f"✅ 训练成功: {position} - {model_type} ({duration:.1f}s)")
                self.logger.info(f"📊 内存峰值: {memory_stats.get('memory_peak_mb', 0):.1f} MB")
            else:
                self.logger.error(f"❌ 训练失败: {position} - {model_type}")
                self.logger.error(f"错误输出: {result.stderr}")
                
            return training_result
            
        except Exception as e:
            self.logger.error(f"❌ 训练异常: {position} - {model_type} - {e}")
            if self.memory_monitor:
                self.memory_monitor.stop_monitoring()
            
            return {
                'position': position,
                'model_type': model_type,
                'issue': issue,
                'train_until': train_until,
                'success': False,
                'duration_seconds': time.time() - start_time,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            
    def train_position_all_models(self, position: str, issue: str = None, 
                                 train_until: str = None) -> List[Dict]:
        """
        训练指定位置的所有模型
        
        Args:
            position: 位置 (hundreds/tens/units)
            issue: 训练期号
            train_until: 训练数据截止期号
            
        Returns:
            训练结果列表
        """
        models = ['xgb', 'lgb', 'lstm', 'ensemble']
        results = []
        
        self.logger.info(f"🎯 开始训练位置: {position} (共{len(models)}个模型)")
        
        for i, model_type in enumerate(models, 1):
            self.logger.info(f"📈 进度: {i}/{len(models)} - {model_type}")
            
            result = self.train_single_model(position, model_type, issue, train_until)
            results.append(result)
            self.training_results.append(result)
            
            # 模型间休息，释放内存
            if i < len(models):
                self.logger.info("⏸️ 模型间休息 5 秒...")
                time.sleep(5)
                gc.collect()
                
        return results
        
    def train_all_positions(self, positions: List[str] = None, issue: str = None,
                           train_until: str = None) -> Dict:
        """
        训练所有位置的模型
        
        Args:
            positions: 位置列表，默认为所有位置
            issue: 训练期号
            train_until: 训练数据截止期号
            
        Returns:
            完整训练结果
        """
        if positions is None:
            positions = ['hundreds', 'tens', 'units']
            
        self.logger.info(f"🚀 开始批量训练: {len(positions)}个位置")
        self.logger.info(f"📋 位置列表: {', '.join(positions)}")
        
        start_time = time.time()
        all_results = {}
        
        for i, position in enumerate(positions, 1):
            self.logger.info(f"🎯 位置进度: {i}/{len(positions)} - {position}")
            
            position_results = self.train_position_all_models(position, issue, train_until)
            all_results[position] = position_results
            
            # 位置间休息，释放内存
            if i < len(positions):
                self.logger.info("⏸️ 位置间休息 10 秒...")
                time.sleep(10)
                gc.collect()
                
        total_duration = time.time() - start_time
        
        # 生成训练摘要
        summary = self._generate_summary(all_results, total_duration)
        self.logger.info("📊 训练摘要:")
        for line in summary.split('\n'):
            if line.strip():
                self.logger.info(line)
                
        # 保存结果
        self._save_results(all_results, summary)
        
        return {
            'results': all_results,
            'summary': summary,
            'total_duration': total_duration
        }
        
    def _generate_summary(self, results: Dict, total_duration: float) -> str:
        """生成训练摘要"""
        total_models = 0
        successful_models = 0
        total_memory_peak = 0
        
        for position, position_results in results.items():
            for result in position_results:
                total_models += 1
                if result['success']:
                    successful_models += 1
                total_memory_peak = max(total_memory_peak, result.get('memory_peak_mb', 0))
                
        success_rate = (successful_models / total_models * 100) if total_models > 0 else 0
        
        summary = f"""
批量训练完成摘要
{'=' * 50}
⏱️ 总耗时: {total_duration / 60:.1f} 分钟
📊 模型总数: {total_models}
✅ 成功数量: {successful_models}
❌ 失败数量: {total_models - successful_models}
📈 成功率: {success_rate:.1f}%
🧠 内存峰值: {total_memory_peak:.1f} MB
📅 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{'=' * 50}
        """.strip()
        
        return summary
        
    def _save_results(self, results: Dict, summary: str):
        """保存训练结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存详细结果
        results_file = self.log_dir / f"training_results_{timestamp}.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
            
        # 保存摘要
        summary_file = self.log_dir / f"training_summary_{timestamp}.txt"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary)
            
        self.logger.info(f"💾 结果已保存: {results_file}")
        self.logger.info(f"💾 摘要已保存: {summary_file}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="批量训练管理器")
    parser.add_argument('--positions', nargs='+', 
                       choices=['hundreds', 'tens', 'units'],
                       default=['hundreds', 'tens', 'units'],
                       help='要训练的位置')
    parser.add_argument('--issue', type=str, help='训练期号')
    parser.add_argument('--train-until', type=str, help='训练数据截止期号')
    parser.add_argument('--log-dir', type=str, default='logs/training',
                       help='日志目录')
    
    args = parser.parse_args()
    
    # 创建批量训练器
    trainer = BatchTrainer(args.log_dir)
    
    try:
        # 执行批量训练
        results = trainer.train_all_positions(
            positions=args.positions,
            issue=args.issue,
            train_until=args.train_until
        )
        
        print("\n🎉 批量训练完成!")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断训练")
    except Exception as e:
        print(f"\n❌ 训练异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
