#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多渠道通知系统
支持邮件、短信、浏览器通知等多种通知方式
"""

import sys
import smtplib
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from email.header import Header
import asyncio

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

@dataclass
class NotificationConfig:
    """通知配置"""
    email_enabled: bool = False
    sms_enabled: bool = False
    browser_enabled: bool = True
    webhook_enabled: bool = False
    
    # 邮件配置
    smtp_server: str = "smtp.gmail.com"
    smtp_port: int = 587
    email_user: str = ""
    email_password: str = ""
    
    # 短信配置（示例配置）
    sms_api_key: str = ""
    sms_api_url: str = ""
    
    # Webhook配置
    webhook_url: str = ""

@dataclass
class NotificationMessage:
    """通知消息"""
    title: str
    content: str
    priority: str = "normal"  # low, normal, high, urgent
    category: str = "training"  # training, system, error
    recipients: List[str] = None
    metadata: Dict[str, Any] = None

class NotificationSystem:
    """通知系统"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化通知系统
        
        Args:
            config_file: 配置文件路径
        """
        self.config = self._load_config(config_file)
        self.logger = logging.getLogger('NotificationSystem')
        self.logger.setLevel(logging.INFO)
        
        # 通知历史
        self.notification_history: List[Dict] = []
        self.max_history_size = 1000
        
        # 数据目录
        self.data_dir = Path("data/notifications")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
    def _load_config(self, config_file: Optional[str]) -> NotificationConfig:
        """加载配置"""
        if config_file and Path(config_file).exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                return NotificationConfig(**config_data)
            except Exception as e:
                self.logger.warning(f"加载配置文件失败: {e}")
                
        return NotificationConfig()
        
    async def send_notification(self, message: NotificationMessage) -> Dict[str, bool]:
        """
        发送通知
        
        Args:
            message: 通知消息
            
        Returns:
            各渠道发送结果
        """
        results = {
            'email': False,
            'sms': False,
            'browser': False,
            'webhook': False
        }
        
        # 记录通知历史
        notification_record = {
            'timestamp': datetime.now().isoformat(),
            'title': message.title,
            'content': message.content,
            'priority': message.priority,
            'category': message.category,
            'recipients': message.recipients or [],
            'results': results
        }
        
        try:
            # 邮件通知
            if self.config.email_enabled and message.recipients:
                results['email'] = await self._send_email(message)
                
            # 短信通知
            if self.config.sms_enabled and message.recipients:
                results['sms'] = await self._send_sms(message)
                
            # 浏览器通知
            if self.config.browser_enabled:
                results['browser'] = await self._send_browser_notification(message)
                
            # Webhook通知
            if self.config.webhook_enabled:
                results['webhook'] = await self._send_webhook(message)
                
        except Exception as e:
            self.logger.error(f"发送通知失败: {e}")
            
        # 更新结果
        notification_record['results'] = results
        self.notification_history.append(notification_record)
        
        # 限制历史记录大小
        if len(self.notification_history) > self.max_history_size:
            self.notification_history = self.notification_history[-self.max_history_size:]
            
        return results
        
    async def _send_email(self, message: NotificationMessage) -> bool:
        """发送邮件通知"""
        if not self.config.email_user or not self.config.email_password:
            self.logger.warning("邮件配置不完整")
            return False
            
        try:
            # 创建邮件
            msg = MimeMultipart()
            msg['From'] = self.config.email_user
            msg['To'] = ', '.join(message.recipients)
            msg['Subject'] = Header(f"[福彩3D训练系统] {message.title}", 'utf-8')
            
            # 邮件内容
            html_content = self._generate_email_html(message)
            msg.attach(MimeText(html_content, 'html', 'utf-8'))
            
            # 发送邮件
            server = smtplib.SMTP(self.config.smtp_server, self.config.smtp_port)
            server.starttls()
            server.login(self.config.email_user, self.config.email_password)
            server.send_message(msg)
            server.quit()
            
            self.logger.info(f"邮件通知发送成功: {message.title}")
            return True
            
        except Exception as e:
            self.logger.error(f"邮件发送失败: {e}")
            return False
            
    async def _send_sms(self, message: NotificationMessage) -> bool:
        """发送短信通知"""
        if not self.config.sms_api_key:
            self.logger.warning("短信配置不完整")
            return False
            
        try:
            # 这里是短信API调用的示例代码
            # 实际实现需要根据具体的短信服务商API
            import aiohttp
            
            sms_content = f"[福彩3D训练系统] {message.title}: {message.content}"
            
            async with aiohttp.ClientSession() as session:
                for phone in message.recipients:
                    payload = {
                        'api_key': self.config.sms_api_key,
                        'phone': phone,
                        'content': sms_content
                    }
                    
                    async with session.post(self.config.sms_api_url, json=payload) as response:
                        if response.status == 200:
                            self.logger.info(f"短信发送成功: {phone}")
                        else:
                            self.logger.error(f"短信发送失败: {phone}")
                            return False
                            
            return True
            
        except Exception as e:
            self.logger.error(f"短信发送失败: {e}")
            return False
            
    async def _send_browser_notification(self, message: NotificationMessage) -> bool:
        """发送浏览器通知"""
        try:
            # 通过WebSocket发送浏览器通知
            # 这里需要集成到现有的WebSocket系统
            notification_data = {
                'type': 'training_notification',
                'data': {
                    'title': message.title,
                    'content': message.content,
                    'priority': message.priority,
                    'category': message.category,
                    'timestamp': datetime.now().isoformat()
                }
            }
            
            # 这里应该调用WebSocket管理器的广播方法
            # 暂时记录日志
            self.logger.info(f"浏览器通知: {message.title}")
            return True
            
        except Exception as e:
            self.logger.error(f"浏览器通知发送失败: {e}")
            return False
            
    async def _send_webhook(self, message: NotificationMessage) -> bool:
        """发送Webhook通知"""
        if not self.config.webhook_url:
            return False
            
        try:
            import aiohttp
            
            payload = {
                'title': message.title,
                'content': message.content,
                'priority': message.priority,
                'category': message.category,
                'timestamp': datetime.now().isoformat(),
                'metadata': message.metadata or {}
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(self.config.webhook_url, json=payload) as response:
                    if response.status == 200:
                        self.logger.info(f"Webhook通知发送成功")
                        return True
                    else:
                        self.logger.error(f"Webhook通知发送失败: {response.status}")
                        return False
                        
        except Exception as e:
            self.logger.error(f"Webhook发送失败: {e}")
            return False
            
    def _generate_email_html(self, message: NotificationMessage) -> str:
        """生成邮件HTML内容"""
        priority_colors = {
            'low': '#28a745',
            'normal': '#007bff',
            'high': '#ffc107',
            'urgent': '#dc3545'
        }
        
        color = priority_colors.get(message.priority, '#007bff')
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>{message.title}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; }}
                .container {{ max-width: 600px; margin: 0 auto; }}
                .header {{ background-color: {color}; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; background-color: #f8f9fa; }}
                .footer {{ padding: 10px; text-align: center; color: #666; font-size: 12px; }}
                .priority {{ display: inline-block; padding: 4px 8px; border-radius: 4px; 
                           background-color: {color}; color: white; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>福彩3D训练系统通知</h1>
                </div>
                <div class="content">
                    <h2>{message.title}</h2>
                    <span class="priority">{message.priority.upper()}</span>
                    <p>{message.content}</p>
                    <hr>
                    <p><strong>时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    <p><strong>类别:</strong> {message.category}</p>
                </div>
                <div class="footer">
                    <p>此邮件由福彩3D训练系统自动发送，请勿回复。</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html
        
    def get_notification_history(self, limit: int = 50) -> List[Dict]:
        """获取通知历史"""
        return self.notification_history[-limit:]
        
    def get_notification_statistics(self) -> Dict:
        """获取通知统计"""
        if not self.notification_history:
            return {}
            
        total_notifications = len(self.notification_history)
        
        # 按渠道统计成功率
        channel_stats = {
            'email': {'sent': 0, 'success': 0},
            'sms': {'sent': 0, 'success': 0},
            'browser': {'sent': 0, 'success': 0},
            'webhook': {'sent': 0, 'success': 0}
        }
        
        for notification in self.notification_history:
            results = notification.get('results', {})
            for channel, success in results.items():
                if channel in channel_stats:
                    channel_stats[channel]['sent'] += 1
                    if success:
                        channel_stats[channel]['success'] += 1
                        
        # 计算成功率
        for channel, stats in channel_stats.items():
            if stats['sent'] > 0:
                stats['success_rate'] = stats['success'] / stats['sent'] * 100
            else:
                stats['success_rate'] = 0
                
        return {
            'total_notifications': total_notifications,
            'channel_statistics': channel_stats,
            'config_status': {
                'email_enabled': self.config.email_enabled,
                'sms_enabled': self.config.sms_enabled,
                'browser_enabled': self.config.browser_enabled,
                'webhook_enabled': self.config.webhook_enabled
            }
        }

# 全局通知系统实例
notification_system = NotificationSystem()

# 便捷函数
async def notify_training_completed(task_id: str, position: str, model_type: str, 
                                  duration: float, recipients: List[str] = None):
    """训练完成通知"""
    message = NotificationMessage(
        title="训练任务完成",
        content=f"{position} {model_type} 模型训练已完成，耗时 {duration:.1f} 秒",
        priority="normal",
        category="training",
        recipients=recipients,
        metadata={'task_id': task_id, 'position': position, 'model_type': model_type}
    )
    
    return await notification_system.send_notification(message)

async def notify_training_failed(task_id: str, position: str, model_type: str, 
                               error: str, recipients: List[str] = None):
    """训练失败通知"""
    message = NotificationMessage(
        title="训练任务失败",
        content=f"{position} {model_type} 模型训练失败: {error}",
        priority="high",
        category="training",
        recipients=recipients,
        metadata={'task_id': task_id, 'position': position, 'model_type': model_type, 'error': error}
    )
    
    return await notification_system.send_notification(message)

async def notify_system_alert(title: str, content: str, priority: str = "high", 
                            recipients: List[str] = None):
    """系统警报通知"""
    message = NotificationMessage(
        title=title,
        content=content,
        priority=priority,
        category="system",
        recipients=recipients
    )
    
    return await notification_system.send_notification(message)

if __name__ == "__main__":
    # 测试代码
    async def test_notifications():
        print("📧 测试通知系统")
        
        # 测试训练完成通知
        await notify_training_completed(
            task_id="test-123",
            position="hundreds",
            model_type="xgb",
            duration=280.5
        )
        
        # 获取统计信息
        stats = notification_system.get_notification_statistics()
        print(f"通知统计: {stats}")
        
    # 运行测试
    asyncio.run(test_notifications())
