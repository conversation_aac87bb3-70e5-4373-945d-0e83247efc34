import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Badge,
  Progress,
  Select,
  Input,
  Form,
  Tabs,
  Table,
  Alert,
  Row,
  Col,
  Statistic,
  Tag,
  Space,
  message,
  Spin,
  Checkbox,
} from 'antd';
import {
  PlayCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  MonitorOutlined,
  CpuOutlined,
  DatabaseOutlined,
} from '@ant-design/icons';

const { Option } = Select;
const { TabPane } = Tabs;

// 类型定义
interface TrainingTask {
  task_id: string;
  position: string;
  model_type: string;
  issue?: string;
  train_until?: string;
  priority: string;
  status: string;
  progress: number;
  message: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  duration_seconds?: number;
  result?: any;
  error?: string;
}

interface QueueStatus {
  total_tasks: number;
  pending: number;
  running: number;
  completed: number;
  failed: number;
  queue_length: number;
  max_concurrent: number;
}

interface SystemMetrics {
  cpu_percent: number;
  memory_percent: number;
  memory_used_gb: number;
  memory_total_gb: number;
}

const TrainingManagerAntd: React.FC = () => {
  // 状态管理
  const [tasks, setTasks] = useState<TrainingTask[]>([]);
  const [queueStatus, setQueueStatus] = useState<QueueStatus | null>(null);
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('create');
  
  // 表单实例
  const [singleForm] = Form.useForm();
  const [batchForm] = Form.useForm();

  // WebSocket连接
  const [ws, setWs] = useState<WebSocket | null>(null);

  // 获取任务列表
  const fetchTasks = useCallback(async () => {
    try {
      const response = await fetch('/api/training/tasks');
      if (response.ok) {
        const data = await response.json();
        setTasks(data.tasks || []);
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
    }
  }, []);

  // 获取队列状态
  const fetchQueueStatus = useCallback(async () => {
    try {
      const response = await fetch('/api/training/queue/status');
      if (response.ok) {
        const data = await response.json();
        setQueueStatus(data.queue);
        setSystemMetrics(data.system_metrics);
      }
    } catch (error) {
      console.error('获取队列状态失败:', error);
    }
  }, []);

  // WebSocket连接管理
  useEffect(() => {
    const connectWebSocket = () => {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/ws`;
      
      const websocket = new WebSocket(wsUrl);
      
      websocket.onopen = () => {
        console.log('WebSocket连接已建立');
        setWs(websocket);
      };
      
      websocket.onmessage = (event) => {
        try {
          const messageData = JSON.parse(event.data);
          handleWebSocketMessage(messageData);
        } catch (error) {
          console.error('解析WebSocket消息失败:', error);
        }
      };
      
      websocket.onclose = () => {
        console.log('WebSocket连接已断开');
        setWs(null);
        // 5秒后重连
        setTimeout(connectWebSocket, 5000);
      };
      
      websocket.onerror = (error) => {
        console.error('WebSocket错误:', error);
      };
    };

    connectWebSocket();

    return () => {
      if (ws) {
        ws.close();
      }
    };
  }, []);

  // 处理WebSocket消息
  const handleWebSocketMessage = (messageData: any) => {
    switch (messageData.type) {
      case 'training_progress':
        updateTaskProgress(messageData.data);
        break;
      case 'training_started':
        handleTrainingStarted(messageData.data);
        break;
      case 'training_completed':
        handleTrainingCompleted(messageData.data);
        break;
      case 'training_failed':
        handleTrainingFailed(messageData.data);
        break;
      case 'queue_status_update':
        setQueueStatus(messageData.data);
        break;
      default:
        break;
    }
  };

  // 更新任务进度
  const updateTaskProgress = (data: any) => {
    setTasks(prevTasks => 
      prevTasks.map(task => 
        task.task_id === data.task_id 
          ? { ...task, progress: data.progress, message: data.message, status: data.status }
          : task
      )
    );
  };

  // 处理训练开始
  const handleTrainingStarted = (data: any) => {
    message.success(`${data.position} ${data.model_type} 模型训练已开始`);
    fetchTasks();
  };

  // 处理训练完成
  const handleTrainingCompleted = (data: any) => {
    message.success(`${data.position} ${data.model_type} 模型训练已完成`);
    fetchTasks();
  };

  // 处理训练失败
  const handleTrainingFailed = (data: any) => {
    message.error(`${data.position} ${data.model_type} 模型训练失败`);
    fetchTasks();
  };

  // 初始化数据
  useEffect(() => {
    fetchTasks();
    fetchQueueStatus();
    
    // 定期刷新数据
    const interval = setInterval(() => {
      fetchTasks();
      fetchQueueStatus();
    }, 10000);

    return () => clearInterval(interval);
  }, [fetchTasks, fetchQueueStatus]);

  // 启动单个训练任务
  const startTraining = async (values: any) => {
    setLoading(true);
    try {
      const response = await fetch('/api/training/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (response.ok) {
        const result = await response.json();
        message.success(`训练任务已创建: ${result.task_id}`);
        singleForm.resetFields();
        fetchTasks();
      } else {
        const error = await response.json();
        throw new Error(error.detail || '创建任务失败');
      }
    } catch (error) {
      message.error(error instanceof Error ? error.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  // 启动批量训练任务
  const startBatchTraining = async (values: any) => {
    setLoading(true);
    try {
      const response = await fetch('/api/training/batch-start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (response.ok) {
        const result = await response.json();
        message.success(`已创建 ${result.total_tasks} 个训练任务`);
        batchForm.resetFields();
        fetchTasks();
      } else {
        const error = await response.json();
        throw new Error(error.detail || '创建批量任务失败');
      }
    } catch (error) {
      message.error(error instanceof Error ? error.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  // 取消任务
  const cancelTask = async (taskId: string) => {
    try {
      const response = await fetch(`/api/training/task/${taskId}/cancel`, {
        method: 'POST',
      });

      if (response.ok) {
        message.success(`任务 ${taskId} 已取消`);
        fetchTasks();
      } else {
        const error = await response.json();
        throw new Error(error.detail || '取消任务失败');
      }
    } catch (error) {
      message.error(error instanceof Error ? error.message : '未知错误');
    }
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusConfig = {
      pending: { color: 'default', icon: <ClockCircleOutlined />, text: '等待中' },
      running: { color: 'processing', icon: <MonitorOutlined />, text: '运行中' },
      completed: { color: 'success', icon: <CheckCircleOutlined />, text: '已完成' },
      failed: { color: 'error', icon: <CloseCircleOutlined />, text: '失败' },
      cancelled: { color: 'default', icon: <StopOutlined />, text: '已取消' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;

    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  // 格式化时长
  const formatDuration = (seconds?: number) => {
    if (!seconds) return '-';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 表格列定义
  const columns = [
    {
      title: '任务ID',
      dataIndex: 'task_id',
      key: 'task_id',
      render: (text: string) => (
        <span style={{ fontFamily: 'monospace', fontSize: '12px' }}>
          {text.slice(0, 8)}...
        </span>
      ),
    },
    {
      title: '位置',
      dataIndex: 'position',
      key: 'position',
      render: (text: string) => {
        const positionMap = {
          hundreds: '百位',
          tens: '十位',
          units: '个位'
        };
        return positionMap[text as keyof typeof positionMap] || text;
      },
    },
    {
      title: '模型',
      dataIndex: 'model_type',
      key: 'model_type',
      render: (text: string) => {
        const modelMap = {
          xgb: 'XGBoost',
          lgb: 'LightGBM',
          lstm: 'LSTM',
          ensemble: '集成'
        };
        return modelMap[text as keyof typeof modelMap] || text;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number) => (
        <div style={{ width: 100 }}>
          <Progress percent={progress} size="small" />
        </div>
      ),
    },
    {
      title: '时长',
      dataIndex: 'duration_seconds',
      key: 'duration_seconds',
      render: (duration: number) => formatDuration(duration),
    },
    {
      title: '操作',
      key: 'action',
      render: (record: TrainingTask) => (
        record.status === 'pending' || record.status === 'running' ? (
          <Button
            size="small"
            icon={<StopOutlined />}
            onClick={() => cancelTask(record.task_id)}
          >
            取消
          </Button>
        ) : null
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* 系统状态概览 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="队列状态"
              value={`${queueStatus?.running || 0} / ${queueStatus?.max_concurrent || 1}`}
              prefix={<MonitorOutlined />}
              suffix="运行中 / 最大并发"
            />
            <div style={{ marginTop: 16, fontSize: '14px' }}>
              <div>等待: {queueStatus?.pending || 0}</div>
              <div>已完成: {queueStatus?.completed || 0}</div>
              <div>失败: {queueStatus?.failed || 0}</div>
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="CPU使用率"
              value={systemMetrics?.cpu_percent?.toFixed(1) || 0}
              suffix="%"
              prefix={<CpuOutlined />}
            />
            <Progress 
              percent={systemMetrics?.cpu_percent || 0} 
              size="small"
              style={{ marginTop: 16 }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="内存使用"
              value={systemMetrics?.memory_percent?.toFixed(1) || 0}
              suffix="%"
              prefix={<DatabaseOutlined />}
            />
            <div style={{ fontSize: '12px', color: '#666', marginTop: 8 }}>
              {systemMetrics?.memory_used_gb?.toFixed(1) || 0}GB / {systemMetrics?.memory_total_gb?.toFixed(1) || 0}GB
            </div>
            <Progress 
              percent={systemMetrics?.memory_percent || 0} 
              size="small"
              style={{ marginTop: 8 }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="创建任务" key="create">
            <Form
              form={singleForm}
              layout="vertical"
              onFinish={startTraining}
              initialValues={{ priority: 'normal' }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="位置"
                    name="position"
                    rules={[{ required: true, message: '请选择位置' }]}
                  >
                    <Select placeholder="选择位置">
                      <Option value="hundreds">百位</Option>
                      <Option value="tens">十位</Option>
                      <Option value="units">个位</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="模型类型"
                    name="model_type"
                    rules={[{ required: true, message: '请选择模型类型' }]}
                  >
                    <Select placeholder="选择模型">
                      <Option value="xgb">XGBoost</Option>
                      <Option value="lgb">LightGBM</Option>
                      <Option value="lstm">LSTM</Option>
                      <Option value="ensemble">集成模型</Option>
                      <Option value="all">所有模型</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="训练期号" name="issue">
                    <Input placeholder="如: 2025217 (可选)" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="截止期号" name="train_until">
                    <Input placeholder="如: 2025216 (可选)" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="优先级" name="priority">
                    <Select>
                      <Option value="low">低</Option>
                      <Option value="normal">普通</Option>
                      <Option value="high">高</Option>
                      <Option value="urgent">紧急</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              <Form.Item>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  loading={loading}
                  icon={<PlayCircleOutlined />}
                  size="large"
                  block
                >
                  开始训练
                </Button>
              </Form.Item>
            </Form>
          </TabPane>

          <TabPane tab="批量训练" key="batch">
            <Form
              form={batchForm}
              layout="vertical"
              onFinish={startBatchTraining}
              initialValues={{ priority: 'normal' }}
            >
              <Form.Item label="位置 (可多选)" name="positions">
                <Checkbox.Group>
                  <Row>
                    <Col span={8}><Checkbox value="hundreds">百位</Checkbox></Col>
                    <Col span={8}><Checkbox value="tens">十位</Checkbox></Col>
                    <Col span={8}><Checkbox value="units">个位</Checkbox></Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>

              <Form.Item label="模型类型 (可多选)" name="model_types">
                <Checkbox.Group>
                  <Row>
                    <Col span={6}><Checkbox value="xgb">XGBoost</Checkbox></Col>
                    <Col span={6}><Checkbox value="lgb">LightGBM</Checkbox></Col>
                    <Col span={6}><Checkbox value="lstm">LSTM</Checkbox></Col>
                    <Col span={6}><Checkbox value="ensemble">集成模型</Checkbox></Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="训练期号" name="issue">
                    <Input placeholder="如: 2025217 (可选)" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="截止期号" name="train_until">
                    <Input placeholder="如: 2025216 (可选)" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item label="优先级" name="priority">
                <Select>
                  <Option value="low">低</Option>
                  <Option value="normal">普通</Option>
                  <Option value="high">高</Option>
                  <Option value="urgent">紧急</Option>
                </Select>
              </Form.Item>

              <Form.Item>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  loading={loading}
                  icon={<PlayCircleOutlined />}
                  size="large"
                  block
                >
                  开始批量训练
                </Button>
              </Form.Item>
            </Form>
          </TabPane>

          <TabPane tab="任务监控" key="monitor">
            <div style={{ marginBottom: 16 }}>
              <Button 
                icon={<ReloadOutlined />}
                onClick={() => {
                  fetchTasks();
                  fetchQueueStatus();
                }}
              >
                刷新
              </Button>
            </div>
            <Table
              columns={columns}
              dataSource={tasks}
              rowKey="task_id"
              pagination={{ pageSize: 10 }}
              size="small"
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default TrainingManagerAntd;
